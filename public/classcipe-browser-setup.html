<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Classcipe in Google Slides: Browser Setup</title>
    <style scoped>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
        }
        .browser-setup-container {
            max-width: 800px;
            margin: auto;
        }
        .browser-setup-container h3 {
          margin-bottom: 0;
          font-size: 16px;
        }
        .browser-setup-container ul {
          margin-top: 0;
          padding-left: 30px;
        }
        h1, h2 {
            color: #222;
        }
        .logo {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 24px;
            font-weight: bold;
            color: #26A69A;
        }
        .browser-section {
          margin-bottom: 25px;
        }
        .browser-section img {
            width: 100%;
            max-width: 600px;
            display: block;
            margin: 30px 0;
        }
        .note {
            font-size: 14px;
            font-style: italic;
            color: gray;
        }

        .browser-setup-container .brand-color {
          color: #26A69A;
        }

        .browser-setup-container .disclaimer {
          color: #808080;
          font-size: 13px;
        }
        .browser-setup-container .section-change {
          margin-top: 80px;
        }
    </style>
</head>
<body>
    <div class="browser-setup-container">
        <div class="logo">
            <img src="logo.png" alt="Classcipe Logo" width="40">
            Classcipe
        </div>

        <h1 style="margin-top: 60px;">Classcipe in Google Slides: Browser Setup</h1>
        <p>Classcipe’s add-on for Google Slides makes it easier than ever to create, edit, and collaborate on Classcipe lessons. Leverage the flexible editing experience of Google Slides while embedding your favorite Classcipe activities and media within.</p>
        <p>Before getting started, we have a few tips and tricks to ensure you have the best experience with the add-on. We recommend jumping to the section of the browser that you are using.</p>

        <i class="note">
          *Disclaimer: The following tips and tricks are suggestions that will allow your browser to be compatible with the Classcipe add-on in Google Slides. Making adjustments to the following settings may impact your browser security that may have been setup by your School/District.  It is at your discretion to enable or disable these browser settings.*
        </i>

        <h2 class="brand-color section-change">Google Chrome</h2>
        <div class="browser-section">
            <h3>- Check that your browser is up to date</h3>
            <ul>
              <li>
                With the browser open, open ‘About Chrome’
              </li>
              <img src="img/browser-settings/image-0.png" alt="About Chrome Menu">
              <li>Update your browser if needed.</li>
              <img src="img/browser-settings/image-1.png" alt="Chrome Update Screen">
            </ul>

        </div>

        <div class="browser-section">
            <h3>-  
               - Enable cookies for Classcipe in your browser</h3>
            <ul>
              <li>Check if your browser is displaying this icon:</li>
              <img src="img/browser-settings/image-2.png" alt="Blocked Cookies Icon">
              <li>If yes, click on the icon and then click ‘Manage’.</li>
              <img src="img/browser-settings/image-3.png" alt="Blocked Cookies Icon">
              <li>If you have “Block third-party cookies” enabled, please disable the toggle</li>
              <img src="img/browser-settings/image-4.png" alt="Blocked Cookies Icon">
              <li>After disabling the toggle for “Block third-party cookies,” please refresh the browser in Google Slides and try again</li>
            </ul>
        </div>

        <div class="browser-section">
          <h3>- Ensure that you are only logged into one account within the browser</h3>
          <ul>
            <li>Sometimes, you may be logged into multiple accounts within the browser. For example, you may be logged into your personal and work accounts at the same time. If this is the case, please sign out from all accounts, and sign in, using your preferred Google account for Classcipe.</li>
          </ul>
      </div>

      <h2 class="brand-color section-change">Safari</h2>
      <div class="browser-section">
          <h3>- Check that pop-ups are not blocked</h3>
          <ul>
            <li>
              With your browser open to Google Slides with the add-on open, click on ‘Safari’ and open “Settings for this website”
            </li>
            <img src="img/browser-settings/image-5.png" alt="About Chrome Menu">
            <li>Click to allow pop-ups</li>
            <img src="img/browser-settings/image-6.png" alt="Chrome Update Screen">
            <li>Refresh the browser and try again</li>
          </ul>
          <h3>- Check if you are preventing “Cross-site tracking”</h3>
          <ul>
            <li>
            With the browser open to Google Slides + the Classcipe add-on, click on ‘Safari’ then click on ‘Preferences’
            </li>
            <img src="img/browser-settings/image-7.png" alt="Chrome Update Screen">
            <li>
              Click on ‘Privacy’ then uncheck the  ‘Prevent cross-site tracking’ box
              </li>
              <img src="img/browser-settings/image-8.png" alt="Chrome Update Screen">
          </ul>
          <h3>- Enable cookies for Classcipe in your browser</h3>
          <ul>
            <li>
              With the browser open to Google Slides + the Classcipe add-on, click on ‘Safari’ then click on ‘Preferences’
            </li>
            <img src="img/browser-settings/image-9.png" alt="Chrome Update Screen">
            <li>
              Click on ‘Privacy’ then uncheck the ‘Block all cookies’ box
              </li>
              <img src="img/browser-settings/image-10.png" alt="Chrome Update Screen">
              <li>Refresh the browser and try again</li>
          </ul>
      </div>

    </div>
</body>
</html>
