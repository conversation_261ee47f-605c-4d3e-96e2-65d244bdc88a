<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
  </head>
  <body>
    <div id="container">
      <h3>WebRTC捕捉屏幕示例 getDisplayMedia</h3>

      <video style="width: 640px;height:320px;" id="gum-local" autoplay playsinline muted></video>
      <button id="startBtn" >开始预览</button>
      <button id="recordBtn" onclick="stopRecord()">开始录制</button>
      <button id="downloadBtn" disabled>下载</button>

      <div id="msg"></div>
    </div>
  </body>
  <!-- <script src="adapter-latest.js" async></script> -->
  <script src="record.js"></script>
</html>
