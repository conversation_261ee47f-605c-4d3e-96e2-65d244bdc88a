<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Logout</title>
  <script src="/v2/plugin/Acan.js"></script>
</head>
<body>
<script>
const ubj = new URL(location.href)
const sid = ubj.searchParams.get('sid')
const from = ubj.searchParams.get('from')
const keys = []
for (let i = 0; i < localStorage.length; i++) keys.push(localStorage.key(i));
function sidRemove() {
  if (!sid) return
  keys.map(k => {
    if (!k.includes(sid)) return
    localStorage.removeItem(k)
  })
}
Acan.cookie.del('feathers-jwt')
switch (from) {
  case 'roomTeacher': // class room
      localStorage.removeItem('feathers-jwt')
      localStorage.removeItem('teacher_token')
    break;
  case 'roomStudent':
    sidRemove()
    keys.map(k => {
      if (!k.includes('student_token:')) return
      localStorage.removeItem(k)
    })
    break;
  case 'student':
    sidRemove()
    keys.map(k => {
      if (!k.includes('student_token:')) return
      localStorage.removeItem(k)
    })
    break;
  default: // www
    localStorage.removeItem('feathers-jwt')
    localStorage.removeItem('Access-Token')
    localStorage.removeItem('token')
    localStorage.removeItem('USER_INFO')
    localStorage.removeItem('Current-Role')
    break;
}
if (top.logoutCall) top.logoutCall()
else console.log('close'),window.close()
</script>
</body>
</html>