Object.assign(Array.prototype, {
  setKey(key) {
    this._key = key
    this._list = {}
    for (const o of this) {
      if (o[key] === undefined) {
        delete this._list, delete this._key
        return false
      }
      this._list[o[key]] = o
    }
    return true
  },
  get(v) {
    return this._list[v]
  },
  up(arr) {
    const drr = Object.keys(this._list)
    let add = 0,
      up = 0
    for (const o of arr) {
      const i = drr.indexOf(o[this._key] + '')
      if (i !== -1) drr.splice(i, 1), up++
      else add++
      this.set(o)
    }
    for (const v of drr) {
      let o = {}
      o[this._key] = v
      this.del(o)
    }
    return {add, up, del: drr.length}
  },
  set(o) {
    if (!this._key) return false
    const v = o[this._key]
    if (this._list[v]) Object.assign(this._list[v], o)
    else {
      this._list[v] = o
      this.push(o)
    }
    return true
  },
  del(obj) {
    const ov = obj[this._key]
    let i = 0
    for (const o of this) {
      i++
      if (ov != o[this._key]) continue
      delete this._list[ov]
      return this.splice(i - 1, 1)[0]
    }
  },
})

if (!window.Fn) window.Fn = {}
Fn.isDev = /(dev|localhost|^\d[\d.]+)/.test(location.hostname)
Fn.urlUnite = (uri, obj) => {
  const u = new URL(location.origin + uri)
  for (const key of Object.keys(obj)) u.searchParams.set(key, obj[key])
  return u.href
}
Fn.fileIcon = (str) => {
  if (!str || !str.split) return 'file'
  const ext = str.toLowerCase().split(':')[1]
  return {doc: 'word', docx: 'word', pdf: 'pdf', jpg: 'image', jpeg: 'image', png: 'image'}[ext] || 'file'
}
Fn.fileType = (str) => {
  if (!str || !str.split) return 'file'
  const ext = str.toLowerCase().split(':')[1]
  let type = 'file'
  if (['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(ext)) type = 'image'
  else if (['mov', 'mp4', 'webm'].includes(ext)) type = 'video'
  else if (['mp3', 'wav', 'aac'].includes(ext)) type = 'audio'
  else if (['doc', 'docx'].includes(ext)) type = 'word'
  else if (['pdf'].includes(ext)) type = 'pdf'
  else if (['task'].includes(ext)) type = 'task'
  return type
}
Fn.youtubeIdThumb = (id) => {
  if (!id) return ''
  const rs = `https://i3.ytimg.com/vi/${id}/hqdefault.jpg`
  return rs
}
Fn.youtubeThumb = (url) => {
  if (!url) return ''
  const mrr = url.match(/(\/|=)([\w\d-]{8,18})/)
  console.log(url, mrr[2])
  return Fn.youtubeIdThumb(mrr[2])
}
Fn.hashToUrl = (hash) => {
  if (!hash) return
  if (hash.includes('://')) return hash
  if (hash.indexOf('/v2/') === 0) return hash
  return `https://r2${Fn.isDev ? 'dev' : ''}.classcipe.com/${hash.split(':')[0]}`
  if (hash.includes('/')) return `https://d19jvc3fkrigab.cloudfront.net/${hash.split(':')[0]}`
  return `https://d19jvc3fkrigab.cloudfront.net/${Fn.isDev ? 'dev' : 'line'}/${hash.split(':')[0]}`
  return `https://classcipe.s3.ap-southeast-2.amazonaws.com/${Fn.isDev ? 'dev' : 'line'}/${hash.split(':')[0]}`
}
Fn.jsonHeaders = () => {
  return {'Content-Type': 'application/json', Authorization: Fn.token}
}
Fn.fileHeaders = () => {
  return {Authorization: Fn.token}
} // 'Content-Type': 'multipart/form-data',
Fn.fileLoading = async (file, callbackProgress, callbackFinal) => {
  var chunkSize = 1024 * 1024 * 2 // bytes
  var offset = 0
  var size = chunkSize
  let index = 0
  if (file.size === 0) callbackFinal(1)
  while (offset < file.size) {
    var reader = new FileReader()
    reader.size = chunkSize
    reader.offset = offset
    reader.index = index
    await new Promise((res) => {
      reader.onload = (evt) => {
        callbackProgress(evt.target.result)
        if (reader.offset + reader.size >= file.size) callbackFinal()
        res()
      }
      reader.readAsArrayBuffer(file.slice(offset, offset + size))
    })
    offset += chunkSize
    index += 1
  }
}
Fn.fileHash = async (file, progressCb) => {
  return new Promise((res) => {
    var counter = 0
    const hash = CryptoJS.algo.SHA1.create()
    const _st = Date.now()
    let speed = ''
    Fn.fileLoading(
      file,
      (data) => {
        var wordBuffer = CryptoJS.lib.WordArray.create(data)
        hash.update(wordBuffer)
        counter += data.byteLength
        speed = (counter / (Date.now() - _st) / 1024).toFixed(2)
        progressCb(parseFloat(((counter / file.size) * 100).toFixed(0)), speed)
      },
      (f) => {
        const sha1 = hash.finalize().toString()
        res(sha1)
        progressCb(100, speed, sha1)
      }
    )
  })
}
Fn.fileUpLoadUiX = async (accept, newUI) => {
  return new Promise((res) => {
    const input = document.createElement('input')
    window.addEventListener(
      'focus',
      async () => {
        await sleep(1000)
        if (input.files[0]) return
        console.log(input.files, '<=========fileUpLoadUiX')
        res()
      },
      {once: true}
    )
    input.addEventListener('change', async () => {
      const file = input.files[0]
      if ((file.size / 1000000) > 100) {
        res({ message: 'The file is too large. The maximum file size is 100 MB.' })
        return console.warn('File type not accepted')
      }
      const [type,ext] = file.type.split('/')
      if (!accept.includes(type) && !accept.includes(ext)) {
        input.remove()
        res({ message: 'File type not accepted' })
        return console.warn('File type not accepted')
      }
      const rs = newUI ? await Fn.fileUpLoadUiNew(file) : await Fn.fileUpLoadUi(file)
      res(rs)
      input.remove()
    })
    input.type = 'file'
    input.accept = accept || '*/*'
    input.click()
  })
}
Fn.fileUpLoadUi = async (file, cb) => {
  const Did = 'Aupfile'
  let dom = document.getElementById(Did)
  if (!dom) {
    const div = document.createElement('div')
    div.id = Did
    div.innerHTML = `<div style="height:1.5rem;width:100%;background-color:#ebeef5;border-radius:0.75rem;text-align:right;">
<div class="_pg" style="display:block;background:green;height:100%;width:0%;border-radius:0.75rem;transition:width .6s ease;">
  <span style="color:white;margin:0.2rem;">80%</span>
</div></div>`
    const css = {position: 'fixed', 'z-index': 4000, top: 0, right: 0, height: '2rem', width: '12rem'}
    for (const key in css) div.style[key] = css[key]
    div.className = 'flex_c'
    document.body.append(div)
    dom = div
  }
  dom.style.display = 'block'
  const pgDom = dom.querySelector('._pg')
  const pgSpan = dom.querySelector('._pg span')
  const rs = await Fn.fileUpLoad(file, (pg, speed, sha1) => {
    if (cb) cb(pg, speed, sha1)
    const rpg = (pg * 0.495).toFixed(1)
    pgSpan.innerText = rpg + '%'
    pgDom.style.width = rpg + '%'
  })
  setTimeout(() => {
    dom.style.display = 'none'
  }, 500)
  if (file.type.includes('video')) {
    return new Promise((resolve, reject) => {
      const videoElement = document.createElement('video');
      if (videoElement) {
        const fileURL = URL.createObjectURL(file);
        videoElement.src = fileURL;
        videoElement.preload = 'metadata';
        videoElement.onloadedmetadata = () => {
          const duration = videoElement.duration;
          const canvas = document.createElement('canvas');
          canvas.width = videoElement.videoWidth;
          canvas.height = videoElement.videoHeight;
          const ctx = canvas.getContext('2d');
          // Set the time to capture the frame for the thumbnail
          videoElement.currentTime = 1; // Adjust as needed
          videoElement.onseeked = () => {
            ctx.drawImage(videoElement, 0, 0, canvas.width, canvas.height);
            const thumbnail = canvas.toDataURL('image/png');
            const finalRs = { ...rs, duration, thumbnail };
            resolve(finalRs);
          };
        };
        videoElement.onerror = (error) => {
          reject(new Error("Failed to load video metadata."));
        };
      } else {
        reject(new Error("Unable to create video element."));
      }
    });
  } else {
    return rs
  }
}

Fn.fileUpLoadUiNew = async (file, cb) => {
  const parentDiv = document.getElementById('upload_ui');
  if (!parentDiv) {
    console.error('Parent div with ID "upload_ui" not found.');
    return;
  }

  const Did = 'Aupfile';
  let dom = document.getElementById(Did);

  const icon = file.type.startsWith('video/')
    ? 'https://r2dev.classcipe.com/cccf072ced04c4ab2d1f908a2927ecd347d70032'
    : file.type.startsWith('image/')
    ? 'https://r2dev.classcipe.com/19b1fe6d5674134944633cdbb7490ba8036c1dea'
    : file.type.startsWith('audio/')
    ? 'https://r2dev.classcipe.com/6339d467fa22e89beab1c2bdcddf4c81fd8bfbc4'
    : 'https://r2dev.classcipe.com/a740dd5b9c668aa9356cd16aec06aa6cbf4de969';

  if (!dom) {
    const div = document.createElement('div');
    div.id = Did;
    div.innerHTML = `
      <div style="width: 100%;">
        <div class="_icon" style="margin-bottom: 0.5rem; display: flex; justify-content: center; align-items: center; height: 48px;"></div>
        <!-- Percentage and Uploading text -->
        <div style="display: flex; align-items: center; margin-bottom: 4px;">
          <span class="_pg-text" style="color: black; font-size: 13px; font-weight: 600">0%</span>
          <span style="color: grey; margin-left: 10px; font-size: 10px;">Uploading...</span>
        </div>

        <!-- Progress Bar -->
        <div style="height: 4px; background-color: #ebeef5; border-radius: 2px; overflow: hidden;">
          <div class="_pg" style="display: block; background: green; height: 100%; width: 0%; border-radius: 2px; transition: width .6s ease;"></div>
        </div>
      </div>
    `;
    div.className = 'flex_c';
    parentDiv.appendChild(div);
    dom = div;
  }

  dom.style.display = 'block';

  const pgDom = dom.querySelector('._pg');
  const pgText = dom.querySelector('._pg-text');
  const iconDom = dom.querySelector('._icon');

  if (pgDom) pgDom.style.width = '0%';
  if (pgText) pgText.innerText = '0%';
  console.log("iconDom", iconDom)
  if (iconDom) {
    iconDom.innerHTML = `<img src="${icon}" style="width: 68px; height: 68px;" />`
  }

  const rs = await Fn.fileUpLoad(file, (pg, speed, sha1) => {
    if (cb) cb(pg, speed, sha1);
    const rpg = (pg * 0.495).toFixed(1);
    pgText.innerText = rpg + '%';
    pgDom.style.width = rpg + '%';
  });

  if(rs) {
    dom.style.display = 'none';
  }

  if (file.type.includes('video')) {
    return new Promise((resolve, reject) => {
      const videoElement = document.createElement('video');
      const fileURL = URL.createObjectURL(file);
      videoElement.src = fileURL;
      videoElement.preload = 'metadata';

      videoElement.onloadedmetadata = () => {
        const duration = videoElement.duration;
        const canvas = document.createElement('canvas');
        canvas.width = videoElement.videoWidth;
        canvas.height = videoElement.videoHeight;
        const ctx = canvas.getContext('2d');

        videoElement.currentTime = 1;

        videoElement.onseeked = () => {
          ctx.drawImage(videoElement, 0, 0, canvas.width, canvas.height);
          const thumbnail = canvas.toDataURL('image/png');
          const finalRs = { ...rs, duration, thumbnail };
          resolve(finalRs);
        };
      };

      videoElement.onerror = () => reject(new Error("Failed to load video metadata."));
    });
  } else {
    return rs;
  }
};


Fn.fetchUpload = async (url, file, headers) => {
  const body = new FormData()
  body.append('file', file)
  return fetch(url, {method: 'POST', body, headers}).then((r) => r.json())
}
Fn.xhrUpload = async (url, file, headers, pgCb, pgx = 0) => {
  return new Promise((res) => {
    var formData = new FormData()
    formData.append('file', file)
    var xhr = new XMLHttpRequest()
    const _st = Date.now()
    xhr.upload.onprogress = (e) => {
      const pg = Math.round((e.loaded * 1000) / e.total) / 10
      const speed = (e.loaded / (Date.now() - _st) / 1024).toFixed(2)
      console.log('progress', pg, speed, e.loaded, e.total, Date.now() - _st)
      pgCb(pg + pgx, speed)
    }
    xhr.onreadystatechange = function () {
      if (this.readyState == 4 && this.status == 200) {
        res(JSON.parse(this.responseText))
        console.log('ok', Date.now() - _st)
      }
    }
    xhr.open('POST', url, true)
    Object.keys(headers).map((k) => xhr.setRequestHeader(k, headers[k]))
    xhr.send(formData)
  })
}
Fn.fileUpLoad = async (file, progressCb) => {
  if (App.authentication) Fn.token = await App.authentication.getAccessToken()
  const sha1 = await Fn.fileHash(file, progressCb)
  const old = await fetch(`/fio/files/${sha1}?filename=${file.name}`, {method: 'GET', headers: Fn.jsonHeaders()}).then((r) => r.json())
  if (old && old._id) return old
  console.log('fileUpload:', file)
  // const rs = await Fn.xhrUpload(`/api3/tool/upfile?sha1=${sha1}&filename=${file.name}`, file, Fn.fileHeaders(), progressCb, 100)
  const rs = await Fn.xhrUpload(`/fio/tool/upfile?sha1=${sha1}&filename=${file.name}`, file, Fn.fileHeaders(), progressCb, 100)
  // const body = new FormData()
  // body.append('file', file)
  // const rs = await fetch('/fio/tool/upfile?sha1='+sha1, { method: 'POST', body, headers: Fn.fileHeaders() }).then(r=>r.json())
  return {...rs}
}
Fn.fileRename = async (sha1, title) => {
  const body = JSON.stringify({$set: {title}})
  return await fetch(`/fio/files/${sha1}`, {method: 'PATCH', body, headers: Fn.jsonHeaders()}).then((r) => r.json())
}
Fn.fileRef = async (sha1, r) => {
  const body = JSON.stringify({$addToSet: {ref: {r}}})
  return await fetch(`/fio/files/${sha1}`, {method: 'PATCH', body, headers: Fn.jsonHeaders()}).then((r) => r.json())
}
Fn.fileUnRef = async (sha1, r) => {
  const body = JSON.stringify({$pull: {ref: {r}}})
  return await fetch(`/fio/files/${sha1}`, {method: 'PATCH', body, headers: Fn.jsonHeaders()}).then((r) => r.json())
}
Fn.fileUnRefAll = async (sha1) => {
  const body = JSON.stringify({$pull: {ref: {}}})
  return await fetch(`/fio/files/${sha1}`, {method: 'PATCH', body, headers: Fn.jsonHeaders()}).then((r) => r.json())
}
Fn.drawKey = async () => {
  const key = await window.crypto.subtle.generateKey({name: 'AES-GCM', length: 128}, true, ['encrypt', 'decrypt'])
  return (await window.crypto.subtle.exportKey('jwk', key)).k
}
