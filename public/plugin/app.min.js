// feathers.min.js
!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.feathers=t():e.feathers=t()}(this,(function(){return e={5898:function(e,t,r){var n;t.formatArgs=function(t){var r,n,o;t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),this.useColors&&(r="color: "+this.color,t.splice(1,0,r,"color: inherit"),t[o=n=0].replace(/%[a-zA-Z%]/g,(function(e){"%%"!==e&&(n++,"%c"===e&&(o=n))})),t.splice(o,0,r))},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){var e;try{e=t.storage.getItem("debug")}catch(e){}return!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG),e},t.useColors=function(){return!("undefined"==typeof window||!window.process||"renderer"!==window.process.type&&!window.process.__nwjs)||("undefined"==typeof navigator||!navigator.userAgent||!navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&31<=parseInt(RegExp.$1,10)||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(n=!1,function(){n||(n=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||function(){},e.exports=r(3604)(t),e.exports.formatters.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},3604:function(e,t,r){function n(e){return function(e){if(Array.isArray(e))return o(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return o(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Map"===(r="Object"===r&&e.constructor?e.constructor.name:r)||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?o(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function o(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}e.exports=function(e){function t(e){var r,n,i,a=null;function c(){for(var e,n,o,i,a=arguments.length,u=new Array(a),s=0;s<a;s++)u[s]=arguments[s];c.enabled&&(e=c,o=(n=Number(new Date))-(r||n),e.diff=o,e.prev=r,e.curr=n,r=n,u[0]=t.coerce(u[0]),"string"!=typeof u[0]&&u.unshift("%O"),u[i=0]=u[0].replace(/%([a-zA-Z%])/g,(function(r,n){if("%%"===r)return"%";i++;var o=t.formatters[n];return"function"==typeof o&&(n=u[i],r=o.call(e,n),u.splice(i,1),i--),r})),t.formatArgs.call(e,u),(e.log||t.log).apply(e,u))}return c.namespace=e,c.useColors=t.useColors(),c.color=t.selectColor(e),c.extend=o,c.destroy=t.destroy,Object.defineProperty(c,"enabled",{enumerable:!0,configurable:!1,get:function(){return null!==a?a:(n!==t.namespaces&&(n=t.namespaces,i=t.enabled(e)),i)},set:function(e){a=e}}),"function"==typeof t.init&&t.init(c),c}function o(e,r){return(e=t(this.namespace+(void 0===r?":":r)+e)).log=this.log,e}function i(e){return e.toString().substring(2,e.toString().length-2).replace(/\.\*\?$/,"*")}return((t.debug=t).default=t).coerce=function(e){return e instanceof Error?e.stack||e.message:e},t.disable=function(){var e=[].concat(n(t.names.map(i)),n(t.skips.map(i).map((function(e){return"-"+e})))).join(",");return t.enable(""),e},t.enable=function(e){var r;t.save(e),t.namespaces=e,t.names=[],t.skips=[];var n=("string"==typeof e?e:"").split(/[\s,]+/),o=n.length;for(r=0;r<o;r++)n[r]&&("-"===(e=n[r].replace(/\*/g,".*?"))[0]?t.skips.push(new RegExp("^"+e.substr(1)+"$")):t.names.push(new RegExp("^"+e+"$")))},t.enabled=function(e){if("*"===e[e.length-1])return!0;var r,n;for(r=0,n=t.skips.length;r<n;r++)if(t.skips[r].test(e))return!1;for(r=0,n=t.names.length;r<n;r++)if(t.names[r].test(e))return!0;return!1},t.humanize=r(1378),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach((function(r){t[r]=e[r]})),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){for(var r=0,n=0;n<e.length;n++)r=(r<<5)-r+e.charCodeAt(n),r|=0;return t.colors[Math.abs(r)%t.colors.length]},t.enable(t.load()),t}},3753:function(e,t,r){"use strict";function n(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i=[],a=!0,c=!1;try{for(r=r.call(e);!(a=(n=r.next()).done)&&(i.push(n.value),!t||i.length!==t);a=!0);}catch(e){c=!0,o=e}finally{try{a||null==r.return||r.return()}finally{if(c)throw o}}return i}}(e,t)||function(e,t){if(e){if("string"==typeof e)return o(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Map"===(r="Object"===r&&e.constructor?e.constructor.name:r)||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?o(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function o(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function i(e,t){return t=new RegExp("(?:&?)".concat(t,"=([^&]*)")),null===(e=e.hash?e.hash.match(t):null)?[null,t]:[n(e,2)[1],t]}Object.defineProperty(t,"__esModule",{value:!0}),t.AuthenticationClient=void 0;var a=r(6441),c=r(826);r=function(){function e(t,r){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e);var n=t.io||t.primus,o=new c.StorageWrapper(t.get("storage")||r.storage);this.app=t,this.options=r,this.authenticated=!1,this.app.set("storage",o),n&&this.handleSocket(n)}var t,r;return t=e,(r=[{key:"service",get:function(){return this.app.service(this.options.path)}},{key:"storage",get:function(){return this.app.get("storage")}},{key:"handleSocket",value:function(e){var t=this,r=this.app.io?"connect":"open",n=this.app.io?"disconnect":"disconnection";e.on(n,(function(){var n=new Promise((function(t){return e.once(r,(function(e){return t(e)}))})).then((function(){return t.authenticated?t.reAuthenticate(!0):null}));t.app.set("authentication",n)}))}},{key:"getFromLocation",value:function(e){var t=(r=n(i(e,this.options.locationKey),2))[0],r=r[1];return null!==t?(e.hash=e.hash.replace(r,""),Promise.resolve(t)):(t=(r=n(i(e,this.options.locationErrorKey),2))[0],r=r[1],null!==t?(e.hash=e.hash.replace(r,""),Promise.reject(new a.NotAuthenticated(decodeURIComponent(t)))):Promise.resolve(null))}},{key:"setAccessToken",value:function(e){return this.storage.setItem(this.options.storageKey,e)}},{key:"getAccessToken",value:function(){var e=this;return this.storage.getItem(this.options.storageKey).then((function(t){return!t&&"undefined"!=typeof window&&window.location?e.getFromLocation(window.location):t||null}))}},{key:"removeAccessToken",value:function(){return this.storage.removeItem(this.options.storageKey)}},{key:"reset",value:function(){return this.app.set("authentication",null),this.authenticated=!1,Promise.resolve(null)}},{key:"handleError",value:function(e,t){var r=this;if(401!==e.code&&403!==e.code)return Promise.reject(e);var n=this.removeAccessToken().then((function(){return r.reset()}));return"logout"===t?n:n.then((function(){return Promise.reject(e)}))}},{key:"reAuthenticate",value:function(){var e=this,t=0<arguments.length&&void 0!==arguments[0]&&arguments[0],r=1<arguments.length?arguments[1]:void 0,n=this.app.get("authentication");return n&&!0!==t?n:this.getAccessToken().then((function(t){if(!t)throw new a.NotAuthenticated("No accessToken found in storage");return e.authenticate({strategy:r||e.options.jwtStrategy,accessToken:t})}))}},{key:"authenticate",value:function(e,t){var r=this;return e?(t=this.service.create(e,t).then((function(e){var t=e.accessToken;return r.authenticated=!0,r.app.emit("login",e),r.app.emit("authenticated",e),r.setAccessToken(t).then((function(){return e}))})).catch((function(e){return r.handleError(e,"authenticate")})),this.app.set("authentication",t),t):this.reAuthenticate()}},{key:"logout",value:function(){var e=this;return Promise.resolve(this.app.get("authentication")).then((function(){return e.service.remove(null).then((function(t){return e.removeAccessToken().then((function(){return e.reset()})).then((function(){return e.app.emit("logout",t),t}))}))})).catch((function(t){return e.handleError(t,"logout")}))}}])&&function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),e}(),t.AuthenticationClient=r},3093:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.authentication=void 0;var n=r(6575);t.authentication=function(){return function(e){var t=e.app,r=e.params,o=e.path,i=e.method,a=e.app.authentication;return(0,n.stripSlashes)(a.options.path)===o&&"create"===i?e:Promise.resolve(t.get("authentication")).then((function(t){return t&&(e.params=Object.assign({},t,r)),e}))}}},8307:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.populateHeader=t.authentication=void 0;var n=r(3093);Object.defineProperty(t,"authentication",{enumerable:!0,get:function(){return n.authentication}});var o=r(442);Object.defineProperty(t,"populateHeader",{enumerable:!0,get:function(){return o.populateHeader}})},442:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.populateHeader=void 0,t.populateHeader=function(){return function(e){var t=e.app,r=e.params.accessToken,n=t.authentication;return t.rest&&r&&(n=(t=n.options).scheme,t=t.header,n="".concat(n," ").concat(r),e.params.headers=Object.assign({},(r=n,(n=t)in(t={})?Object.defineProperty(t,n,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[n]=r,t),e.params.headers)),e}}},2503:function(e,t,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){e[n=void 0===n?r:n]=t[r]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),i=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)"default"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&n(t,e,r);return o(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.defaults=t.defaultStorage=t.hooks=t.MemoryStorage=t.AuthenticationClient=t.getDefaultStorage=void 0;var a=r(3753);Object.defineProperty(t,"AuthenticationClient",{enumerable:!0,get:function(){return a.AuthenticationClient}});var c=i(r(8307));t.hooks=c;var u=r(826);function s(){var e=Object.assign({},t.defaults,0<arguments.length&&void 0!==arguments[0]?arguments[0]:{}),r=e.Authentication;return function(t){var n=new r(t,e);t.authentication=n,t.authenticate=n.authenticate.bind(n),t.reAuthenticate=n.reAuthenticate.bind(n),t.logout=n.logout.bind(n),t.hooks({before:{all:[c.authentication(),c.populateHeader()]}})}}Object.defineProperty(t,"MemoryStorage",{enumerable:!0,get:function(){return u.MemoryStorage}}),t.getDefaultStorage=function(){try{return new u.StorageWrapper(window.localStorage)}catch(e){}return new u.MemoryStorage},t.defaultStorage=(0,t.getDefaultStorage)(),t.defaults={header:"Authorization",scheme:"Bearer",storageKey:"feathers-jwt",locationKey:"access_token",locationErrorKey:"error",jwtStrategy:"jwt",path:"/authentication",Authentication:a.AuthenticationClient,storage:t.defaultStorage},t.default=s,e.exports=Object.assign(s,e.exports)},826:function(e,t){"use strict";function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function n(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function o(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}Object.defineProperty(t,"__esModule",{value:!0}),t.StorageWrapper=t.MemoryStorage=void 0,t.MemoryStorage=function(){function e(){r(this,e),this.store={}}return o(e,[{key:"getItem",value:function(e){return Promise.resolve(this.store[e])}},{key:"setItem",value:function(e,t){return Promise.resolve(this.store[e]=t)}},{key:"removeItem",value:function(e){var t=this.store[e];return delete this.store[e],Promise.resolve(t)}}]),e}(),t.StorageWrapper=function(){function e(t){r(this,e),this.storage=t}return o(e,[{key:"getItem",value:function(e){return Promise.resolve(this.storage.getItem(e))}},{key:"setItem",value:function(e,t){return Promise.resolve(this.storage.setItem(e,t))}},{key:"removeItem",value:function(e){return Promise.resolve(this.storage.removeItem(e))}}]),e}()},3219:function(e,t,r){var n=r(6845),o=r(6441),i=r(2503),a=r(8357),c=r(6105);r=r(7849),Object.assign(n,{errors:o,socketio:c,primus:r,rest:a,authentication:i}),e.exports=n},196:function(e,t,r){"use strict";function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}Object.defineProperty(t,"__esModule",{value:!0}),t.enableHooks=t.processHooks=t.getHooks=t.isHookObject=t.convertHookData=t.makeArguments=t.defaultMakeArguments=t.createHookObject=t.ACTIVATE_HOOKS=void 0;var o=r(3541),i=(r=o._).each,a=r.pick;function c(e){var t=[];return void 0!==e.id&&t.push(e.id),e.data&&t.push(e.data),t.push(e.params||{}),t}function u(e){var t={};return Array.isArray(e)?t={all:e}:"object"!==n(e)?t={all:[e]}:i(e,(function(e,r){t[r]=Array.isArray(e)?e:[e]})),t}function s(e){return"object"===n(e)&&"string"==typeof e.method&&"string"==typeof e.type}t.ACTIVATE_HOOKS=(0,o.createSymbol)("__feathersActivateHooks"),t.createHookObject=function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},r={};return Object.defineProperty(r,"toJSON",{value:function(){return a(this,"type","method","path","params","id","data","result","error")}}),Object.assign(r,t,{method:e,get path(){var e=t.app,r=t.service;return r&&e&&e.services?Object.keys(e.services).find((function(t){return e.services[t]===r})):null}})},t.defaultMakeArguments=c,t.makeArguments=function(e){switch(e.method){case"find":return[e.params];case"get":case"remove":return[e.id,e.params];case"update":case"patch":return[e.id,e.data,e.params];case"create":return[e.data,e.params]}return c(e)},t.convertHookData=u,t.isHookObject=s,t.getHooks=function(e,t,r,n){return e=e.__hooks[r][n]||[],n=t.__hooks[r][n]||[],4<arguments.length&&void 0!==arguments[4]&&arguments[4]?n.concat(e):e.concat(n)},t.processHooks=function(e,t){function r(e){if(e){if(!s(e))throw new Error("".concat(o.type," hook for '").concat(o.method,"' method returned invalid hook object"));o=e}return o}var n=this,o=t;return e.reduce((function(e,t){var o=t.bind(n);return e.then((function(e){return o(e)})).then(r)}),Promise.resolve(o)).then((function(){return o})).catch((function(e){throw e.hook=o,e}))},t.enableHooks=function(e,t,r){if("function"==typeof e.hooks)return e;var n={};return r.forEach((function(e){n[e]={}})),Object.defineProperty(e,"__hooks",{configurable:!0,value:n,writable:!0}),Object.assign(e,{hooks:function(e){var r=this;return i(e,(function(e,n){if(!r.__hooks[n])throw new Error("'".concat(n,"' is not a valid hook type"));var o=u(e);i(o,(function(e,r){if("all"!==r&&-1===t.indexOf(r))throw new Error("'".concat(r,"' is not a valid hook method"))})),t.forEach((function(e){var t=r.__hooks[n][e]||(r.__hooks[n][e]=[]);o.all&&t.push.apply(t,o.all),o[e]&&t.push.apply(t,o[e])}))})),this}})}},6575:function(e,t,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){e[n=void 0===n?r:n]=t[r]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),i=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)"default"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&n(t,e,r);return o(t,e),t},a=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),t.hooks=void 0,i=i(r(196)),a(r(3541),t),t.hooks=i},3541:function(e,t){"use strict";function r(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function o(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i=[],a=!0,c=!1;try{for(r=r.call(e);!(a=(n=r.next()).done)&&(i.push(n.value),!t||i.length!==t);a=!0);}catch(e){c=!0,o=e}finally{try{a||null==r.return||r.return()}finally{if(c)throw o}}return i}}(e,t)||function(e,t){if(e){if("string"==typeof e)return i(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Map"===(r="Object"===r&&e.constructor?e.constructor.name:r)||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?i(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function i(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}Object.defineProperty(t,"__esModule",{value:!0}),t.createSymbol=t.makeUrl=t.isPromise=t._=t.stripSlashes=void 0,t.stripSlashes=function(e){return e.replace(/^(\/+)|(\/+)$/g,"")},t._={each:function(e,r){e&&"function"==typeof e.forEach?e.forEach(r):t._.isObject(e)&&Object.keys(e).forEach((function(t){return r(e[t],t)}))},some:function(e,t){return Object.keys(e).map((function(t){return[e[t],t]})).some((function(e){e=(r=o(e,2))[0];var r=r[1];return t(e,r)}))},every:function(e,t){return Object.keys(e).map((function(t){return[e[t],t]})).every((function(e){e=(r=o(e,2))[0];var r=r[1];return t(e,r)}))},keys:function(e){return Object.keys(e)},values:function(e){return t._.keys(e).map((function(t){return e[t]}))},isMatch:function(e,r){return t._.keys(r).every((function(t){return e[t]===r[t]}))},isEmpty:function(e){return 0===t._.keys(e).length},isObject:function(e){return"object"===n(e)&&!Array.isArray(e)&&null!==e},isObjectOrArray:function(e){return"object"===n(e)&&null!==e},extend:function(e){for(var t=arguments.length,r=new Array(1<t?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return Object.assign.apply(Object,[e].concat(r))},omit:function(e){for(var r=t._.extend({},e),n=arguments.length,o=new Array(1<n?n-1:0),i=1;i<n;i++)o[i-1]=arguments[i];return o.forEach((function(e){return delete r[e]})),r},pick:function(e){for(var t=arguments.length,r=new Array(1<t?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return r.reduce((function(t,r){return void 0!==e[r]&&(t[r]=e[r]),t}),{})},merge:function(e,n){return t._.isObject(e)&&t._.isObject(n)&&Object.keys(n).forEach((function(o){t._.isObject(n[o])?(e[o]||Object.assign(e,r({},o,{})),t._.merge(e[o],n[o])):Object.assign(e,r({},o,n[o]))})),e}},t.isPromise=function(e){return t._.isObject(e)&&"function"==typeof e.then},t.makeUrl=function(e){var r=(i="function"==typeof(o=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{}).get?o.get.bind(o):function(){})("env")||"production",n=i("host")||process.env.HOST_NAME||"localhost",o="development"===r||"test"===r?"http":"https",i=i("port")||process.env.PORT||3030;return i="development"===r||"test"===r?":".concat(i):"",e=e||"","".concat(o,"://").concat(n).concat(i,"/").concat(t.stripSlashes(e))},t.createSymbol=function(e){return"undefined"!=typeof Symbol?Symbol(e):e}},6441:function(e,t,r){function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var o=r(5898)("@feathersjs/errors");function i(e,t,r,a,c){var u,s,f;(e=e||"Error")instanceof Error?(s=e.message||"Error",e.errors&&(u=e.errors)):"object"===n(e)?(s=e.message||"Error",c=e):s=e,c&&((f=JSON.parse(JSON.stringify(c))).errors?(u=f.errors,delete f.errors):c.errors&&(u=JSON.parse(JSON.stringify(c.errors)))),this.type="FeathersError",this.name=t,this.message=s,this.code=r,this.className=a,this.data=f,this.errors=u||{},o("".concat(this.name,"(").concat(this.code,"): ").concat(this.message)),o(this.errors),Error.captureStackTrace?Error.captureStackTrace(this,i):this.stack=(new Error).stack}function a(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e}function c(e,t){i.call(this,e,"BadRequest",400,"bad-request",t)}function u(e,t){i.call(this,e,"NotAuthenticated",401,"not-authenticated",t)}function s(e,t){i.call(this,e,"PaymentError",402,"payment-error",t)}function f(e,t){i.call(this,e,"Forbidden",403,"forbidden",t)}function l(e,t){i.call(this,e,"NotFound",404,"not-found",t)}function p(e,t){i.call(this,e,"MethodNotAllowed",405,"method-not-allowed",t)}function y(e,t){i.call(this,e,"NotAcceptable",406,"not-acceptable",t)}function h(e,t){i.call(this,e,"Timeout",408,"timeout",t)}function d(e,t){i.call(this,e,"Conflict",409,"conflict",t)}function v(e,t){i(this,e,"Gone",410,"gone")}function b(e,t){i.call(this,e,"LengthRequired",411,"length-required",t)}function m(e,t){i.call(this,e,"Unprocessable",422,"unprocessable",t)}function g(e,t){i.call(this,e,"TooManyRequests",429,"too-many-requests",t)}function w(e,t){i.call(this,e,"GeneralError",500,"general-error",t)}function O(e,t){i.call(this,e,"NotImplemented",501,"not-implemented",t)}function j(e,t){i.call(this,e,"BadGateway",502,"bad-gateway",t)}function S(e,t){i.call(this,e,"Unavailable",503,"unavailable",t)}a(i,Error),Object.defineProperty(i.prototype,"toJSON",{value:function(){return{name:this.name,message:this.message,code:this.code,className:this.className,data:this.data,errors:this.errors}}}),a(c,i),a(u,i),a(s,i),a(f,i),a(l,i),a(p,i),a(y,i),a(h,i),a(d,i),a(v,i),a(b,i),a(m,i),a(g,i),a(w,i),a(O,i),a(j,i),a(S,i);var P={FeathersError:i,BadRequest:c,NotAuthenticated:u,PaymentError:s,Forbidden:f,NotFound:l,MethodNotAllowed:p,NotAcceptable:y,Timeout:h,Conflict:d,Gone:v,LengthRequired:b,Unprocessable:m,TooManyRequests:g,GeneralError:w,NotImplemented:O,BadGateway:j,Unavailable:S,400:c,401:u,402:s,403:f,404:l,405:p,406:y,408:h,409:d,410:v,411:b,422:m,429:g,500:w,501:O,502:j,503:S};e.exports=Object.assign({convert:function(e){if(!e)return e;var t=(t=P[e.name])?new t(e.message,e.data):new Error(e.message||e);return"object"===n(e)&&Object.assign(t,e),t}},P)},1495:function(e,t,r){var n=r(5898)("feathers:application"),o=r(6575).stripSlashes,i=r(9425),a=r(944),c=r(8341),u=r(3803),s=i.extend({create:null});e.exports={init:function(){Object.assign(this,{version:u,methods:["find","get","create","update","patch","remove"],mixins:[],services:{},providers:[],_setup:!1,settings:{}}),this.configure(c()),this.configure(a())},get:function(e){return this.settings[e]},set:function(e,t){return this.settings[e]=t,this},disable:function(e){return this.settings[e]=!1,this},disabled:function(e){return!this.settings[e]},enable:function(e){return this.settings[e]=!0,this},enabled:function(e){return!!this.settings[e]},configure:function(e){return e.call(this,this),this},service:function(e,t){if(void 0!==t)throw new Error("Registering a new service with `app.service(path, service)` is no longer supported. Use `app.use(path, service)` instead.");return t=o(e)||"/",void 0===(e=this.services[t])&&"function"==typeof this.defaultService?this.use(t,this.defaultService(t)).service(t):e},use:function(e,t){var r=this,i=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};if("string"!=typeof e)throw new Error("'".concat(e,"' is not a valid service path."));var a=o(e)||"/",c="function"==typeof t.service&&t.services,u=this.methods.concat("setup").some((function(e){return"function"==typeof t[e]}));if(c){var f=t;return Object.keys(f.services).forEach((function(e){return r.use("".concat(a,"/").concat(e),f.service(e))})),this}if(!u)throw new Error("Invalid service object passed for path `".concat(a,"`"));var l=s.isPrototypeOf(t)?t:s.extend(t);return n("Registering new service at `".concat(a,"`")),this.mixins.forEach((function(e){return e.call(r,l,a,i)})),"function"==typeof l._setup&&l._setup(this,a),this.providers.forEach((function(e){return e.call(r,l,a,i)})),this._isSetup&&"function"==typeof l.setup&&(n("Setting up service for `".concat(a,"`")),l.setup(this,a)),this.services[a]=l,this},setup:function(){var e=this;return Object.keys(this.services).forEach((function(t){var r=e.services[t];n("Setting up service for `".concat(t,"`")),"function"==typeof r.setup&&r.setup(e,t)})),this._isSetup=!0,this}}},944:function(e,t,r){var n=r(2699).EventEmitter,o=r(9425),i=t.eventHook=function(){return function(e){var t=e.app,r=e.service,n=null===e.event?e.event:t.eventMappings[e.method];t=r._hookEvents&&-1!==r._hookEvents.indexOf(n),n&&t&&"error"!==e.type&&(Array.isArray(e.result)?e.result:[e.result]).forEach((function(t){return r.emit(n,t,e)}))}},a=t.eventMixin=function(e){var t,r;e._serviceEvents||(t=this,r="function"==typeof e.on&&"function"==typeof e.emit,"function"!=typeof e.mixin||r||e.mixin(n.prototype),Object.defineProperties(e,{_serviceEvents:{value:Array.isArray(e.events)?e.events.slice():[]},_hookEvents:{value:[]}}),Object.keys(t.eventMappings).forEach((function(r){var n=t.eventMappings[r],o=-1!==e._serviceEvents.indexOf(n);"function"!=typeof e[r]||o||(e._serviceEvents.push(n),e._hookEvents.push(n))})))};e.exports=function(){return function(e){Object.assign(e,{eventMappings:{create:"created",update:"updated",remove:"removed",patch:"patched"}}),e.hooks({finally:i()}),o.mixin(n.prototype,e),e.mixins.push(a)}}},5392:function(e,t,r){var n=r(6575)._;e.exports=[function(e){var t=e.service,r=e.method,n=t.methods[r];return e.arguments.forEach((function(t,r){e[n[r]]=t})),e.params||(e.params={}),e},function(e){var t=e.service,r=e.method,o=e.path;if((t=t.methods[r]).includes("id")&&void 0===e.id)throw new Error("An id must be provided to the '".concat(o,".").concat(r,"' method"));if(t.includes("data")&&!n.isObjectOrArray(e.data))throw new Error("A data object must be provided to the '".concat(o,".").concat(r,"' method"));return e}]},8341:function(e,t,r){function n(e){return function(e){if(Array.isArray(e))return o(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return o(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Map"===(r="Object"===r&&e.constructor?e.constructor.name:r)||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?o(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function o(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function i(e){var t=e.app,r=e.service,n=e.method,o=e.original;return function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},i=t.hookTypes.reduce((function(t,r){var n=e[r]||[];return t[r]=Array.isArray(n)?n:[n],t}),{});return function(){for(var e=arguments.length,a=new Array(e),c=0;c<e;c++)a[c]=arguments[c];var l=!0===a[a.length-1]&&a.pop(),y=f(n,{type:"before",arguments:a,service:r,app:t});return Promise.resolve(y).then((function(e){return p.call(r,s.concat(i.before),e)})).then((function(e){return void 0!==e.result?e:new Promise((function(t){var i=o||r[n],a=r.methods[n].map((function(t){return e[t]}));if(a=i.apply(r,a),!u(a))throw new Error("Service method '".concat(e.method,"' for '").concat(e.path,"' service must return a promise"));t(a)})).then((function(t){return e.result=t,e})).catch((function(t){throw t.hook=e,t}))})).then((function(e){return e=Object.assign({},e,{type:"after"}),p.call(r,i.after,e)})).catch((function(e){return e=Object.assign({},e.hook,{type:"error",original:e.hook,error:e,result:void 0}),p.call(r,i.error,e).catch((function(e){return Object.assign({},e.hook,{error:e,result:void 0})}))})).then((function(e){return p.call(r,i.finally,e).catch((function(e){return Object.assign({},e.hook,{error:e,result:void 0})}))})).then((function(e){return void 0!==e.error&&void 0===e.result?Promise.reject(l?e:e.error):l?e:e.result}))}}}var a=r(6575),c=a.hooks,u=a.isPromise,s=r(5392),f=c.createHookObject,l=c.getHooks,p=c.processHooks,y=c.enableHooks,h=c.ACTIVATE_HOOKS,d=t.hookMixin=function(e){var t,r,o;"function"!=typeof e.hooks&&(e.methods=Object.getOwnPropertyNames(e).filter((function(t){return"function"==typeof e[t]&&e[t][h]})).reduce((function(t,r){return t[r]=e[r][h],t}),e.methods||{}),Object.assign(e.methods,{find:["params"],get:["id","params"],create:["data","params"],update:["id","data","params"],patch:["id","data","params"],remove:["id","params"]}),t=this,o=(r=Object.keys(e.methods)).reduce((function(r,o){return"function"!=typeof e[o]||(r[o]=function(){var e=this,r=Array.from(arguments),a=e._super.bind(e);return i({app:t,service:e,method:o,original:a})({before:l(t,e,"before",o),after:l(t,e,"after",o,!0),error:l(t,e,"error",o,!0),finally:l(t,e,"finally",o,!0)}).apply(void 0,n(r))}),r}),{}),y(e,r,t.hookTypes),e.mixin(o))};e.exports=function(){return function(e){Object.assign(e,{hookTypes:["before","after","error","finally"]}),y(e,e.methods,e.hookTypes),e.mixins.push(d)}},e.exports.withHooks=i,e.exports.ACTIVATE_HOOKS=h,e.exports.activateHooks=function(e){return function(t){return Object.defineProperty(t,h,{value:e}),t}}},6845:function(e,t,r){var n=r(9425),o=r(1495),i=r(3803),a=(r=(a=r(8341)).ACTIVATE_HOOKS,a.activateHooks),c=Object.create(null);function u(){var e=Object.create(c);return n.mixin(o,e),e.init(),e}u.version=i,u.ACTIVATE_HOOKS=r,u.activateHooks=a,e.exports=u,e.exports.default=u},3803:function(e){e.exports="4.5.12"},7849:function(e,t,r){var n=r(9910);function o(e,t){if(!e)throw new Error("Primus connection needs to be provided");function r(r){return new n(Object.assign({},t,{name:r,connection:e,method:"send"}))}function o(t){if("function"==typeof t.defaultService)throw new Error("Only one default client provider can be configured");t.primus=e,t.defaultService=r}return o.Service=n,o.service=r,o}e.exports=o,e.exports.default=o},357:function(e,t,r){function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function o(e,t){return(o=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function i(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,o=a(e);return function(e,t){if(t&&("object"===n(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0!==e)return e;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}(e)}(this,t?(r=a(this).constructor,Reflect.construct(o,arguments,r)):o.apply(this,arguments))}}function a(e){return(a=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var c=r(7421);r=function(){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&o(e,t)}(n,c);var e,t,r=i(n);function n(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,n),r.apply(this,arguments)}return e=n,(t=[{key:"request",value:function(e,t){var r=this.connection,n=this.options.HttpHeaders;if(!r||!n)throw new Error("Please pass angular's 'httpClient' (instance) and and object with 'HttpHeaders' (class) to feathers-rest");var o=e.url,i=t.connection,a=(i=new n(Object.assign({Accept:"application/json"},this.options.headers,e.headers,(void 0===i?{}:i).headers)),Object.assign({body:e.body},t.connection,{headers:i}));return new Promise((function(t,n){r.request(e.method,o,a).subscribe(t,n)})).catch((function(e){var t=e.error;if(t)throw"string"==typeof t?JSON.parse(t):t;throw e}))}}])&&function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),n}(),e.exports=r},4003:function(e,t,r){function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function o(e,t){return(o=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function i(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,o=a(e);return function(e,t){if(t&&("object"===n(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0!==e)return e;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}(e)}(this,t?(r=a(this).constructor,Reflect.construct(o,arguments,r)):o.apply(this,arguments))}}function a(e){return(a=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var c=r(7421);r=function(){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&o(e,t)}(n,c);var e,t,r=i(n);function n(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,n),r.apply(this,arguments)}return e=n,(t=[{key:"request",value:function(e,t){var r=this.connection,n=this.options.Headers;if(!r||!n)throw new Error("Please pass angular's 'http' (instance) and and object with 'Headers' (class) to feathers-rest");var o=e.url,i=(t=void 0===(t=t.connection)?{}:t,n=new n(Object.assign({Accept:"application/json"},this.options.headers,e.headers,t.headers)),Object.assign({method:e.method,body:e.body},t,{headers:n}));return new Promise((function(e,t){r.request(o,i).subscribe(e,t)})).then((function(e){return e.json()})).catch((function(e){throw!((e=e.response||e)instanceof Error)&&e.json()||e}))}}])&&function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),n}(),e.exports=r},1167:function(e,t,r){function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function o(e,t){return(o=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function i(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,o=a(e);return function(e,t){if(t&&("object"===n(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0!==e)return e;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}(e)}(this,t?(r=a(this).constructor,Reflect.construct(o,arguments,r)):o.apply(this,arguments))}}function a(e){return(a=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var c=r(7421);r=function(){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&o(e,t)}(n,c);var e,t,r=i(n);function n(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,n),r.apply(this,arguments)}return e=n,(t=[{key:"request",value:function(e,t){return t=Object.assign({url:e.url,method:e.method,data:e.body,headers:Object.assign({Accept:"application/json"},this.options.headers,e.headers)},t.connection),this.connection.request(t).then((function(e){return e.data})).catch((function(e){throw!((e=e.response||e)instanceof Error)&&e.data||e}))}}])&&function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),n}(),e.exports=r},7421:function(e,t,r){var n=r(9126),o=r(6441).Unavailable,i=r(6575)._,a=r(6575).stripSlashes,c=r(6441).convert;function u(e){if("ECONNREFUSED"===e.code)throw new o(e.message,i.pick(e,"address","port","config"));throw c(e)}e.exports=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.name=a(t.name),this.options=t.options,this.connection=t.connection,this.base="".concat(t.base,"/").concat(this.name)}var t,r;return t=e,(r=[{key:"makeUrl",value:function(e,t){e=e||{};var r=this.base;return null!=t&&(r+="/".concat(encodeURIComponent(t))),r+this.getQuery(e)}},{key:"getQuery",value:function(e){return 0===Object.keys(e).length?"":(e=n.stringify(e),"?".concat(e))}},{key:"find",value:function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};return this.request({url:this.makeUrl(e.query),method:"GET",headers:Object.assign({},e.headers)},e).catch(u)}},{key:"get",value:function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};return void 0===e?Promise.reject(new Error("id for 'get' can not be undefined")):this.request({url:this.makeUrl(t.query,e),method:"GET",headers:Object.assign({},t.headers)},t).catch(u)}},{key:"create",value:function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};return this.request({url:this.makeUrl(t.query),body:e,method:"POST",headers:Object.assign({"Content-Type":"application/json"},t.headers)},t).catch(u)}},{key:"update",value:function(e,t){var r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};return void 0===e?Promise.reject(new Error("id for 'update' can not be undefined, only 'null' when updating multiple entries")):this.request({url:this.makeUrl(r.query,e),body:t,method:"PUT",headers:Object.assign({"Content-Type":"application/json"},r.headers)},r).catch(u)}},{key:"patch",value:function(e,t){var r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};return void 0===e?Promise.reject(new Error("id for 'patch' can not be undefined, only 'null' when updating multiple entries")):this.request({url:this.makeUrl(r.query,e),body:t,method:"PATCH",headers:Object.assign({"Content-Type":"application/json"},r.headers)},r).catch(u)}},{key:"remove",value:function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};return void 0===e?Promise.reject(new Error("id for 'remove' can not be undefined, only 'null' when removing multiple entries")):this.request({url:this.makeUrl(t.query,e),method:"DELETE",headers:Object.assign({},t.headers)},t).catch(u)}}])&&function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),e}()},7798:function(e,t,r){function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function o(e,t){return(o=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function i(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,o=a(e);return function(e,t){if(t&&("object"===n(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0!==e)return e;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}(e)}(this,t?(r=a(this).constructor,Reflect.construct(o,arguments,r)):o.apply(this,arguments))}}function a(e){return(a=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var c=r(7421);r=function(){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&o(e,t)}(n,c);var e,t,r=i(n);function n(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,n),r.apply(this,arguments)}return e=n,(t=[{key:"request",value:function(e,t){return(t=Object.assign({},e,t.connection)).headers=Object.assign({Accept:"application/json"},this.options.headers,t.headers),e.body&&(t.body=JSON.stringify(e.body)),(0,this.connection)(e.url,t).then(this.checkStatus).then((function(e){return 204===e.status?null:e.json()}))}},{key:"checkStatus",value:function(e){return e.ok?e:e.json().catch((function(){return new(errors[e.status]||Error)("JSON parsing error")})).then((function(t){throw t.response=e,t}))}}])&&function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),n}(),e.exports=r},8357:function(e,t,r){var n=r(3063),o=r(1467),i=r(7364),a=r(7798),c=r(1167),u=r(4003),s=r(7421),f={jquery:n,superagent:o,request:i,fetch:a,axios:c,angular:u,angularHttpClient:r=r(357)};function l(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:"",t={Base:s};return Object.keys(f).forEach((function(r){t[r]=function(t){var n=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},o=2<arguments.length&&void 0!==arguments[2]?arguments[2]:f[r];if(!t)throw new Error("".concat(r," has to be provided to feathers-rest"));function i(e){if("function"==typeof e.defaultService)throw new Error("Only one default client provider can be configured");e.rest=t,e.defaultService=a}"function"==typeof n&&(o=n,n={});var a=function(r){return new o({base:e,name:r,connection:t,options:n})};return i.Service=o,i.service=a,i}})),t}e.exports=Object.assign(l,{SuperagentClient:o,FetchClient:a,jQueryClient:n,RequestClient:i,AxiosClient:c,AngularClient:u,AngularHttpClient:r}),e.exports.default=l},3063:function(e,t,r){function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function o(e,t){return(o=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function i(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,o=a(e);return function(e,t){if(t&&("object"===n(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0!==e)return e;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}(e)}(this,t?(r=a(this).constructor,Reflect.construct(o,arguments,r)):o.apply(this,arguments))}}function a(e){return(a=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var c=r(7421);r=function(){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&o(e,t)}(n,c);var e,t,r=i(n);function n(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,n),r.apply(this,arguments)}return e=n,(t=[{key:"request",value:function(e,t){var r=this,n=(t=void 0===(n=t.connection)?{}:n,Object.assign({},e.headers,this.options.headers,t.headers)),o=Object.assign({dataType:e.type||"json"},t,e,{headers:n});return e.body&&(o.data=JSON.stringify(e.body),o.contentType="application/json"),delete o.type,delete o.body,new Promise((function(e,t){r.connection.ajax(o).then(e,(function(e){var r=e.responseText;try{r=JSON.parse(r)}catch(t){r=new Error(e.responseText)}r.xhr=r.response=e,t(r)}))}))}}])&&function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),n}(),e.exports=r},7364:function(e,t,r){function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function o(e,t){return(o=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function i(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,o=a(e);return function(e,t){if(t&&("object"===n(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0!==e)return e;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}(e)}(this,t?(r=a(this).constructor,Reflect.construct(o,arguments,r)):o.apply(this,arguments))}}function a(e){return(a=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var c=r(7421);r=function(){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&o(e,t)}(n,c);var e,t,r=i(n);function n(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,n),r.apply(this,arguments)}return e=n,(t=[{key:"request",value:function(e,t){var r=this;return new Promise((function(n,o){var i=t.connection;i=Object.assign({},e.headers,(void 0===i?{}:i).headers),r.connection(Object.assign({json:!0},e,t.connection,{headers:i}),(function(e,t,r){return e?o(e):!e&&400<=t.statusCode?"string"==typeof r?o(new Error(r)):(r.response=t,o(Object.assign(new Error(r.message),r))):void n(r)}))}))}}])&&function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),n}(),e.exports=r},1467:function(e,t,r){function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function o(e,t){return(o=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function i(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,o=a(e);return function(e,t){if(t&&("object"===n(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0!==e)return e;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}(e)}(this,t?(r=a(this).constructor,Reflect.construct(o,arguments,r)):o.apply(this,arguments))}}function a(e){return(a=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var c=r(7421);r=function(){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&o(e,t)}(n,c);var e,t,r=i(n);function n(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,n),r.apply(this,arguments)}return e=n,(t=[{key:"request",value:function(e,t){var r=this.connection(e.method,e.url).set(this.options.headers||{}).set("Accept","application/json").set(t.connection||{}).set(e.headers||{}).type(e.type||"json");return new Promise((function(t,n){r.set(e.headers),e.body&&r.send(e.body),r.end((function(e,r){if(e){try{var o=e.response;(e=JSON.parse(e.response.text)).response=o}catch(e){}return n(e)}t(r&&r.body)}))}))}}])&&function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),n}(),e.exports=r},6105:function(e,t,r){var n=r(9910);function o(e,t){if(!e)throw new Error("Socket.io connection needs to be provided");if(e&&e.io&&e.io.engine&&e.io.engine.transport&&e.io.engine.transport.query&&3<e.io.engine.transport.query.EIO)throw console.error("You are trying to use the Socket.io client version 3 or later with Feathers v4 which only supports Socket.io version 2. Please use socket.io-client version 2 instead."),new Error("socket.io-client must be version 2.x");function r(r){var o=this,i=Object.keys(this.eventMappings||{}).map((function(e){return o.eventMappings[e]}));return r=Object.assign({},t,{events:i,name:r,connection:e,method:"emit"}),new n(r)}function o(t){if("function"==typeof t.defaultService)throw new Error("Only one default client provider can be configured");t.io=e,t.defaultService=r}return o.Service=n,o.service=r,o}e.exports=o,e.exports.default=o},9910:function(e,t,r){e.exports=r(2004).Service},2004:function(e,t,r){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.Service=void 0,n=n(r(5898));var o=r(6441),i=(0,n.default)("@feathersjs/transport-commons/client"),a=["addListener","emit","listenerCount","listeners","on","once","prependListener","prependOnceListener","removeAllListeners","removeListener"],c=["eventNames","getMaxListeners","setMaxListeners"];n=function(){function e(t){var r;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.events=t.events,this.path=t.name,this.connection=t.connection,this.method=t.method,this.timeout=t.timeout||5e3,r=this,c.forEach((function(e){r[e]=function(){var t;if("function"!=typeof this.connection[e])throw new Error("Can not call '".concat(e,"' on the client service connection"));return(t=this.connection)[e].apply(t,arguments)}})),a.forEach((function(e){r[e]=function(t){if("function"!=typeof this.connection[e])throw new Error("Can not call '".concat(e,"' on the client service connection"));var r="".concat(this.path," ").concat(t);i("Calling emitter method ".concat(e," with ")+"namespaced event '".concat(r,"'"));for(var n=arguments.length,o=new Array(1<n?n-1:0),a=1;a<n;a++)o[a-1]=arguments[a];return(r=(t=this.connection)[e].apply(t,[r].concat(o)))===this.connection?this:r}}))}var t,r;return t=e,(r=[{key:"send",value:function(e){for(var t=this,r=arguments.length,n=new Array(1<r?r-1:0),a=1;a<r;a++)n[a-1]=arguments[a];return new Promise((function(r,a){var c,u=setTimeout((function(){return a(new o.Timeout("Timeout of ".concat(t.timeout,"ms exceeded calling ").concat(e," on ").concat(t.path),{timeout:t.timeout,method:e,path:t.path}))}),t.timeout);n.unshift(e,t.path),n.push((function(e,t){return e=(0,o.convert)(e),clearTimeout(u),e?a(e):r(t)})),i("Sending socket.".concat(t.method),n),(c=t.connection)[t.method].apply(c,n)}))}},{key:"find",value:function(){return this.send("find",(0<arguments.length&&void 0!==arguments[0]?arguments[0]:{}).query||{})}},{key:"get",value:function(e){return this.send("get",e,(1<arguments.length&&void 0!==arguments[1]?arguments[1]:{}).query||{})}},{key:"create",value:function(e){return this.send("create",e,(1<arguments.length&&void 0!==arguments[1]?arguments[1]:{}).query||{})}},{key:"update",value:function(e,t){return this.send("update",e,t,(2<arguments.length&&void 0!==arguments[2]?arguments[2]:{}).query||{})}},{key:"patch",value:function(e,t){return this.send("patch",e,t,(2<arguments.length&&void 0!==arguments[2]?arguments[2]:{}).query||{})}},{key:"remove",value:function(e){return this.send("remove",e,(1<arguments.length&&void 0!==arguments[1]?arguments[1]:{}).query||{})}},{key:"off",value:function(e){for(var t=arguments.length,r=new Array(1<t?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return"function"!=typeof this.connection.off?0===r.length?this.removeAllListeners(e):this.removeListener.apply(this,[e].concat(r)):(e=(o=this.connection).off.apply(o,["".concat(this.path," ").concat(e)].concat(r)))===this.connection?this:e;var o}}])&&function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),e}(),t.Service=n},2680:function(e,t,r){"use strict";var n=r(7286),o=r(9429),i=o(n("String.prototype.indexOf"));e.exports=function(e,t){return"function"==typeof(t=n(e,!!t))&&-1<i(e,".prototype.")?o(t):t}},9429:function(e,t,r){"use strict";var n=r(4090),o=(r=r(7286))("%Function.prototype.apply%"),i=r("%Function.prototype.call%"),a=r("%Reflect.apply%",!0)||n.call(i,o),c=r("%Object.getOwnPropertyDescriptor%",!0),u=r("%Object.defineProperty%",!0),s=r("%Math.max%");if(u)try{u({},"a",{value:1})}catch(e){u=null}function f(){return a(n,o,arguments)}e.exports=function(e){var t=a(n,i,arguments);return c&&u&&c(t,"length").configurable&&u(t,"length",{value:1+s(0,e.length-(arguments.length-1))}),t},u?u(e.exports,"apply",{value:f}):e.exports.apply=f},2699:function(e){"use strict";var t="object"==typeof Reflect?Reflect:null,r=t&&"function"==typeof t.apply?t.apply:function(e,t,r){return Function.prototype.apply.call(e,t,r)},n=t&&"function"==typeof t.ownKeys?t.ownKeys:Object.getOwnPropertySymbols?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:function(e){return Object.getOwnPropertyNames(e)},o=Number.isNaN||function(e){return e!=e};function i(){i.init.call(this)}e.exports=i,e.exports.once=function(e,t){return new Promise((function(r,n){function o(r){e.removeListener(t,i),n(r)}function i(){"function"==typeof e.removeListener&&e.removeListener("error",o),r([].slice.call(arguments))}var a,c,u;h(e,t,i,{once:!0}),"error"!==t&&(c=o,u={once:!0},"function"==typeof(a=e).on&&h(a,"error",c,u))}))},(i.EventEmitter=i).prototype._events=void 0,i.prototype._eventsCount=0,i.prototype._maxListeners=void 0;var a=10;function c(e){if("function"!=typeof e)throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof e)}function u(e){return void 0===e._maxListeners?i.defaultMaxListeners:e._maxListeners}function s(e,t,r,n){var o,i;return c(r),void 0===(o=e._events)?(o=e._events=Object.create(null),e._eventsCount=0):(void 0!==o.newListener&&(e.emit("newListener",t,r.listener||r),o=e._events),i=o[t]),void 0===i?(i=o[t]=r,++e._eventsCount):("function"==typeof i?i=o[t]=n?[r,i]:[i,r]:n?i.unshift(r):i.push(r),0<(r=u(e))&&i.length>r&&!i.warned&&(i.warned=!0,(r=new Error("Possible EventEmitter memory leak detected. "+i.length+" "+String(t)+" listeners added. Use emitter.setMaxListeners() to increase limit")).name="MaxListenersExceededWarning",r.emitter=e,r.type=t,r.count=i.length,r=r,console&&console.warn&&console.warn(r))),e}function f(e,t,r){return t=function(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,0===arguments.length?this.listener.call(this.target):this.listener.apply(this.target,arguments)}.bind(e={fired:!1,wrapFn:void 0,target:e,type:t,listener:r}),t.listener=r,e.wrapFn=t}function l(e,t,r){return void 0===(e=e._events)||void 0===(t=e[t])?[]:"function"==typeof t?r?[t.listener||t]:[t]:r?function(e){for(var t=new Array(e.length),r=0;r<t.length;++r)t[r]=e[r].listener||e[r];return t}(t):y(t,t.length)}function p(e){var t=this._events;if(void 0!==t){if("function"==typeof(e=t[e]))return 1;if(void 0!==e)return e.length}return 0}function y(e,t){for(var r=new Array(t),n=0;n<t;++n)r[n]=e[n];return r}function h(e,t,r,n){if("function"==typeof e.on)n.once?e.once(t,r):e.on(t,r);else{if("function"!=typeof e.addEventListener)throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof e);e.addEventListener(t,(function o(i){n.once&&e.removeEventListener(t,o),r(i)}))}}Object.defineProperty(i,"defaultMaxListeners",{enumerable:!0,get:function(){return a},set:function(e){if("number"!=typeof e||e<0||o(e))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");a=e}}),i.init=function(){void 0!==this._events&&this._events!==Object.getPrototypeOf(this)._events||(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},i.prototype.setMaxListeners=function(e){if("number"!=typeof e||e<0||o(e))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+e+".");return this._maxListeners=e,this},i.prototype.getMaxListeners=function(){return u(this)},i.prototype.emit=function(e){for(var t=[],n=1;n<arguments.length;n++)t.push(arguments[n]);var o,i="error"===e,a=this._events;if(void 0!==a)i=i&&void 0===a.error;else if(!i)return!1;if(i){if((o=0<t.length?t[0]:o)instanceof Error)throw o;throw(i=new Error("Unhandled error."+(o?" ("+o.message+")":""))).context=o,i}if(void 0===(e=a[e]))return!1;if("function"==typeof e)r(e,this,t);else{var c=e.length,u=y(e,c);for(n=0;n<c;++n)r(u[n],this,t)}return!0},i.prototype.on=i.prototype.addListener=function(e,t){return s(this,e,t,!1)},i.prototype.prependListener=function(e,t){return s(this,e,t,!0)},i.prototype.once=function(e,t){return c(t),this.on(e,f(this,e,t)),this},i.prototype.prependOnceListener=function(e,t){return c(t),this.prependListener(e,f(this,e,t)),this},i.prototype.off=i.prototype.removeListener=function(e,t){var r,n,o,i,a;if(c(t),void 0===(n=this._events))return this;if(void 0===(r=n[e]))return this;if(r===t||r.listener===t)0==--this._eventsCount?this._events=Object.create(null):(delete n[e],n.removeListener&&this.emit("removeListener",e,r.listener||t));else if("function"!=typeof r){for(o=-1,i=r.length-1;0<=i;i--)if(r[i]===t||r[i].listener===t){a=r[i].listener,o=i;break}if(o<0)return this;0===o?r.shift():function(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}(r,o),1===r.length&&(n[e]=r[0]),void 0!==n.removeListener&&this.emit("removeListener",e,a||t)}return this},i.prototype.removeAllListeners=function(e){var t,r=this._events;if(void 0===r)return this;if(void 0===r.removeListener)return 0===arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==r[e]&&(0==--this._eventsCount?this._events=Object.create(null):delete r[e]),this;if(0===arguments.length){for(var n,o=Object.keys(r),i=0;i<o.length;++i)"removeListener"!==(n=o[i])&&this.removeAllListeners(n);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if("function"==typeof(t=r[e]))this.removeListener(e,t);else if(void 0!==t)for(i=t.length-1;0<=i;i--)this.removeListener(e,t[i]);return this},i.prototype.listeners=function(e){return l(this,e,!0)},i.prototype.rawListeners=function(e){return l(this,e,!1)},i.listenerCount=function(e,t){return"function"==typeof e.listenerCount?e.listenerCount(t):p.call(e,t)},i.prototype.listenerCount=p,i.prototype.eventNames=function(){return 0<this._eventsCount?n(this._events):[]}},7795:function(e){"use strict";var t=Array.prototype.slice,r=Object.prototype.toString;e.exports=function(e){var n=this;if("function"!=typeof n||"[object Function]"!==r.call(n))throw new TypeError("Function.prototype.bind called on incompatible "+n);for(var o,i,a=t.call(arguments,1),c=Math.max(0,n.length-a.length),u=[],s=0;s<c;s++)u.push("$"+s);return o=Function("binder","return function ("+u.join(",")+"){ return binder.apply(this,arguments); }")((function(){if(this instanceof o){var r=n.apply(this,a.concat(t.call(arguments)));return Object(r)===r?r:this}return n.apply(e,a.concat(t.call(arguments)))})),n.prototype&&((i=function(){}).prototype=n.prototype,o.prototype=new i,i.prototype=null),o}},4090:function(e,t,r){"use strict";r=r(7795),e.exports=Function.prototype.bind||r},7286:function(e,t,r){"use strict";var n=SyntaxError,o=Function,i=TypeError,a=function(e){try{return o('"use strict"; return ('+e+").constructor;")()}catch(e){}},c=Object.getOwnPropertyDescriptor;if(c)try{c({},"")}catch(e){c=null}function u(){throw new i}function s(e){var t,r;return"%AsyncFunction%"===e?t=a("async function () {}"):"%GeneratorFunction%"===e?t=a("function* () {}"):"%AsyncGeneratorFunction%"===e?t=a("async function* () {}"):"%AsyncGenerator%"===e?(r=s("%AsyncGeneratorFunction%"))&&(t=r.prototype):"%AsyncIteratorPrototype%"!==e||(r=s("%AsyncGenerator%"))&&(t=p(r.prototype)),d[e]=t}var f=c?function(){try{return u}catch(e){try{return c(arguments,"callee").get}catch(e){return u}}}():u,l=r(2636)(),p=Object.getPrototypeOf||function(e){return e.__proto__},y={},h="undefined"==typeof Uint8Array?b:p(Uint8Array),d={"%AggregateError%":"undefined"==typeof AggregateError?b:AggregateError,"%Array%":Array,"%ArrayBuffer%":"undefined"==typeof ArrayBuffer?b:ArrayBuffer,"%ArrayIteratorPrototype%":l?p([][Symbol.iterator]()):b,"%AsyncFromSyncIteratorPrototype%":b,"%AsyncFunction%":y,"%AsyncGenerator%":y,"%AsyncGeneratorFunction%":y,"%AsyncIteratorPrototype%":y,"%Atomics%":"undefined"==typeof Atomics?b:Atomics,"%BigInt%":"undefined"==typeof BigInt?b:BigInt,"%Boolean%":Boolean,"%DataView%":"undefined"==typeof DataView?b:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":Error,"%eval%":eval,"%EvalError%":EvalError,"%Float32Array%":"undefined"==typeof Float32Array?b:Float32Array,"%Float64Array%":"undefined"==typeof Float64Array?b:Float64Array,"%FinalizationRegistry%":"undefined"==typeof FinalizationRegistry?b:FinalizationRegistry,"%Function%":o,"%GeneratorFunction%":y,"%Int8Array%":"undefined"==typeof Int8Array?b:Int8Array,"%Int16Array%":"undefined"==typeof Int16Array?b:Int16Array,"%Int32Array%":"undefined"==typeof Int32Array?b:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":l?p(p([][Symbol.iterator]())):b,"%JSON%":"object"==typeof JSON?JSON:b,"%Map%":"undefined"==typeof Map?b:Map,"%MapIteratorPrototype%":"undefined"!=typeof Map&&l?p((new Map)[Symbol.iterator]()):b,"%Math%":Math,"%Number%":Number,"%Object%":Object,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":"undefined"==typeof Promise?b:Promise,"%Proxy%":"undefined"==typeof Proxy?b:Proxy,"%RangeError%":RangeError,"%ReferenceError%":ReferenceError,"%Reflect%":"undefined"==typeof Reflect?b:Reflect,"%RegExp%":RegExp,"%Set%":"undefined"==typeof Set?b:Set,"%SetIteratorPrototype%":"undefined"!=typeof Set&&l?p((new Set)[Symbol.iterator]()):b,"%SharedArrayBuffer%":"undefined"==typeof SharedArrayBuffer?b:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":l?p(""[Symbol.iterator]()):b,"%Symbol%":l?Symbol:b,"%SyntaxError%":n,"%ThrowTypeError%":f,"%TypedArray%":h,"%TypeError%":i,"%Uint8Array%":"undefined"==typeof Uint8Array?b:Uint8Array,"%Uint8ClampedArray%":"undefined"==typeof Uint8ClampedArray?b:Uint8ClampedArray,"%Uint16Array%":"undefined"==typeof Uint16Array?b:Uint16Array,"%Uint32Array%":"undefined"==typeof Uint32Array?b:Uint32Array,"%URIError%":URIError,"%WeakMap%":"undefined"==typeof WeakMap?b:WeakMap,"%WeakRef%":"undefined"==typeof WeakRef?b:WeakRef,"%WeakSet%":"undefined"==typeof WeakSet?b:WeakSet},v={"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},b=r(4090),m=r(3198),g=b.call(Function.call,Array.prototype.concat),w=b.call(Function.apply,Array.prototype.splice),O=b.call(Function.call,String.prototype.replace),j=b.call(Function.call,String.prototype.slice),S=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,P=/\\(\\)?/g;e.exports=function(e,t){if("string"!=typeof e||0===e.length)throw new i("intrinsic name must be a non-empty string");if(1<arguments.length&&"boolean"!=typeof t)throw new i('"allowMissing" argument must be a boolean');var r=function(e){var t=j(e,0,1),r=j(e,-1);if("%"===t&&"%"!==r)throw new n("invalid intrinsic syntax, expected closing `%`");if("%"===r&&"%"!==t)throw new n("invalid intrinsic syntax, expected opening `%`");var o=[];return O(e,S,(function(e,t,r,n){o[o.length]=r?O(n,P,"$1"):t||e})),o}(e),o=0<r.length?r[0]:"",a=function(e,t){var r,o=e;if(m(v,o)&&(o="%"+(r=v[o])[0]+"%"),m(d,o)){var a=d[o];if(void 0===(a=a===y?s(o):a)&&!t)throw new i("intrinsic "+e+" exists, but is not available. Please file an issue!");return{alias:r,name:o,value:a}}throw new n("intrinsic "+e+" does not exist!")}("%"+o+"%",t),u=(a.name,a.value),f=!1;(a=a.alias)&&(o=a[0],w(r,g([0,1],a)));for(var l=1,p=!0;l<r.length;l+=1){var h=r[l],b=j(h,0,1),A=j(h,-1);if(('"'===b||"'"===b||"`"===b||'"'===A||"'"===A||"`"===A)&&b!==A)throw new n("property names with quotes must have matching quotes");if("constructor"!==h&&p||(f=!0),m(d,b="%"+(o+="."+h)+"%"))u=d[b];else if(null!=u){if(!(h in u)){if(!t)throw new i("base intrinsic for "+e+" exists, but the property is not available.");return}u=c&&l+1>=r.length?(p=!!(A=c(u,h)))&&"get"in A&&!("originalValue"in A.get)?A.get:u[h]:(p=m(u,h),u[h]),p&&!f&&(d[b]=u)}}return u}},2636:function(e,t,r){"use strict";var n="undefined"!=typeof Symbol&&Symbol,o=r(6679);e.exports=function(){return"function"==typeof n&&"function"==typeof Symbol&&"symbol"==typeof n("foo")&&"symbol"==typeof Symbol("bar")&&o()}},6679:function(e){"use strict";e.exports=function(){if("function"!=typeof Symbol||"function"!=typeof Object.getOwnPropertySymbols)return!1;if("symbol"==typeof Symbol.iterator)return!0;var e={},t=Symbol("test"),r=Object(t);if("string"==typeof t)return!1;if("[object Symbol]"!==Object.prototype.toString.call(t))return!1;if("[object Symbol]"!==Object.prototype.toString.call(r))return!1;for(t in e[t]=42,e)return!1;return!("function"==typeof Object.keys&&0!==Object.keys(e).length||"function"==typeof Object.getOwnPropertyNames&&0!==Object.getOwnPropertyNames(e).length||1!==(r=Object.getOwnPropertySymbols(e)).length||r[0]!==t||!Object.prototype.propertyIsEnumerable.call(e,t)||"function"==typeof Object.getOwnPropertyDescriptor&&(42!==(e=Object.getOwnPropertyDescriptor(e,t)).value||!0!==e.enumerable))}},3198:function(e,t,r){"use strict";r=r(4090),e.exports=r.call(Function.call,Object.prototype.hasOwnProperty)},1378:function(e){var t=36e5,r=864e5;function n(e,t,r,n){return t=1.5*r<=t,Math.round(e/r)+" "+n+(t?"s":"")}e.exports=function(e,o){o=o||{};var i=typeof e;if("string"==i&&0<e.length)return function(e){if(!(100<(e=String(e)).length)&&(e=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e))){var n=parseFloat(e[1]);switch((e[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*n;case"weeks":case"week":case"w":return 6048e5*n;case"days":case"day":case"d":return n*r;case"hours":case"hour":case"hrs":case"hr":case"h":return n*t;case"minutes":case"minute":case"mins":case"min":case"m":return 6e4*n;case"seconds":case"second":case"secs":case"sec":case"s":return 1e3*n;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return n;default:return}}}(e);if("number"==i&&isFinite(e))return(o.long?function(e){var o=Math.abs(e);return r<=o?n(e,o,r,"day"):t<=o?n(e,o,t,"hour"):6e4<=o?n(e,o,6e4,"minute"):1e3<=o?n(e,o,1e3,"second"):e+" ms"}:function(e){var n=Math.abs(e);return r<=n?Math.round(e/r)+"d":t<=n?Math.round(e/t)+"h":6e4<=n?Math.round(e/6e4)+"m":1e3<=n?Math.round(e/1e3)+"s":e+"ms"})(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},9500:function(e,t,r){var n="function"==typeof Map&&Map.prototype,o=Object.getOwnPropertyDescriptor&&n?Object.getOwnPropertyDescriptor(Map.prototype,"size"):null,i=n&&o&&"function"==typeof o.get?o.get:null,a=n&&Map.prototype.forEach,c=(o="function"==typeof Set&&Set.prototype,n=Object.getOwnPropertyDescriptor&&o?Object.getOwnPropertyDescriptor(Set.prototype,"size"):null,o&&n&&"function"==typeof n.get?n.get:null),u=o&&Set.prototype.forEach,s="function"==typeof WeakMap&&WeakMap.prototype?WeakMap.prototype.has:null,f="function"==typeof WeakSet&&WeakSet.prototype?WeakSet.prototype.has:null,l="function"==typeof WeakRef&&WeakRef.prototype?WeakRef.prototype.deref:null,p=Boolean.prototype.valueOf,y=Object.prototype.toString,h=Function.prototype.toString,d=String.prototype.match,v=String.prototype.slice,b=String.prototype.replace,m=String.prototype.toUpperCase,g=String.prototype.toLowerCase,w=RegExp.prototype.test,O=Array.prototype.concat,j=Array.prototype.join,S=Array.prototype.slice,P=Math.floor,A="function"==typeof BigInt?BigInt.prototype.valueOf:null,k=Object.getOwnPropertySymbols,E="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?Symbol.prototype.toString:null,_="function"==typeof Symbol&&"object"==typeof Symbol.iterator,C="function"==typeof Symbol&&Symbol.toStringTag&&(Symbol.toStringTag,1)?Symbol.toStringTag:null,x=Object.prototype.propertyIsEnumerable,F=("function"==typeof Reflect?Reflect:Object).getPrototypeOf||([].__proto__===Array.prototype?function(e){return e.__proto__}:null);function R(e,t){if(e===1/0||e===-1/0||e!=e||e&&-1e3<e&&e<1e3||w.call(/e/,t))return t;var r=/[0-9](?=(?:[0-9]{3})+(?![0-9]))/g;if("number"==typeof e){var n=e<0?-P(-e):P(e);if(n!==e)return e=String(n),n=v.call(t,e.length+1),b.call(e,r,"$&_")+"."+b.call(b.call(n,/([0-9]{3})/g,"$&_"),/_$/,"")}return b.call(t,r,"$&_")}var T=(r=r(3260).custom)&&N(r)?r:null;function M(e,t,r){return(t="double"===(r.quoteStyle||t)?'"':"'")+e+t}function I(e){return!("[object Array]"!==D(e)||C&&"object"==typeof e&&C in e)}function N(e){if(_)return e&&"object"==typeof e&&e instanceof Symbol;if("symbol"==typeof e)return 1;if(e&&"object"==typeof e&&E)try{return E.call(e),1}catch(e){}}e.exports=function e(t,r,n,o){var y=r||{};if(U(y,"quoteStyle")&&"single"!==y.quoteStyle&&"double"!==y.quoteStyle)throw new TypeError('option "quoteStyle" must be "single" or "double"');if(U(y,"maxStringLength")&&("number"==typeof y.maxStringLength?y.maxStringLength<0&&y.maxStringLength!==1/0:null!==y.maxStringLength))throw new TypeError('option "maxStringLength", if provided, must be a positive integer, Infinity, or `null`');if("boolean"!=typeof(ee=!U(y,"customInspect")||y.customInspect)&&"symbol"!==ee)throw new TypeError("option \"customInspect\", if provided, must be `true`, `false`, or `'symbol'`");if(U(y,"indent")&&null!==y.indent&&"\t"!==y.indent&&!(parseInt(y.indent,10)===y.indent&&0<y.indent))throw new TypeError('option "indent" must be "\\t", an integer > 0, or `null`');if(U(y,"numericSeparator")&&"boolean"!=typeof y.numericSeparator)throw new TypeError('option "numericSeparator", if provided, must be `true` or `false`');var m,w=y.numericSeparator;if(void 0===t)return"undefined";if(null===t)return"null";if("boolean"==typeof t)return t?"true":"false";if("string"==typeof t)return function e(t,r){if(t.length>r.maxStringLength){var n="... "+(n=t.length-r.maxStringLength)+" more character"+(1<n?"s":"");return e(v.call(t,0,r.maxStringLength),r)+n}return M(t=b.call(b.call(t,/(['\\])/g,"\\$1"),/[\x00-\x1f]/g,q),"single",r)}(t,y);if("number"==typeof t)return 0===t?0<1/0/t?"0":"-0":(r=String(t),w?R(t,r):r);if("bigint"==typeof t){var P=String(t)+"n";return w?R(t,P):P}if((w=void 0===y.depth?5:y.depth)<=(n=void 0===n?0:n)&&0<w&&"object"==typeof t)return I(t)?"[Array]":"[Object]";if(P=function(e,t){var r;if("\t"===e.indent)r="\t";else{if(!("number"==typeof e.indent&&0<e.indent))return null;r=j.call(Array(e.indent+1)," ")}return{base:r,prev:j.call(Array(t+1),r)}}(y,n),void 0===o)o=[];else if(0<=H(o,t))return"[Circular]";function k(t,r,i){return r&&(o=S.call(o)).push(r),i?(i={depth:y.depth},U(y,"quoteStyle")&&(i.quoteStyle=y.quoteStyle),e(t,i,n+1,o)):e(t,y,n+1,o)}if("function"==typeof t){w=function(e){return e.name?e.name:(e=d.call(h.call(e),/^function\s*([\w$]+)/))?e[1]:null}(t);var L=J(t,k);return"[Function"+(w?": "+w:" (anonymous)")+"]"+(0<L.length?" { "+j.call(L,", ")+" }":"")}if(N(t))return L=_?b.call(String(t),/^(Symbol\(.*\))_[^)]*$/,"$1"):E.call(t),"object"!=typeof t||_?L:B(L);if(function(e){return!(!e||"object"!=typeof e)&&("undefined"!=typeof HTMLElement&&e instanceof HTMLElement||"string"==typeof e.nodeName&&"function"==typeof e.getAttribute)}(t)){for(var V="<"+g.call(String(t.nodeName)),K=t.attributes||[],z=0;z<K.length;z++)V+=" "+K[z].name+"="+M((m=K[z].value,b.call(String(m),/"/g,"&quot;")),"double",y);return V+=">",t.childNodes&&t.childNodes.length&&(V+="..."),V+"</"+g.call(String(t.nodeName))+">"}if(I(t)){if(0===t.length)return"[]";var Q=J(t,k);return P&&!function(e){for(var t=0;t<e.length;t++)if(0<=H(e[t],"\n"))return!1;return!0}(Q)?"["+$(Q,P)+"]":"[ "+j.call(Q,", ")+" ]"}if(!("[object Error]"!==D(Q=t)||C&&"object"==typeof Q&&C in Q)){var Z=J(t,k);return"cause"in t&&!x.call(t,"cause")?"{ ["+String(t)+"] "+j.call(O.call("[cause]: "+k(t.cause),Z),", ")+" }":0===Z.length?"["+String(t)+"]":"{ ["+String(t)+"] "+j.call(Z,", ")+" }"}if("object"==typeof t&&ee){if(T&&"function"==typeof t[T])return t[T]();if("symbol"!==ee&&"function"==typeof t.inspect)return t.inspect()}if(function(e){if(!i||!e||"object"!=typeof e)return!1;try{i.call(e);try{c.call(e)}catch(e){return!0}return e instanceof Map}catch(e){}return!1}(t)){var Y=[];return a.call(t,(function(e,r){Y.push(k(r,t,!0)+" => "+k(e,t))})),G("Map",i.call(t),Y,P)}if(function(e){if(!c||!e||"object"!=typeof e)return!1;try{c.call(e);try{i.call(e)}catch(e){return!0}return e instanceof Set}catch(e){}return!1}(t)){var X=[];return u.call(t,(function(e){X.push(k(e,t))})),G("Set",c.call(t),X,P)}if(function(e){if(!s||!e||"object"!=typeof e)return!1;try{s.call(e,s);try{f.call(e,f)}catch(e){return!0}return e instanceof WeakMap}catch(e){}return!1}(t))return W("WeakMap");if(function(e){if(!f||!e||"object"!=typeof e)return!1;try{f.call(e,f);try{s.call(e,s)}catch(e){return!0}return e instanceof WeakSet}catch(e){}return!1}(t))return W("WeakSet");if(function(e){if(!l||!e||"object"!=typeof e)return!1;try{return l.call(e),!0}catch(e){}return!1}(t))return W("WeakRef");if(!("[object Number]"!==D(Q=t)||C&&"object"==typeof Q&&C in Q))return B(k(Number(t)));if(function(e){if(!e||"object"!=typeof e||!A)return!1;try{return A.call(e),!0}catch(e){}return!1}(t))return B(k(A.call(t)));if(!("[object Boolean]"!==D(Z=t)||C&&"object"==typeof Z&&C in Z))return B(p.call(t));if(!("[object String]"!==D(ee=t)||C&&"object"==typeof ee&&C in ee))return B(k(String(t)));if(("[object Date]"!==D(Q=t)||C&&"object"==typeof Q&&C in Q)&&("[object RegExp]"!==D(te=t)||C&&"object"==typeof te&&C in te)){Z=J(t,k);var ee=F?F(t)===Object.prototype:t instanceof Object||t.constructor===Object,te=(Q=t instanceof Object?"":"null prototype",!ee&&C&&Object(t)===t&&C in t?v.call(D(t),8,-1):Q?"Object":"");return Q=(!ee&&"function"==typeof t.constructor&&t.constructor.name?t.constructor.name+" ":"")+(te||Q?"["+j.call(O.call([],te||[],Q||[]),": ")+"] ":""),0===Z.length?Q+"{}":P?Q+"{"+$(Z,P)+"}":Q+"{ "+j.call(Z,", ")+" }"}return String(t)};var L=Object.prototype.hasOwnProperty||function(e){return e in this};function U(e,t){return L.call(e,t)}function D(e){return y.call(e)}function H(e,t){if(e.indexOf)return e.indexOf(t);for(var r=0,n=e.length;r<n;r++)if(e[r]===t)return r;return-1}function q(e){var t=e.charCodeAt(0);return(e={8:"b",9:"t",10:"n",12:"f",13:"r"}[t])?"\\"+e:"\\x"+(t<16?"0":"")+m.call(t.toString(16))}function B(e){return"Object("+e+")"}function W(e){return e+" { ? }"}function G(e,t,r,n){return e+" ("+t+") {"+(n?$(r,n):j.call(r,", "))+"}"}function $(e,t){if(0===e.length)return"";var r="\n"+t.prev+t.base;return r+j.call(e,","+r)+"\n"+t.prev}function J(e,t){var r=I(e),n=[];if(r){n.length=e.length;for(var o=0;o<e.length;o++)n[o]=U(e,o)?t(e[o],e):""}var i,a="function"==typeof k?k(e):[];if(_)for(var c={},u=0;u<a.length;u++)c["$"+a[u]]=a[u];for(i in e)U(e,i)&&(r&&String(Number(i))===i&&i<e.length||_&&c["$"+i]instanceof Symbol||(w.call(/[^\w$]/,i)?n.push(t(i,e)+": "+t(e[i],e)):n.push(i+": "+t(e[i],e))));if("function"==typeof k)for(var s=0;s<a.length;s++)x.call(e,a[s])&&n.push("["+t(a[s])+"]: "+t(e[a[s]],e));return n}},5527:function(e){"use strict";var t=String.prototype.replace,r=/%20/g,n="RFC3986";e.exports={default:n,formatters:{RFC1738:function(e){return t.call(e,r,"+")},RFC3986:function(e){return String(e)}},RFC1738:"RFC1738",RFC3986:n}},9126:function(e,t,r){"use strict";var n=r(5690),o=r(9166);r=r(5527),e.exports={formats:r,parse:o,stringify:n}},9166:function(e,t,r){"use strict";function n(e,t,r,n){if(e){var o=r.allowDots?e.replace(/\.([^.[]+)/g,"[$1]"):e,a=/(\[[^[\]]*])/g,c=0<r.depth&&/(\[[^[\]]*])/.exec(o),s=[];if(e=c?o.slice(0,c.index):o){if(!r.plainObjects&&i.call(Object.prototype,e)&&!r.allowPrototypes)return;s.push(e)}for(var f=0;0<r.depth&&null!==(c=a.exec(o))&&f<r.depth;){if(f+=1,!r.plainObjects&&i.call(Object.prototype,c[1].slice(1,-1))&&!r.allowPrototypes)return;s.push(c[1])}return c&&s.push("["+o.slice(c.index)+"]"),function(e,t,r,n){for(var o=n?t:u(t,r),i=e.length-1;0<=i;--i){var a,c,s,f=e[i];"[]"===f&&r.parseArrays?a=[].concat(o):(a=r.plainObjects?Object.create(null):{},c="["===f.charAt(0)&&"]"===f.charAt(f.length-1)?f.slice(1,-1):f,s=parseInt(c,10),r.parseArrays||""!==c?!isNaN(s)&&f!==c&&String(s)===c&&0<=s&&r.parseArrays&&s<=r.arrayLimit?(a=[])[s]=o:a[c]=o:a={0:o}),o=a}return o}(s,t,r,n)}}var o=r(2493),i=Object.prototype.hasOwnProperty,a=Array.isArray,c={allowDots:!1,allowPrototypes:!1,allowSparse:!1,arrayLimit:20,charset:"utf-8",charsetSentinel:!1,comma:!1,decoder:o.decode,delimiter:"&",depth:5,ignoreQueryPrefix:!1,interpretNumericEntities:!1,parameterLimit:1e3,parseArrays:!0,plainObjects:!1,strictNullHandling:!1},u=function(e,t){return e&&"string"==typeof e&&t.comma&&-1<e.indexOf(",")?e.split(","):e};e.exports=function(e,t){var r=function(e){if(!e)return c;if(null!==e.decoder&&void 0!==e.decoder&&"function"!=typeof e.decoder)throw new TypeError("Decoder has to be a function.");if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var t=(void 0===e.charset?c:e).charset;return{allowDots:void 0===e.allowDots?c.allowDots:!!e.allowDots,allowPrototypes:("boolean"==typeof e.allowPrototypes?e:c).allowPrototypes,allowSparse:("boolean"==typeof e.allowSparse?e:c).allowSparse,arrayLimit:("number"==typeof e.arrayLimit?e:c).arrayLimit,charset:t,charsetSentinel:("boolean"==typeof e.charsetSentinel?e:c).charsetSentinel,comma:("boolean"==typeof e.comma?e:c).comma,decoder:("function"==typeof e.decoder?e:c).decoder,delimiter:("string"==typeof e.delimiter||o.isRegExp(e.delimiter)?e:c).delimiter,depth:"number"==typeof e.depth||!1===e.depth?+e.depth:c.depth,ignoreQueryPrefix:!0===e.ignoreQueryPrefix,interpretNumericEntities:("boolean"==typeof e.interpretNumericEntities?e:c).interpretNumericEntities,parameterLimit:("number"==typeof e.parameterLimit?e:c).parameterLimit,parseArrays:!1!==e.parseArrays,plainObjects:("boolean"==typeof e.plainObjects?e:c).plainObjects,strictNullHandling:("boolean"==typeof e.strictNullHandling?e:c).strictNullHandling}}(t);if(""===e||null==e)return r.plainObjects?Object.create(null):{};for(var s="string"==typeof e?function(e,t){var r,n,s,f,l={},p=t.ignoreQueryPrefix?e.replace(/^\?/,""):e,y=(e=t.parameterLimit===1/0?void 0:t.parameterLimit,p.split(t.delimiter,e)),h=-1,d=t.charset;if(t.charsetSentinel)for(r=0;r<y.length;++r)0===y[r].indexOf("utf8=")&&("utf8=%E2%9C%93"===y[r]?d="utf-8":"utf8=%26%2310003%3B"===y[r]&&(d="iso-8859-1"),h=r,r=y.length);for(r=0;r<y.length;++r)r!==h&&((f=-1===(f=-1===(f=(n=y[r]).indexOf("]="))?n.indexOf("="):f+1)?(s=t.decoder(n,c.decoder,d,"key"),t.strictNullHandling?null:""):(s=t.decoder(n.slice(0,f),c.decoder,d,"key"),o.maybeMap(u(n.slice(f+1),t),(function(e){return t.decoder(e,c.decoder,d,"value")}))))&&t.interpretNumericEntities&&"iso-8859-1"===d&&(f=f.replace(/&#(\d+);/g,(function(e,t){return String.fromCharCode(parseInt(t,10))}))),-1<n.indexOf("[]=")&&(f=a(f)?[f]:f),i.call(l,s)?l[s]=o.combine(l[s],f):l[s]=f);return l}(e,r):e,f=r.plainObjects?Object.create(null):{},l=Object.keys(s),p=0;p<l.length;++p){var y=n(y=l[p],s[y],r,"string"==typeof e);f=o.merge(f,y,r)}return!0===r.allowSparse?f:o.compact(f)}},5690:function(e,t,r){"use strict";function n(e,t){p.apply(e,f(t)?t:[t])}function o(e,t,r,c,u,s,p,y,v,b,m,g,w,O,j){for(var S,P=e,A=j,k=0,E=!1;void 0!==(A=A.get(d))&&!E;){var _=A.get(e);if(k+=1,void 0!==_){if(_===k)throw new RangeError("Cyclic object value");E=!0}void 0===A.get(d)&&(k=0)}if("function"==typeof p?P=p(t,P):P instanceof Date?P=b(P):"comma"===r&&f(P)&&(P=a.maybeMap(P,(function(e){return e instanceof Date?b(e):e}))),null===P){if(c)return s&&!w?s(t,h.encoder,O,"key",m):t;P=""}if("string"==typeof(S=P)||"number"==typeof S||"boolean"==typeof S||"symbol"==typeof S||"bigint"==typeof S||a.isBuffer(P)){if(s){var C=w?t:s(t,h.encoder,O,"key",m);if("comma"===r&&w){for(var x=l.call(String(P),","),F="",R=0;R<x.length;++R)F+=(0===R?"":",")+g(s(x[R],h.encoder,O,"value",m));return[g(C)+"="+F]}return[g(C)+"="+g(s(P,h.encoder,O,"value",m))]}return[g(t)+"="+g(String(P))]}var T,M=[];if(void 0===P)return M;T="comma"===r&&f(P)?[{value:0<P.length?P.join(",")||null:void 0}]:f(p)?p:(C=Object.keys(P),y?C.sort(y):C);for(var I=0;I<T.length;++I){var N,L=T[I],U="object"==typeof L&&void 0!==L.value?L.value:P[L];u&&null===U||(N=f(P)?"function"==typeof r?r(t,L):t:t+(v?"."+L:"["+L+"]"),j.set(e,k),(L=i()).set(d,j),n(M,o(U,N,r,c,u,s,p,y,v,b,m,g,w,O,L)))}return M}var i=r(4294),a=r(2493),c=r(5527),u=Object.prototype.hasOwnProperty,s={brackets:function(e){return e+"[]"},comma:"comma",indices:function(e,t){return e+"["+t+"]"},repeat:function(e){return e}},f=Array.isArray,l=String.prototype.split,p=Array.prototype.push,y=Date.prototype.toISOString,h=(r=c.default,{addQueryPrefix:!1,allowDots:!1,charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encoder:a.encode,encodeValuesOnly:!1,format:r,formatter:c.formatters[r],indices:!1,serializeDate:function(e){return y.call(e)},skipNulls:!1,strictNullHandling:!1}),d={};e.exports=function(e,t){var r=e,a=function(e){if(!e)return h;if(null!==e.encoder&&void 0!==e.encoder&&"function"!=typeof e.encoder)throw new TypeError("Encoder has to be a function.");var t=e.charset||h.charset;if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var r=c.default;if(void 0!==e.format){if(!u.call(c.formatters,e.format))throw new TypeError("Unknown format option provided.");r=e.format}var n=c.formatters[r],o=h.filter;return"function"!=typeof e.filter&&!f(e.filter)||(o=e.filter),{addQueryPrefix:("boolean"==typeof e.addQueryPrefix?e:h).addQueryPrefix,allowDots:void 0===e.allowDots?h.allowDots:!!e.allowDots,charset:t,charsetSentinel:("boolean"==typeof e.charsetSentinel?e:h).charsetSentinel,delimiter:(void 0===e.delimiter?h:e).delimiter,encode:("boolean"==typeof e.encode?e:h).encode,encoder:("function"==typeof e.encoder?e:h).encoder,encodeValuesOnly:("boolean"==typeof e.encodeValuesOnly?e:h).encodeValuesOnly,filter:o,format:r,formatter:n,serializeDate:("function"==typeof e.serializeDate?e:h).serializeDate,skipNulls:("boolean"==typeof e.skipNulls?e:h).skipNulls,sort:"function"==typeof e.sort?e.sort:null,strictNullHandling:("boolean"==typeof e.strictNullHandling?e:h).strictNullHandling}}(t);"function"==typeof a.filter?r=(0,a.filter)("",r):f(a.filter)&&(y=a.filter);var l=[];if("object"!=typeof r||null===r)return"";e=t&&t.arrayFormat in s?t.arrayFormat:t&&"indices"in t&&!t.indices?"repeat":"indices";var p=s[e],y=y||Object.keys(r);a.sort&&y.sort(a.sort);for(var d=i(),v=0;v<y.length;++v){var b=y[v];a.skipNulls&&null===r[b]||n(l,o(r[b],b,p,a.strictNullHandling,a.skipNulls,a.encode?a.encoder:null,a.filter,a.sort,a.allowDots,a.serializeDate,a.format,a.formatter,a.encodeValuesOnly,a.charset,d))}return t=l.join(a.delimiter),e=!0===a.addQueryPrefix?"?":"",a.charsetSentinel&&("iso-8859-1"===a.charset?e+="utf8=%26%2310003%3B&":e+="utf8=%E2%9C%93&"),0<t.length?e+t:""}},2493:function(e,t,r){"use strict";function n(e,t){for(var r=t&&t.plainObjects?Object.create(null):{},n=0;n<e.length;++n)void 0!==e[n]&&(r[n]=e[n]);return r}var o=r(5527),i=Object.prototype.hasOwnProperty,a=Array.isArray,c=function(){for(var e=[],t=0;t<256;++t)e.push("%"+((t<16?"0":"")+t.toString(16)).toUpperCase());return e}();e.exports={arrayToObject:n,assign:function(e,t){return Object.keys(t).reduce((function(e,r){return e[r]=t[r],e}),e)},combine:function(e,t){return[].concat(e,t)},compact:function(e){for(var t=[{obj:{o:e},prop:"o"}],r=[],n=0;n<t.length;++n)for(var o=t[n],i=o.obj[o.prop],c=Object.keys(i),u=0;u<c.length;++u){var s=c[u],f=i[s];"object"==typeof f&&null!==f&&-1===r.indexOf(f)&&(t.push({obj:i,prop:s}),r.push(f))}return function(e){for(;1<e.length;){var t=e.pop(),r=t.obj[t.prop];if(a(r)){for(var n=[],o=0;o<r.length;++o)void 0!==r[o]&&n.push(r[o]);t.obj[t.prop]=n}}}(t),e},decode:function(e,t,r){if(e=e.replace(/\+/g," "),"iso-8859-1"===r)return e.replace(/%[0-9a-f]{2}/gi,unescape);try{return decodeURIComponent(e)}catch(t){return e}},encode:function(e,t,r,n,i){if(0===e.length)return e;var a=e;if("symbol"==typeof e?a=Symbol.prototype.toString.call(e):"string"!=typeof e&&(a=String(e)),"iso-8859-1"===r)return escape(a).replace(/%u[0-9a-f]{4}/gi,(function(e){return"%26%23"+parseInt(e.slice(2),16)+"%3B"}));for(var u="",s=0;s<a.length;++s){var f=a.charCodeAt(s);45===f||46===f||95===f||126===f||48<=f&&f<=57||65<=f&&f<=90||97<=f&&f<=122||i===o.RFC1738&&(40===f||41===f)?u+=a.charAt(s):f<128?u+=c[f]:f<2048?u+=c[192|f>>6]+c[128|63&f]:f<55296||57344<=f?u+=c[224|f>>12]+c[128|f>>6&63]+c[128|63&f]:(s+=1,f=65536+((1023&f)<<10|1023&a.charCodeAt(s)),u+=c[240|f>>18]+c[128|f>>12&63]+c[128|f>>6&63]+c[128|63&f])}return u},isBuffer:function(e){return!(!e||"object"!=typeof e||!(e.constructor&&e.constructor.isBuffer&&e.constructor.isBuffer(e)))},isRegExp:function(e){return"[object RegExp]"===Object.prototype.toString.call(e)},maybeMap:function(e,t){if(a(e)){for(var r=[],n=0;n<e.length;n+=1)r.push(t(e[n]));return r}return t(e)},merge:function e(t,r,o){if(!r)return t;if("object"!=typeof r){if(a(t))t.push(r);else{if(!t||"object"!=typeof t)return[t,r];(o&&(o.plainObjects||o.allowPrototypes)||!i.call(Object.prototype,r))&&(t[r]=!0)}return t}if(!t||"object"!=typeof t)return[t].concat(r);var c=t;return a(t)&&!a(r)&&(c=n(t,o)),a(t)&&a(r)?(r.forEach((function(r,n){var a;i.call(t,n)?(a=t[n])&&"object"==typeof a&&r&&"object"==typeof r?t[n]=e(a,r,o):t.push(r):t[n]=r})),t):Object.keys(r).reduce((function(t,n){var a=r[n];return i.call(t,n)?t[n]=e(t[n],a,o):t[n]=a,t}),c)}}},4294:function(e,t,r){"use strict";function n(e,t){for(var r,n=e;null!==(r=n.next);n=r)if(r.key===t)return n.next=r.next,r.next=e.next,e.next=r}var o=r(7286),i=r(2680),a=r(9500),c=o("%TypeError%"),u=o("%WeakMap%",!0),s=o("%Map%",!0),f=i("WeakMap.prototype.get",!0),l=i("WeakMap.prototype.set",!0),p=i("WeakMap.prototype.has",!0),y=i("Map.prototype.get",!0),h=i("Map.prototype.set",!0),d=i("Map.prototype.has",!0);e.exports=function(){var e,t,r,o={assert:function(e){if(!o.has(e))throw new c("Side channel does not contain "+a(e))},get:function(o){if(u&&o&&("object"==typeof o||"function"==typeof o)){if(e)return f(e,o)}else if(s){if(t)return y(t,o)}else if(r)return function(e,t){return(t=n(e,t))&&t.value}(r,o)},has:function(o){if(u&&o&&("object"==typeof o||"function"==typeof o)){if(e)return p(e,o)}else if(s){if(t)return d(t,o)}else if(r)return!!n(r,o);return!1},set:function(o,i){var a,c;u&&o&&("object"==typeof o||"function"==typeof o)?(e=e||new u,l(e,o,i)):s?(t=t||new s,h(t,o,i)):(c=i,(o=n(a=r=r||{key:{},next:null},i=o))?o.value=c:a.next={key:i,next:a.next,value:c})}};return o}},9425:function(e,t){var r;void 0===(r="function"==typeof(r=function(){var e="function"==typeof Object.getOwnPropertySymbols;return{create:function(){var e=Object.create(this),t="string"==typeof e.__init?e.__init:"init";return"function"==typeof e[t]&&e[t].apply(e,arguments),e},mixin:function(t,r){var n=r||this,o=/\b_super\b/,i=Object.getPrototypeOf(n)||n.prototype,a={},c=t,u=function(e){var t=Object.getOwnPropertyDescriptor(c,e);!a[e]&&t&&(a[e]=t)};do{Object.getOwnPropertyNames(c).forEach(u),e&&Object.getOwnPropertySymbols(c).forEach(u)}while((c=Object.getPrototypeOf(c))&&Object.getPrototypeOf(c));var s=function(t){var r=a[t];"function"==typeof r.value&&o.test(r.value)&&(r.value=function(t,r,n,o){var i="function"==typeof r,a=function(){var e=this._super;this._super=i?r:t[n];var a=o.apply(this,arguments);return this._super=e,a};return i&&(Object.keys(r).forEach((function(e){a[e]=r[e]})),e&&Object.getOwnPropertySymbols(r).forEach((function(e){a[e]=r[e]}))),a}(i,n[t],t,r.value)),Object.defineProperty(n,t,r)};return Object.keys(a).forEach(s),e&&Object.getOwnPropertySymbols(a).forEach(s),n},extend:function(e,t){return this.mixin(e,Object.create(t||this))},proxy:function(e){var t=this[e],r=Array.prototype.slice.call(arguments,1);return r.unshift(this),t.bind.apply(t,r)}}})?r.apply(t,[]):r)||(e.exports=r)},3260:function(){}},t={},function r(n){var o=t[n];return void 0!==o||(o=t[n]={exports:{}},e[n].call(o.exports,o,o.exports,r)),o.exports}(3219);var e,t}));
// socket.io.min.js
!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.io=e():t.io=e()}(this,function(){return o={},n.m=r=[function(t,e,n){function r(t,e){"object"==typeof t&&(e=t,t=void 0),e=e||{};var n=i(t),r=n.source,o=n.id,t=n.path,t=c[o]&&t in c[o].nsps,o=e.forceNew||e["force new connection"]||!1===e.multiplex||t?(a("ignoring socket cache for %s",r),s(r,e)):(c[o]||(a("new io instance for %s",r),c[o]=s(r,e)),c[o]);return n.query&&!e.query&&(e.query=n.query),o.socket(n.path,e)}var i=n(1),o=n(7),s=n(12),a=n(3)("socket.io-client");t.exports=e=r;var c=e.managers={};e.protocol=o.protocol,e.connect=r,e.Manager=n(12),e.Socket=n(37)},function(t,e,n){var r=n(2),o=n(3)("socket.io-client:url");t.exports=function(t,e){var n=t;return e=e||"undefined"!=typeof location&&location,null==t&&(t=e.protocol+"//"+e.host),"string"==typeof t&&("/"===t.charAt(0)&&(t="/"===t.charAt(1)?e.protocol+t:e.host+t),/^(https?|wss?):\/\//.test(t)||(o("protocol-less url %s",t),t=void 0!==e?e.protocol+"//"+t:"https://"+t),o("parse %s",t),n=r(t)),n.port||(/^(http|ws)$/.test(n.protocol)?n.port="80":/^(http|ws)s$/.test(n.protocol)&&(n.port="443")),n.path=n.path||"/",t=-1!==n.host.indexOf(":")?"["+n.host+"]":n.host,n.id=n.protocol+"://"+t+":"+n.port,n.href=n.protocol+"://"+t+(e&&e.port===n.port?"":":"+n.port),n}},function(t,e){var c=/^(?:(?![^:@]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,p=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];t.exports=function(t){var e=t,n=t.indexOf("["),r=t.indexOf("]");-1!=n&&-1!=r&&(t=t.substring(0,n)+t.substring(n,r).replace(/:/g,";")+t.substring(r,t.length));for(var o,i=c.exec(t||""),s={},a=14;a--;)s[p[a]]=i[a]||"";return-1!=n&&-1!=r&&(s.source=e,s.host=s.host.substring(1,s.host.length-1).replace(/;/g,":"),s.authority=s.authority.replace("[","").replace("]","").replace(/;/g,":"),s.ipv6uri=!0),s.pathNames=(r=s.path,e=r.replace(/\/{2,9}/g,"/").split("/"),"/"!=r.substr(0,1)&&0!==r.length||e.splice(0,1),"/"==r.substr(r.length-1,1)&&e.splice(e.length-1,1),e),s.queryKey=(e=s.query,o={},e.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,function(t,e,n){e&&(o[e]=n)}),o),s}},function(r,o,i){(function(e){"use strict";function t(){var t;try{t=o.storage.debug}catch(t){}return!t&&void 0!==e&&"env"in e&&(t=e.env.DEBUG),t}var n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};(o=r.exports=i(5)).log=function(){return"object"===("undefined"==typeof console?"undefined":n(console))&&console.log&&Function.prototype.apply.call(console.log,console,arguments)},o.formatArgs=function(t){var e,n,r=this.useColors;t[0]=(r?"%c":"")+this.namespace+(r?" %c":" ")+t[0]+(r?"%c ":" ")+"+"+o.humanize(this.diff),r&&(r="color: "+this.color,t.splice(1,0,r,"color: inherit"),t[n=e=0].replace(/%[a-zA-Z%]/g,function(t){"%%"!==t&&(e++,"%c"===t&&(n=e))}),t.splice(n,0,r))},o.save=function(t){try{null==t?o.storage.removeItem("debug"):o.storage.debug=t}catch(t){}},o.load=t,o.useColors=function(){return!("undefined"==typeof window||!window.process||"renderer"!==window.process.type)||("undefined"==typeof navigator||!navigator.userAgent||!navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&31<=parseInt(RegExp.$1,10)||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},o.storage="undefined"!=typeof chrome&&void 0!==chrome.storage?chrome.storage.local:function(){try{return window.localStorage}catch(t){}}(),o.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],o.formatters.j=function(t){try{return JSON.stringify(t)}catch(t){return"[UnexpectedJSONParseError]: "+t.message}},o.enable(t())}).call(o,i(4))},function(t,e){function n(){throw new Error("setTimeout has not been defined")}function r(){throw new Error("clearTimeout has not been defined")}function o(e){if(p===setTimeout)return setTimeout(e,0);if((p===n||!p)&&setTimeout)return p=setTimeout,setTimeout(e,0);try{return p(e,0)}catch(t){try{return p.call(null,e,0)}catch(t){return p.call(this,e,0)}}}function i(){l&&u&&(l=!1,u.length?f=u.concat(f):d=-1,f.length&&s())}function s(){if(!l){var t=o(i);l=!0;for(var e=f.length;e;){for(u=f,f=[];++d<e;)u&&u[d].run();d=-1,e=f.length}u=null,l=!1,function(e){if(h===clearTimeout)return clearTimeout(e);if((h===r||!h)&&clearTimeout)return h=clearTimeout,clearTimeout(e);try{h(e)}catch(t){try{return h.call(null,e)}catch(t){return h.call(this,e)}}}(t)}}function a(t,e){this.fun=t,this.array=e}function c(){}var p,h,t=t.exports={};!function(){try{p="function"==typeof setTimeout?setTimeout:n}catch(t){p=n}try{h="function"==typeof clearTimeout?clearTimeout:r}catch(t){h=r}}();var u,f=[],l=!1,d=-1;t.nextTick=function(t){var e=new Array(arguments.length-1);if(1<arguments.length)for(var n=1;n<arguments.length;n++)e[n-1]=arguments[n];f.push(new a(t,e)),1!==f.length||l||o(s)},a.prototype.run=function(){this.fun.apply(null,this.array)},t.title="browser",t.browser=!0,t.env={},t.argv=[],t.version="",t.versions={},t.on=c,t.addListener=c,t.once=c,t.off=c,t.removeListener=c,t.removeAllListeners=c,t.emit=c,t.prependListener=c,t.prependOnceListener=c,t.listeners=function(t){return[]},t.binding=function(t){throw new Error("process.binding is not supported")},t.cwd=function(){return"/"},t.chdir=function(t){throw new Error("process.chdir is not supported")},t.umask=function(){return 0}},function(t,c,e){"use strict";function n(t){function s(){if(s.enabled){var r=s,t=+new Date,e=t-(a||t);r.diff=e,r.prev=a,r.curr=t,a=t;for(var o=new Array(arguments.length),n=0;n<o.length;n++)o[n]=arguments[n];o[0]=c.coerce(o[0]),"string"!=typeof o[0]&&o.unshift("%O");var i=0;o[0]=o[0].replace(/%([a-zA-Z%])/g,function(t,e){if("%%"===t)return t;i++;var n=c.formatters[e];return"function"==typeof n&&(e=o[i],t=n.call(r,e),o.splice(i,1),i--),t}),c.formatArgs.call(r,o),(s.log||c.log||console.log.bind(console)).apply(r,o)}}var a;return s.namespace=t,s.enabled=c.enabled(t),s.useColors=c.useColors(),s.color=function(t){var e,n=0;for(e in t)n=(n<<5)-n+t.charCodeAt(e),n|=0;return c.colors[Math.abs(n)%c.colors.length]}(t),s.destroy=r,"function"==typeof c.init&&c.init(s),c.instances.push(s),s}function r(){var t=c.instances.indexOf(this);return-1!==t&&(c.instances.splice(t,1),!0)}(c=t.exports=n.debug=n.default=n).coerce=function(t){return t instanceof Error?t.stack||t.message:t},c.disable=function(){c.enable("")},c.enable=function(t){c.save(t),c.names=[],c.skips=[];for(var e=("string"==typeof t?t:"").split(/[\s,]+/),n=e.length,r=0;r<n;r++)e[r]&&("-"===(t=e[r].replace(/\*/g,".*?"))[0]?c.skips.push(new RegExp("^"+t.substr(1)+"$")):c.names.push(new RegExp("^"+t+"$")));for(r=0;r<c.instances.length;r++){var o=c.instances[r];o.enabled=c.enabled(o.namespace)}},c.enabled=function(t){if("*"===t[t.length-1])return!0;for(var e=0,n=c.skips.length;e<n;e++)if(c.skips[e].test(t))return!1;for(e=0,n=c.names.length;e<n;e++)if(c.names[e].test(t))return!0;return!1},c.humanize=e(6),c.instances=[],c.names=[],c.skips=[],c.formatters={}},function(t,e){function o(t,e,n){if(!(t<e))return t<1.5*e?Math.floor(t/e)+" "+n:Math.ceil(t/e)+" "+n+"s"}var i=36e5,s=864e5;t.exports=function(t,e){e=e||{};var n,r=typeof t;if("string"==r&&0<t.length)return function(t){if(!(100<(t=String(t)).length)){var e=/^((?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|years?|yrs?|y)?$/i.exec(t);if(e){t=parseFloat(e[1]);switch((e[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*t;case"days":case"day":case"d":return t*s;case"hours":case"hour":case"hrs":case"hr":case"h":return t*i;case"minutes":case"minute":case"mins":case"min":case"m":return 6e4*t;case"seconds":case"second":case"secs":case"sec":case"s":return 1e3*t;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return t;default:return}}}}(t);if("number"==r&&!1===isNaN(t))return e.long?o(n=t,s,"day")||o(n,i,"hour")||o(n,6e4,"minute")||o(n,1e3,"second")||n+" ms":s<=(n=t)?Math.round(n/s)+"d":i<=n?Math.round(n/i)+"h":6e4<=n?Math.round(n/6e4)+"m":1e3<=n?Math.round(n/1e3)+"s":n+"ms";throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(t))}},function(t,s,e){function n(){}function r(t){var e=""+t.type;if(s.BINARY_EVENT!==t.type&&s.BINARY_ACK!==t.type||(e+=t.attachments+"-"),t.nsp&&"/"!==t.nsp&&(e+=t.nsp+","),null!=t.id&&(e+=t.id),null!=t.data){var n=function(t){try{return JSON.stringify(t)}catch(t){return!1}}(t.data);if(!1===n)return y;e+=n}return h("encoded %j as %s",t,e),e}function o(t,n){f.removeBlobs(t,function(t){var e=f.deconstructPacket(t),t=r(e.packet);(e=e.buffers).unshift(t),n(e)})}function i(){this.reconstructor=null}function a(t){var e=0,n={type:Number(t.charAt(0))};if(null==s.types[n.type])return p("unknown packet type "+n.type);if(s.BINARY_EVENT===n.type||s.BINARY_ACK===n.type){for(var r="";"-"!==t.charAt(++e)&&(r+=t.charAt(e),e!=t.length););if(r!=Number(r)||"-"!==t.charAt(e))throw new Error("Illegal attachments");n.attachments=Number(r)}if("/"===t.charAt(e+1))for(n.nsp="";++e;){if(","===(o=t.charAt(e)))break;if(n.nsp+=o,e===t.length)break}else n.nsp="/";var o,i=t.charAt(e+1);if(""!==i&&Number(i)==i){for(n.id="";++e;){if(null==(o=t.charAt(e))||Number(o)!=o){--e;break}if(n.id+=t.charAt(e),e===t.length)break}n.id=Number(n.id)}if(t.charAt(++e)){i=function(t){try{return JSON.parse(t)}catch(t){return!1}}(t.substr(e));if(!(!1!==i&&(n.type===s.ERROR||l(i))))return p("invalid payload");n.data=i}return h("decoded %s as %j",t,n),n}function c(t){this.reconPack=t,this.buffers=[]}function p(t){return{type:s.ERROR,data:"parser error: "+t}}var h=e(3)("socket.io-parser"),u=e(8),f=e(9),l=e(10),d=e(11);s.protocol=4,s.types=["CONNECT","DISCONNECT","EVENT","ACK","ERROR","BINARY_EVENT","BINARY_ACK"],s.CONNECT=0,s.DISCONNECT=1,s.EVENT=2,s.ACK=3,s.ERROR=4,s.BINARY_EVENT=5,s.BINARY_ACK=6,s.Encoder=n,s.Decoder=i;var y=s.ERROR+'"encode error"';n.prototype.encode=function(t,e){h("encoding packet %j",t),s.BINARY_EVENT===t.type||s.BINARY_ACK===t.type?o(t,e):e([r(t)])},u(i.prototype),i.prototype.add=function(t){var e;if("string"==typeof t)e=a(t),s.BINARY_EVENT===e.type||s.BINARY_ACK===e.type?(this.reconstructor=new c(e),0===this.reconstructor.reconPack.attachments&&this.emit("decoded",e)):this.emit("decoded",e);else{if(!d(t)&&!t.base64)throw new Error("Unknown type: "+t);if(!this.reconstructor)throw new Error("got binary data when not reconstructing a packet");(e=this.reconstructor.takeBinaryData(t))&&(this.reconstructor=null,this.emit("decoded",e))}},i.prototype.destroy=function(){this.reconstructor&&this.reconstructor.finishedReconstruction()},c.prototype.takeBinaryData=function(t){if(this.buffers.push(t),this.buffers.length!==this.reconPack.attachments)return null;t=f.reconstructPacket(this.reconPack,this.buffers);return this.finishedReconstruction(),t},c.prototype.finishedReconstruction=function(){this.reconPack=null,this.buffers=[]}},function(t,e,n){function r(t){if(t)return function(t){for(var e in r.prototype)t[e]=r.prototype[e];return t}(t)}(t.exports=r).prototype.on=r.prototype.addEventListener=function(t,e){return this._callbacks=this._callbacks||{},(this._callbacks["$"+t]=this._callbacks["$"+t]||[]).push(e),this},r.prototype.once=function(t,e){function n(){this.off(t,n),e.apply(this,arguments)}return n.fn=e,this.on(t,n),this},r.prototype.off=r.prototype.removeListener=r.prototype.removeAllListeners=r.prototype.removeEventListener=function(t,e){if(this._callbacks=this._callbacks||{},0==arguments.length)return this._callbacks={},this;var n=this._callbacks["$"+t];if(!n)return this;if(1==arguments.length)return delete this._callbacks["$"+t],this;for(var r,o=0;o<n.length;o++)if((r=n[o])===e||r.fn===e){n.splice(o,1);break}return 0===n.length&&delete this._callbacks["$"+t],this},r.prototype.emit=function(t){this._callbacks=this._callbacks||{};for(var e=new Array(arguments.length-1),n=this._callbacks["$"+t],r=1;r<arguments.length;r++)e[r-1]=arguments[r];if(n)for(var r=0,o=(n=n.slice(0)).length;r<o;++r)n[r].apply(this,e);return this},r.prototype.listeners=function(t){return this._callbacks=this._callbacks||{},this._callbacks["$"+t]||[]},r.prototype.hasListeners=function(t){return!!this.listeners(t).length}},function(t,e,n){var h=n(10),u=n(11),n=Object.prototype.toString,f="function"==typeof Blob||"undefined"!=typeof Blob&&"[object BlobConstructor]"===n.call(Blob),l="function"==typeof File||"undefined"!=typeof File&&"[object FileConstructor]"===n.call(File);e.deconstructPacket=function(t){var e=[],n=t.data,t=t;return t.data=function t(e,n){if(!e)return e;if(u(e)){var r={_placeholder:!0,num:n.length};return n.push(e),r}if(h(e)){for(var o=new Array(e.length),i=0;i<e.length;i++)o[i]=t(e[i],n);return o}if("object"!=typeof e||e instanceof Date)return e;var s,o={};for(s in e)o[s]=t(e[s],n);return o}(n,e),t.attachments=e.length,{packet:t,buffers:e}},e.reconstructPacket=function(t,e){return t.data=function t(e,n){if(!e)return e;if(e&&e._placeholder)return n[e.num];if(h(e))for(var r=0;r<e.length;r++)e[r]=t(e[r],n);else if("object"==typeof e)for(var o in e)e[o]=t(e[o],n);return e}(t.data,e),t.attachments=void 0,t},e.removeBlobs=function(t,a){var c=0,p=t;(function t(e,n,r){if(!e)return e;if(f&&e instanceof Blob||l&&e instanceof File){c++;var o=new FileReader;o.onload=function(){r?r[n]=this.result:p=this.result,--c||a(p)},o.readAsArrayBuffer(e)}else if(h(e))for(var i=0;i<e.length;i++)t(e[i],i,e);else if("object"==typeof e&&!u(e))for(var s in e)t(e[s],s,e)})(p),c||a(p)}},function(t,e){var n={}.toString;t.exports=Array.isArray||function(t){return"[object Array]"==n.call(t)}},function(t,e){t.exports=function(t){return n&&Buffer.isBuffer(t)||r&&(t instanceof ArrayBuffer||o(t))};var n="function"==typeof Buffer&&"function"==typeof Buffer.isBuffer,r="function"==typeof ArrayBuffer,o=function(t){return"function"==typeof ArrayBuffer.isView?ArrayBuffer.isView(t):t.buffer instanceof ArrayBuffer}},function(t,e,n){function r(t,e){if(!(this instanceof r))return new r(t,e);t&&"object"==typeof t&&(e=t,t=void 0),(e=e||{}).path=e.path||"/socket.io",this.nsps={},this.subs=[],this.opts=e,this.reconnection(!1!==e.reconnection),this.reconnectionAttempts(e.reconnectionAttempts||1/0),this.reconnectionDelay(e.reconnectionDelay||1e3),this.reconnectionDelayMax(e.reconnectionDelayMax||5e3),this.randomizationFactor(e.randomizationFactor||.5),this.backoff=new f({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(null==e.timeout?2e4:e.timeout),this.readyState="closed",this.uri=t,this.connecting=[],this.lastPing=null,this.encoding=!1,this.packetBuffer=[];t=e.parser||s;this.encoder=new t.Encoder,this.decoder=new t.Decoder,this.autoConnect=!1!==e.autoConnect,this.autoConnect&&this.open()}var c=n(13),i=n(37),o=n(8),s=n(7),p=n(39),a=n(40),h=n(3)("socket.io-client:manager"),u=n(36),f=n(41),l=Object.prototype.hasOwnProperty;(t.exports=r).prototype.emitAll=function(){for(var t in this.emit.apply(this,arguments),this.nsps)l.call(this.nsps,t)&&this.nsps[t].emit.apply(this.nsps[t],arguments)},r.prototype.updateSocketIds=function(){for(var t in this.nsps)l.call(this.nsps,t)&&(this.nsps[t].id=this.generateId(t))},r.prototype.generateId=function(t){return("/"===t?"":t+"#")+this.engine.id},o(r.prototype),r.prototype.reconnection=function(t){return arguments.length?(this._reconnection=!!t,this):this._reconnection},r.prototype.reconnectionAttempts=function(t){return arguments.length?(this._reconnectionAttempts=t,this):this._reconnectionAttempts},r.prototype.reconnectionDelay=function(t){return arguments.length?(this._reconnectionDelay=t,this.backoff&&this.backoff.setMin(t),this):this._reconnectionDelay},r.prototype.randomizationFactor=function(t){return arguments.length?(this._randomizationFactor=t,this.backoff&&this.backoff.setJitter(t),this):this._randomizationFactor},r.prototype.reconnectionDelayMax=function(t){return arguments.length?(this._reconnectionDelayMax=t,this.backoff&&this.backoff.setMax(t),this):this._reconnectionDelayMax},r.prototype.timeout=function(t){return arguments.length?(this._timeout=t,this):this._timeout},r.prototype.maybeReconnectOnOpen=function(){!this.reconnecting&&this._reconnection&&0===this.backoff.attempts&&this.reconnect()},r.prototype.open=r.prototype.connect=function(n,t){if(h("readyState %s",this.readyState),~this.readyState.indexOf("open"))return this;h("opening %s",this.uri),this.engine=c(this.uri,this.opts);var e=this.engine,r=this;this.readyState="opening",this.skipReconnect=!1;var o,i,s=p(e,"open",function(){r.onopen(),n&&n()}),a=p(e,"error",function(t){var e;h("connect_error"),r.cleanup(),r.readyState="closed",r.emitAll("connect_error",t),n?((e=new Error("Connection error")).data=t,n(e)):r.maybeReconnectOnOpen()});return!1!==this._timeout&&(o=this._timeout,h("connect attempt will timeout after %d",o),0===o&&s.destroy(),i=setTimeout(function(){h("connect attempt timed out after %d",o),s.destroy(),e.close(),e.emit("error","timeout"),r.emitAll("connect_timeout",o)},o),this.subs.push({destroy:function(){clearTimeout(i)}})),this.subs.push(s),this.subs.push(a),this},r.prototype.onopen=function(){h("open"),this.cleanup(),this.readyState="open",this.emit("open");var t=this.engine;this.subs.push(p(t,"data",a(this,"ondata"))),this.subs.push(p(t,"ping",a(this,"onping"))),this.subs.push(p(t,"pong",a(this,"onpong"))),this.subs.push(p(t,"error",a(this,"onerror"))),this.subs.push(p(t,"close",a(this,"onclose"))),this.subs.push(p(this.decoder,"decoded",a(this,"ondecoded")))},r.prototype.onping=function(){this.lastPing=new Date,this.emitAll("ping")},r.prototype.onpong=function(){this.emitAll("pong",new Date-this.lastPing)},r.prototype.ondata=function(t){this.decoder.add(t)},r.prototype.ondecoded=function(t){this.emit("packet",t)},r.prototype.onerror=function(t){h("error",t),this.emitAll("error",t)},r.prototype.socket=function(t,e){function n(){~u(r.connecting,o)||r.connecting.push(o)}var r,o=this.nsps[t];return o||(o=new i(this,t,e),this.nsps[t]=o,r=this,o.on("connecting",n),o.on("connect",function(){o.id=r.generateId(t)}),this.autoConnect&&n()),o},r.prototype.destroy=function(t){t=u(this.connecting,t);~t&&this.connecting.splice(t,1),this.connecting.length||this.close()},r.prototype.packet=function(n){h("writing packet %j",n);var r=this;n.query&&0===n.type&&(n.nsp+="?"+n.query),r.encoding?r.packetBuffer.push(n):(r.encoding=!0,this.encoder.encode(n,function(t){for(var e=0;e<t.length;e++)r.engine.write(t[e],n.options);r.encoding=!1,r.processPacketQueue()}))},r.prototype.processPacketQueue=function(){var t;0<this.packetBuffer.length&&!this.encoding&&(t=this.packetBuffer.shift(),this.packet(t))},r.prototype.cleanup=function(){h("cleanup");for(var t=this.subs.length,e=0;e<t;e++)this.subs.shift().destroy();this.packetBuffer=[],this.encoding=!1,this.lastPing=null,this.decoder.destroy()},r.prototype.close=r.prototype.disconnect=function(){h("disconnect"),this.skipReconnect=!0,this.reconnecting=!1,"opening"===this.readyState&&this.cleanup(),this.backoff.reset(),this.readyState="closed",this.engine&&this.engine.close()},r.prototype.onclose=function(t){h("onclose"),this.cleanup(),this.backoff.reset(),this.readyState="closed",this.emit("close",t),this._reconnection&&!this.skipReconnect&&this.reconnect()},r.prototype.reconnect=function(){if(this.reconnecting||this.skipReconnect)return this;var t,e,n=this;this.backoff.attempts>=this._reconnectionAttempts?(h("reconnect failed"),this.backoff.reset(),this.emitAll("reconnect_failed"),this.reconnecting=!1):(t=this.backoff.duration(),h("will wait %dms before reconnect attempt",t),this.reconnecting=!0,e=setTimeout(function(){n.skipReconnect||(h("attempting reconnect"),n.emitAll("reconnect_attempt",n.backoff.attempts),n.emitAll("reconnecting",n.backoff.attempts),n.skipReconnect||n.open(function(t){t?(h("reconnect attempt error"),n.reconnecting=!1,n.reconnect(),n.emitAll("reconnect_error",t.data)):(h("reconnect success"),n.onreconnect())}))},t),this.subs.push({destroy:function(){clearTimeout(e)}}))},r.prototype.onreconnect=function(){var t=this.backoff.attempts;this.reconnecting=!1,this.backoff.reset(),this.updateSocketIds(),this.emitAll("reconnect",t)}},function(t,e,n){t.exports=n(14),t.exports.parser=n(22)},function(t,e,n){function u(t,e){return this instanceof u?(e=e||{},t&&"object"==typeof t&&(e=t,t=null),t?(t=a(t),e.hostname=t.host,e.secure="https"===t.protocol||"wss"===t.protocol,e.port=t.port,t.query&&(e.query=t.query)):e.host&&(e.hostname=a(e.host).host),this.secure=null!=e.secure?e.secure:"undefined"!=typeof location&&"https:"===location.protocol,e.hostname&&!e.port&&(e.port=this.secure?"443":"80"),this.agent=e.agent||!1,this.hostname=e.hostname||("undefined"!=typeof location?location.hostname:"localhost"),this.port=e.port||("undefined"!=typeof location&&location.port?location.port:this.secure?443:80),this.query=e.query||{},"string"==typeof this.query&&(this.query=c.decode(this.query)),this.upgrade=!1!==e.upgrade,this.path=(e.path||"/engine.io").replace(/\/$/,"")+"/",this.forceJSONP=!!e.forceJSONP,this.jsonp=!1!==e.jsonp,this.forceBase64=!!e.forceBase64,this.enablesXDR=!!e.enablesXDR,this.withCredentials=!1!==e.withCredentials,this.timestampParam=e.timestampParam||"t",this.timestampRequests=e.timestampRequests,this.transports=e.transports||["polling","websocket"],this.transportOptions=e.transportOptions||{},this.readyState="",this.writeBuffer=[],this.prevBufferLen=0,this.policyPort=e.policyPort||843,this.rememberUpgrade=e.rememberUpgrade||!1,this.binaryType=null,this.onlyBinaryUpgrades=e.onlyBinaryUpgrades,this.perMessageDeflate=!1!==e.perMessageDeflate&&(e.perMessageDeflate||{}),!0===this.perMessageDeflate&&(this.perMessageDeflate={}),this.perMessageDeflate&&null==this.perMessageDeflate.threshold&&(this.perMessageDeflate.threshold=1024),this.pfx=e.pfx||null,this.key=e.key||null,this.passphrase=e.passphrase||null,this.cert=e.cert||null,this.ca=e.ca||null,this.ciphers=e.ciphers||null,this.rejectUnauthorized=void 0===e.rejectUnauthorized||e.rejectUnauthorized,this.forceNode=!!e.forceNode,this.isReactNative="undefined"!=typeof navigator&&"string"==typeof navigator.product&&"reactnative"===navigator.product.toLowerCase(),"undefined"!=typeof self&&!this.isReactNative||(e.extraHeaders&&0<Object.keys(e.extraHeaders).length&&(this.extraHeaders=e.extraHeaders),e.localAddress&&(this.localAddress=e.localAddress)),this.id=null,this.upgrades=null,this.pingInterval=null,this.pingTimeout=null,this.pingIntervalTimer=null,this.pingTimeoutTimer=null,void this.open()):new u(t,e)}var r=n(15),o=n(8),f=n(3)("engine.io-client:socket"),i=n(36),s=n(22),a=n(2),c=n(30);(t.exports=u).priorWebsocketSuccess=!1,o(u.prototype),u.protocol=s.protocol,(u.Socket=u).Transport=n(21),u.transports=n(15),u.parser=n(22),u.prototype.createTransport=function(t){f('creating transport "%s"',t);var e=function(t){var e,n={};for(e in t)t.hasOwnProperty(e)&&(n[e]=t[e]);return n}(this.query);e.EIO=s.protocol,e.transport=t;var n=this.transportOptions[t]||{};return this.id&&(e.sid=this.id),new r[t]({query:e,socket:this,agent:n.agent||this.agent,hostname:n.hostname||this.hostname,port:n.port||this.port,secure:n.secure||this.secure,path:n.path||this.path,forceJSONP:n.forceJSONP||this.forceJSONP,jsonp:n.jsonp||this.jsonp,forceBase64:n.forceBase64||this.forceBase64,enablesXDR:n.enablesXDR||this.enablesXDR,withCredentials:n.withCredentials||this.withCredentials,timestampRequests:n.timestampRequests||this.timestampRequests,timestampParam:n.timestampParam||this.timestampParam,policyPort:n.policyPort||this.policyPort,pfx:n.pfx||this.pfx,key:n.key||this.key,passphrase:n.passphrase||this.passphrase,cert:n.cert||this.cert,ca:n.ca||this.ca,ciphers:n.ciphers||this.ciphers,rejectUnauthorized:n.rejectUnauthorized||this.rejectUnauthorized,perMessageDeflate:n.perMessageDeflate||this.perMessageDeflate,extraHeaders:n.extraHeaders||this.extraHeaders,forceNode:n.forceNode||this.forceNode,localAddress:n.localAddress||this.localAddress,requestTimeout:n.requestTimeout||this.requestTimeout,protocols:n.protocols||void 0,isReactNative:this.isReactNative})},u.prototype.open=function(){var t;if(this.rememberUpgrade&&u.priorWebsocketSuccess&&-1!==this.transports.indexOf("websocket"))t="websocket";else{if(0===this.transports.length){var e=this;return void setTimeout(function(){e.emit("error","No transports available")},0)}t=this.transports[0]}this.readyState="opening";try{t=this.createTransport(t)}catch(t){return this.transports.shift(),void this.open()}t.open(),this.setTransport(t)},u.prototype.setTransport=function(t){f("setting transport %s",t.name);var e=this;this.transport&&(f("clearing existing transport %s",this.transport.name),this.transport.removeAllListeners()),(this.transport=t).on("drain",function(){e.onDrain()}).on("packet",function(t){e.onPacket(t)}).on("error",function(t){e.onError(t)}).on("close",function(){e.onClose("transport close")})},u.prototype.probe=function(n){function t(){var t;h.onlyBinaryUpgrades&&(t=!this.supportsBinary&&h.transport.supportsBinary,p=p||t),p||(f('probe transport "%s" opened',n),c.send([{type:"ping",data:"probe"}]),c.once("packet",function(t){p||("pong"===t.type&&"probe"===t.data?(f('probe transport "%s" pong',n),h.upgrading=!0,h.emit("upgrading",c),c&&(u.priorWebsocketSuccess="websocket"===c.name,f('pausing current transport "%s"',h.transport.name),h.transport.pause(function(){p||"closed"!==h.readyState&&(f("changing transport and sending upgrade packet"),a(),h.setTransport(c),c.send([{type:"upgrade"}]),h.emit("upgrade",c),c=null,h.upgrading=!1,h.flush())}))):(f('probe transport "%s" failed',n),(t=new Error("probe error")).transport=c.name,h.emit("upgradeError",t)))}))}function r(){p||(p=!0,a(),c.close(),c=null)}function e(t){var e=new Error("probe error: "+t);e.transport=c.name,r(),f('probe transport "%s" failed because of error: %s',n,t),h.emit("upgradeError",e)}function o(){e("transport closed")}function i(){e("socket closed")}function s(t){c&&t.name!==c.name&&(f('"%s" works - aborting "%s"',t.name,c.name),r())}function a(){c.removeListener("open",t),c.removeListener("error",e),c.removeListener("close",o),h.removeListener("close",i),h.removeListener("upgrading",s)}f('probing transport "%s"',n);var c=this.createTransport(n,{probe:1}),p=!1,h=this;u.priorWebsocketSuccess=!1,c.once("open",t),c.once("error",e),c.once("close",o),this.once("close",i),this.once("upgrading",s),c.open()},u.prototype.onOpen=function(){if(f("socket open"),this.readyState="open",u.priorWebsocketSuccess="websocket"===this.transport.name,this.emit("open"),this.flush(),"open"===this.readyState&&this.upgrade&&this.transport.pause){f("starting upgrade probes");for(var t=0,e=this.upgrades.length;t<e;t++)this.probe(this.upgrades[t])}},u.prototype.onPacket=function(t){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState)switch(f('socket receive: type "%s", data "%s"',t.type,t.data),this.emit("packet",t),this.emit("heartbeat"),t.type){case"open":this.onHandshake(JSON.parse(t.data));break;case"pong":this.setPing(),this.emit("pong");break;case"error":var e=new Error("server error");e.code=t.data,this.onError(e);break;case"message":this.emit("data",t.data),this.emit("message",t.data)}else f('packet received with socket readyState "%s"',this.readyState)},u.prototype.onHandshake=function(t){this.emit("handshake",t),this.id=t.sid,this.transport.query.sid=t.sid,this.upgrades=this.filterUpgrades(t.upgrades),this.pingInterval=t.pingInterval,this.pingTimeout=t.pingTimeout,this.onOpen(),"closed"!==this.readyState&&(this.setPing(),this.removeListener("heartbeat",this.onHeartbeat),this.on("heartbeat",this.onHeartbeat))},u.prototype.onHeartbeat=function(t){clearTimeout(this.pingTimeoutTimer);var e=this;e.pingTimeoutTimer=setTimeout(function(){"closed"!==e.readyState&&e.onClose("ping timeout")},t||e.pingInterval+e.pingTimeout)},u.prototype.setPing=function(){var t=this;clearTimeout(t.pingIntervalTimer),t.pingIntervalTimer=setTimeout(function(){f("writing ping packet - expecting pong within %sms",t.pingTimeout),t.ping(),t.onHeartbeat(t.pingTimeout)},t.pingInterval)},u.prototype.ping=function(){var t=this;this.sendPacket("ping",function(){t.emit("ping")})},u.prototype.onDrain=function(){this.writeBuffer.splice(0,this.prevBufferLen),(this.prevBufferLen=0)===this.writeBuffer.length?this.emit("drain"):this.flush()},u.prototype.flush=function(){"closed"!==this.readyState&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length&&(f("flushing %d packets in socket",this.writeBuffer.length),this.transport.send(this.writeBuffer),this.prevBufferLen=this.writeBuffer.length,this.emit("flush"))},u.prototype.write=u.prototype.send=function(t,e,n){return this.sendPacket("message",t,e,n),this},u.prototype.sendPacket=function(t,e,n,r){"function"==typeof e&&(r=e,e=void 0),"function"==typeof n&&(r=n,n=null),"closing"!==this.readyState&&"closed"!==this.readyState&&((n=n||{}).compress=!1!==n.compress,n={type:t,data:e,options:n},this.emit("packetCreate",n),this.writeBuffer.push(n),r&&this.once("flush",r),this.flush())},u.prototype.close=function(){function t(){r.onClose("forced close"),f("socket closing - telling transport to close"),r.transport.close()}function e(){r.removeListener("upgrade",e),r.removeListener("upgradeError",e),t()}function n(){r.once("upgrade",e),r.once("upgradeError",e)}var r;return"opening"!==this.readyState&&"open"!==this.readyState||(this.readyState="closing",(r=this).writeBuffer.length?this.once("drain",function(){(this.upgrading?n:t)()}):(this.upgrading?n:t)()),this},u.prototype.onError=function(t){f("socket error %j",t),u.priorWebsocketSuccess=!1,this.emit("error",t),this.onClose("transport error",t)},u.prototype.onClose=function(t,e){"opening"!==this.readyState&&"open"!==this.readyState&&"closing"!==this.readyState||(f('socket close with reason: "%s"',t),clearTimeout(this.pingIntervalTimer),clearTimeout(this.pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),this.readyState="closed",this.id=null,this.emit("close",t,e),this.writeBuffer=[],this.prevBufferLen=0)},u.prototype.filterUpgrades=function(t){for(var e=[],n=0,r=t.length;n<r;n++)~i(this.transports,t[n])&&e.push(t[n]);return e}},function(t,e,n){var s=n(16),a=n(19),c=n(33),n=n(34);e.polling=function(t){var e,n,r=!1,o=!1,i=!1!==t.jsonp;if("undefined"!=typeof location&&(e="https:"===location.protocol,n=(n=location.port)||(e?443:80),r=t.hostname!==location.hostname||n!==t.port,o=t.secure!==e),t.xdomain=r,t.xscheme=o,"open"in new s(t)&&!t.forceJSONP)return new a(t);if(!i)throw new Error("JSONP disabled");return new c(t)},e.websocket=n},function(t,e,n){var o=n(17),i=n(18);t.exports=function(t){var e=t.xdomain,n=t.xscheme,r=t.enablesXDR;try{if("undefined"!=typeof XMLHttpRequest&&(!e||o))return new XMLHttpRequest}catch(t){}try{if("undefined"!=typeof XDomainRequest&&!n&&r)return new XDomainRequest}catch(t){}if(!e)try{return new i[["Active"].concat("Object").join("X")]("Microsoft.XMLHTTP")}catch(t){}}},function(e,t){try{e.exports="undefined"!=typeof XMLHttpRequest&&"withCredentials"in new XMLHttpRequest}catch(t){e.exports=!1}},function(t,e){t.exports="undefined"!=typeof self?self:"undefined"!=typeof window?window:Function("return this")()},function(t,e,n){function r(){}function o(t){var e,n;c.call(this,t),this.requestTimeout=t.requestTimeout,this.extraHeaders=t.extraHeaders,"undefined"!=typeof location&&(e="https:"===location.protocol,n=(n=location.port)||(e?443:80),this.xd="undefined"!=typeof location&&t.hostname!==location.hostname||n!==t.port,this.xs=t.secure!==e)}function i(t){this.method=t.method||"GET",this.uri=t.uri,this.xd=!!t.xd,this.xs=!!t.xs,this.async=!1!==t.async,this.data=void 0!==t.data?t.data:null,this.agent=t.agent,this.isBinary=t.isBinary,this.supportsBinary=t.supportsBinary,this.enablesXDR=t.enablesXDR,this.withCredentials=t.withCredentials,this.requestTimeout=t.requestTimeout,this.pfx=t.pfx,this.key=t.key,this.passphrase=t.passphrase,this.cert=t.cert,this.ca=t.ca,this.ciphers=t.ciphers,this.rejectUnauthorized=t.rejectUnauthorized,this.extraHeaders=t.extraHeaders,this.create()}function s(){for(var t in i.requests)i.requests.hasOwnProperty(t)&&i.requests[t].abort()}var a=n(16),c=n(20),p=n(8),h=n(31),u=n(3)("engine.io-client:polling-xhr"),n=n(18);t.exports=o,t.exports.Request=i,h(o,c),o.prototype.supportsBinary=!0,o.prototype.request=function(t){return(t=t||{}).uri=this.uri(),t.xd=this.xd,t.xs=this.xs,t.agent=this.agent||!1,t.supportsBinary=this.supportsBinary,t.enablesXDR=this.enablesXDR,t.withCredentials=this.withCredentials,t.pfx=this.pfx,t.key=this.key,t.passphrase=this.passphrase,t.cert=this.cert,t.ca=this.ca,t.ciphers=this.ciphers,t.rejectUnauthorized=this.rejectUnauthorized,t.requestTimeout=this.requestTimeout,t.extraHeaders=this.extraHeaders,new i(t)},o.prototype.doWrite=function(t,e){var n="string"!=typeof t&&void 0!==t,n=this.request({method:"POST",data:t,isBinary:n}),r=this;n.on("success",e),n.on("error",function(t){r.onError("xhr post error",t)}),this.sendXhr=n},o.prototype.doPoll=function(){u("xhr poll");var t=this.request(),e=this;t.on("data",function(t){e.onData(t)}),t.on("error",function(t){e.onError("xhr poll error",t)}),this.pollXhr=t},p(i.prototype),i.prototype.create=function(){var t={agent:this.agent,xdomain:this.xd,xscheme:this.xs,enablesXDR:this.enablesXDR};t.pfx=this.pfx,t.key=this.key,t.passphrase=this.passphrase,t.cert=this.cert,t.ca=this.ca,t.ciphers=this.ciphers,t.rejectUnauthorized=this.rejectUnauthorized;var e=this.xhr=new a(t),n=this;try{u("xhr open %s: %s",this.method,this.uri),e.open(this.method,this.uri,this.async);try{if(this.extraHeaders)for(var r in e.setDisableHeaderCheck&&e.setDisableHeaderCheck(!0),this.extraHeaders)this.extraHeaders.hasOwnProperty(r)&&e.setRequestHeader(r,this.extraHeaders[r])}catch(t){}if("POST"===this.method)try{this.isBinary?e.setRequestHeader("Content-type","application/octet-stream"):e.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch(t){}try{e.setRequestHeader("Accept","*/*")}catch(t){}"withCredentials"in e&&(e.withCredentials=this.withCredentials),this.requestTimeout&&(e.timeout=this.requestTimeout),this.hasXDR()?(e.onload=function(){n.onLoad()},e.onerror=function(){n.onError(e.responseText)}):e.onreadystatechange=function(){if(2===e.readyState)try{var t=e.getResponseHeader("Content-Type");(n.supportsBinary&&"application/octet-stream"===t||"application/octet-stream; charset=UTF-8"===t)&&(e.responseType="arraybuffer")}catch(t){}4===e.readyState&&(200===e.status||1223===e.status?n.onLoad():setTimeout(function(){n.onError("number"==typeof e.status?e.status:0)},0))},u("xhr data %s",this.data),e.send(this.data)}catch(t){return void setTimeout(function(){n.onError(t)},0)}"undefined"!=typeof document&&(this.index=i.requestsCount++,i.requests[this.index]=this)},i.prototype.onSuccess=function(){this.emit("success"),this.cleanup()},i.prototype.onData=function(t){this.emit("data",t),this.onSuccess()},i.prototype.onError=function(t){this.emit("error",t),this.cleanup(!0)},i.prototype.cleanup=function(t){if(void 0!==this.xhr&&null!==this.xhr){if(this.hasXDR()?this.xhr.onload=this.xhr.onerror=r:this.xhr.onreadystatechange=r,t)try{this.xhr.abort()}catch(t){}"undefined"!=typeof document&&delete i.requests[this.index],this.xhr=null}},i.prototype.onLoad=function(){var t,e;try{try{e=this.xhr.getResponseHeader("Content-Type")}catch(t){}t=("application/octet-stream"===e||"application/octet-stream; charset=UTF-8"===e)&&this.xhr.response||this.xhr.responseText}catch(t){this.onError(t)}null!=t&&this.onData(t)},i.prototype.hasXDR=function(){return"undefined"!=typeof XDomainRequest&&!this.xs&&this.enablesXDR},i.prototype.abort=function(){this.cleanup()},i.requestsCount=0,i.requests={},"undefined"!=typeof document&&("function"==typeof attachEvent?attachEvent("onunload",s):"function"==typeof addEventListener&&(n="onpagehide"in n?"pagehide":"unload",addEventListener(n,s,!1)))},function(t,e,n){function r(t){var e=t&&t.forceBase64;h&&!e||(this.supportsBinary=!1),o.call(this,t)}var o=n(21),i=n(30),s=n(22),a=n(31),c=n(32),p=n(3)("engine.io-client:polling");t.exports=r;var h=null!=new(n(16))({xdomain:!1}).responseType;a(r,o),r.prototype.name="polling",r.prototype.doOpen=function(){this.poll()},r.prototype.pause=function(t){function e(){p("paused"),r.readyState="paused",t()}var n,r=this;this.readyState="pausing",this.polling||!this.writable?(n=0,this.polling&&(p("we are currently polling - waiting to pause"),n++,this.once("pollComplete",function(){p("pre-pause polling complete"),--n||e()})),this.writable||(p("we are currently writing - waiting to pause"),n++,this.once("drain",function(){p("pre-pause writing complete"),--n||e()}))):e()},r.prototype.poll=function(){p("polling"),this.polling=!0,this.doPoll(),this.emit("poll")},r.prototype.onData=function(t){var r=this;p("polling got data %s",t);s.decodePayload(t,this.socket.binaryType,function(t,e,n){return"opening"===r.readyState&&"open"===t.type&&r.onOpen(),"close"===t.type?(r.onClose(),!1):void r.onPacket(t)}),"closed"!==this.readyState&&(this.polling=!1,this.emit("pollComplete"),"open"===this.readyState?this.poll():p('ignoring poll - transport state "%s"',this.readyState))},r.prototype.doClose=function(){function t(){p("writing close packet"),e.write([{type:"close"}])}var e=this;"open"===this.readyState?(p("transport open - closing"),t()):(p("transport not open - deferring close"),this.once("open",t))},r.prototype.write=function(t){var e=this;this.writable=!1;function n(){e.writable=!0,e.emit("drain")}s.encodePayload(t,this.supportsBinary,function(t){e.doWrite(t,n)})},r.prototype.uri=function(){var t=this.query||{},e=this.secure?"https":"http",n="";return!1!==this.timestampRequests&&(t[this.timestampParam]=c()),this.supportsBinary||t.sid||(t.b64=1),t=i.encode(t),this.port&&("https"==e&&443!==Number(this.port)||"http"==e&&80!==Number(this.port))&&(n=":"+this.port),t.length&&(t="?"+t),e+"://"+(-1!==this.hostname.indexOf(":")?"["+this.hostname+"]":this.hostname)+n+this.path+t}},function(t,e,n){function r(t){this.path=t.path,this.hostname=t.hostname,this.port=t.port,this.secure=t.secure,this.query=t.query,this.timestampParam=t.timestampParam,this.timestampRequests=t.timestampRequests,this.readyState="",this.agent=t.agent||!1,this.socket=t.socket,this.enablesXDR=t.enablesXDR,this.withCredentials=t.withCredentials,this.pfx=t.pfx,this.key=t.key,this.passphrase=t.passphrase,this.cert=t.cert,this.ca=t.ca,this.ciphers=t.ciphers,this.rejectUnauthorized=t.rejectUnauthorized,this.forceNode=t.forceNode,this.isReactNative=t.isReactNative,this.extraHeaders=t.extraHeaders,this.localAddress=t.localAddress}var o=n(22);n(8)((t.exports=r).prototype),r.prototype.onError=function(t,e){t=new Error(t);return t.type="TransportError",t.description=e,this.emit("error",t),this},r.prototype.open=function(){return"closed"!==this.readyState&&""!==this.readyState||(this.readyState="opening",this.doOpen()),this},r.prototype.close=function(){return"opening"!==this.readyState&&"open"!==this.readyState||(this.doClose(),this.onClose()),this},r.prototype.send=function(t){if("open"!==this.readyState)throw new Error("Transport not open");this.write(t)},r.prototype.onOpen=function(){this.readyState="open",this.writable=!0,this.emit("open")},r.prototype.onData=function(t){t=o.decodePacket(t,this.socket.binaryType);this.onPacket(t)},r.prototype.onPacket=function(t){this.emit("packet",t)},r.prototype.onClose=function(){this.readyState="closed",this.emit("close")}},function(t,f,e){function s(t,e,n){if(!e)return f.encodeBase64Packet(t,n);if(u)return function(t,e,n){if(!e)return f.encodeBase64Packet(t,n);var r=new FileReader;return r.onload=function(){f.encodePacket({type:t.type,data:r.result},e,!0,n)},r.readAsArrayBuffer(t.data)}(t,e,n);e=new Uint8Array(1);return e[0]=d[t.type],n(new m([e.buffer,t.data]))}function i(t,e,n){for(var o=new Array(t.length),r=c(t.length,n),i=0;i<t.length;i++)!function(n,t,r){e(t,function(t,e){o[n]=e,r(t,o)})}(i,t[i],r)}var r,n=e(23),a=e(24),l=e(25),c=e(26),p=e(27);"undefined"!=typeof ArrayBuffer&&(r=e(28));var o="undefined"!=typeof navigator&&/Android/i.test(navigator.userAgent),h="undefined"!=typeof navigator&&/PhantomJS/i.test(navigator.userAgent),u=o||h;f.protocol=3;var d=f.packets={open:0,close:1,ping:2,pong:3,message:4,upgrade:5,noop:6},y=n(d),g={type:"error",data:"parser error"},m=e(29);f.encodePacket=function(t,e,n,r){"function"==typeof e&&(r=e,e=!1),"function"==typeof n&&(r=n,n=null);var o=void 0===t.data?void 0:t.data.buffer||t.data;if("undefined"!=typeof ArrayBuffer&&o instanceof ArrayBuffer)return function(t,e,n){if(!e)return f.encodeBase64Packet(t,n);var e=t.data,r=new Uint8Array(e),o=new Uint8Array(1+e.byteLength);o[0]=d[t.type];for(var i=0;i<r.length;i++)o[i+1]=r[i];return n(o.buffer)}(t,e,r);if(void 0!==m&&o instanceof m)return s(t,e,r);if(o&&o.base64)return i=t,r("b"+f.packets[i.type]+i.data.data);var i=d[t.type];return void 0!==t.data&&(i+=n?p.encode(String(t.data),{strict:!1}):String(t.data)),r(""+i)},f.encodeBase64Packet=function(e,n){var r,o="b"+f.packets[e.type];if(void 0!==m&&e.data instanceof m){var i=new FileReader;return i.onload=function(){var t=i.result.split(",")[1];n(o+t)},i.readAsDataURL(e.data)}try{r=String.fromCharCode.apply(null,new Uint8Array(e.data))}catch(t){for(var s=new Uint8Array(e.data),a=new Array(s.length),c=0;c<s.length;c++)a[c]=s[c];r=String.fromCharCode.apply(null,a)}return o+=btoa(r),n(o)},f.decodePacket=function(t,e,n){if(void 0===t)return g;if("string"==typeof t){if("b"===t.charAt(0))return f.decodeBase64Packet(t.substr(1),e);if(n&&!1===(t=function(t){try{t=p.decode(t,{strict:!1})}catch(t){return!1}return t}(t)))return g;var r=t.charAt(0);return Number(r)==r&&y[r]?1<t.length?{type:y[r],data:t.substring(1)}:{type:y[r]}:g}r=new Uint8Array(t)[0],t=l(t,1);return m&&"blob"===e&&(t=new m([t])),{type:y[r],data:t}},f.decodeBase64Packet=function(t,e){var n=y[t.charAt(0)];if(!r)return{type:n,data:{base64:!0,data:t.substr(1)}};t=r.decode(t.substr(1));return"blob"===e&&m&&(t=new m([t])),{type:n,data:t}},f.encodePayload=function(t,n,r){"function"==typeof n&&(r=n,n=null);var o=a(t);return n&&o?m&&!u?f.encodePayloadAsBlob(t,r):f.encodePayloadAsArrayBuffer(t,r):t.length?void i(t,function(t,e){f.encodePacket(t,!!o&&n,!1,function(t){e(null,(t=t).length+":"+t)})},function(t,e){return r(e.join(""))}):r("0:")},f.decodePayload=function(t,e,n){if("string"!=typeof t)return f.decodePayloadAsBinary(t,e,n);if("function"==typeof e&&(n=e,e=null),""===t)return n(g,0,1);for(var r,o,i="",s=0,a=t.length;s<a;s++){var c=t.charAt(s);if(":"===c){if(""===i||i!=(r=Number(i)))return n(g,0,1);if(i!=(o=t.substr(s+1,r)).length)return n(g,0,1);if(o.length){if(o=f.decodePacket(o,e,!1),g.type===o.type&&g.data===o.data)return n(g,0,1);if(!1===n(o,s+r,a))return}s+=r,i=""}else i+=c}return""!==i?n(g,0,1):void 0},f.encodePayloadAsArrayBuffer=function(t,r){return t.length?void i(t,function(t,e){f.encodePacket(t,!0,!0,function(t){return e(null,t)})},function(t,e){var n=e.reduce(function(t,e){e="string"==typeof e?e.length:e.byteLength;return t+e.toString().length+e+2},0),s=new Uint8Array(n),a=0;return e.forEach(function(t){var e="string"==typeof t,n=t;if(e){for(var r=new Uint8Array(t.length),o=0;o<t.length;o++)r[o]=t.charCodeAt(o);n=r.buffer}s[a++]=e?0:1;for(var i=n.byteLength.toString(),o=0;o<i.length;o++)s[a++]=parseInt(i[o]);s[a++]=255;for(r=new Uint8Array(n),o=0;o<r.length;o++)s[a++]=r[o]}),r(s.buffer)}):r(new ArrayBuffer(0))},f.encodePayloadAsBlob=function(t,n){i(t,function(t,s){f.encodePacket(t,!0,!0,function(t){var e=new Uint8Array(1);if(e[0]=1,"string"==typeof t){for(var n=new Uint8Array(t.length),r=0;r<t.length;r++)n[r]=t.charCodeAt(r);t=n.buffer,e[0]=0}for(var o=(t instanceof ArrayBuffer?t.byteLength:t.size).toString(),i=new Uint8Array(o.length+1),r=0;r<o.length;r++)i[r]=parseInt(o[r]);i[o.length]=255,m&&(e=new m([e.buffer,i.buffer,t]),s(null,e))})},function(t,e){return n(new m(e))})},f.decodePayloadAsBinary=function(t,n,r){"function"==typeof n&&(r=n,n=null);for(var e=t,o=[];0<e.byteLength;){for(var i=new Uint8Array(e),s=0===i[0],a="",c=1;255!==i[c];c++){if(310<a.length)return r(g,0,1);a+=i[c]}e=l(e,2+a.length),a=parseInt(a);var p=l(e,0,a);if(s)try{p=String.fromCharCode.apply(null,new Uint8Array(p))}catch(t){for(var h=new Uint8Array(p),p="",c=0;c<h.length;c++)p+=String.fromCharCode(h[c])}o.push(p),e=l(e,a)}var u=o.length;o.forEach(function(t,e){r(f.decodePacket(t,n,!0),e,u)})}},function(t,e){t.exports=Object.keys||function(t){var e,n=[],r=Object.prototype.hasOwnProperty;for(e in t)r.call(t,e)&&n.push(e);return n}},function(t,e,n){var i=n(10),n=Object.prototype.toString,s="function"==typeof Blob||"undefined"!=typeof Blob&&"[object BlobConstructor]"===n.call(Blob),a="function"==typeof File||"undefined"!=typeof File&&"[object FileConstructor]"===n.call(File);t.exports=function t(e){if(!e||"object"!=typeof e)return!1;if(i(e)){for(var n=0,r=e.length;n<r;n++)if(t(e[n]))return!0;return!1}if("function"==typeof Buffer&&Buffer.isBuffer&&Buffer.isBuffer(e)||"function"==typeof ArrayBuffer&&e instanceof ArrayBuffer||s&&e instanceof Blob||a&&e instanceof File)return!0;if(e.toJSON&&"function"==typeof e.toJSON&&1===arguments.length)return t(e.toJSON(),!0);for(var o in e)if(Object.prototype.hasOwnProperty.call(e,o)&&t(e[o]))return!0;return!1}},function(t,e){t.exports=function(t,e,n){var r=t.byteLength;if(e=e||0,n=n||r,t.slice)return t.slice(e,n);if(e<0&&(e+=r),n<0&&(n+=r),r<n&&(n=r),r<=e||n<=e||0===r)return new ArrayBuffer(0);for(var o=new Uint8Array(t),i=new Uint8Array(n-e),s=e,a=0;s<n;s++,a++)i[a]=o[s];return i.buffer}},function(t,e){function s(){}t.exports=function(t,n,r){function o(t,e){if(o.count<=0)throw new Error("after called too many times");--o.count,t?(i=!0,n(t),n=r):0!==o.count||i||n(null,e)}var i=!1;return r=r||s,0===(o.count=t)?n():o}},function(t,e){function a(t){for(var e,n,r=[],o=0,i=t.length;o<i;)55296<=(e=t.charCodeAt(o++))&&e<=56319&&o<i?56320==(64512&(n=t.charCodeAt(o++)))?r.push(((1023&e)<<10)+(1023&n)+65536):(r.push(e),o--):r.push(e);return r}function c(t,e){if(!(55296<=t&&t<=57343))return 1;if(e)throw Error("Lone surrogate U+"+t.toString(16).toUpperCase()+" is not a scalar value")}function p(t,e){return f(t>>e&63|128)}function i(){if(h<=u)throw Error("Invalid byte index");var t=255&s[u];if(u++,128==(192&t))return 63&t;throw Error("Invalid continuation byte")}var s,h,u,f=String.fromCharCode;t.exports={version:"2.1.2",encode:function(t,e){for(var n=!1!==(e=e||{}).strict,r=a(t),o=r.length,i=-1,s="";++i<o;)s+=function(t,e){if(0==(4294967168&t))return f(t);var n="";return 0==(4294965248&t)?n=f(t>>6&31|192):0==(4294901760&t)?(c(t,e)||(t=65533),n=f(t>>12&15|224),n+=p(t,6)):0==(4292870144&t)&&(n=f(t>>18&7|240),n+=p(t,12),n+=p(t,6)),n+f(63&t|128)}(r[i],n);return s},decode:function(t,e){var n=!1!==(e=e||{}).strict;s=a(t),h=s.length,u=0;for(var r,o=[];!1!==(r=function(t){var e,n;if(h<u)throw Error("Invalid byte index");if(u==h)return!1;if(e=255&s[u],u++,0==(128&e))return e;if(192==(224&e)){if(128<=(n=(31&e)<<6|i()))return n;throw Error("Invalid continuation byte")}if(224==(240&e)){if(2048<=(n=(15&e)<<12|i()<<6|i()))return c(n,t)?n:65533;throw Error("Invalid continuation byte")}if(240==(248&e)&&(65536<=(n=(7&e)<<18|i()<<12|i()<<6|i())&&n<=1114111))return n;throw Error("Invalid UTF-8 detected")}(n));)o.push(r);return function(t){for(var e,n=t.length,r=-1,o="";++r<n;)65535<(e=t[r])&&(o+=f((e-=65536)>>>10&1023|55296),e=56320|1023&e),o+=f(e);return o}(o)}}},function(t,e){!function(h){"use strict";e.encode=function(t){for(var e=new Uint8Array(t),n=e.length,r="",o=0;o<n;o+=3)r+=h[e[o]>>2],r+=h[(3&e[o])<<4|e[o+1]>>4],r+=h[(15&e[o+1])<<2|e[o+2]>>6],r+=h[63&e[o+2]];return n%3==2?r=r.substring(0,r.length-1)+"=":n%3==1&&(r=r.substring(0,r.length-2)+"=="),r},e.decode=function(t){var e,n,r,o,i=.75*t.length,s=t.length,a=0;"="===t[t.length-1]&&(i--,"="===t[t.length-2]&&i--);for(var i=new ArrayBuffer(i),c=new Uint8Array(i),p=0;p<s;p+=4)e=h.indexOf(t[p]),n=h.indexOf(t[p+1]),r=h.indexOf(t[p+2]),o=h.indexOf(t[p+3]),c[a++]=e<<2|n>>4,c[a++]=(15&n)<<4|r>>2,c[a++]=(3&r)<<6|63&o;return i}}("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/")},function(t,e){function r(t){return t.map(function(t){if(t.buffer instanceof ArrayBuffer){var e,n=t.buffer;return t.byteLength!==n.byteLength&&((e=new Uint8Array(t.byteLength)).set(new Uint8Array(n,t.byteOffset,t.byteLength)),n=e.buffer),n}return t})}function n(t,e){e=e||{};var n=new i;return r(t).forEach(function(t){n.append(t)}),e.type?n.getBlob(e.type):n.getBlob()}function o(t,e){return new Blob(r(t),e||{})}var i=void 0!==i?i:"undefined"!=typeof WebKitBlobBuilder?WebKitBlobBuilder:"undefined"!=typeof MSBlobBuilder?MSBlobBuilder:"undefined"!=typeof MozBlobBuilder&&MozBlobBuilder,s=function(){try{return 2===new Blob(["hi"]).size}catch(t){return!1}}(),a=s&&function(){try{return 2===new Blob([new Uint8Array([1,2])]).size}catch(t){return!1}}(),c=i&&i.prototype.append&&i.prototype.getBlob;"undefined"!=typeof Blob&&(n.prototype=Blob.prototype,o.prototype=Blob.prototype),t.exports=s?a?Blob:o:c?n:void 0},function(t,e){e.encode=function(t){var e,n="";for(e in t)t.hasOwnProperty(e)&&(n.length&&(n+="&"),n+=encodeURIComponent(e)+"="+encodeURIComponent(t[e]));return n},e.decode=function(t){for(var e={},n=t.split("&"),r=0,o=n.length;r<o;r++){var i=n[r].split("=");e[decodeURIComponent(i[0])]=decodeURIComponent(i[1])}return e}},function(t,e){t.exports=function(t,e){function n(){}n.prototype=e.prototype,t.prototype=new n,t.prototype.constructor=t}},function(t,e){"use strict";function n(t){for(var e="";e=i[t%s]+e,t=Math.floor(t/s),0<t;);return e}function r(){var t=n(+new Date);return t!==o?(c=0,o=t):t+"."+n(c++)}for(var o,i="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_".split(""),s=64,a={},c=0,p=0;p<s;p++)a[i[p]]=p;r.encode=n,r.decode=function(t){var e=0;for(p=0;p<t.length;p++)e=e*s+a[t.charAt(p)];return e},t.exports=r},function(t,e,n){function r(){}function o(t){i.call(this,t),this.query=this.query||{},c=c||(a.___eio=a.___eio||[]),this.index=c.length;var e=this;c.push(function(t){e.onData(t)}),this.query.j=this.index,"function"==typeof addEventListener&&addEventListener("beforeunload",function(){e.script&&(e.script.onerror=r)},!1)}var i=n(20),s=n(31),a=n(18);t.exports=o;var c,p=/\n/g,h=/\\n/g;s(o,i),o.prototype.supportsBinary=!1,o.prototype.doClose=function(){this.script&&(this.script.parentNode.removeChild(this.script),this.script=null),this.form&&(this.form.parentNode.removeChild(this.form),this.form=null,this.iframe=null),i.prototype.doClose.call(this)},o.prototype.doPoll=function(){var e=this,t=document.createElement("script");this.script&&(this.script.parentNode.removeChild(this.script),this.script=null),t.async=!0,t.src=this.uri(),t.onerror=function(t){e.onError("jsonp poll error",t)};var n=document.getElementsByTagName("script")[0];n?n.parentNode.insertBefore(t,n):(document.head||document.body).appendChild(t),this.script=t,"undefined"!=typeof navigator&&/gecko/i.test(navigator.userAgent)&&setTimeout(function(){var t=document.createElement("iframe");document.body.appendChild(t),document.body.removeChild(t)},100)},o.prototype.doWrite=function(t,e){function n(){r(),e()}function r(){if(c.iframe)try{c.form.removeChild(c.iframe)}catch(t){c.onError("jsonp polling iframe removal error",t)}try{var t='<iframe src="javascript:0" name="'+c.iframeId+'">';o=document.createElement(t)}catch(t){(o=document.createElement("iframe")).name=c.iframeId,o.src="javascript:0"}o.id=c.iframeId,c.form.appendChild(o),c.iframe=o}var o,i,s,a,c=this;this.form||(i=document.createElement("form"),s=document.createElement("textarea"),a=this.iframeId="eio_iframe_"+this.index,i.className="socketio",i.style.position="absolute",i.style.top="-1000px",i.style.left="-1000px",i.target=a,i.method="POST",i.setAttribute("accept-charset","utf-8"),s.name="d",i.appendChild(s),document.body.appendChild(i),this.form=i,this.area=s),this.form.action=this.uri(),r(),t=t.replace(h,"\\\n"),this.area.value=t.replace(p,"\\n");try{this.form.submit()}catch(t){}this.iframe.attachEvent?this.iframe.onreadystatechange=function(){"complete"===c.iframe.readyState&&n()}:this.iframe.onload=n}},function(t,e,n){function r(t){t&&t.forceBase64&&(this.supportsBinary=!1),this.perMessageDeflate=t.perMessageDeflate,this.usingBrowserWebSocket=o&&!t.forceNode,this.protocols=t.protocols,this.usingBrowserWebSocket||(f=i),s.call(this,t)}var o,i,s=n(21),a=n(22),c=n(30),p=n(31),h=n(32),u=n(3)("engine.io-client:websocket");if("undefined"!=typeof WebSocket?o=WebSocket:"undefined"!=typeof self&&(o=self.WebSocket||self.MozWebSocket),"undefined"==typeof window)try{i=n(35)}catch(t){}var f=o||i;p(t.exports=r,s),r.prototype.name="websocket",r.prototype.supportsBinary=!0,r.prototype.doOpen=function(){if(this.check()){var t=this.uri(),e=this.protocols,n={};this.isReactNative||(n.agent=this.agent,n.perMessageDeflate=this.perMessageDeflate,n.pfx=this.pfx,n.key=this.key,n.passphrase=this.passphrase,n.cert=this.cert,n.ca=this.ca,n.ciphers=this.ciphers,n.rejectUnauthorized=this.rejectUnauthorized),this.extraHeaders&&(n.headers=this.extraHeaders),this.localAddress&&(n.localAddress=this.localAddress);try{this.ws=this.usingBrowserWebSocket&&!this.isReactNative?e?new f(t,e):new f(t):new f(t,e,n)}catch(t){return this.emit("error",t)}void 0===this.ws.binaryType&&(this.supportsBinary=!1),this.ws.supports&&this.ws.supports.binary?(this.supportsBinary=!0,this.ws.binaryType="nodebuffer"):this.ws.binaryType="arraybuffer",this.addEventListeners()}},r.prototype.addEventListeners=function(){var e=this;this.ws.onopen=function(){e.onOpen()},this.ws.onclose=function(){e.onClose()},this.ws.onmessage=function(t){e.onData(t.data)},this.ws.onerror=function(t){e.onError("websocket error",t)}},r.prototype.write=function(t){var r=this;this.writable=!1;for(var o=t.length,e=0,n=o;e<n;e++)!function(n){a.encodePacket(n,r.supportsBinary,function(t){var e;r.usingBrowserWebSocket||(e={},n.options&&(e.compress=n.options.compress),!r.perMessageDeflate||("string"==typeof t?Buffer.byteLength(t):t.length)<r.perMessageDeflate.threshold&&(e.compress=!1));try{r.usingBrowserWebSocket?r.ws.send(t):r.ws.send(t,e)}catch(t){u("websocket closed before onclose event")}--o||(r.emit("flush"),setTimeout(function(){r.writable=!0,r.emit("drain")},0))})}(t[e])},r.prototype.onClose=function(){s.prototype.onClose.call(this)},r.prototype.doClose=function(){void 0!==this.ws&&this.ws.close()},r.prototype.uri=function(){var t=this.query||{},e=this.secure?"wss":"ws",n="";return this.port&&("wss"==e&&443!==Number(this.port)||"ws"==e&&80!==Number(this.port))&&(n=":"+this.port),this.timestampRequests&&(t[this.timestampParam]=h()),this.supportsBinary||(t.b64=1),(t=c.encode(t)).length&&(t="?"+t),e+"://"+(-1!==this.hostname.indexOf(":")?"["+this.hostname+"]":this.hostname)+n+this.path+t},r.prototype.check=function(){return!(!f||"__initialize"in f&&this.name===r.prototype.name)}},function(t,e){},function(t,e){var r=[].indexOf;t.exports=function(t,e){if(r)return t.indexOf(e);for(var n=0;n<t.length;++n)if(t[n]===e)return n;return-1}},function(t,e,n){function r(t,e,n){this.io=t,this.nsp=e,(this.json=this).ids=0,this.acks={},this.receiveBuffer=[],this.sendBuffer=[],this.connected=!1,this.disconnected=!0,this.flags={},n&&n.query&&(this.query=n.query),this.io.autoConnect&&this.open()}var o=n(7),i=n(8),s=n(38),a=n(39),c=n(40),p=n(3)("socket.io-client:socket"),h=n(30),u=n(24);t.exports=r;var f={connect:1,connect_error:1,connect_timeout:1,connecting:1,disconnect:1,error:1,reconnect:1,reconnect_attempt:1,reconnect_failed:1,reconnect_error:1,reconnecting:1,ping:1,pong:1},l=i.prototype.emit;i(r.prototype),r.prototype.subEvents=function(){var t;this.subs||(t=this.io,this.subs=[a(t,"open",c(this,"onopen")),a(t,"packet",c(this,"onpacket")),a(t,"close",c(this,"onclose"))])},r.prototype.open=r.prototype.connect=function(){return this.connected||(this.subEvents(),this.io.reconnecting||this.io.open(),"open"===this.io.readyState&&this.onopen(),this.emit("connecting")),this},r.prototype.send=function(){var t=s(arguments);return t.unshift("message"),this.emit.apply(this,t),this},r.prototype.emit=function(t){if(f.hasOwnProperty(t))return l.apply(this,arguments),this;var e=s(arguments),n={type:(void 0!==this.flags.binary?this.flags.binary:u(e))?o.BINARY_EVENT:o.EVENT,data:e,options:{}};return n.options.compress=!this.flags||!1!==this.flags.compress,"function"==typeof e[e.length-1]&&(p("emitting packet with ack id %d",this.ids),this.acks[this.ids]=e.pop(),n.id=this.ids++),this.connected?this.packet(n):this.sendBuffer.push(n),this.flags={},this},r.prototype.packet=function(t){t.nsp=this.nsp,this.io.packet(t)},r.prototype.onopen=function(){var t;p("transport is open - connecting"),"/"!==this.nsp&&(this.query?(t="object"==typeof this.query?h.encode(this.query):this.query,p("sending connect packet with query %s",t),this.packet({type:o.CONNECT,query:t})):this.packet({type:o.CONNECT}))},r.prototype.onclose=function(t){p("close (%s)",t),this.connected=!1,this.disconnected=!0,delete this.id,this.emit("disconnect",t)},r.prototype.onpacket=function(t){var e=t.nsp===this.nsp,n=t.type===o.ERROR&&"/"===t.nsp;if(e||n)switch(t.type){case o.CONNECT:this.onconnect();break;case o.EVENT:case o.BINARY_EVENT:this.onevent(t);break;case o.ACK:case o.BINARY_ACK:this.onack(t);break;case o.DISCONNECT:this.ondisconnect();break;case o.ERROR:this.emit("error",t.data)}},r.prototype.onevent=function(t){var e=t.data||[];p("emitting event %j",e),null!=t.id&&(p("attaching ack callback to event"),e.push(this.ack(t.id))),this.connected?l.apply(this,e):this.receiveBuffer.push(e)},r.prototype.ack=function(e){var n=this,r=!1;return function(){var t;r||(r=!0,t=s(arguments),p("sending ack %j",t),n.packet({type:u(t)?o.BINARY_ACK:o.ACK,id:e,data:t}))}},r.prototype.onack=function(t){var e=this.acks[t.id];"function"==typeof e?(p("calling ack %s with %j",t.id,t.data),e.apply(this,t.data),delete this.acks[t.id]):p("bad ack %s",t.id)},r.prototype.onconnect=function(){this.connected=!0,this.disconnected=!1,this.emit("connect"),this.emitBuffered()},r.prototype.emitBuffered=function(){for(var t=0;t<this.receiveBuffer.length;t++)l.apply(this,this.receiveBuffer[t]);for(this.receiveBuffer=[],t=0;t<this.sendBuffer.length;t++)this.packet(this.sendBuffer[t]);this.sendBuffer=[]},r.prototype.ondisconnect=function(){p("server disconnect (%s)",this.nsp),this.destroy(),this.onclose("io server disconnect")},r.prototype.destroy=function(){if(this.subs){for(var t=0;t<this.subs.length;t++)this.subs[t].destroy();this.subs=null}this.io.destroy(this)},r.prototype.close=r.prototype.disconnect=function(){return this.connected&&(p("performing disconnect (%s)",this.nsp),this.packet({type:o.DISCONNECT})),this.destroy(),this.connected&&this.onclose("io client disconnect"),this},r.prototype.compress=function(t){return this.flags.compress=t,this},r.prototype.binary=function(t){return this.flags.binary=t,this}},function(t,e){t.exports=function(t,e){for(var n=[],r=(e=e||0)||0;r<t.length;r++)n[r-e]=t[r];return n}},function(t,e){t.exports=function(t,e,n){return t.on(e,n),{destroy:function(){t.removeListener(e,n)}}}},function(t,e){var r=[].slice;t.exports=function(t,e){if("string"==typeof e&&(e=t[e]),"function"!=typeof e)throw new Error("bind() requires a function");var n=r.call(arguments,2);return function(){return e.apply(t,n.concat(r.call(arguments)))}}},function(t,e){function n(t){t=t||{},this.ms=t.min||100,this.max=t.max||1e4,this.factor=t.factor||2,this.jitter=0<t.jitter&&t.jitter<=1?t.jitter:0,this.attempts=0}(t.exports=n).prototype.duration=function(){var t,e,n=this.ms*Math.pow(this.factor,this.attempts++);return this.jitter&&(t=Math.random(),e=Math.floor(t*this.jitter*n),n=0==(1&Math.floor(10*t))?n-e:n+e),0|Math.min(n,this.max)},n.prototype.reset=function(){this.attempts=0},n.prototype.setMin=function(t){this.ms=t},n.prototype.setMax=function(t){this.max=t},n.prototype.setJitter=function(t){this.jitter=t}}],n.c=o,n.p="",n(0);function n(t){if(o[t])return o[t].exports;var e=o[t]={exports:{},id:t,loaded:!1};return r[t].call(e.exports,e,e.exports,n),e.loaded=!0,e.exports}var r,o});
// crypto-js.min.js
!function(t,e){"object"==typeof exports?module.exports=exports=e():"function"==typeof define&&define.amd?define([],e):t.CryptoJS=e()}(this,function(){var n,o,s,a,h,t,e,l,r,i,c,f,d,u,p,S,x,b,A,H,z,_,v,g,y,B,w,k,m,C,D,E,R,M,F,P,W,O,I,U=U||function(h){var i;if("undefined"!=typeof window&&window.crypto&&(i=window.crypto),"undefined"!=typeof self&&self.crypto&&(i=self.crypto),!(i=!(i=!(i="undefined"!=typeof globalThis&&globalThis.crypto?globalThis.crypto:i)&&"undefined"!=typeof window&&window.msCrypto?window.msCrypto:i)&&"undefined"!=typeof global&&global.crypto?global.crypto:i)&&"function"==typeof require)try{i=require("crypto")}catch(t){}var r=Object.create||function(t){return e.prototype=t,t=new e,e.prototype=null,t};function e(){}var t={},n=t.lib={},o=n.Base={extend:function(t){var e=r(this);return t&&e.mixIn(t),e.hasOwnProperty("init")&&this.init!==e.init||(e.init=function(){e.$super.init.apply(this,arguments)}),(e.init.prototype=e).$super=this,e},create:function(){var t=this.extend();return t.init.apply(t,arguments),t},init:function(){},mixIn:function(t){for(var e in t)t.hasOwnProperty(e)&&(this[e]=t[e]);t.hasOwnProperty("toString")&&(this.toString=t.toString)},clone:function(){return this.init.prototype.extend(this)}},l=n.WordArray=o.extend({init:function(t,e){t=this.words=t||[],this.sigBytes=null!=e?e:4*t.length},toString:function(t){return(t||c).stringify(this)},concat:function(t){var e=this.words,r=t.words,i=this.sigBytes,n=t.sigBytes;if(this.clamp(),i%4)for(var o=0;o<n;o++){var s=r[o>>>2]>>>24-o%4*8&255;e[i+o>>>2]|=s<<24-(i+o)%4*8}else for(var c=0;c<n;c+=4)e[i+c>>>2]=r[c>>>2];return this.sigBytes+=n,this},clamp:function(){var t=this.words,e=this.sigBytes;t[e>>>2]&=4294967295<<32-e%4*8,t.length=h.ceil(e/4)},clone:function(){var t=o.clone.call(this);return t.words=this.words.slice(0),t},random:function(t){for(var e=[],r=0;r<t;r+=4)e.push(function(){if(i){if("function"==typeof i.getRandomValues)try{return i.getRandomValues(new Uint32Array(1))[0]}catch(t){}if("function"==typeof i.randomBytes)try{return i.randomBytes(4).readInt32LE()}catch(t){}}throw new Error("Native crypto module could not be used to get secure random number.")}());return new l.init(e,t)}}),s=t.enc={},c=s.Hex={stringify:function(t){for(var e=t.words,r=t.sigBytes,i=[],n=0;n<r;n++){var o=e[n>>>2]>>>24-n%4*8&255;i.push((o>>>4).toString(16)),i.push((15&o).toString(16))}return i.join("")},parse:function(t){for(var e=t.length,r=[],i=0;i<e;i+=2)r[i>>>3]|=parseInt(t.substr(i,2),16)<<24-i%8*4;return new l.init(r,e/2)}},a=s.Latin1={stringify:function(t){for(var e=t.words,r=t.sigBytes,i=[],n=0;n<r;n++){var o=e[n>>>2]>>>24-n%4*8&255;i.push(String.fromCharCode(o))}return i.join("")},parse:function(t){for(var e=t.length,r=[],i=0;i<e;i++)r[i>>>2]|=(255&t.charCodeAt(i))<<24-i%4*8;return new l.init(r,e)}},f=s.Utf8={stringify:function(t){try{return decodeURIComponent(escape(a.stringify(t)))}catch(t){throw new Error("Malformed UTF-8 data")}},parse:function(t){return a.parse(unescape(encodeURIComponent(t)))}},d=n.BufferedBlockAlgorithm=o.extend({reset:function(){this._data=new l.init,this._nDataBytes=0},_append:function(t){"string"==typeof t&&(t=f.parse(t)),this._data.concat(t),this._nDataBytes+=t.sigBytes},_process:function(t){var e,r=this._data,i=r.words,n=r.sigBytes,o=this.blockSize,s=n/(4*o),c=(s=t?h.ceil(s):h.max((0|s)-this._minBufferSize,0))*o,n=h.min(4*c,n);if(c){for(var a=0;a<c;a+=o)this._doProcessBlock(i,a);e=i.splice(0,c),r.sigBytes-=n}return new l.init(e,n)},clone:function(){var t=o.clone.call(this);return t._data=this._data.clone(),t},_minBufferSize:0}),u=(n.Hasher=d.extend({cfg:o.extend(),init:function(t){this.cfg=this.cfg.extend(t),this.reset()},reset:function(){d.reset.call(this),this._doReset()},update:function(t){return this._append(t),this._process(),this},finalize:function(t){return t&&this._append(t),this._doFinalize()},blockSize:16,_createHelper:function(r){return function(t,e){return new r.init(e).finalize(t)}},_createHmacHelper:function(r){return function(t,e){return new u.HMAC.init(r,e).finalize(t)}}}),t.algo={});return t}(Math);function K(t,e,r){return t&e|~t&r}function X(t,e,r){return t&r|e&~r}function L(t,e){return t<<e|t>>>32-e}function j(t,e,r,i){var n,o=this._iv;o?(n=o.slice(0),this._iv=void 0):n=this._prevBlock,i.encryptBlock(n,0);for(var s=0;s<r;s++)t[e+s]^=n[s]}function T(t){var e,r,i;return 255==(t>>24&255)?(r=t>>8&255,i=255&t,255===(e=t>>16&255)?(e=0,255===r?(r=0,255===i?i=0:++i):++r):++e,t=0,t+=e<<16,t+=r<<8,t+=i):t+=1<<24,t}function N(){for(var t=this._X,e=this._C,r=0;r<8;r++)E[r]=e[r];e[0]=e[0]+1295307597+this._b|0,e[1]=e[1]+3545052371+(e[0]>>>0<E[0]>>>0?1:0)|0,e[2]=e[2]+886263092+(e[1]>>>0<E[1]>>>0?1:0)|0,e[3]=e[3]+1295307597+(e[2]>>>0<E[2]>>>0?1:0)|0,e[4]=e[4]+3545052371+(e[3]>>>0<E[3]>>>0?1:0)|0,e[5]=e[5]+886263092+(e[4]>>>0<E[4]>>>0?1:0)|0,e[6]=e[6]+1295307597+(e[5]>>>0<E[5]>>>0?1:0)|0,e[7]=e[7]+3545052371+(e[6]>>>0<E[6]>>>0?1:0)|0,this._b=e[7]>>>0<E[7]>>>0?1:0;for(r=0;r<8;r++){var i=t[r]+e[r],n=65535&i,o=i>>>16;R[r]=((n*n>>>17)+n*o>>>15)+o*o^((4294901760&i)*i|0)+((65535&i)*i|0)}t[0]=R[0]+(R[7]<<16|R[7]>>>16)+(R[6]<<16|R[6]>>>16)|0,t[1]=R[1]+(R[0]<<8|R[0]>>>24)+R[7]|0,t[2]=R[2]+(R[1]<<16|R[1]>>>16)+(R[0]<<16|R[0]>>>16)|0,t[3]=R[3]+(R[2]<<8|R[2]>>>24)+R[1]|0,t[4]=R[4]+(R[3]<<16|R[3]>>>16)+(R[2]<<16|R[2]>>>16)|0,t[5]=R[5]+(R[4]<<8|R[4]>>>24)+R[3]|0,t[6]=R[6]+(R[5]<<16|R[5]>>>16)+(R[4]<<16|R[4]>>>16)|0,t[7]=R[7]+(R[6]<<8|R[6]>>>24)+R[5]|0}function q(){for(var t=this._X,e=this._C,r=0;r<8;r++)O[r]=e[r];e[0]=e[0]+1295307597+this._b|0,e[1]=e[1]+3545052371+(e[0]>>>0<O[0]>>>0?1:0)|0,e[2]=e[2]+886263092+(e[1]>>>0<O[1]>>>0?1:0)|0,e[3]=e[3]+1295307597+(e[2]>>>0<O[2]>>>0?1:0)|0,e[4]=e[4]+3545052371+(e[3]>>>0<O[3]>>>0?1:0)|0,e[5]=e[5]+886263092+(e[4]>>>0<O[4]>>>0?1:0)|0,e[6]=e[6]+1295307597+(e[5]>>>0<O[5]>>>0?1:0)|0,e[7]=e[7]+3545052371+(e[6]>>>0<O[6]>>>0?1:0)|0,this._b=e[7]>>>0<O[7]>>>0?1:0;for(r=0;r<8;r++){var i=t[r]+e[r],n=65535&i,o=i>>>16;I[r]=((n*n>>>17)+n*o>>>15)+o*o^((4294901760&i)*i|0)+((65535&i)*i|0)}t[0]=I[0]+(I[7]<<16|I[7]>>>16)+(I[6]<<16|I[6]>>>16)|0,t[1]=I[1]+(I[0]<<8|I[0]>>>24)+I[7]|0,t[2]=I[2]+(I[1]<<16|I[1]>>>16)+(I[0]<<16|I[0]>>>16)|0,t[3]=I[3]+(I[2]<<8|I[2]>>>24)+I[1]|0,t[4]=I[4]+(I[3]<<16|I[3]>>>16)+(I[2]<<16|I[2]>>>16)|0,t[5]=I[5]+(I[4]<<8|I[4]>>>24)+I[3]|0,t[6]=I[6]+(I[5]<<16|I[5]>>>16)+(I[4]<<16|I[4]>>>16)|0,t[7]=I[7]+(I[6]<<8|I[6]>>>24)+I[5]|0}return F=(M=U).lib,n=F.Base,o=F.WordArray,(M=M.x64={}).Word=n.extend({init:function(t,e){this.high=t,this.low=e}}),M.WordArray=n.extend({init:function(t,e){t=this.words=t||[],this.sigBytes=null!=e?e:8*t.length},toX32:function(){for(var t=this.words,e=t.length,r=[],i=0;i<e;i++){var n=t[i];r.push(n.high),r.push(n.low)}return o.create(r,this.sigBytes)},clone:function(){for(var t=n.clone.call(this),e=t.words=this.words.slice(0),r=e.length,i=0;i<r;i++)e[i]=e[i].clone();return t}}),"function"==typeof ArrayBuffer&&(P=U.lib.WordArray,s=P.init,(P.init=function(t){if((t=(t=t instanceof ArrayBuffer?new Uint8Array(t):t)instanceof Int8Array||"undefined"!=typeof Uint8ClampedArray&&t instanceof Uint8ClampedArray||t instanceof Int16Array||t instanceof Uint16Array||t instanceof Int32Array||t instanceof Uint32Array||t instanceof Float32Array||t instanceof Float64Array?new Uint8Array(t.buffer,t.byteOffset,t.byteLength):t)instanceof Uint8Array){for(var e=t.byteLength,r=[],i=0;i<e;i++)r[i>>>2]|=t[i]<<24-i%4*8;s.call(this,r,e)}else s.apply(this,arguments)}).prototype=P),function(){var t=U,n=t.lib.WordArray,t=t.enc;t.Utf16=t.Utf16BE={stringify:function(t){for(var e=t.words,r=t.sigBytes,i=[],n=0;n<r;n+=2){var o=e[n>>>2]>>>16-n%4*8&65535;i.push(String.fromCharCode(o))}return i.join("")},parse:function(t){for(var e=t.length,r=[],i=0;i<e;i++)r[i>>>1]|=t.charCodeAt(i)<<16-i%2*16;return n.create(r,2*e)}};function s(t){return t<<8&4278255360|t>>>8&16711935}t.Utf16LE={stringify:function(t){for(var e=t.words,r=t.sigBytes,i=[],n=0;n<r;n+=2){var o=s(e[n>>>2]>>>16-n%4*8&65535);i.push(String.fromCharCode(o))}return i.join("")},parse:function(t){for(var e=t.length,r=[],i=0;i<e;i++)r[i>>>1]|=s(t.charCodeAt(i)<<16-i%2*16);return n.create(r,2*e)}}}(),a=(w=U).lib.WordArray,w.enc.Base64={stringify:function(t){var e=t.words,r=t.sigBytes,i=this._map;t.clamp();for(var n=[],o=0;o<r;o+=3)for(var s=(e[o>>>2]>>>24-o%4*8&255)<<16|(e[o+1>>>2]>>>24-(o+1)%4*8&255)<<8|e[o+2>>>2]>>>24-(o+2)%4*8&255,c=0;c<4&&o+.75*c<r;c++)n.push(i.charAt(s>>>6*(3-c)&63));var a=i.charAt(64);if(a)for(;n.length%4;)n.push(a);return n.join("")},parse:function(t){var e=t.length,r=this._map;if(!(i=this._reverseMap))for(var i=this._reverseMap=[],n=0;n<r.length;n++)i[r.charCodeAt(n)]=n;var o=r.charAt(64);return!o||-1!==(o=t.indexOf(o))&&(e=o),function(t,e,r){for(var i=[],n=0,o=0;o<e;o++){var s,c;o%4&&(s=r[t.charCodeAt(o-1)]<<o%4*2,c=r[t.charCodeAt(o)]>>>6-o%4*2,c=s|c,i[n>>>2]|=c<<24-n%4*8,n++)}return a.create(i,n)}(t,e,i)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="},h=(F=U).lib.WordArray,F.enc.Base64url={stringify:function(t,e=!0){var r=t.words,i=t.sigBytes,n=e?this._safe_map:this._map;t.clamp();for(var o=[],s=0;s<i;s+=3)for(var c=(r[s>>>2]>>>24-s%4*8&255)<<16|(r[s+1>>>2]>>>24-(s+1)%4*8&255)<<8|r[s+2>>>2]>>>24-(s+2)%4*8&255,a=0;a<4&&s+.75*a<i;a++)o.push(n.charAt(c>>>6*(3-a)&63));var h=n.charAt(64);if(h)for(;o.length%4;)o.push(h);return o.join("")},parse:function(t,e=!0){var r=t.length,i=e?this._safe_map:this._map;if(!(n=this._reverseMap))for(var n=this._reverseMap=[],o=0;o<i.length;o++)n[i.charCodeAt(o)]=o;e=i.charAt(64);return!e||-1!==(e=t.indexOf(e))&&(r=e),function(t,e,r){for(var i=[],n=0,o=0;o<e;o++){var s,c;o%4&&(s=r[t.charCodeAt(o-1)]<<o%4*2,c=r[t.charCodeAt(o)]>>>6-o%4*2,c=s|c,i[n>>>2]|=c<<24-n%4*8,n++)}return h.create(i,n)}(t,r,n)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",_safe_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_"},function(a){var t=U,e=t.lib,r=e.WordArray,i=e.Hasher,e=t.algo,A=[];!function(){for(var t=0;t<64;t++)A[t]=4294967296*a.abs(a.sin(t+1))|0}();e=e.MD5=i.extend({_doReset:function(){this._hash=new r.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(t,e){for(var r=0;r<16;r++){var i=e+r,n=t[i];t[i]=16711935&(n<<8|n>>>24)|4278255360&(n<<24|n>>>8)}var o=this._hash.words,s=t[e+0],c=t[e+1],a=t[e+2],h=t[e+3],l=t[e+4],f=t[e+5],d=t[e+6],u=t[e+7],p=t[e+8],_=t[e+9],y=t[e+10],v=t[e+11],g=t[e+12],B=t[e+13],w=t[e+14],k=t[e+15],m=H(m=o[0],b=o[1],x=o[2],S=o[3],s,7,A[0]),S=H(S,m,b,x,c,12,A[1]),x=H(x,S,m,b,a,17,A[2]),b=H(b,x,S,m,h,22,A[3]);m=H(m,b,x,S,l,7,A[4]),S=H(S,m,b,x,f,12,A[5]),x=H(x,S,m,b,d,17,A[6]),b=H(b,x,S,m,u,22,A[7]),m=H(m,b,x,S,p,7,A[8]),S=H(S,m,b,x,_,12,A[9]),x=H(x,S,m,b,y,17,A[10]),b=H(b,x,S,m,v,22,A[11]),m=H(m,b,x,S,g,7,A[12]),S=H(S,m,b,x,B,12,A[13]),x=H(x,S,m,b,w,17,A[14]),m=z(m,b=H(b,x,S,m,k,22,A[15]),x,S,c,5,A[16]),S=z(S,m,b,x,d,9,A[17]),x=z(x,S,m,b,v,14,A[18]),b=z(b,x,S,m,s,20,A[19]),m=z(m,b,x,S,f,5,A[20]),S=z(S,m,b,x,y,9,A[21]),x=z(x,S,m,b,k,14,A[22]),b=z(b,x,S,m,l,20,A[23]),m=z(m,b,x,S,_,5,A[24]),S=z(S,m,b,x,w,9,A[25]),x=z(x,S,m,b,h,14,A[26]),b=z(b,x,S,m,p,20,A[27]),m=z(m,b,x,S,B,5,A[28]),S=z(S,m,b,x,a,9,A[29]),x=z(x,S,m,b,u,14,A[30]),m=C(m,b=z(b,x,S,m,g,20,A[31]),x,S,f,4,A[32]),S=C(S,m,b,x,p,11,A[33]),x=C(x,S,m,b,v,16,A[34]),b=C(b,x,S,m,w,23,A[35]),m=C(m,b,x,S,c,4,A[36]),S=C(S,m,b,x,l,11,A[37]),x=C(x,S,m,b,u,16,A[38]),b=C(b,x,S,m,y,23,A[39]),m=C(m,b,x,S,B,4,A[40]),S=C(S,m,b,x,s,11,A[41]),x=C(x,S,m,b,h,16,A[42]),b=C(b,x,S,m,d,23,A[43]),m=C(m,b,x,S,_,4,A[44]),S=C(S,m,b,x,g,11,A[45]),x=C(x,S,m,b,k,16,A[46]),m=D(m,b=C(b,x,S,m,a,23,A[47]),x,S,s,6,A[48]),S=D(S,m,b,x,u,10,A[49]),x=D(x,S,m,b,w,15,A[50]),b=D(b,x,S,m,f,21,A[51]),m=D(m,b,x,S,g,6,A[52]),S=D(S,m,b,x,h,10,A[53]),x=D(x,S,m,b,y,15,A[54]),b=D(b,x,S,m,c,21,A[55]),m=D(m,b,x,S,p,6,A[56]),S=D(S,m,b,x,k,10,A[57]),x=D(x,S,m,b,d,15,A[58]),b=D(b,x,S,m,B,21,A[59]),m=D(m,b,x,S,l,6,A[60]),S=D(S,m,b,x,v,10,A[61]),x=D(x,S,m,b,a,15,A[62]),b=D(b,x,S,m,_,21,A[63]),o[0]=o[0]+m|0,o[1]=o[1]+b|0,o[2]=o[2]+x|0,o[3]=o[3]+S|0},_doFinalize:function(){var t=this._data,e=t.words,r=8*this._nDataBytes,i=8*t.sigBytes;e[i>>>5]|=128<<24-i%32;var n=a.floor(r/4294967296),r=r;e[15+(64+i>>>9<<4)]=16711935&(n<<8|n>>>24)|4278255360&(n<<24|n>>>8),e[14+(64+i>>>9<<4)]=16711935&(r<<8|r>>>24)|4278255360&(r<<24|r>>>8),t.sigBytes=4*(e.length+1),this._process();for(var e=this._hash,o=e.words,s=0;s<4;s++){var c=o[s];o[s]=16711935&(c<<8|c>>>24)|4278255360&(c<<24|c>>>8)}return e},clone:function(){var t=i.clone.call(this);return t._hash=this._hash.clone(),t}});function H(t,e,r,i,n,o,s){s=t+(e&r|~e&i)+n+s;return(s<<o|s>>>32-o)+e}function z(t,e,r,i,n,o,s){s=t+(e&i|r&~i)+n+s;return(s<<o|s>>>32-o)+e}function C(t,e,r,i,n,o,s){s=t+(e^r^i)+n+s;return(s<<o|s>>>32-o)+e}function D(t,e,r,i,n,o,s){s=t+(r^(e|~i))+n+s;return(s<<o|s>>>32-o)+e}t.MD5=i._createHelper(e),t.HmacMD5=i._createHmacHelper(e)}(Math),P=(M=U).lib,t=P.WordArray,e=P.Hasher,P=M.algo,l=[],P=P.SHA1=e.extend({_doReset:function(){this._hash=new t.init([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(t,e){for(var r=this._hash.words,i=r[0],n=r[1],o=r[2],s=r[3],c=r[4],a=0;a<80;a++){a<16?l[a]=0|t[e+a]:(h=l[a-3]^l[a-8]^l[a-14]^l[a-16],l[a]=h<<1|h>>>31);var h=(i<<5|i>>>27)+c+l[a];h+=a<20?1518500249+(n&o|~n&s):a<40?1859775393+(n^o^s):a<60?(n&o|n&s|o&s)-1894007588:(n^o^s)-899497514,c=s,s=o,o=n<<30|n>>>2,n=i,i=h}r[0]=r[0]+i|0,r[1]=r[1]+n|0,r[2]=r[2]+o|0,r[3]=r[3]+s|0,r[4]=r[4]+c|0},_doFinalize:function(){var t=this._data,e=t.words,r=8*this._nDataBytes,i=8*t.sigBytes;return e[i>>>5]|=128<<24-i%32,e[14+(64+i>>>9<<4)]=Math.floor(r/4294967296),e[15+(64+i>>>9<<4)]=r,t.sigBytes=4*e.length,this._process(),this._hash},clone:function(){var t=e.clone.call(this);return t._hash=this._hash.clone(),t}}),M.SHA1=e._createHelper(P),M.HmacSHA1=e._createHmacHelper(P),function(n){var t=U,e=t.lib,r=e.WordArray,i=e.Hasher,e=t.algo,o=[],p=[];!function(){function t(t){return 4294967296*(t-(0|t))|0}for(var e=2,r=0;r<64;)!function(t){for(var e=n.sqrt(t),r=2;r<=e;r++)if(!(t%r))return;return 1}(e)||(r<8&&(o[r]=t(n.pow(e,.5))),p[r]=t(n.pow(e,1/3)),r++),e++}();var _=[],e=e.SHA256=i.extend({_doReset:function(){this._hash=new r.init(o.slice(0))},_doProcessBlock:function(t,e){for(var r=this._hash.words,i=r[0],n=r[1],o=r[2],s=r[3],c=r[4],a=r[5],h=r[6],l=r[7],f=0;f<64;f++){f<16?_[f]=0|t[e+f]:(d=_[f-15],u=_[f-2],_[f]=((d<<25|d>>>7)^(d<<14|d>>>18)^d>>>3)+_[f-7]+((u<<15|u>>>17)^(u<<13|u>>>19)^u>>>10)+_[f-16]);var d=i&n^i&o^n&o,u=l+((c<<26|c>>>6)^(c<<21|c>>>11)^(c<<7|c>>>25))+(c&a^~c&h)+p[f]+_[f],l=h,h=a,a=c,c=s+u|0,s=o,o=n,n=i,i=u+(((i<<30|i>>>2)^(i<<19|i>>>13)^(i<<10|i>>>22))+d)|0}r[0]=r[0]+i|0,r[1]=r[1]+n|0,r[2]=r[2]+o|0,r[3]=r[3]+s|0,r[4]=r[4]+c|0,r[5]=r[5]+a|0,r[6]=r[6]+h|0,r[7]=r[7]+l|0},_doFinalize:function(){var t=this._data,e=t.words,r=8*this._nDataBytes,i=8*t.sigBytes;return e[i>>>5]|=128<<24-i%32,e[14+(64+i>>>9<<4)]=n.floor(r/4294967296),e[15+(64+i>>>9<<4)]=r,t.sigBytes=4*e.length,this._process(),this._hash},clone:function(){var t=i.clone.call(this);return t._hash=this._hash.clone(),t}});t.SHA256=i._createHelper(e),t.HmacSHA256=i._createHmacHelper(e)}(Math),r=(w=U).lib.WordArray,F=w.algo,i=F.SHA256,F=F.SHA224=i.extend({_doReset:function(){this._hash=new r.init([3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428])},_doFinalize:function(){var t=i._doFinalize.call(this);return t.sigBytes-=4,t}}),w.SHA224=i._createHelper(F),w.HmacSHA224=i._createHmacHelper(F),function(){var t=U,e=t.lib.Hasher,r=t.x64,i=r.Word,n=r.WordArray,r=t.algo;function o(){return i.create.apply(i,arguments)}var t1=[o(1116352408,3609767458),o(1899447441,602891725),o(3049323471,3964484399),o(3921009573,2173295548),o(961987163,4081628472),o(1508970993,3053834265),o(2453635748,2937671579),o(2870763221,3664609560),o(3624381080,2734883394),o(310598401,1164996542),o(607225278,1323610764),o(1426881987,3590304994),o(1925078388,4068182383),o(2162078206,991336113),o(2614888103,633803317),o(3248222580,3479774868),o(3835390401,2666613458),o(4022224774,944711139),o(264347078,2341262773),o(604807628,2007800933),o(770255983,1495990901),o(1249150122,1856431235),o(1555081692,3175218132),o(1996064986,2198950837),o(2554220882,3999719339),o(2821834349,766784016),o(2952996808,2566594879),o(3210313671,3203337956),o(3336571891,1034457026),o(3584528711,2466948901),o(113926993,3758326383),o(338241895,168717936),o(666307205,1188179964),o(773529912,1546045734),o(1294757372,1522805485),o(1396182291,2643833823),o(1695183700,2343527390),o(1986661051,1014477480),o(2177026350,1206759142),o(2456956037,344077627),o(2730485921,1290863460),o(2820302411,3158454273),o(3259730800,3505952657),o(3345764771,106217008),o(3516065817,3606008344),o(3600352804,1432725776),o(4094571909,1467031594),o(275423344,851169720),o(430227734,3100823752),o(506948616,1363258195),o(659060556,3750685593),o(883997877,3785050280),o(958139571,3318307427),o(1322822218,3812723403),o(1537002063,2003034995),o(1747873779,3602036899),o(1955562222,1575990012),o(2024104815,1125592928),o(2227730452,2716904306),o(2361852424,442776044),o(2428436474,593698344),o(2756734187,3733110249),o(3204031479,2999351573),o(3329325298,3815920427),o(3391569614,3928383900),o(3515267271,566280711),o(3940187606,3454069534),o(4118630271,4000239992),o(116418474,1914138554),o(174292421,2731055270),o(289380356,3203993006),o(460393269,320620315),o(685471733,587496836),o(852142971,1086792851),o(1017036298,365543100),o(1126000580,2618297676),o(1288033470,3409855158),o(1501505948,4234509866),o(1607167915,987167468),o(1816402316,1246189591)],e1=[];!function(){for(var t=0;t<80;t++)e1[t]=o()}();r=r.SHA512=e.extend({_doReset:function(){this._hash=new n.init([new i.init(1779033703,4089235720),new i.init(3144134277,2227873595),new i.init(1013904242,4271175723),new i.init(2773480762,1595750129),new i.init(1359893119,2917565137),new i.init(2600822924,725511199),new i.init(528734635,4215389547),new i.init(1541459225,327033209)])},_doProcessBlock:function(t,e){for(var r=this._hash.words,i=r[0],n=r[1],o=r[2],s=r[3],c=r[4],a=r[5],h=r[6],l=r[7],f=i.high,d=i.low,u=n.high,p=n.low,_=o.high,y=o.low,v=s.high,g=s.low,B=c.high,w=c.low,k=a.high,m=a.low,S=h.high,x=h.low,b=l.high,r=l.low,A=f,H=d,z=u,C=p,D=_,E=y,R=v,M=g,F=B,P=w,W=k,O=m,I=S,U=x,K=b,X=r,L=0;L<80;L++){var j,T,N=e1[L];L<16?(T=N.high=0|t[e+2*L],j=N.low=0|t[e+2*L+1]):($=(q=e1[L-15]).high,J=q.low,G=(Q=e1[L-2]).high,V=Q.low,Z=(Y=e1[L-7]).high,q=Y.low,Y=(Q=e1[L-16]).high,T=(T=(($>>>1|J<<31)^($>>>8|J<<24)^$>>>7)+Z+((j=(Z=(J>>>1|$<<31)^(J>>>8|$<<24)^(J>>>7|$<<25))+q)>>>0<Z>>>0?1:0))+((G>>>19|V<<13)^(G<<3|V>>>29)^G>>>6)+((j+=J=(V>>>19|G<<13)^(V<<3|G>>>29)^(V>>>6|G<<26))>>>0<J>>>0?1:0),j+=$=Q.low,N.high=T=T+Y+(j>>>0<$>>>0?1:0),N.low=j);var q=F&W^~F&I,Z=P&O^~P&U,V=A&z^A&D^z&D,G=(H>>>28|A<<4)^(H<<30|A>>>2)^(H<<25|A>>>7),J=t1[L],Q=J.high,Y=J.low,$=X+((P>>>14|F<<18)^(P>>>18|F<<14)^(P<<23|F>>>9)),N=K+((F>>>14|P<<18)^(F>>>18|P<<14)^(F<<23|P>>>9))+($>>>0<X>>>0?1:0),J=G+(H&C^H&E^C&E),K=I,X=U,I=W,U=O,W=F,O=P,F=R+(N=(N=(N=N+q+(($=$+Z)>>>0<Z>>>0?1:0))+Q+(($=$+Y)>>>0<Y>>>0?1:0))+T+(($=$+j)>>>0<j>>>0?1:0))+((P=M+$|0)>>>0<M>>>0?1:0)|0,R=D,M=E,D=z,E=C,z=A,C=H,A=N+(((A>>>28|H<<4)^(A<<30|H>>>2)^(A<<25|H>>>7))+V+(J>>>0<G>>>0?1:0))+((H=$+J|0)>>>0<$>>>0?1:0)|0}d=i.low=d+H,i.high=f+A+(d>>>0<H>>>0?1:0),p=n.low=p+C,n.high=u+z+(p>>>0<C>>>0?1:0),y=o.low=y+E,o.high=_+D+(y>>>0<E>>>0?1:0),g=s.low=g+M,s.high=v+R+(g>>>0<M>>>0?1:0),w=c.low=w+P,c.high=B+F+(w>>>0<P>>>0?1:0),m=a.low=m+O,a.high=k+W+(m>>>0<O>>>0?1:0),x=h.low=x+U,h.high=S+I+(x>>>0<U>>>0?1:0),r=l.low=r+X,l.high=b+K+(r>>>0<X>>>0?1:0)},_doFinalize:function(){var t=this._data,e=t.words,r=8*this._nDataBytes,i=8*t.sigBytes;return e[i>>>5]|=128<<24-i%32,e[30+(128+i>>>10<<5)]=Math.floor(r/4294967296),e[31+(128+i>>>10<<5)]=r,t.sigBytes=4*e.length,this._process(),this._hash.toX32()},clone:function(){var t=e.clone.call(this);return t._hash=this._hash.clone(),t},blockSize:32});t.SHA512=e._createHelper(r),t.HmacSHA512=e._createHmacHelper(r)}(),P=(M=U).x64,c=P.Word,f=P.WordArray,P=M.algo,d=P.SHA512,P=P.SHA384=d.extend({_doReset:function(){this._hash=new f.init([new c.init(3418070365,3238371032),new c.init(1654270250,914150663),new c.init(2438529370,812702999),new c.init(355462360,4144912697),new c.init(1731405415,4290775857),new c.init(2394180231,1750603025),new c.init(3675008525,1694076839),new c.init(1203062813,3204075428)])},_doFinalize:function(){var t=d._doFinalize.call(this);return t.sigBytes-=16,t}}),M.SHA384=d._createHelper(P),M.HmacSHA384=d._createHmacHelper(P),function(l){var t=U,e=t.lib,f=e.WordArray,i=e.Hasher,d=t.x64.Word,e=t.algo,A=[],H=[],z=[];!function(){for(var t=1,e=0,r=0;r<24;r++){A[t+5*e]=(r+1)*(r+2)/2%64;var i=(2*t+3*e)%5;t=e%5,e=i}for(t=0;t<5;t++)for(e=0;e<5;e++)H[t+5*e]=e+(2*t+3*e)%5*5;for(var n=1,o=0;o<24;o++){for(var s,c=0,a=0,h=0;h<7;h++)1&n&&((s=(1<<h)-1)<32?a^=1<<s:c^=1<<s-32),128&n?n=n<<1^113:n<<=1;z[o]=d.create(c,a)}}();var C=[];!function(){for(var t=0;t<25;t++)C[t]=d.create()}();e=e.SHA3=i.extend({cfg:i.cfg.extend({outputLength:512}),_doReset:function(){for(var t=this._state=[],e=0;e<25;e++)t[e]=new d.init;this.blockSize=(1600-2*this.cfg.outputLength)/32},_doProcessBlock:function(t,e){for(var r=this._state,i=this.blockSize/2,n=0;n<i;n++){var o=t[e+2*n],s=t[e+2*n+1],o=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8);(m=r[n]).high^=s=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),m.low^=o}for(var c=0;c<24;c++){for(var a=0;a<5;a++){for(var h=0,l=0,f=0;f<5;f++)h^=(m=r[a+5*f]).high,l^=m.low;var d=C[a];d.high=h,d.low=l}for(a=0;a<5;a++)for(var u=C[(a+4)%5],p=C[(a+1)%5],_=p.high,p=p.low,h=u.high^(_<<1|p>>>31),l=u.low^(p<<1|_>>>31),f=0;f<5;f++)(m=r[a+5*f]).high^=h,m.low^=l;for(var y=1;y<25;y++){var v=(m=r[y]).high,g=m.low,B=A[y];l=B<32?(h=v<<B|g>>>32-B,g<<B|v>>>32-B):(h=g<<B-32|v>>>64-B,v<<B-32|g>>>64-B);B=C[H[y]];B.high=h,B.low=l}var w=C[0],k=r[0];w.high=k.high,w.low=k.low;for(a=0;a<5;a++)for(f=0;f<5;f++){var m=r[y=a+5*f],S=C[y],x=C[(a+1)%5+5*f],b=C[(a+2)%5+5*f];m.high=S.high^~x.high&b.high,m.low=S.low^~x.low&b.low}m=r[0],k=z[c];m.high^=k.high,m.low^=k.low}},_doFinalize:function(){var t=this._data,e=t.words,r=(this._nDataBytes,8*t.sigBytes),i=32*this.blockSize;e[r>>>5]|=1<<24-r%32,e[(l.ceil((1+r)/i)*i>>>5)-1]|=128,t.sigBytes=4*e.length,this._process();for(var n=this._state,e=this.cfg.outputLength/8,o=e/8,s=[],c=0;c<o;c++){var a=n[c],h=a.high,a=a.low,h=16711935&(h<<8|h>>>24)|4278255360&(h<<24|h>>>8);s.push(a=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8)),s.push(h)}return new f.init(s,e)},clone:function(){for(var t=i.clone.call(this),e=t._state=this._state.slice(0),r=0;r<25;r++)e[r]=e[r].clone();return t}});t.SHA3=i._createHelper(e),t.HmacSHA3=i._createHmacHelper(e)}(Math),Math,F=(w=U).lib,u=F.WordArray,p=F.Hasher,F=w.algo,S=u.create([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8,3,10,14,4,9,15,8,1,2,7,0,6,13,11,5,12,1,9,11,10,0,8,12,4,13,3,7,15,14,5,6,2,4,0,5,9,7,12,2,10,14,1,3,8,11,6,15,13]),x=u.create([5,14,7,0,9,2,11,4,13,6,15,8,1,10,3,12,6,11,3,7,0,13,5,10,14,15,8,12,4,9,1,2,15,5,1,3,7,14,6,9,11,8,12,2,10,0,4,13,8,6,4,1,3,11,15,0,5,12,2,13,9,7,10,14,12,15,10,4,1,5,8,7,6,2,13,14,0,3,9,11]),b=u.create([11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8,7,6,8,13,11,9,7,15,7,12,15,9,11,7,13,12,11,13,6,7,14,9,13,15,14,8,13,6,5,12,7,5,11,12,14,15,14,15,9,8,9,14,5,6,8,6,5,12,9,15,5,11,6,8,13,12,5,12,13,14,11,8,5,6]),A=u.create([8,9,9,11,13,15,15,5,7,7,8,11,14,14,12,6,9,13,15,7,12,8,9,11,7,7,12,7,6,15,13,11,9,7,15,11,8,6,6,14,12,13,5,14,13,13,7,5,15,5,8,11,14,14,6,14,6,9,12,9,12,5,15,8,8,5,12,9,12,5,14,6,8,13,6,5,15,13,11,11]),H=u.create([0,1518500249,1859775393,2400959708,2840853838]),z=u.create([1352829926,1548603684,1836072691,2053994217,0]),F=F.RIPEMD160=p.extend({_doReset:function(){this._hash=u.create([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(t,e){for(var r=0;r<16;r++){var i=e+r,n=t[i];t[i]=16711935&(n<<8|n>>>24)|4278255360&(n<<24|n>>>8)}for(var o,s,c,a,h,l,f=this._hash.words,d=H.words,u=z.words,p=S.words,_=x.words,y=b.words,v=A.words,g=o=f[0],B=s=f[1],w=c=f[2],k=a=f[3],m=h=f[4],r=0;r<80;r+=1)l=o+t[e+p[r]]|0,l+=r<16?(s^c^a)+d[0]:r<32?K(s,c,a)+d[1]:r<48?((s|~c)^a)+d[2]:r<64?X(s,c,a)+d[3]:(s^(c|~a))+d[4],l=(l=L(l|=0,y[r]))+h|0,o=h,h=a,a=L(c,10),c=s,s=l,l=g+t[e+_[r]]|0,l+=r<16?(B^(w|~k))+u[0]:r<32?X(B,w,k)+u[1]:r<48?((B|~w)^k)+u[2]:r<64?K(B,w,k)+u[3]:(B^w^k)+u[4],l=(l=L(l|=0,v[r]))+m|0,g=m,m=k,k=L(w,10),w=B,B=l;l=f[1]+c+k|0,f[1]=f[2]+a+m|0,f[2]=f[3]+h+g|0,f[3]=f[4]+o+B|0,f[4]=f[0]+s+w|0,f[0]=l},_doFinalize:function(){var t=this._data,e=t.words,r=8*this._nDataBytes,i=8*t.sigBytes;e[i>>>5]|=128<<24-i%32,e[14+(64+i>>>9<<4)]=16711935&(r<<8|r>>>24)|4278255360&(r<<24|r>>>8),t.sigBytes=4*(e.length+1),this._process();for(var e=this._hash,n=e.words,o=0;o<5;o++){var s=n[o];n[o]=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8)}return e},clone:function(){var t=p.clone.call(this);return t._hash=this._hash.clone(),t}}),w.RIPEMD160=p._createHelper(F),w.HmacRIPEMD160=p._createHmacHelper(F),P=(M=U).lib.Base,_=M.enc.Utf8,M.algo.HMAC=P.extend({init:function(t,e){t=this._hasher=new t.init,"string"==typeof e&&(e=_.parse(e));var r=t.blockSize,i=4*r;(e=e.sigBytes>i?t.finalize(e):e).clamp();for(var t=this._oKey=e.clone(),e=this._iKey=e.clone(),n=t.words,o=e.words,s=0;s<r;s++)n[s]^=1549556828,o[s]^=909522486;t.sigBytes=e.sigBytes=i,this.reset()},reset:function(){var t=this._hasher;t.reset(),t.update(this._iKey)},update:function(t){return this._hasher.update(t),this},finalize:function(t){var e=this._hasher,t=e.finalize(t);return e.reset(),e.finalize(this._oKey.clone().concat(t))}}),F=(w=U).lib,M=F.Base,v=F.WordArray,P=w.algo,F=P.SHA1,g=P.HMAC,y=P.PBKDF2=M.extend({cfg:M.extend({keySize:4,hasher:F,iterations:1}),init:function(t){this.cfg=this.cfg.extend(t)},compute:function(t,e){for(var r=this.cfg,i=g.create(r.hasher,t),n=v.create(),o=v.create([1]),s=n.words,c=o.words,a=r.keySize,h=r.iterations;s.length<a;){var l=i.update(e).finalize(o);i.reset();for(var f=l.words,d=f.length,u=l,p=1;p<h;p++){u=i.finalize(u),i.reset();for(var _=u.words,y=0;y<d;y++)f[y]^=_[y]}n.concat(l),c[0]++}return n.sigBytes=4*a,n}}),w.PBKDF2=function(t,e,r){return y.create(r).compute(t,e)},M=(P=U).lib,F=M.Base,B=M.WordArray,w=P.algo,M=w.MD5,k=w.EvpKDF=F.extend({cfg:F.extend({keySize:4,hasher:M,iterations:1}),init:function(t){this.cfg=this.cfg.extend(t)},compute:function(t,e){for(var r,i=this.cfg,n=i.hasher.create(),o=B.create(),s=o.words,c=i.keySize,a=i.iterations;s.length<c;){r&&n.update(r),r=n.update(t).finalize(e),n.reset();for(var h=1;h<a;h++)r=n.finalize(r),n.reset();o.concat(r)}return o.sigBytes=4*c,o}}),P.EvpKDF=function(t,e,r){return k.create(r).compute(t,e)},U.lib.Cipher||function(){var t=U,e=t.lib,r=e.Base,s=e.WordArray,i=e.BufferedBlockAlgorithm,n=t.enc,o=(n.Utf8,n.Base64),c=t.algo.EvpKDF,a=e.Cipher=i.extend({cfg:r.extend(),createEncryptor:function(t,e){return this.create(this._ENC_XFORM_MODE,t,e)},createDecryptor:function(t,e){return this.create(this._DEC_XFORM_MODE,t,e)},init:function(t,e,r){this.cfg=this.cfg.extend(r),this._xformMode=t,this._key=e,this.reset()},reset:function(){i.reset.call(this),this._doReset()},process:function(t){return this._append(t),this._process()},finalize:function(t){return t&&this._append(t),this._doFinalize()},keySize:4,ivSize:4,_ENC_XFORM_MODE:1,_DEC_XFORM_MODE:2,_createHelper:function(i){return{encrypt:function(t,e,r){return h(e).encrypt(i,t,e,r)},decrypt:function(t,e,r){return h(e).decrypt(i,t,e,r)}}}});function h(t){return"string"==typeof t?p:u}e.StreamCipher=a.extend({_doFinalize:function(){return this._process(!0)},blockSize:1});var l=t.mode={},n=e.BlockCipherMode=r.extend({createEncryptor:function(t,e){return this.Encryptor.create(t,e)},createDecryptor:function(t,e){return this.Decryptor.create(t,e)},init:function(t,e){this._cipher=t,this._iv=e}}),n=l.CBC=((l=n.extend()).Encryptor=l.extend({processBlock:function(t,e){var r=this._cipher,i=r.blockSize;f.call(this,t,e,i),r.encryptBlock(t,e),this._prevBlock=t.slice(e,e+i)}}),l.Decryptor=l.extend({processBlock:function(t,e){var r=this._cipher,i=r.blockSize,n=t.slice(e,e+i);r.decryptBlock(t,e),f.call(this,t,e,i),this._prevBlock=n}}),l);function f(t,e,r){var i,n=this._iv;n?(i=n,this._iv=void 0):i=this._prevBlock;for(var o=0;o<r;o++)t[e+o]^=i[o]}var l=(t.pad={}).Pkcs7={pad:function(t,e){for(var e=4*e,r=e-t.sigBytes%e,i=r<<24|r<<16|r<<8|r,n=[],o=0;o<r;o+=4)n.push(i);e=s.create(n,r);t.concat(e)},unpad:function(t){var e=255&t.words[t.sigBytes-1>>>2];t.sigBytes-=e}},d=(e.BlockCipher=a.extend({cfg:a.cfg.extend({mode:n,padding:l}),reset:function(){var t;a.reset.call(this);var e=this.cfg,r=e.iv,e=e.mode;this._xformMode==this._ENC_XFORM_MODE?t=e.createEncryptor:(t=e.createDecryptor,this._minBufferSize=1),this._mode&&this._mode.__creator==t?this._mode.init(this,r&&r.words):(this._mode=t.call(e,this,r&&r.words),this._mode.__creator=t)},_doProcessBlock:function(t,e){this._mode.processBlock(t,e)},_doFinalize:function(){var t,e=this.cfg.padding;return this._xformMode==this._ENC_XFORM_MODE?(e.pad(this._data,this.blockSize),t=this._process(!0)):(t=this._process(!0),e.unpad(t)),t},blockSize:4}),e.CipherParams=r.extend({init:function(t){this.mixIn(t)},toString:function(t){return(t||this.formatter).stringify(this)}})),l=(t.format={}).OpenSSL={stringify:function(t){var e=t.ciphertext,t=t.salt,e=t?s.create([1398893684,1701076831]).concat(t).concat(e):e;return e.toString(o)},parse:function(t){var e,r=o.parse(t),t=r.words;return 1398893684==t[0]&&1701076831==t[1]&&(e=s.create(t.slice(2,4)),t.splice(0,4),r.sigBytes-=16),d.create({ciphertext:r,salt:e})}},u=e.SerializableCipher=r.extend({cfg:r.extend({format:l}),encrypt:function(t,e,r,i){i=this.cfg.extend(i);var n=t.createEncryptor(r,i),e=n.finalize(e),n=n.cfg;return d.create({ciphertext:e,key:r,iv:n.iv,algorithm:t,mode:n.mode,padding:n.padding,blockSize:t.blockSize,formatter:i.format})},decrypt:function(t,e,r,i){return i=this.cfg.extend(i),e=this._parse(e,i.format),t.createDecryptor(r,i).finalize(e.ciphertext)},_parse:function(t,e){return"string"==typeof t?e.parse(t,this):t}}),t=(t.kdf={}).OpenSSL={execute:function(t,e,r,i){i=i||s.random(8);t=c.create({keySize:e+r}).compute(t,i),r=s.create(t.words.slice(e),4*r);return t.sigBytes=4*e,d.create({key:t,iv:r,salt:i})}},p=e.PasswordBasedCipher=u.extend({cfg:u.cfg.extend({kdf:t}),encrypt:function(t,e,r,i){r=(i=this.cfg.extend(i)).kdf.execute(r,t.keySize,t.ivSize);i.iv=r.iv;i=u.encrypt.call(this,t,e,r.key,i);return i.mixIn(r),i},decrypt:function(t,e,r,i){i=this.cfg.extend(i),e=this._parse(e,i.format);r=i.kdf.execute(r,t.keySize,t.ivSize,e.salt);return i.iv=r.iv,u.decrypt.call(this,t,e,r.key,i)}})}(),U.mode.CFB=((F=U.lib.BlockCipherMode.extend()).Encryptor=F.extend({processBlock:function(t,e){var r=this._cipher,i=r.blockSize;j.call(this,t,e,i,r),this._prevBlock=t.slice(e,e+i)}}),F.Decryptor=F.extend({processBlock:function(t,e){var r=this._cipher,i=r.blockSize,n=t.slice(e,e+i);j.call(this,t,e,i,r),this._prevBlock=n}}),F),U.mode.CTR=(M=U.lib.BlockCipherMode.extend(),P=M.Encryptor=M.extend({processBlock:function(t,e){var r=this._cipher,i=r.blockSize,n=this._iv,o=this._counter;n&&(o=this._counter=n.slice(0),this._iv=void 0);var s=o.slice(0);r.encryptBlock(s,0),o[i-1]=o[i-1]+1|0;for(var c=0;c<i;c++)t[e+c]^=s[c]}}),M.Decryptor=P,M),U.mode.CTRGladman=(F=U.lib.BlockCipherMode.extend(),P=F.Encryptor=F.extend({processBlock:function(t,e){var r=this._cipher,i=r.blockSize,n=this._iv,o=this._counter;n&&(o=this._counter=n.slice(0),this._iv=void 0),0===((n=o)[0]=T(n[0]))&&(n[1]=T(n[1]));var s=o.slice(0);r.encryptBlock(s,0);for(var c=0;c<i;c++)t[e+c]^=s[c]}}),F.Decryptor=P,F),U.mode.OFB=(M=U.lib.BlockCipherMode.extend(),P=M.Encryptor=M.extend({processBlock:function(t,e){var r=this._cipher,i=r.blockSize,n=this._iv,o=this._keystream;n&&(o=this._keystream=n.slice(0),this._iv=void 0),r.encryptBlock(o,0);for(var s=0;s<i;s++)t[e+s]^=o[s]}}),M.Decryptor=P,M),U.mode.ECB=((F=U.lib.BlockCipherMode.extend()).Encryptor=F.extend({processBlock:function(t,e){this._cipher.encryptBlock(t,e)}}),F.Decryptor=F.extend({processBlock:function(t,e){this._cipher.decryptBlock(t,e)}}),F),U.pad.AnsiX923={pad:function(t,e){var r=t.sigBytes,e=4*e,e=e-r%e,r=r+e-1;t.clamp(),t.words[r>>>2]|=e<<24-r%4*8,t.sigBytes+=e},unpad:function(t){var e=255&t.words[t.sigBytes-1>>>2];t.sigBytes-=e}},U.pad.Iso10126={pad:function(t,e){e*=4,e-=t.sigBytes%e;t.concat(U.lib.WordArray.random(e-1)).concat(U.lib.WordArray.create([e<<24],1))},unpad:function(t){var e=255&t.words[t.sigBytes-1>>>2];t.sigBytes-=e}},U.pad.Iso97971={pad:function(t,e){t.concat(U.lib.WordArray.create([2147483648],1)),U.pad.ZeroPadding.pad(t,e)},unpad:function(t){U.pad.ZeroPadding.unpad(t),t.sigBytes--}},U.pad.ZeroPadding={pad:function(t,e){e*=4;t.clamp(),t.sigBytes+=e-(t.sigBytes%e||e)},unpad:function(t){for(var e=t.words,r=t.sigBytes-1,r=t.sigBytes-1;0<=r;r--)if(e[r>>>2]>>>24-r%4*8&255){t.sigBytes=r+1;break}}},U.pad.NoPadding={pad:function(){},unpad:function(){}},m=(P=U).lib.CipherParams,C=P.enc.Hex,P.format.Hex={stringify:function(t){return t.ciphertext.toString(C)},parse:function(t){t=C.parse(t);return m.create({ciphertext:t})}},function(){var t=U,e=t.lib.BlockCipher,r=t.algo,h=[],l=[],f=[],d=[],u=[],p=[],_=[],y=[],v=[],g=[];!function(){for(var t=[],e=0;e<256;e++)t[e]=e<128?e<<1:e<<1^283;for(var r=0,i=0,e=0;e<256;e++){var n=i^i<<1^i<<2^i<<3^i<<4;h[r]=n=n>>>8^255&n^99;var o=t[l[n]=r],s=t[o],c=t[s],a=257*t[n]^16843008*n;f[r]=a<<24|a>>>8,d[r]=a<<16|a>>>16,u[r]=a<<8|a>>>24,p[r]=a,_[n]=(a=16843009*c^65537*s^257*o^16843008*r)<<24|a>>>8,y[n]=a<<16|a>>>16,v[n]=a<<8|a>>>24,g[n]=a,r?(r=o^t[t[t[c^o]]],i^=t[t[i]]):r=i=1}}();var B=[0,1,2,4,8,16,32,64,128,27,54],r=r.AES=e.extend({_doReset:function(){if(!this._nRounds||this._keyPriorReset!==this._key){for(var t=this._keyPriorReset=this._key,e=t.words,r=t.sigBytes/4,i=4*(1+(this._nRounds=6+r)),n=this._keySchedule=[],o=0;o<i;o++)o<r?n[o]=e[o]:(a=n[o-1],o%r?6<r&&o%r==4&&(a=h[a>>>24]<<24|h[a>>>16&255]<<16|h[a>>>8&255]<<8|h[255&a]):(a=h[(a=a<<8|a>>>24)>>>24]<<24|h[a>>>16&255]<<16|h[a>>>8&255]<<8|h[255&a],a^=B[o/r|0]<<24),n[o]=n[o-r]^a);for(var s=this._invKeySchedule=[],c=0;c<i;c++){var a,o=i-c;a=c%4?n[o]:n[o-4],s[c]=c<4||o<=4?a:_[h[a>>>24]]^y[h[a>>>16&255]]^v[h[a>>>8&255]]^g[h[255&a]]}}},encryptBlock:function(t,e){this._doCryptBlock(t,e,this._keySchedule,f,d,u,p,h)},decryptBlock:function(t,e){var r=t[e+1];t[e+1]=t[e+3],t[e+3]=r,this._doCryptBlock(t,e,this._invKeySchedule,_,y,v,g,l);r=t[e+1];t[e+1]=t[e+3],t[e+3]=r},_doCryptBlock:function(t,e,r,i,n,o,s,c){for(var a=this._nRounds,h=t[e]^r[0],l=t[e+1]^r[1],f=t[e+2]^r[2],d=t[e+3]^r[3],u=4,p=1;p<a;p++)var _=i[h>>>24]^n[l>>>16&255]^o[f>>>8&255]^s[255&d]^r[u++],y=i[l>>>24]^n[f>>>16&255]^o[d>>>8&255]^s[255&h]^r[u++],v=i[f>>>24]^n[d>>>16&255]^o[h>>>8&255]^s[255&l]^r[u++],g=i[d>>>24]^n[h>>>16&255]^o[l>>>8&255]^s[255&f]^r[u++],h=_,l=y,f=v,d=g;_=(c[h>>>24]<<24|c[l>>>16&255]<<16|c[f>>>8&255]<<8|c[255&d])^r[u++],y=(c[l>>>24]<<24|c[f>>>16&255]<<16|c[d>>>8&255]<<8|c[255&h])^r[u++],v=(c[f>>>24]<<24|c[d>>>16&255]<<16|c[h>>>8&255]<<8|c[255&l])^r[u++],g=(c[d>>>24]<<24|c[h>>>16&255]<<16|c[l>>>8&255]<<8|c[255&f])^r[u++];t[e]=_,t[e+1]=y,t[e+2]=v,t[e+3]=g},keySize:8});t.AES=e._createHelper(r)}(),function(){var t=U,e=t.lib,i=e.WordArray,r=e.BlockCipher,e=t.algo,h=[57,49,41,33,25,17,9,1,58,50,42,34,26,18,10,2,59,51,43,35,27,19,11,3,60,52,44,36,63,55,47,39,31,23,15,7,62,54,46,38,30,22,14,6,61,53,45,37,29,21,13,5,28,20,12,4],l=[14,17,11,24,1,5,3,28,15,6,21,10,23,19,12,4,26,8,16,7,27,20,13,2,41,52,31,37,47,55,30,40,51,45,33,48,44,49,39,56,34,53,46,42,50,36,29,32],f=[1,2,4,6,8,10,12,14,15,17,19,21,23,25,27,28],d=[{0:8421888,268435456:32768,536870912:8421378,805306368:2,1073741824:512,1342177280:8421890,1610612736:8389122,1879048192:8388608,2147483648:514,2415919104:8389120,2684354560:33280,2952790016:8421376,3221225472:32770,3489660928:8388610,3758096384:0,4026531840:33282,134217728:0,402653184:8421890,671088640:33282,939524096:32768,1207959552:8421888,1476395008:512,1744830464:8421378,2013265920:2,2281701376:8389120,2550136832:33280,2818572288:8421376,3087007744:8389122,3355443200:8388610,3623878656:32770,3892314112:514,4160749568:8388608,1:32768,268435457:2,536870913:8421888,805306369:8388608,1073741825:8421378,1342177281:33280,1610612737:512,1879048193:8389122,2147483649:8421890,2415919105:8421376,2684354561:8388610,2952790017:33282,3221225473:514,3489660929:8389120,3758096385:32770,4026531841:0,134217729:8421890,402653185:8421376,671088641:8388608,939524097:512,1207959553:32768,1476395009:8388610,1744830465:2,2013265921:33282,2281701377:32770,2550136833:8389122,2818572289:514,3087007745:8421888,3355443201:8389120,3623878657:0,3892314113:33280,4160749569:8421378},{0:1074282512,16777216:16384,33554432:524288,50331648:1074266128,67108864:1073741840,83886080:1074282496,100663296:1073758208,117440512:16,134217728:540672,150994944:1073758224,167772160:1073741824,184549376:540688,201326592:524304,218103808:0,234881024:16400,251658240:1074266112,8388608:1073758208,25165824:540688,41943040:16,58720256:1073758224,75497472:1074282512,92274688:1073741824,109051904:524288,125829120:1074266128,142606336:524304,159383552:0,176160768:16384,192937984:1074266112,209715200:1073741840,226492416:540672,243269632:1074282496,260046848:16400,268435456:0,285212672:1074266128,301989888:1073758224,318767104:1074282496,335544320:1074266112,352321536:16,369098752:540688,385875968:16384,402653184:16400,419430400:524288,436207616:524304,452984832:1073741840,469762048:540672,486539264:1073758208,503316480:1073741824,520093696:1074282512,276824064:540688,293601280:524288,310378496:1074266112,327155712:16384,343932928:1073758208,360710144:1074282512,377487360:16,394264576:1073741824,411041792:1074282496,427819008:1073741840,444596224:1073758224,461373440:524304,478150656:0,494927872:16400,511705088:1074266128,528482304:540672},{0:260,1048576:0,2097152:67109120,3145728:65796,4194304:65540,5242880:67108868,6291456:67174660,7340032:67174400,8388608:67108864,9437184:67174656,10485760:65792,11534336:67174404,12582912:67109124,13631488:65536,14680064:4,15728640:256,524288:67174656,1572864:67174404,2621440:0,3670016:67109120,4718592:67108868,5767168:65536,6815744:65540,7864320:260,8912896:4,9961472:256,11010048:67174400,12058624:65796,13107200:65792,14155776:67109124,15204352:67174660,16252928:67108864,16777216:67174656,17825792:65540,18874368:65536,19922944:67109120,20971520:256,22020096:67174660,23068672:67108868,24117248:0,25165824:67109124,26214400:67108864,27262976:4,28311552:65792,29360128:67174400,30408704:260,31457280:65796,32505856:67174404,17301504:67108864,18350080:260,19398656:67174656,20447232:0,21495808:65540,22544384:67109120,23592960:256,24641536:67174404,25690112:65536,26738688:67174660,27787264:65796,28835840:67108868,29884416:67109124,30932992:67174400,31981568:4,33030144:65792},{0:2151682048,65536:2147487808,131072:4198464,196608:2151677952,262144:0,327680:4198400,393216:2147483712,458752:4194368,524288:2147483648,589824:4194304,655360:64,720896:2147487744,786432:2151678016,851968:4160,917504:4096,983040:2151682112,32768:2147487808,98304:64,163840:2151678016,229376:2147487744,294912:4198400,360448:2151682112,425984:0,491520:2151677952,557056:4096,622592:2151682048,688128:4194304,753664:4160,819200:2147483648,884736:4194368,950272:4198464,1015808:2147483712,1048576:4194368,1114112:4198400,1179648:2147483712,1245184:0,1310720:4160,1376256:2151678016,1441792:2151682048,1507328:2147487808,1572864:2151682112,1638400:2147483648,1703936:2151677952,1769472:4198464,1835008:2147487744,1900544:4194304,1966080:64,2031616:4096,1081344:2151677952,1146880:2151682112,1212416:0,1277952:4198400,1343488:4194368,1409024:2147483648,1474560:2147487808,1540096:64,1605632:2147483712,1671168:4096,1736704:2147487744,1802240:2151678016,1867776:4160,1933312:2151682048,1998848:4194304,2064384:4198464},{0:128,4096:17039360,8192:262144,12288:536870912,16384:537133184,20480:16777344,24576:553648256,28672:262272,32768:16777216,36864:537133056,40960:536871040,45056:553910400,49152:553910272,53248:0,57344:17039488,61440:553648128,2048:17039488,6144:553648256,10240:128,14336:17039360,18432:262144,22528:537133184,26624:553910272,30720:536870912,34816:537133056,38912:0,43008:553910400,47104:16777344,51200:536871040,55296:553648128,59392:16777216,63488:262272,65536:262144,69632:128,73728:536870912,77824:553648256,81920:16777344,86016:553910272,90112:537133184,94208:16777216,98304:553910400,102400:553648128,106496:17039360,110592:537133056,114688:262272,118784:536871040,122880:0,126976:17039488,67584:553648256,71680:16777216,75776:17039360,79872:537133184,83968:536870912,88064:17039488,92160:128,96256:553910272,100352:262272,104448:553910400,108544:0,112640:553648128,116736:16777344,120832:262144,124928:537133056,129024:536871040},{0:268435464,256:8192,512:270532608,768:270540808,1024:268443648,1280:2097152,1536:2097160,1792:268435456,2048:0,2304:268443656,2560:2105344,2816:8,3072:270532616,3328:2105352,3584:8200,3840:270540800,128:270532608,384:270540808,640:8,896:2097152,1152:2105352,1408:268435464,1664:268443648,1920:8200,2176:2097160,2432:8192,2688:268443656,2944:270532616,3200:0,3456:270540800,3712:2105344,3968:268435456,4096:268443648,4352:270532616,4608:270540808,4864:8200,5120:2097152,5376:268435456,5632:268435464,5888:2105344,6144:2105352,6400:0,6656:8,6912:270532608,7168:8192,7424:268443656,7680:270540800,7936:2097160,4224:8,4480:2105344,4736:2097152,4992:268435464,5248:268443648,5504:8200,5760:270540808,6016:270532608,6272:270540800,6528:270532616,6784:8192,7040:2105352,7296:2097160,7552:0,7808:268435456,8064:268443656},{0:1048576,16:33555457,32:1024,48:1049601,64:34604033,80:0,96:1,112:34603009,128:33555456,144:1048577,160:33554433,176:34604032,192:34603008,208:1025,224:1049600,240:33554432,8:34603009,24:0,40:33555457,56:34604032,72:1048576,88:33554433,104:33554432,120:1025,136:1049601,152:33555456,168:34603008,184:1048577,200:1024,216:34604033,232:1,248:1049600,256:33554432,272:1048576,288:33555457,304:34603009,320:1048577,336:33555456,352:34604032,368:1049601,384:1025,400:34604033,416:1049600,432:1,448:0,464:34603008,480:33554433,496:1024,264:1049600,280:33555457,296:34603009,312:1,328:33554432,344:1048576,360:1025,376:34604032,392:33554433,408:34603008,424:0,440:34604033,456:1049601,472:1024,488:33555456,504:1048577},{0:134219808,1:131072,2:134217728,3:32,4:131104,5:134350880,6:134350848,7:2048,8:134348800,9:134219776,10:133120,11:134348832,12:2080,13:0,14:134217760,15:133152,2147483648:2048,2147483649:134350880,2147483650:134219808,2147483651:134217728,2147483652:134348800,2147483653:133120,2147483654:133152,2147483655:32,2147483656:134217760,2147483657:2080,2147483658:131104,2147483659:134350848,2147483660:0,2147483661:134348832,2147483662:134219776,2147483663:131072,16:133152,17:134350848,18:32,19:2048,20:134219776,21:134217760,22:134348832,23:131072,24:0,25:131104,26:134348800,27:134219808,28:134350880,29:133120,30:2080,31:134217728,2147483664:131072,2147483665:2048,2147483666:134348832,2147483667:133152,2147483668:32,2147483669:134348800,2147483670:134217728,2147483671:134219808,2147483672:134350880,2147483673:134217760,2147483674:134219776,2147483675:0,2147483676:133120,2147483677:2080,2147483678:131104,2147483679:134350848}],u=[4160749569,528482304,33030144,2064384,129024,8064,504,2147483679],n=e.DES=r.extend({_doReset:function(){for(var t=this._key.words,e=[],r=0;r<56;r++){var i=h[r]-1;e[r]=t[i>>>5]>>>31-i%32&1}for(var n=this._subKeys=[],o=0;o<16;o++){for(var s=n[o]=[],c=f[o],r=0;r<24;r++)s[r/6|0]|=e[(l[r]-1+c)%28]<<31-r%6,s[4+(r/6|0)]|=e[28+(l[r+24]-1+c)%28]<<31-r%6;s[0]=s[0]<<1|s[0]>>>31;for(r=1;r<7;r++)s[r]=s[r]>>>4*(r-1)+3;s[7]=s[7]<<5|s[7]>>>27}for(var a=this._invSubKeys=[],r=0;r<16;r++)a[r]=n[15-r]},encryptBlock:function(t,e){this._doCryptBlock(t,e,this._subKeys)},decryptBlock:function(t,e){this._doCryptBlock(t,e,this._invSubKeys)},_doCryptBlock:function(t,e,r){this._lBlock=t[e],this._rBlock=t[e+1],p.call(this,4,252645135),p.call(this,16,65535),_.call(this,2,858993459),_.call(this,8,16711935),p.call(this,1,1431655765);for(var i=0;i<16;i++){for(var n=r[i],o=this._lBlock,s=this._rBlock,c=0,a=0;a<8;a++)c|=d[a][((s^n[a])&u[a])>>>0];this._lBlock=s,this._rBlock=o^c}var h=this._lBlock;this._lBlock=this._rBlock,this._rBlock=h,p.call(this,1,1431655765),_.call(this,8,16711935),_.call(this,2,858993459),p.call(this,16,65535),p.call(this,4,252645135),t[e]=this._lBlock,t[e+1]=this._rBlock},keySize:2,ivSize:2,blockSize:2});function p(t,e){e=(this._lBlock>>>t^this._rBlock)&e;this._rBlock^=e,this._lBlock^=e<<t}function _(t,e){e=(this._rBlock>>>t^this._lBlock)&e;this._lBlock^=e,this._rBlock^=e<<t}t.DES=r._createHelper(n);e=e.TripleDES=r.extend({_doReset:function(){var t=this._key.words;if(2!==t.length&&4!==t.length&&t.length<6)throw new Error("Invalid key length - 3DES requires the key length to be 64, 128, 192 or >192.");var e=t.slice(0,2),r=t.length<4?t.slice(0,2):t.slice(2,4),t=t.length<6?t.slice(0,2):t.slice(4,6);this._des1=n.createEncryptor(i.create(e)),this._des2=n.createEncryptor(i.create(r)),this._des3=n.createEncryptor(i.create(t))},encryptBlock:function(t,e){this._des1.encryptBlock(t,e),this._des2.decryptBlock(t,e),this._des3.encryptBlock(t,e)},decryptBlock:function(t,e){this._des3.decryptBlock(t,e),this._des2.encryptBlock(t,e),this._des1.decryptBlock(t,e)},keySize:6,ivSize:2,blockSize:2});t.TripleDES=r._createHelper(e)}(),function(){var t=U,e=t.lib.StreamCipher,r=t.algo,i=r.RC4=e.extend({_doReset:function(){for(var t=this._key,e=t.words,r=t.sigBytes,i=this._S=[],n=0;n<256;n++)i[n]=n;for(var n=0,o=0;n<256;n++){var s=n%r,s=e[s>>>2]>>>24-s%4*8&255,o=(o+i[n]+s)%256,s=i[n];i[n]=i[o],i[o]=s}this._i=this._j=0},_doProcessBlock:function(t,e){t[e]^=n.call(this)},keySize:8,ivSize:0});function n(){for(var t=this._S,e=this._i,r=this._j,i=0,n=0;n<4;n++){var r=(r+t[e=(e+1)%256])%256,o=t[e];t[e]=t[r],t[r]=o,i|=t[(t[e]+t[r])%256]<<24-8*n}return this._i=e,this._j=r,i}t.RC4=e._createHelper(i);r=r.RC4Drop=i.extend({cfg:i.cfg.extend({drop:192}),_doReset:function(){i._doReset.call(this);for(var t=this.cfg.drop;0<t;t--)n.call(this)}});t.RC4Drop=e._createHelper(r)}(),F=(M=U).lib.StreamCipher,P=M.algo,D=[],E=[],R=[],P=P.Rabbit=F.extend({_doReset:function(){for(var t=this._key.words,e=this.cfg.iv,r=0;r<4;r++)t[r]=16711935&(t[r]<<8|t[r]>>>24)|4278255360&(t[r]<<24|t[r]>>>8);for(var i=this._X=[t[0],t[3]<<16|t[2]>>>16,t[1],t[0]<<16|t[3]>>>16,t[2],t[1]<<16|t[0]>>>16,t[3],t[2]<<16|t[1]>>>16],n=this._C=[t[2]<<16|t[2]>>>16,4294901760&t[0]|65535&t[1],t[3]<<16|t[3]>>>16,4294901760&t[1]|65535&t[2],t[0]<<16|t[0]>>>16,4294901760&t[2]|65535&t[3],t[1]<<16|t[1]>>>16,4294901760&t[3]|65535&t[0]],r=this._b=0;r<4;r++)N.call(this);for(r=0;r<8;r++)n[r]^=i[r+4&7];if(e){var o=e.words,s=o[0],c=o[1],e=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),o=16711935&(c<<8|c>>>24)|4278255360&(c<<24|c>>>8),s=e>>>16|4294901760&o,c=o<<16|65535&e;n[0]^=e,n[1]^=s,n[2]^=o,n[3]^=c,n[4]^=e,n[5]^=s,n[6]^=o,n[7]^=c;for(r=0;r<4;r++)N.call(this)}},_doProcessBlock:function(t,e){var r=this._X;N.call(this),D[0]=r[0]^r[5]>>>16^r[3]<<16,D[1]=r[2]^r[7]>>>16^r[5]<<16,D[2]=r[4]^r[1]>>>16^r[7]<<16,D[3]=r[6]^r[3]>>>16^r[1]<<16;for(var i=0;i<4;i++)D[i]=16711935&(D[i]<<8|D[i]>>>24)|4278255360&(D[i]<<24|D[i]>>>8),t[e+i]^=D[i]},blockSize:4,ivSize:2}),M.Rabbit=F._createHelper(P),F=(M=U).lib.StreamCipher,P=M.algo,W=[],O=[],I=[],P=P.RabbitLegacy=F.extend({_doReset:function(){for(var t=this._key.words,e=this.cfg.iv,r=this._X=[t[0],t[3]<<16|t[2]>>>16,t[1],t[0]<<16|t[3]>>>16,t[2],t[1]<<16|t[0]>>>16,t[3],t[2]<<16|t[1]>>>16],i=this._C=[t[2]<<16|t[2]>>>16,4294901760&t[0]|65535&t[1],t[3]<<16|t[3]>>>16,4294901760&t[1]|65535&t[2],t[0]<<16|t[0]>>>16,4294901760&t[2]|65535&t[3],t[1]<<16|t[1]>>>16,4294901760&t[3]|65535&t[0]],n=this._b=0;n<4;n++)q.call(this);for(n=0;n<8;n++)i[n]^=r[n+4&7];if(e){var o=e.words,s=o[0],t=o[1],e=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),o=16711935&(t<<8|t>>>24)|4278255360&(t<<24|t>>>8),s=e>>>16|4294901760&o,t=o<<16|65535&e;i[0]^=e,i[1]^=s,i[2]^=o,i[3]^=t,i[4]^=e,i[5]^=s,i[6]^=o,i[7]^=t;for(n=0;n<4;n++)q.call(this)}},_doProcessBlock:function(t,e){var r=this._X;q.call(this),W[0]=r[0]^r[5]>>>16^r[3]<<16,W[1]=r[2]^r[7]>>>16^r[5]<<16,W[2]=r[4]^r[1]>>>16^r[7]<<16,W[3]=r[6]^r[3]>>>16^r[1]<<16;for(var i=0;i<4;i++)W[i]=16711935&(W[i]<<8|W[i]>>>24)|4278255360&(W[i]<<24|W[i]>>>8),t[e+i]^=W[i]},blockSize:4,ivSize:2}),M.RabbitLegacy=F._createHelper(P),U});
//https://unpkg.com/default-passive-events@2.0.0/dist/index.umd.js
!function(e){"function"==typeof define&&define.amd?define(e):e()}(function(){var e,t=["scroll","wheel","touchstart","touchmove","touchenter","touchend","touchleave","mouseout","mouseleave","mouseup","mousedown","mousemove","mouseenter","mousewheel","mouseover"];if(function(){var e=!1;try{var t=Object.defineProperty({},"passive",{get:function(){e=!0}});window.addEventListener("test",null,t),window.removeEventListener("test",null,t)}catch(e){}return e}()){var n=EventTarget.prototype.addEventListener;e=n,EventTarget.prototype.addEventListener=function(n,o,r){var i,s="object"==typeof r&&null!==r,u=s?r.capture:r;(r=s?function(e){var t=Object.getOwnPropertyDescriptor(e,"passive");return t&&!0!==t.writable&&void 0===t.set?Object.assign({},e):e}(r):{}).passive=void 0!==(i=r.passive)?i:-1!==t.indexOf(n)&&!0,r.capture=void 0!==u&&u,e.call(this,n,o,r)},EventTarget.prototype.addEventListener._original=e}});
