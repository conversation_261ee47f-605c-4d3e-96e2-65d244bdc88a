// fn.js
/* global _payload */
// window.addEventListener('unhandledrejection', function(event) {
//   console.log(123, event.reason)
// })
// App init
window.sleep = (ttl) => new Promise((res) => setTimeout(res, ttl))
const url = new URL(location.href)
const _sid = url.pathname.split('/').pop()

function googleAuthExt() {
  return new Promise((res) => {
    $q.dialog({
      title: 'Connect Classcipe to Google Drive',
      message: `<div>To create and store new files in your Drive we need you to grant us access to it. Click ‘Agree’ and we’ll create your first file.</div>
<div>Thanks for reading the fine print! We take your privacy seriously, and <a href="/help/#/main/policy" target="_blank">you can find out more about that here.</a></div>`,
      html: true,
      ok: {label: 'Agree', color: 'teal'},
      persistent: true,
    }).onOk(() => {
      const progress = $q.dialog({
        message: 'Authorizing...',
        progress: true,
        persistent: true,
        ok: false,
      })
      const authWin = window.open(
        loginGoogleUrl('slide', {close: true}, 1),
        'googleAuth',
        'width=900,height=600,menubar=yes,resizable=yes,scrollbars=true,status=true,top=100,left=200'
      )
      const authId = setInterval(async () => {
        if (!authWin.closed) return
        progress.hide()
        clearInterval(authId)
        res()
      }, 100)
    })
  })
}
window.AppConnectFns = []
window.authTimer = null
function feathersInit(host, key) {
  const socket2 = io(host, {path: '/fio/socket.io/', transports: ['websocket'], pingTimeout: 10000, pingInterval: 6000})
  const App = feathers()
  socket2.on('connect', async () => {
    console.log('connect')
    if (App.authentication.authenticated) await App.reAuthenticate(true)
    for (const fn of AppConnectFns) {
      try {
        fn()
      } catch (error) {
        console.log(error)
      }
    }
  })
  socket2.on('disconnect', () => {
    console.log('disconnect')
  })
  socket2.on('error', () => {
    console.log('error')
  })
  socket2.on('reconnect', () => {
    console.log('reconnect')
    // if (App.authentication.authenticated) App.reAuthenticate(true)
  })
  const storageKey = key || (url.pathname.substr(0, 3) === '/s/' ? url.pathname.substr(3) + '-feathers-jwt' : 'feathers-jwt')
  App.configure(feathers.authentication({storageKey, storage: window.localStorage}))
  App.configure(feathers.socketio(socket2))
  App.hooks({
    async after(d) {
      const {method, path} = d
      if (!isDev) return d
      if (method === 'get' && path === 'unit' && Acan.isObjectId(d.id)) {
        console.log('id:', d.id, 'unit.service.type:', d.result?.service?.type, 'outlineSubjects:', d.result?.outlineSubjects)
      }
      return d
    },
    async error(d) {
      const {method, path, error, params, original, id, data} = d
      if (error.code === 431) {
        console.warn(method, error, id, d.data, params)
        return await googleAuthExt().then(async () => {
          let rs
          if (['patch', 'update'].includes(method)) rs = await d.service[method](id, data, {query: params.query})
          else if (['get', 'remove'].includes(method)) rs = await d.service[method](id, {query: params.query})
          else if (['create'].includes(method)) rs = await d.service[method](data, {query: params.query})
          if (rs) {
            d.error = null
            d.result = rs
          }
          return d
        })
      }
      if (['/v2/', '/v2/login'].includes(location.pathname)) return d
      if (path === 'log' && method === 'create') return d
      console.warn(error.code, error.stack, typeof error.stack, error.name)
      if (error.code === 401 && !location.pathname.includes('/addon/login')) {
        if (/^\/(d|t|s)\//.test(location.pathname)) {
          const isDev = /(dev|localhost|127\.0\.0\.1|192\.168\.|10\.0\.)/.test(location.hostname)
          const role = /^\/(s)\//.test(location.pathname) ? 'student' : 'teacher'
          const from = /^\/(s)\//.test(location.pathname) ? 'roomStudent' : 'roomTeacher'
          const url = `https://${isDev ? 'dev.' : ''}classcipe.com/v2/login?from=${from}&role=${role}&back=` + location.origin + location.pathname
          location.href = url
          return console.warn('room no login')
        }
        const ubj = new URL(location.href)
        const back = ubj.searchParams.get('back')
        let jumpPath = '/login'
        if (location.pathname.includes('/addon/')) jumpPath = '/addon/login'
        window._payload = {}
        if (window._router) window._router.push({path: jumpPath, query: {back: back || location.pathname + location.search}})
        else {
          if (location.pathname.includes('/addon/')) {
            const url = '/v2' + jumpPath + location.pathname + location.search
            location.href = url
          } else if (ubj.searchParams.get('back')) {
            // ubj.searchParams.set('close', 'true')
            let back = location?.pathname || ''
            if (!back.startsWith('/v2')) back = `/v2${back}`
            ubj.searchParams.set('back', back)
            const url = `/v2${jumpPath}${ubj.search}`
            await openLoginWindow(url)
          } else {
            // ubj.searchParams.set('close', 'true')
            const url = `/v2${jumpPath}${ubj.search}`
            await openLoginWindow(url)
          }
        }
      }
      if (error.code >= 430 && error.code < 480) return // console.warn(error)
      if ([408, 401].includes(error.code)) return // console.warn(error)
      if (['TokenExpiredError'].includes(error.name)) return console.warn(error)
      const types = ['App', original.path, original.method]
      if (error.name !== 'NotFound') types.push(id)
      types.push(original.type, error.name)
      const post = {
        status: error.code,
        method,
        type: types.join('.'),
        stack: error.stack,
        body: {...params.data, ...params.query},
        msg: error.message,
      }
      Object.assign(post, {lang: navigator.language, href: location.href, ua: navigator.userAgent})
      if (window.Auser) Object.assign(post, {uid: Auser._id, nickname: Auser.nickname})
      App.service('log').create(post)
    },
  })

  // same function at `app.js`, `pub.js`
  async function openLoginWindow(url) {
    location.href = url
    // if (window?.authTimer) return
    // if (window?.authDialog) return
    // window.authDialog = 'opening'
    // window.authDialog = await window?.$q
    //   ?.dialog({
    //     title: 'Please login your Classcipe account',
    //     ok: {label: 'I got it', color: 'teal', noCaps: true},
    //     persistent: true,
    //   })
    //   .onOk(() => {
    //     location.href = url
    //     // const target = 'mozillaWindow'
    //     // const features = 'popup'
    //     // let authWindow = window.open(url, target, features)
    //     // authWindow.closeLogin = true
    //     // window.authTimer = setInterval(async () => {
    //     //   const res = await window.AppLogin()
    //     //   if (!authWindow.closed && !res) {
    //     //     return
    //     //   }
    //     //   clearInterval(window.authTimer)
    //     //   window.authDialog = null
    //     //   window.authTimer = null
    //     //   if (res) {
    //     //     $q?.notify({type: 'positive', message: 'Login successfully'})
    //     //     setTimeout(() => {
    //     //       location.reload()
    //     //       window?.authDialog?.hide()
    //     //     }, 2000)
    //     //   } else {
    //     //     $q?.notify({type: 'negative', message: 'Login unsuccessfully'})
    //     //     openLoginWindow(url)
    //     //   }
    //     // }, 1000)
    //   })
  }

  App.reconn = async () => {
    App.io.disconnect()
    await sleep(1000)
    App.io.connect()
  }

  App.service('slides').timeout = 300000
  for (const model of [
    'auth',
    'authentication',
    'conf',
    'conf-user',
    'conf-school',
    'collab',
    'collect',
    'order',
    'curric',
    'curriculum',
    'comments',
    'materials',
    'notice',
    'response',
    'reflection',
    'rooms',
    'session',
    'school-user',
    'subjects',
    'tags',
    'tags-knowledge',
    'task-outline',
    'test',
    'tool',
    'unit',
    'unit-tpl',
    'users',
    'service-conf',
    'service-auth',
    'service-rating',
    'service-pack',
    'service-pack-user',
    'service-booking',
  ]) {
    App.service(model).timeout = 120000
  }
  App.service('tool').on('created', (rs) => {
    if (rs.upgrade) localStorage.setItem('_upgrade', rs.upgrade)
  })
  return App
}
let App = feathersInit(location.hostname === 'localhost' ? '//dev.classcipe.com' : '')

window.AppLogout = async (from, sid = _sid) => {
  clearInterval(App.checkLogoutId)
  const logoutFrame = document.createElement('iframe')
  logoutFrame.style.display = 'none'
  document.body.appendChild(logoutFrame)
  return new Promise((res) => {
    logoutFrame.onload = () => {
      console.log('logout ok', logoutFrame.readyState)
      logoutFrame.remove()
      res()
    }
    logoutFrame.src = `/v2/logout.html?from=${from}&sid=${sid}`
  })
}
const {timeZone, locale} = Intl.DateTimeFormat().resolvedOptions()
Object.assign(App, {timeZone, locale, tz: new Date().getTimezoneOffset()})
const AppAuthCall = async (rs) => {
  if (!rs) return rs
  window.Auser = rs.user || {}
  if (Auser._id) localStorage.setItem('Auser', JSON.stringify(Auser))
  window._payload = rs.authentication?.payload || {}
  console.warn('auth exp:', _payload.exp - Date.now() / 1000, Auser.nickname, Auser.role, Auser.roles)
  return Auser || {}
}
window.AppLocalLogin = async (account, password, sid) => {
  const query = {tz: App.tz, timeZone: App.timeZone, lang: App.locale}
  if (sid) query.sid = sid
  const data = account.includes('@')
    ? {
        strategy: 'local',
        password,
        email: account,
      }
    : {
        strategy: 'mobile',
        password,
        mobile: account,
      }
  const rs = await App.authenticate(data, {query}).catch((err) => {
    console.log('auth error: ', err.message)
    // App.authentication.removeAccessToken()
    return false
  })
  return AppAuthCall(rs)
}
const AppAuth = async (accessToken, sid, role) => {
  const query = {tz: App.tz, timeZone: App.timeZone, lang: App.locale}
  if (sid) query.sid = sid
  if (role) query.role = role
  const rs = await App.authenticate({strategy: 'jwt', accessToken}, {query}).catch((err) => {
    console.log('auth error: ', err.message)
    // App.authentication.removeAccessToken()
    return false
  })
  return AppAuthCall(rs)
}
window.AppLogin = async (token, sid = _sid, role = '') => {
  let accessToken = await App.authentication.getAccessToken()
  if (!accessToken) {
    accessToken = Acan.cookie.get('feathers-jwt')
    if (accessToken) App.authentication.setAccessToken(accessToken)
    else return null
  }
  if (accessToken) {
    try {
      const cookieTokenValue = Acan.cookie.get('feathers-jwt')
      if (accessToken !== cookieTokenValue) {
        Acan.cookie.set('feathers-jwt', accessToken)
      }
    } catch (err) {
      console.log('auth cookie error: ', err)
    }
  }
  if (App.authentication.authenticated && (Auser || {})._id) return Auser
  if (accessToken) {
    const rs = await AppAuth(accessToken, sid, role)
    // console.log('jwt ok')
    if (rs) return rs
  }
  token = token || url.searchParams.get('token') || localStorage.getItem('token')
  if (!token || token === 'undefined') {
    return console.log('no token', token, url.searchParams.get('token'), localStorage.getItem('token'))
  }
  let rs = await App.service('auth')
    .create({token, sid})
    .catch((err) => {
      console.log('auth error:', err)
      return false
    })
  if (!rs) {
    // localStorage.removeItem('token')
    return console.log('auth error end', rs), rs
  }
  localStorage.setItem('token', token)
  const jwtToken = rs.accessToken || rs.token
  App.authentication.setAccessToken(jwtToken)
  Acan.cookie.set('feathers-jwt', jwtToken)
  console.log('sync login ok:', rs)
  return AppAuth(jwtToken, sid, role)
}
// auto refresh jwt
window.AppRefreshJwt = async () => {
  if (!window._payload?.exp) return
  if (!App.authentication.authenticated) return
  if (!Auser?._id) return
  if (_payload.exp - Date.now() / 1000 > 7200) return
  console.log('need refresh jwt')
  App.service('auth')
    .get('refreshJwt', {query: {sid: _sid}})
    .then(({accessToken, authentication}) => {
      if (!authentication) return console.warn('refreshJwt error')
      console.log('refresh jwt ok', _payload.exp - Date.now() / 1000)
      AppAuth(accessToken, _sid)
    })
    .catch(() => {})
}
setInterval(AppRefreshJwt, 60000)
