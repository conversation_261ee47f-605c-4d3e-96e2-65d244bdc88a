var _errLast = []
window.ErrCall = async function (o) {
  let body = JSON.stringify(o)
  if (_errLast.includes(body)) return console.log('same errlog!', o)
  _errLast.push(body)
  if (_errLast.length > 10) _errLast.shift()
  Object.assign(o, { lang: navigator.language, ua: navigator.userAgent, ref: document.referrer })
  if (!o.href) o.href = location.href
  if (window._payload) o.exp = new Date(_payload.exp*1000)
  try {
    let user = JSON.parse(localStorage.getItem('Auser') ?? null) ?? {}
    if (user) {
      o.uid = user._id
      o.nickname = user.nickname
      o.email = user.email
      o.userMode = JSON.parse(localStorage.getItem('user_mode') ?? null) ?? ''
      if (o.userMode === 'school') {
        let school = JSON.parse(localStorage.getItem('SET_CURRENT_SCHOOL') ?? null) ?? ''
        o.schoolName = school.schoolName
        o.schoolId = school.id
      }
    }
  } catch (e) {}
  body = JSON.stringify(o)
  await fetch('/fio/log', { method: 'POST', headers: { "Content-Type": "application/json;charset=UTF-8" }, body }).then(r => r.json())
  // throw o
}
window.onerror = async function (msg, url, num, line, err) {
  await ErrCall({ type: 'error', msg, url, num, line, stack: err?.stack ?? '' })
  if (msg.includes("Script error")) location.reload()
  if (msg.includes("Unexpected token '<'")) location.reload()
  if (msg.includes("SyntaxError: expected expression")) location.reload()
  console.warn(msg, url, num, line, err)
  // throw msg, err
}
window.addEventListener('unhandledrejection', (err) => {
  var reason = err.reason || {}
  var msg = reason.message || err.message || ''
  ErrCall({ type: 'unhandledrejection', msg, stack: reason.stack || err.stack || '' })
  if (msg.includes("Failed to fetch dynamically imported module")) location.reload()
  // throw err
})
window.VueError = function (err, vm, info) {
  ErrCall({ type: 'vueError', msg: err.message, html: vm.$el.outerHTML, info, stack: err.stack })
  // throw err
}

