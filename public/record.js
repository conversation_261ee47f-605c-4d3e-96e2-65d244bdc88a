const startBtn = document.getElementById('startBtn')
const recordBtn = document.getElementById('recordBtn')
const downloadBtn = document.getElementById('downloadBtn')
const video = document.querySelector('video') // 预览用的

let mediaRecorder
let isRecording = false
let recordedBlobs = [] // 暂存视频数据的地方
startBtn.addEventListener('click', () => {
  navigator.mediaDevices
    .getDisplayMedia({
      video: {
        cursor: 'always',
      },
      audio: {
        echoCancellation: true,
        noiseSuppression: true,
        sampleRate: 44100,
      },
      systemAudio: 'include'
    })
    .then(gotDisplayStream, onErr)
})

// 拿到屏幕数据流
async function gotDisplayStream(stream) {
  startBtn.disabled = true
  video.srcObject = stream // 显示出来
  const audioStream = await navigator.mediaDevices
    .getUserMedia({
      audio: {
        echoCancellation: true,
        noiseSuppression: true,
        sampleRate: 44100,
      },
    })
    .catch((e) => {
      throw e
    })
  ;[videoTrack] = stream.getVideoTracks()
  // ;[vaTrack] = stream.getAudioTracks()
  ;[audioTrack] = audioStream.getAudioTracks()
  window.streams = new MediaStream([videoTrack, audioTrack ]) // audioTrack
  videoTrack.addEventListener('ended', () => {
    showMsg('用户停止了分享屏幕')
    stopRecord()
    startBtn.disabled = false
    recordBtn.disabled = true
  })
  recordBtn.disabled = false
  console.log('start record')
  setTimeout(startRecording, 500)
}

function onErr(error) {
  showMsg(`getDisplayMedia on err: ${error.name}`, error)
}

function showMsg(msg, error) {
  const msgEle = document.querySelector('#msg')
  msgEle.innerHTML += `<p>${msg}</p>`
  if (typeof error !== 'undefined') {
    console.error(error)
  }
}
// 找到支持的格式
function getSupportedMimeTypes() {
  const possibleTypes = ['video/webm;codecs=vp9,opus', 'video/webm;codecs=vp8,opus', 'video/webm;codecs=h264,opus', 'video/mp4;codecs=h264,aac']
  return possibleTypes.filter((mimeType) => {
    return MediaRecorder.isTypeSupported(mimeType)
  })
}
function startRecording() {
  console.log('startRecording')
  recordedBlobs.length = 0
  const mimeType = getSupportedMimeTypes()[0]
  const options = {mimeType}

  try {
    mediaRecorder = new MediaRecorder(window.streams, options)
  } catch (e) {
    console.log(`创建MediaRecorder出错: ${JSON.stringify(e)}`)
    return
  }
  recordBtn.textContent = '停止录制'
  isRecording = true
  downloadBtn.disabled = true
  mediaRecorder.onstop = (event) => {
    console.log('录制停止了: ' + event)
  }
  mediaRecorder.ondataavailable = handleDataAvailable
  mediaRecorder.start()
  console.log('录制开始 mediaRecorder: ' + mediaRecorder)
}

function handleDataAvailable(event) {
  console.log('handleDataAvailable', event)
  if (event.data && event.data.size > 0) {
    recordedBlobs.push(event.data)
  }
}
function stopRecord() {
  isRecording = false
  mediaRecorder.stop()
  downloadBtn.disabled = false
  recordBtn.textContent = '开始录制'
}
downloadBtn.addEventListener('click', () => {
  const blob = new Blob(recordedBlobs, {type: 'video/webm'})
  const url = window.URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.style.display = 'none'
  a.href = url
  a.download = '录屏_' + new Date().getTime() + '.webm'
  document.body.appendChild(a)
  a.click()
  setTimeout(() => {
    document.body.removeChild(a)
    window.URL.revokeObjectURL(url)
  }, 100)
})
