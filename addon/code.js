function init() {
  var ui = HtmlService.createTemplateFromFile('start').evaluate().setTitle('Classcipe').setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL)
  // .setSandboxMode(HtmlService.SandboxMode.IFRAME);
  SlidesApp.getUi().showSidebar(ui)
}
function popWin(url, close = false, title = 'Classcipe') {
  const pageId = SlidesApp.getActivePresentation().getSelection().getCurrentPage().getObjectId()
  const rurl = url.replace('${pageId}', pageId)
  var htmlText = HtmlService.createTemplateFromFile('pop').getRawContent().replace('src=""', `src="${rurl}"`)
  if (close) {
    htmlText = '<script>google.script.host.close();</script>'
  }
  var html = HtmlService.createHtmlOutput(htmlText)
    // .evaluate()
    .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL)
    .setWidth('1080')
    .setHeight('1080')
  //.setWidth('640').setHeight('480')
  // .setSandboxMode(HtmlService.SandboxMode.IFRAME);
  // SlidesApp.getUi().showDialog(html)
  SlidesApp.getUi().showModalDialog(html, title)
  // html.append(`<script>alert('123');</script>`)
  return {ok: 1, rurl, html: HtmlService.createTemplateFromFile('pop').getRawContent()}
}
function openUrl(url) {
  var js = `<script>
window.open('${url}', '_blank');
google.script.host.close();
</script>`
  var html = HtmlService.createHtmlOutput(js).setHeight(1).setWidth(1)
  SlidesApp.getUi().showModalDialog(html, 'loading...') // If you use this on Spreadsheet
}
function setNoteToSlide(note) {
  const slides = SlidesApp.getActivePresentation().getSlides()
  const pageId = SlidesApp.getActivePresentation().getSelection().getCurrentPage().getObjectId()
  const index = slides.findIndex((o) => o.getObjectId() === pageId)
  if (index === -1) return {ok: 0, message: 'not find page'}
  if (note) {
    slides[index].getNotesPage().getSpeakerNotesShape().getText().setText(note)
  } else {
    slides[index].getNotesPage().getSpeakerNotesShape().getText().clear()
  }
}
function test() {
  const slide = SlidesApp.getActivePresentation().getSlides()[3]
  slide.selectAsCurrentPage()
  slide.getPageElements()[0]?.select(false)
  return {ok: 1}
}
function tpPage(pageId) {
  const slides = SlidesApp.getActivePresentation().getSlides()
  const index = slides.findIndex((o) => o.getObjectId() === pageId)
  if (index === -1) return {ok: 0, message: 'not find page'}
  slides[index].selectAsCurrentPage()
  slides[index].getPageElements()[0]?.select(false)
  return {ok: 1}
}
function info(i) {
  const p = SlidesApp.getActivePresentation()
  if (!p.getSlides().length) return {id: p.getId(), value: false}

  let pageId = p.getSelection().getCurrentPage().getObjectId()
  const slides = p.getSlides()
  const total = slides.length
  let index = slides.findIndex((o) => o.getObjectId() === pageId)
  if (i === 1 && index + 1 < total) {
    index = index + 1
    slides[index].selectAsCurrentPage()
    slides[index].getPageElements()[0]?.select(false)
  } else if (i === -1 && index > 0) {
    index = index - 1
    slides[index].selectAsCurrentPage()
    slides[index].getPageElements()[0]?.select(false)
  }
  pageId = slides[index].getObjectId()
  const keys = Object.keys(p.getSelection().getCurrentPage())
  return {id: p.getId(), name: p.getName(), pageId, keys, index, total: slides.length}
}
function addNewSlide() {
  const p = SlidesApp.getActivePresentation()
  let slides = p.getSlides()
  let slide
  if (!slides.length) {
    slide = p.insertSlide(0)
    slide.selectAsCurrentPage()
  }
  slides = p.getSlides()
  return {id: p.getId(), name: p.getName(), pageId: slides[0].getObjectId(), index: 0, total: 1, slide: slide, value: false}
}
function setName(name) {
  const p = SlidesApp.getActivePresentation()
  p.setName(name)
  return {id: p.getId(), name: p.getName()}
}
function onOpen(event) {
  SlidesApp.getUi().createAddonMenu().addItem('Open Classcipe Add-on', 'init').addToUi()
}
function onInstall(event) {
  SlidesApp.getUi().createAddonMenu().addItem('Open Classcipe Add-on', 'init').addToUi()
  init()
}

var descList = {
  text: '📝This is a text slide',
  draw: '🎨This is a draw slide',
  choice: '✅This is a choice slide',
  media: '🎬This is a media slide',
  comment: '💬This is a comment slide',
  website: '🌐This is a website slide',
}
const cdnHost = 'https://dcdkqlzgpl5ba.cloudfront.net/1486807354523619329/file/'
var imageList = {
  website: cdnHost + '202204251248218774-website.png',
  choice: cdnHost + '20220425124916210-choice.png',
  draw: cdnHost + '202204251248547817-draw.png',
  comment: cdnHost + '202204261302431647-commint1.png',
  media: cdnHost + '202204251241299222-media.png',
  text: cdnHost + '202204261303394262-text1.png',
  logo: cdnHost + '202204221336506652-logo_icon.png',
}

function copy(sid, pageIds = []) {
  console.log('copy', sid, pageIds)
  var slides
  try {
    slides = SlidesApp.openById(sid).getSlides()
  } catch (err) {
    console.log(err)
    return {message: err.message}
  }
  var p = SlidesApp.getActivePresentation()
  const currentId = p.getSelection().getCurrentPage().getObjectId()
  var index = p.getSlides().findIndex((v) => v.getObjectId() === currentId)
  var i = 1
  var rs = []
  slides.map((slide) => {
    if (pageIds.length > 0 && !pageIds.includes(slide.getObjectId())) return
    var nSlide = p.insertSlide(index + i)
    const nt = Date.now()
    copySlide(slide, nSlide)
    console.log(i, nSlide.getObjectId(), 'cp ttl:', Date.now() - nt, 'ms')
    rs.push(nSlide.getObjectId())
    i++
  })
  return rs
}

function copySlide(sourceSlide, targetSlide) {
  var eles = sourceSlide.getPageElements()
  var background = sourceSlide.getBackground()
  var type = background.getType()
  if (background && type) {
    console.log(type.toString())
    if (type == SlidesApp.PageBackgroundType.PICTURE) {
      targetSlide.getBackground().setPictureFill(background.getPictureFill().getContentUrl())
    } else if (type == SlidesApp.PageBackgroundType.SOLID) {
      var solide = background.getSolidFill()
      targetSlide.getBackground().setSolidFill(solide.getColor(), solide.getAlpha())
    }
  }
  for (var ele of eles) {
    targetSlide.insertPageElement(ele)
  }
}

function delFlag(pageId) {
  var noteTextList = Object.values(descList)
  let p = SlidesApp.getActivePresentation()
  let slide = p.getSlides().find((o) => o.getObjectId() === pageId)
  if (!slide) return {message: 'not find page'}
  slide.selectAsCurrentPage()
  slide.getPageElements()[0]?.select(false)
  var images = slide.getImages()
  const rs = []
  for (const image of images) {
    if (noteTextList.includes(image.getTitle())) image.remove(), rs.push(image.getObjectId())
  }
  return rs
}

function addFlagToPage(pageId, type, questionId = '') {
  var imageUrl = imageList[type]
  var noteTextList = Object.values(descList)
  console.log('draw image', type, imageUrl)
  let p = SlidesApp.getActivePresentation()
  // let page = p.getSelection().getCurrentPage()
  let page = p.getSlides().find((o) => o.getObjectId() === pageId)
  if (!page) return {message: 'not find page'}
  page.selectAsCurrentPage()
  page.getPageElements()[0]?.select(false)
  let pageHeight = p.getPageHeight()
  let pageWidth = p.getPageWidth()
  console.log('getPageHeight()', pageHeight, pageWidth)
  let imageWidth = pageWidth
  let imageHeight = (imageWidth / 1920) * 150
  let x = 0
  let y = pageHeight - imageHeight
  var images = page.getImages()
  var image = images.find((image) => noteTextList.includes(image.getTitle()))
  if (image) {
    if (image.getTitle() === descList[type]) return {imageId: image.getObjectId()} // same type
    image.replace(imageUrl)
    image.bringToFront()
  } else {
    image = page.insertImage(imageUrl, x, y, imageWidth, imageHeight)
  }
  image.setLinkUrl('https://classcipe.com/#' + questionId)
  image.setTitle(descList[type])
  return {pageHeight, pageWidth, imageUrl, imageId: image.getObjectId()}
}

function addFlagAndNoteToPage(pageId, type, questionId, note) {
  var imageUrl = imageList[type];
  var noteTextList = Object.values(descList);
  let p = SlidesApp.getActivePresentation();
  let page = p.getSlides().find((o) => o.getObjectId() === pageId);
  if (!page) return { message: 'not find page' };

  page.selectAsCurrentPage();
  page.getPageElements()[0]?.select(false);

  // Set speaker notes
  if (note) {
    page.getNotesPage().getSpeakerNotesShape().getText().setText(note);
  } else {
    page.getNotesPage().getSpeakerNotesShape().getText().clear();
  }

  // Add or update flag image
  let pageHeight = p.getPageHeight();
  let pageWidth = p.getPageWidth();
  let imageWidth = pageWidth;
  let imageHeight = (imageWidth / 1920) * 150;
  let x = 0;
  let y = pageHeight - imageHeight;

  var images = page.getImages();
  var image = images.find((img) => noteTextList.includes(img.getTitle()));

  if (image) {
    if (image.getTitle() === descList[type]) return { imageId: image.getObjectId() }; // Same type, no need to replace
    image.replace(imageUrl);
    image.bringToFront();
  } else {
    image = page.insertImage(imageUrl, x, y, imageWidth, imageHeight);
  }

  image.setLinkUrl('https://classcipe.com/#' + questionId);
  image.setTitle(descList[type]);

  return { pageHeight, pageWidth, imageUrl, imageId: image.getObjectId() };
}
