<!DOCTYPE html>
<html>
<head>
  <base target="_top">
</head>
<style>
html,body,iframe{margin:0;border:0;width:100%;height:100%;overflow: hidden;}
body{display:flex;justify-content:center;align-items:center;}
#loading{position:fixed;}
</style>
<body>
<!-- <svg id="loading" fill="currentColor" width="5em" height="5em" viewBox="0 0 120 30" xmlns="http://www.w3.org/2000/svg">
  <circle cx="15" cy="15" r="15" style="color: green;" >
    <animate attributeName="r" from="15" to="15" begin="0s" dur="0.8s" values="15;9;15" calcMode="linear" repeatCount="indefinite"></animate>
    <animate attributeName="fill-opacity" from="1" to="1" begin="0s" dur="0.8s" values="1;.5;1" calcMode="linear" repeatCount="indefinite"></animate>
  </circle>
  <circle cx="60" cy="15" r="9" style="color: orange;">
    <animate attributeName="r" from="9" to="9" begin="0s" dur="0.8s" values="9;15;9" calcMode="linear" repeatCount="indefinite"></animate>
    <animate attributeName="fill-opacity" from=".5" to=".5" begin="0s" dur="0.8s" values=".5;1;.5" calcMode="linear" repeatCount="indefinite"></animate>
  </circle>
  <circle cx="105" cy="15" r="15" style="color: red;">
    <animate attributeName="r" from="15" to="15" begin="0s" dur="0.8s" values="15;9;15" calcMode="linear" repeatCount="indefinite"></animate>
    <animate attributeName="fill-opacity" from="1" to="1" begin="0s" dur="0.8s" values="1;.5;1" calcMode="linear" repeatCount="indefinite"></animate>
  </circle>
</svg> -->
<script>
function hideLoading() {
  // loading.style.display = 'none'
}
function onPost({ data }) {
  const args = data.args || []
  if (data.act === 'close') {
    google.script.host.close()
  } else if (data.act === 'ttl') {
    addon.contentWindow.postMessage({ ...data, data: { message: 'ok' }}, '*')
  } else if (google.script.run[data.act]) {
    google.script.run.withSuccessHandler((result) => {
      addon.contentWindow.postMessage({ ...data, data: result }, '*')
    })[data.act](...args)
  }
}

window.addEventListener('message', onPost)
</script>
<iframe id="addon" onload="hideLoading()" src="">
</body>
</html>
