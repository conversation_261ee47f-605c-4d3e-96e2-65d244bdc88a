<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" width="64" height="64">
  <style><![CDATA[.N{fill:none}.O{clip-path:url(#A)}.P{clip-rule:evenodd}]]></style>
  <defs>
    <clipPath id="A">
      <path d="M-200-175H800v562H-200z" class="N"/>
    </clipPath>
    <clipPath id="B">
      <path
        d="M370 89a24 24 0 1 1-34 0 24 24 0 0 1 34 0zm-17-23a40 40 0 1 1-.05 0zm101 23a24 24 0 1 1-34 0 24 24 0 0 1 34 0zm11.3-11.3A40 40 0 1 1 437 66a40 40 0 0 1 28.28 11.72zM253 145.8l4 .2h60l-.2-4c-.54-6.6-5.2-11.4-11.8-11.8l-4-.2h-36l48-48-.2-4A12.1 12.1 0 0 0 301 66.2l-4-.2h-60l.2 4A12.41 12.41 0 0 0 249 81.8l4 .2h36l-48 48 .2 4a12.23 12.23 0 0 0 11.8 11.8zM526.84 90a17.29 17.29 0 0 1 2 8l.2 4v28l.2 4A12.29 12.29 0 0 0 541 145.8l4 .2v-44l.2-4a17.44 17.44 0 0 1 2-8.05 16 16 0 0 1 27.69.05 17.54 17.54 0 0 1 1.94 8l.2 4v28l.2 4A12.24 12.24 0 0 0 589 145.8l4 .2V98a32 32 0 0 0-56-21.17 32 32 0 0 0-41.92-5.35C492 68 485 66 481 66v80l4-.2c6.68-.44 11.54-5.16 11.8-11.8l.2-4v-28l.2-4a17.45 17.45 0 0 1 2-8 16 16 0 0 1 27.68 0z"
        class="N P"/>
    </clipPath>
    <clipPath id="C">
      <circle cx="107" cy="106" r="102" class="N"/>
    </clipPath>
    <clipPath id="D">
      <circle cx="107" cy="106" r="100" class="N"/>
    </clipPath>
    <clipPath id="E">
      <circle cx="107" cy="106" r="92" class="N"/>
    </clipPath>
    <clipPath id="F">
      <path
        d="M135 94.06l26-19c2.27-1.85 4-1.42 4 2V135c0 3.84-2.16 3.4-4 2l-26-19zM47 77.2v43.2A17.69 17.69 0 0 0 64.77 138h63a3.22 3.22 0 0 0 3.23-3.2V91.6A17.69 17.69 0 0 0 113.23 74h-63A3.22 3.22 0 0 0 47 77.2z"
        class="N P"/>
    </clipPath>
    <clipPath id="G">
      <path
        d="M390.36 50.67l-.36 1.6q.44 0 .86.1a5.64 5.64 0 0 0 .83.06 4.18 4.18 0 0 0 3.89-2.15L407 30.42h-2.12l-7.42 13.65h-.06l-2.3-13.65H393l3 16.08-1.46 2.5a5.21 5.21 0 0 1-1 1.18 2.2 2.2 0 0 1-1.53.56 15 15 0 0 1-1.65-.07zm-10.13-5.36a4.52 4.52 0 0 1-1.95-.39 4 4 0 0 1-1.37-1 4.29 4.29 0 0 1-.81-1.54 6.68 6.68 0 0 1-.26-1.92 11.09 11.09 0 0 1 .42-2.91 10.47 10.47 0 0 1 1.25-2.85 7.64 7.64 0 0 1 2.06-2.18 4.8 4.8 0 0 1 2.85-.87 4.23 4.23 0 0 1 3.51 1.35 5.64 5.64 0 0 1 1.07 3.59 11.19 11.19 0 0 1-.44 3 9.58 9.58 0 0 1-1.29 2.82 7.32 7.32 0 0 1-2.12 2.07 5.33 5.33 0 0 1-2.92.83zm-3-21.06L372.5 46.5h1.74l.78-3.68h.06a4.77 4.77 0 0 0 1.92 3.06 5.85 5.85 0 0 0 3.52 1.06 7.15 7.15 0 0 0 3.65-.92 8.62 8.62 0 0 0 2.66-2.38 10.91 10.91 0 0 0 1.64-3.3 12.54 12.54 0 0 0 .56-3.68 9.12 9.12 0 0 0-.37-2.66 5.78 5.78 0 0 0-1.14-2.12 5.4 5.4 0 0 0-1.9-1.4 6.5 6.5 0 0 0-2.7-.51 6 6 0 0 0-3.29.9 6.68 6.68 0 0 0-2.26 2.4h-.06l1.9-9zM351 45.3a3.87 3.87 0 0 1-3.27-1.37 5.44 5.44 0 0 1-1.06-3.43 11.36 11.36 0 0 1 .44-3 10.2 10.2 0 0 1 1.29-2.88 7.31 7.31 0 0 1 2.15-2.15 5.33 5.33 0 0 1 3-.84 4.33 4.33 0 0 1 3.32 1.25 4.77 4.77 0 0 1 1.17 3.4 11.07 11.07 0 0 1-.47 3.1 10.49 10.49 0 0 1-1.36 2.93 7.71 7.71 0 0 1-2.2 2.18 5.34 5.34 0 0 1-3.01.81zm7 1.2l4.77-22.25h-2l-1.93 9.2h-.06a4.22 4.22 0 0 0-1.93-2.7 6.59 6.59 0 0 0-3.3-.79 7.16 7.16 0 0 0-3.65.93 9.4 9.4 0 0 0-2.77 2.43 11.06 11.06 0 0 0-1.76 3.36 11.94 11.94 0 0 0-.61 3.71 7.12 7.12 0 0 0 1.51 4.75 5.68 5.68 0 0 0 4.6 1.79 6.31 6.31 0 0 0 3.47-.87 8.08 8.08 0 0 0 2.38-2.46h.06l-.72 2.9zM329.67 39h12.6q.1-.72.12-1.4.03-.68 0-1.37a7.66 7.66 0 0 0-.41-2.55 5.39 5.39 0 0 0-1.2-2 5.45 5.45 0 0 0-1.92-1.28 6.88 6.88 0 0 0-2.55-.4 7.63 7.63 0 0 0-3.68.87 8.65 8.65 0 0 0-2.74 2.31 10.24 10.24 0 0 0-1.7 3.26 12.38 12.38 0 0 0-.58 3.75 7.07 7.07 0 0 0 1.67 5 6.31 6.31 0 0 0 4.88 1.81 7.35 7.35 0 0 0 4.7-1.43 8.05 8.05 0 0 0 2.62-4.17h-2a5.88 5.88 0 0 1-1.93 2.88 5.62 5.62 0 0 1-5.41.72 4 4 0 0 1-1.46-1.06 4.33 4.33 0 0 1-.86-1.6 7 7 0 0 1-.28-2 7.62 7.62 0 0 1 .14-1.34zm10.75-1.65h-10.5a8.88 8.88 0 0 1 .78-2.15 7.26 7.26 0 0 1 1.3-1.83 6.49 6.49 0 0 1 1.81-1.28 5.11 5.11 0 0 1 2.23-.48 4.26 4.26 0 0 1 3.22 1.22 4.52 4.52 0 0 1 1.17 3.27q0 .3 0 .6 0 .3-.01.63zm-20.35-6.92l-3.43 16.07h1.93l1.8-8.4a11 11 0 0 1 .73-2.18 7.29 7.29 0 0 1 1.23-1.91 5.82 5.82 0 0 1 1.76-1.32 5.49 5.49 0 0 1 2.32-.53h1.12l.44-2a1 1 0 0 0-.25 0h-.8a5.48 5.48 0 0 0-3.52 1.09 9 9 0 0 0-2.31 2.84h-.1l.8-3.7zM302.3 39h12.6q.1-.72.12-1.4.03-.68 0-1.37a7.67 7.67 0 0 0-.4-2.55 5.39 5.39 0 0 0-1.2-2 5.44 5.44 0 0 0-1.92-1.28 6.88 6.88 0 0 0-2.59-.45 7.63 7.63 0 0 0-3.68.87 8.65 8.65 0 0 0-2.74 2.31 10.24 10.24 0 0 0-1.7 3.26 12.38 12.38 0 0 0-.58 3.75 7.07 7.07 0 0 0 1.67 5 6.31 6.31 0 0 0 4.88 1.81 7.34 7.34 0 0 0 4.7-1.43 8 8 0 0 0 2.62-4.17h-2a5.88 5.88 0 0 1-1.93 2.88 5.62 5.62 0 0 1-5.41.72 4 4 0 0 1-1.46-1.06 4.33 4.33 0 0 1-.86-1.6 7 7 0 0 1-.28-2 7.62 7.62 0 0 1 .17-1.29zm10.7-1.66h-10.5a8.88 8.88 0 0 1 .78-2.15 7.26 7.26 0 0 1 1.32-1.82 6.48 6.48 0 0 1 1.81-1.28 5.11 5.11 0 0 1 2.23-.48 4.26 4.26 0 0 1 3.22 1.22 4.52 4.52 0 0 1 1.17 3.27q0 .3 0 .6 0 .3-.03.63zm-36.37-6.92l1.87 16.08h2.15L287.3 33h.06l1.15 13.5h2.12l8.44-16.08H297L290.17 44h-.06L289 30.42h-2.3l-6.64 13.7H280l-1.28-13.7zm-9.77-.42a8.11 8.11 0 0 0-3.82.87 8.83 8.83 0 0 0-2.82 2.31 10.06 10.06 0 0 0-1.74 3.3 12.62 12.62 0 0 0-.59 3.83 6.93 6.93 0 0 0 1.65 4.86 6.13 6.13 0 0 0 4.77 1.81 8.08 8.08 0 0 0 3.91-.9 8.36 8.36 0 0 0 2.77-2.4 10.74 10.74 0 0 0 1.67-3.43 14.12 14.12 0 0 0 .56-4 7.13 7.13 0 0 0-.47-2.66 5.45 5.45 0 0 0-3.32-3.21 7.54 7.54 0 0 0-2.57-.38zm-2.18 15.36a4.43 4.43 0 0 1-3.68-1.49 5.79 5.79 0 0 1-1.18-3.79 10.49 10.49 0 0 1 .44-2.91 9.48 9.48 0 0 1 1.28-2.74 7.18 7.18 0 0 1 2.09-2 5.22 5.22 0 0 1 2.87-.79 4.39 4.39 0 0 1 3.55 1.4 5.46 5.46 0 0 1 1.18 3.65 11 11 0 0 1-.42 2.93 10 10 0 0 1-1.25 2.8 7.22 7.22 0 0 1-2.06 2.1 5 5 0 0 1-2.82.79zm-22.88-21.1L237 46.5h2.12l2.06-9.5H249a9.42 9.42 0 0 0 3.08-.48 6.87 6.87 0 0 0 2.41-1.4 6.41 6.41 0 0 0 1.59-2.27 7.78 7.78 0 0 0 .58-3.07 5.67 5.67 0 0 0-.45-2.31A4.9 4.9 0 0 0 255 25.7a5.54 5.54 0 0 0-1.87-1.07 7.12 7.12 0 0 0-2.35-.37zm-.22 10.9l2-9.1h6.5a7.8 7.8 0 0 1 1.85.2 3.66 3.66 0 0 1 1.42.67 3.08 3.08 0 0 1 .92 1.23 4.79 4.79 0 0 1 .33 1.88 5.3 5.3 0 0 1-.42 2.17 4.55 4.55 0 0 1-1.17 1.6 5.17 5.17 0 0 1-1.74 1 6.57 6.57 0 0 1-2.15.34z"
        class="N P"/>
    </clipPath>
  </defs>
  <g clip-path="url(#B)" transform="translate(0 -178)" class="O">
    <path d="M232 61h366v90H232z" fill="#4a8cff"/>
  </g>
  <g transform="matrix(.156863 0 0 .156863 -.784314 -.627496)" class="O">
    <g clip-path="url(#C)">
      <path d="M0-1h214v214H0z" fill="#e5e5e4"/>
    </g>
    <g clip-path="url(#D)">
      <path d="M2 1h210v210H2z" fill="#fff"/>
    </g>
    <g clip-path="url(#E)">
      <path d="M10 9h194v194H10z" fill="#4a8cff"/>
    </g>
    <g clip-path="url(#F)">
      <path d="M42 69h128v74H42z" fill="#fff"/>
    </g>
  </g>
  <g clip-path="url(#G)" transform="translate(0 -178)" class="O">
    <path d="M232 19.25h180v38.17H232z" fill="#90908f"/>
  </g>
</svg>