<template>
  <q-layout view="hHh lpR fFf" class="full-height">
    <q-header class="bg-white text-grey-8" v-if="isHeader">
      <q-toolbar class="GNL__toolbar" :class="isTop ? 'shadow-2' : 'shadow-2'" style="padding-bottom: 0.2rem">
        <router-link to="/" style="text-decoration: none">
          <q-toolbar-title shrink class="row items-center no-wrap">
            <q-avatar v-if="$q.screen.gt.xs" size="2rem"><img src="~assets/img/logo2.png" /></q-avatar>
            <span class="q-ml-sm text-teal-5">
              <b>{{ !isCn ? 'Classcipe' : '课时学' }}</b>
            </span>
          </q-toolbar-title>
        </router-link>

        <q-space />
        <div class="q-gutter-sm row items-center no-wrap">
          <template v-if="!$route.path.includes('/my/')">
            <ShareDetailBtn />
            <!-- <ShareBtn /> -->
          </template>
          <AccountMenu :ifNotice="true" />
        </div>
      </q-toolbar>
    </q-header>

    <q-page-container class="full-height pc-sm">
      <router-view class="full-height" :key="$route.fullPath" />
    </q-page-container>
    <q-inner-loading :showing="loading" label="Please wait..." dark label-class="text-teal" label-style="font-size: 1.1em" />
    <ShareBox
      :show="shareShow"
      :close="
        () => {
          shareShow = false
        }
      " />
  </q-layout>
</template>

<script setup>
import {ref, watch, computed, onMounted, onUnmounted} from 'vue'
import {useRoute} from 'vue-router'
const route = useRoute()
import {useQuasar} from 'quasar'
window.$q = useQuasar()
import {pubStore} from 'stores/pub'
const pub = pubStore()
import ShareDetailBtn from 'components/ShareBtn.vue'
import AccountMenu from 'components/AccountMenu.vue'
import ShareBox from 'components/ShareBox.vue'

const shareShow = ref(false)
const isHeader = computed(() => {
  return !(route.query.header === '0' || ['/v2Box', '/login', '/signup', '/dash'].includes(route.path))
}) // 0: disable header
const loading = ref(true),
  isTop = ref(true)

function scrollFn() {
  const scrollTop = document.documentElement.scrollTop || document.body.scrollTop
  isTop.value = scrollTop === 0
}

onUnmounted(() => {
  window.removeEventListener('scroll', scrollFn)
})

watch(
  () => pub.title,
  (o) => {
    if (!o || typeof window === 'undefined') return
    document.title = o
  },
  {deep: true, immediate: true}
)
onMounted(async () => {
  window.addEventListener('scroll', scrollFn)
  loading.value = false
})
</script>
