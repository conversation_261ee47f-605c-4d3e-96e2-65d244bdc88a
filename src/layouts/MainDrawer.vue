<template>
  <q-drawer bordered class="bg-white" :width="280">
    <q-scroll-area class="fit">
      <q-list class="q-mx-sm">
        <template v-if="showNavTabs">
          <q-item
            :class="{hidden: link.hidden}"
            dense
            class="q-my-md q-py-sm rounded-borders"
            v-for="link in links1"
            :key="link.text"
            clickable
            :href="link.href"
            :to="link.to"
            :active="link.to === route.path"
            active-class="bg-teal-2 text-black text-weight-medium">
            <q-item-section side>
              <q-icon :name="link.icon" />
            </q-item-section>
            <q-item-section>
              <q-item-label>{{ link.text }}</q-item-label>
            </q-item-section>
          </q-item>
          <q-separator inset class="q-my-sm" />
        </template>

        <template v-else>
          <div v-for="link in links2.filter((l) => l.show)" :key="link.text">
            <q-item
              v-if="!link.tabs?.length"
              dense
              class="q-my-md q-py-sm rounded-borders"
              clickable
              @click="link.click"
              :disable="link.disable"
              :to="link.to"
              :active="link.to === route.path"
              active-class="bg-teal-2 text-black text-weight-medium">
              <q-item-section side>
                <q-icon :name="link.icon" />
              </q-item-section>
              <q-item-section>
                <q-item-label>{{ link.text }}</q-item-label>
              </q-item-section>
              <q-tooltip v-if="link.disable" max-width="280px"> Please contact your school admin to add you to classes. </q-tooltip>
            </q-item>
            <q-expansion-item
              v-else
              dense
              class="q-my-md rounded-borders overflow-hidden"
              :header-class="link.active ? 'bg-teal-2 text-black text-weight-medium' : ''"
              :content-inset-level="0.7">
              <template v-slot:header>
                <q-item-section side>
                  <q-icon :color="link.active ? 'black' : 'grey-7'" :name="link.icon" />
                </q-item-section>
                <q-item-section class="q-py-sm">
                  {{ link.text }}
                </q-item-section>
              </template>

              <q-item
                v-for="(o, i) in link.tabs"
                :key="i"
                class="q-my-md q-py-sm rounded-borders"
                dense
                clickable
                :disable="o.disable"
                @click="onLinkClick(link, o)">
                <q-item-section>
                  <q-item-label>{{ o.name }}</q-item-label>
                </q-item-section>
              </q-item>
            </q-expansion-item>
          </div>

          <q-separator inset class="q-my-sm" />
        </template>

        <q-item class="GNL__drawer-item" v-for="(link, i) in links3" :key="i" clickable>
          <q-item-section>
            <q-item-label>{{ link.text }} <q-icon v-if="link.icon" :name="link.icon" /></q-item-label>
          </q-item-section>
        </q-item>

        <div class="q-mt-md">
          <div class="flex flex-center q-gutter-xs">
            <a class="GNL__drawer-footer-link text-grey-7" href="/help/#/main/policy" target="_blank" aria-label="Privacy">Privacy</a>
            <span> · </span>
            <a class="GNL__drawer-footer-link text-grey-7" href="/help/#/main/terms" target="_blank" aria-label="Terms">Terms</a>
            <span> · </span>
            <a class="GNL__drawer-footer-link text-grey-7" href="/help/#/" target="_blank" aria-label="About">About Classcipe</a>
          </div>
          <div class="flex flex-center">version: {{ version }}</div>
        </div>
      </q-list>
    </q-scroll-area>
  </q-drawer>
</template>

<script setup>
import {ref, watch, inject, computed, onMounted, onUnmounted} from 'vue'
import {useRoute, useRouter} from 'vue-router'
const route = useRoute()
const router = useRouter()
import {pubStore} from 'stores/pub'
const pub = pubStore()
const version = window._version
import useClasses from 'src/composables/account/school/useClasses'
import useSchool from 'src/composables/common/useSchool'

const props = defineProps(['showNavTabs'])
const schoolClassesList = ref([])
const myClassesList = ref([])

const links1 = [
  //{icon: 'home', text: 'Home', to: '/'},
  {icon: 'app_registration', text: 'Quote', to: '/quote', hidden: pub.isStudent},
  {icon: 'price_check', text: 'Pricing', to: '/pricing', hidden: pub.isStudent},
  {icon: 'library_books', text: 'Library', to: '/library', hidden: pub.isStudent},
  {icon: 'o_local_library', text: "Students' center", to: '/center', hidden: pub.isStudent},
  {icon: 'settings_accessibility', text: 'Refer Principal', to: '/refer-principal', hidden: pub.isStudent},
  {icon: 'help', text: 'Help Desk', href: 'https://news.classcipe.com/?view=article&id=45:classcipe-helpdesk-2&catid=2'},
]
//const classes = ref([])
const {list: classes} = useClasses()
const {schoolId} = useSchool()

const classesList = computed(() => {
  return schoolId.value ? schoolClassesList.value : myClassesList.value
})

const links2 = computed(() => {
  if (pub.isStudent) {
    return [
      {icon: 'o_house', text: 'Homepage', to: '/study/index', show: true},
      {icon: 'o_production_quantity_limits', text: "Students' center", to: '/study/center', show: true},
      {
        icon: 'o_room_preferences',
        text: 'My classes',
        to: '/study/class',
        disable: !classesList.value.length,
        tabs: classesList.value,
        show: true,
      },
      {icon: 'calendar_month', text: 'Journal', to: '/journal', show: schoolId.value && schoolClassesList.value?.length},
      {icon: 'o_request_page', text: 'Booking center', to: '/study/booking?tab=featured', show: !(schoolId.value && schoolClassesList.value?.length)},
      {
        icon: 'o_local_mall',
        text: 'My purchased',
        to: '/study/purchased',
        show: !pub.currentSchoolId && !(schoolId.value && schoolClassesList.value?.length),
        tabs: [
          {_id: 'workshop', name: 'Workshop'},
          {_id: 'mentoring', name: 'Mentoring'},
          {_id: 'self', name: 'Self-study'},
        ],
      },
    ]
  }
  return [
    {icon: 'o_house', text: 'Homepage', to: '/home/<USER>', show: true},
    {icon: 'library_books', text: 'Library', to: '/home/<USER>', show: !pub.isStudent},
    {icon: 'o_published_with_changes', text: 'My published', to: '/home/<USER>', show: !pub.currentSchoolId},
    {icon: 'o_content_paste', text: 'My contents', to: '/home/<USER>', show: true},
    {icon: 'o_bookmark_border', text: 'My saved', to: '/home/<USER>', show: true},
    {
      icon: 'o_room_preferences',
      text: 'My classes',
      to: '/home/<USER>',
      disable: !classesList.value.length,
      show: true,
      tabs: classesList.value,
    },
    {icon: 'o_request_page', text: 'Booking center', to: '/home/<USER>', show: true},
    {
      icon: 'o_local_mall',
      text: 'I participate',
      to: '/home/<USER>',
      show: true,
      tabs: [
        {_id: 'workshop', name: 'Workshop'},
        {_id: 'mentoring', name: 'Mentoring'},
      ],
    },
    {
      icon: 'o_person_4',
      text: 'I facilitate',
      to: '/home/<USER>',
      show: true,
      tabs: [
        {_id: 'workshop', name: 'Workshop', disable: false},
        {_id: 'substitute', name: 'Substitute', disable: true},
        {_id: 'correcting', name: 'Correcting', disable: true},
        {_id: 'mentoring', name: 'Mentoring', disable: false},
        {_id: 'qa', name: 'Q&A', disable: true, active: route.query.tab == 'qa'},
      ],
    },
  ]
})
const links3 = [
  // { icon: '', text: 'Language & region' },
]

const isDetail = computed(() => {
  return /\/(task|unit|content|unitOld|pd|video|workshop|session|courses|v2Box|detail|reflect)\//.test(route.path)
  //return /\/(task|pd|video|workshop|session|v2Box|detail)\//.test(route.path)
})

function onLinkClick(link, sublink) {
  router.push({path: link.to, query: {tab: sublink._id}})
}

async function loadClasses() {
  schoolClassesList.value = []
  myClassesList.value = []
  if (schoolId.value) {
    if (pub.isStudent) {
      schoolClassesList.value = await App.service('students').get('classList', {query: {school: schoolId.value}})
      schoolClassesList.value.unshift({_id: 'lecture', name: 'Lecture room'})
    } else {
      schoolClassesList.value = await App.service('school-user').get('classList', {query: {school: schoolId.value}})
    }
  } else {
    if (pub.isStudent) {
      myClassesList.value.unshift({_id: 'private', name: 'Private'})
    } else {
      const rs = await App.service('classes').find()
      myClassesList.value = rs.data
    }
  }
  /*
  await pub.getClassList()
  classes.value.length = 0
  pub.classUserList.map((v) => {
    //classes.value.push({icon: v.subject ? 'school' : 'people', text: v.name, to: `/my/classSession/${v.id}?header=0`})
    classes.value.push({icon: v.subject ? 'school' : 'people', text: v.name, to: `/com/class/${v.id}?header=0`})
  })
  */
}

watch(schoolId, (newVal, oldVal) => {
  if (!pub.user?._id) return
  loadClasses()
})

onMounted(async () => {
  if (pub.user?._id) loadClasses()
})
</script>
<style>
.q-drawer-container > .q-drawer {
  position: fixed;
}
</style>
<style lang="sass">
.GNL
  &__drawer-item
    line-height: 24px
    border-radius: 0 24px 24px 0
    margin-right: 12px

    .q-item__section--avatar
      min-width: 45px
      .q-icon
        color: #5f6368

    .q-item__label
      color: #3c4043
      letter-spacing: .01785714em
      font-size: .875rem
      font-weight: 500
      line-height: 1.25rem

  &__drawer-footer-link
    color: inherit
    text-decoration: none
    font-weight: 500
    font-size: .75rem

    &:hover
      color: #000
</style>
