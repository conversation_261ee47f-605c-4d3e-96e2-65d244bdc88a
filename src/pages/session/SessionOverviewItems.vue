<template>
  <div class="row q-mt-md" v-if="!Acan.isEmpty(defaultTpl)">
    <div class="col-12 q-mb-md" :class="{'q-pa-md shadow-3 rounded-borders-md': pub.isStudent}" v-for="(item, lidx) in defaultTpl" :key="lidx">
      <div class="text-h6" :class="{'text-teal-6': !pub.isStudent}">{{ item.name }}</div>
      <div class="inline-block" v-if="item.type == 'choice-mark' || item.type == 'choice'">
        <div v-for="(choice, cidx) in rearrangeObject(item.value)" :key="cidx">
          <div class="text-weight-medium q-mt-sm">
            <span v-html="convertToLink(choice.value)"></span>
          </div>
          <div class="q-pl-md">
            <span v-html="convertToLink(choice.mark)"></span>
          </div>
          <div v-for="(item, iidx) in choice.child" :key="iidx">
            <div class="q-pl-md">
              <span v-html="convertToLink(item.value)"></span>
            </div>
            <div class="q-pl-lg">
              <span v-html="convertToLink(item.mark)"></span>
            </div>
          </div>
        </div>
      </div>
      <div class="inline-block" v-else-if="['string', 'number'].includes(typeof item.value)">
        <span v-html="convertToLink(item.value)"></span>
      </div>
      <div class="inline-block" v-else v-for="(b, bi) in item.value" :key="bi">
        <template v-if="item.code == 'words'">
          <q-chip class="text-weight-medium" :ripple="false" size="12px" color="teal-1" text-color="primary">{{ b }}</q-chip>
        </template>
        <span v-else v-html="convertToLink(b)"></span>
      </div>
    </div>
  </div>
  <OutlinePreview :isStudent="pub.isStudent" v-if="content.outline" :outline="content.outline" :gradeOptions="gradeOptions" />
</template>

<script setup>
import {ref, onMounted} from 'vue'

import {pubStore} from 'stores/pub'
const pub = pubStore()

import useUnitPlanTemplate from 'src/composables/account/unit-plan-template/useUnitPlanTemplate'
const {mergeTemplateDataByMode} = useUnitPlanTemplate()

import OutlinePreview from 'components/OutlinePreview.vue'
const props = defineProps(['content', 'gradeOptions'])

const defaultTpl = ref(null)

const convertToLink = (text) => {
  const urlRegex = /(https?:\/\/[^\s]+)/g
  const newText = text.toString()?.replace(urlRegex, '<a href="$1" target="_blank">$1</a>')
  return newText.split('\n').join('<br/>')
}
const mergeData = async () => {
  defaultTpl.value = {}
  const sectionData = {}
  sectionData.data = props.content.template
  const mergedSectionData = await mergeTemplateDataByMode(props.content.mode, sectionData)
  const dataFiltered = []
  mergedSectionData?.data?.forEach((data) => {
    if (data.code && !Acan.isEmpty(props.content[data.code]) && !['duration', 'cover', 'name', 'grades', 'subjects', 'type'].includes(data.code)) {
      data.value = props.content[data.code]
    } else if (data.tags && props.content.ext?.[data.tags]) {
      //data.value = props.content.ext[data.tags]
    } else if (props.content.ext?.[data._id]) {
      //data.value = props.content.ext[data._id]
    } else if (data.code && props.content.ext?.[data.code]) {
      data.value = props.content.ext[data.code] //Real World Connection(s)
    }
    if (!Acan.isEmpty(data.value) && data.required) {
      dataFiltered.push(data)
    }
  })
  defaultTpl.value = dataFiltered.reverse().sort((a) => {
    if (a.code && ['inquiry', 'overview', 'idea', 'words'].includes(a.code)) {
      return -1
    } else {
      return 1
    }
  })
}
onMounted(() => {
  mergeData()
})
</script>
