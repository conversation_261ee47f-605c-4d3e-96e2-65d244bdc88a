<template>
  <q-layout view="hHh LpR fFf">
    <PubHeader :showShare="false" isShareDetail />
    <q-page-container class="explicit-page-container bg-grey-1">
      <q-page class="column overflow-hidden" v-if="loading">
        <div class="col text-center q-pa-xl text-grey">
          <q-spinner-ball color="primary" size="2em" class="full-width" />
        </div>
      </q-page>
      <q-page class="pc-sm" v-else>
        <SessionDetail :main="main">
          <template v-slot:btns>
            <template v-for="(btn, i) in btns" :key="i">
              <q-item v-if="btn.show">
                <q-btn
                  class="col"
                  :disable="btn.disable && !btn.dialog"
                  :loading="btn.loading"
                  outline
                  rounded
                  color="primary"
                  no-caps
                  :to="btn.to"
                  @click="btn.fn ? btn.fn(btn) : () => {}">
                  <q-icon :name="btn.icon" size="1.2rem"></q-icon>
                  <span class="q-pl-sm"> {{ btn.label }}</span>
                </q-btn>
                <q-tooltip v-if="btn.disable && btn.tips" max-width="300px">{{ btn.tips }}</q-tooltip>
              </q-item>
            </template>
          </template>
          <template v-slot:listItems>
            <template v-for="(item, index) in listItems" :key="index">
              <template v-if="!studentId || (studentId && (item.overview || item.slides || item.studyData))">
                <q-item
                  v-if="item.show"
                  :clickable="!item.disable && !item.assess"
                  :v-ripple="!item.disable"
                  :disable="item.disable"
                  :to="item.to"
                  :href="item.href"
                  @click="item.fn && item.fn()"
                  :class="{'assessing-status': item.assess}">
                  <q-item-section avatar>
                    <q-icon :color="item.evaluation ? 'red' : ''" :name="item.icon" />
                  </q-item-section>
                  <q-item-section>
                    <q-item-label lines="1">
                      <div class="row items-center">
                        <div>
                          {{ item.label }}
                        </div>
                        <!-- <template v-if="item.evaluation">
                          <q-space></q-space>
                          <div class="row q-pl-md">
                            <q-icon color="primary" name="done" size="sm" v-if="session.one?.rating"></q-icon>
                            <span v-else class="text-negative">Incomplete</span>
                          </div>
                        </template> -->
                        <template v-if="item.roster">
                          <div class="row q-pl-md text-primary">
                            <q-icon name="o_group" size="1rem"></q-icon>
                            <template v-if="session.one.booking"> 1 </template>
                            <template v-else>
                              {{ session.membersCount }}
                            </template>
                            <q-icon name="chevron_right" size="1rem" color="teal-2"></q-icon>
                            <q-icon name="o_task_alt" size="1rem"></q-icon>
                            {{ session.roomStat?.attend?.length ?? 0 }}
                            <q-icon name="remove" size="1rem" color="teal-2" class="rotate-90"></q-icon>
                            <q-icon name="o_person_off" size="1rem"></q-icon>
                            {{ session.roomStat?.block?.length ?? 0 }}
                          </div>
                        </template>
                        <template v-if="item.assess">
                          <div v-if="item.self" class="q-ml-md" :class="{'gray-filter': !session.one.task?.toolCount?.self}">
                            <PersonAvatar
                              :tagText="getTextAndChecked(session.one, 'self')['text']"
                              :isChecked="getTextAndChecked(session.one, 'self')['checked']"
                              src="/v2/img/assessment-tool/self-avatar.png"></PersonAvatar>
                          </div>
                          <div class="row q-gutter-md justify-center q-pl-md" v-else-if="item.all">
                            <div v-if="session.one.task?.toolCount?.self" :class="{'gray-filter': avatarDisabled('self')}">
                              <PersonAvatar
                                :tagText="getTextAndChecked(session.one, 'self')['text']"
                                :isChecked="getTextAndChecked(session.one, 'self')['checked']"
                                src="/v2/img/assessment-tool/self-avatar.png"></PersonAvatar>
                            </div>
                            <div v-if="session.one.task?.toolCount?.others" :class="{'gray-filter': avatarDisabled('others')}">
                              <PersonAvatar
                                :tagText="getTextAndChecked(session.one, 'others')['text']"
                                :isChecked="getTextAndChecked(session.one, 'others')['checked']"
                                src="/v2/img/assessment-tool/peer-avatar.png"></PersonAvatar>
                            </div>
                            <div :class="{'gray-filter': avatarDisabled('teacher')}" v-if="session.one.task?.toolCount?.teacher && !isStudent">
                              <PersonAvatar
                                :tagText="getTextAndChecked(session.one, 'teacher')['text']"
                                :isChecked="getTextAndChecked(session.one, 'teacher')['checked']"
                                src="/v2/img/assessment-tool/teacher-avatar.png"></PersonAvatar>
                            </div>
                          </div>
                        </template>
                      </div>
                    </q-item-label>
                    <q-item-label lines="1" caption v-if="item.caption">{{ item.path }}{{ item.caption }}</q-item-label>
                    <q-item-label lines="1" v-if="session.takeaway?._id && item.takeaway && session.one?.count">
                      <div class="row text-grey-7 items-center">
                        <q-icon name="o_mark_email_read" color="green"></q-icon>
                        <div class="q-px-xs text-caption q-pr-sm">{{ session.one.count.report }}/{{ session.membersCount }}</div>
                        <q-icon name="o_mark_email_read" color="red"></q-icon>
                        <div class="q-px-xs text-caption">{{ session.membersCount - session.one.count?.report }}/{{ session.membersCount }}</div>
                      </div>
                    </q-item-label>
                  </q-item-section>

                  <q-item-section side v-if="item.roster && session.one?.block">
                    <q-icon name="o_block" color="negative">
                      <q-tooltip max-width="260px">Participants will blocked once they end the session</q-tooltip>
                    </q-icon>
                  </q-item-section>
                  <q-item-section side v-if="item.takeaway && session.takeaway?._id">
                    <div class="row q-pl-md">
                      <q-chip
                        v-if="session.one?.count?.report && session.one?.count?.report === session.one?.count?.students"
                        color="green-2"
                        text-color="green"
                        square
                        :ripple="false"
                        >Complete</q-chip
                      >
                      <q-chip v-else color="red-2" text-color="red" square :ripple="false">Incomplete</q-chip>
                    </div>
                  </q-item-section>
                  <q-item-section side v-if="!item.assess">
                    <q-spinner-ball v-if="item.evaluation && item.loading" color="primary" size="sm" />
                    <q-icon v-else name="navigate_next" />
                  </q-item-section>
                  <q-tooltip v-if="item.disable && item.tips" max-width="300px">{{ item.tips }}</q-tooltip>
                </q-item>
                <!-- <template v-if="item.show && item.isCustom">
                    <InviteBtn v-if="item.type === 'substitute'" :detail="session.one" @cb="main" />
                  </template> -->
              </template>
            </template>
          </template>
        </SessionDetail>
        <q-separator inset></q-separator>
      </q-page>
    </q-page-container>
  </q-layout>
</template>

<script setup>
import {ref, onMounted, inject} from 'vue'
import SessionDetail from './ui/SessionDetail.vue'
import PersonAvatar from 'src/pages/account/assessment-tool/components/PersonAvatar.vue'
import {useRoute, useRouter} from 'vue-router'
const route = useRoute()
const router = useRouter()

import {pubStore} from 'stores/pub'
const pub = pubStore()
import {sessionStore} from 'stores/session'
const session = sessionStore()
import {slidesStore} from 'stores/slides'
const slides = slidesStore()

const attributes = ref({}),
  loading = ref(true)

const studentId = ref(route.params.sid)

const goBack = (hash) => {
  let path = route.query.back || '/home/<USER>'
  const query = route.query
  if (query?.back) delete query.back
  if (hash && query.back) {
    path = query.back.replace(/(#\w+)$/, hash)
  }
  //router.replace({path, query})
  router.replace(path)
}

const contentsType = inject('ContentsType')
const getAttributes = (item, key) => {
  let obj = contentsType[item.mode || item.type]
  if (key) {
    return obj?.[key]
  } else {
    return obj
  }
}
const upslidesFn = (o) => {
  slides.pull(o)
}

const takeawayFn = () => {
  const {_id} = session.one
  if (!_id) return
  if (pub.isStudent) {
    if (session.myTakeaway?._id) {
      router.push(`/account/takeaway/${_id}/student-view/${pub.user._id}`)
    } else {
      $q.notify({message: 'The takeaway report hasn’t been generated yet.'})
    }
  } else if (attributes.value.booking || session.takeaway?._id) {
    router.push(`/account/takeaway/${_id}`)
  } else {
    generateTakeaway()
  }
}

const generateTakeaway = (back) => {
  $q.dialog({
    message:
      "Would you like to generate the takeaways of this session for students?  Please notice that once takeaways are generated, students' answers will remain consistent with the time of generation and will not be updated further.",
    persistent: true,
    cancel: {
      label: 'Not this time',
      color: 'primary',
      'no-caps': true,
      outline: true,
      rounded: true,
    },
    ok: {
      label: 'Generate now',
      color: 'primary',
      'no-caps': true,
      rounded: true,
    },
  })
    .onOk(async () => {
      takeawayFn()
    })
    .onCancel(() => {
      if (back) {
        goBack('#ended')
      }
    })
}
import {date} from 'quasar'
const cancelBooking = async () => {
  let message = 'Please confirm that you want to cancel this booking?'
  const rs = await App.service('service-booking').get(session.one.booking)
  const diff = date.getDateDiff(new Date(rs.start), new Date(), 'seconds')
  if (diff < 2 * 3600) {
    message = 'Since the current session will start within 2 hours, your cancellation will result in a corresponding penalty. Are you sure you want to cancel?'
  } else {
    message = 'Frequent cancellations of session services can result in a lower ranking of your service offering referrals. Are you sure you want to cancel?'
  }
  $q.dialog({
    title: 'Confirm cancellation',
    message,
    cancel: true,
  }).onOk(async () => {
    if (!rs.cancel) {
      await App.service('service-booking').patch('cancel', {
        _id: session.one.booking,
      })
    }
    goBack()
  })
}
const doRemove = async () => {
  if (session.isInterviewConsultant) {
    cancelBooking()
  } else {
    $q.dialog({
      title: `Confirm cancel`,
      message: `Please confirm that you want to cancel : "${session.one.name}"?`,
      cancel: true,
    }).onOk(async () => {
      loading.value = true
      await App.service('session').remove(session.one._id)
      goBack()
      $q.notify({type: 'info', message: 'Successful.'})
    })
  }
}
const removeFn = async (item) => {
  if (item?.dialog && item?.disable) {
    const config = {
      message: item.tips,
      cancel: {
        label: 'I got it',
        noCaps: true,
        rounded: true,
        class: 'full-width',
      },
      ok: null,
    }
    if (session.isPremiumLecture) {
      config.ok = {label: 'Cancel', icon: 'o_check', noCaps: true, rounded: true, class: 'col'}
      config.cancel = {label: 'Not now', icon: 'o_arrow_back', outline: true, noCaps: true, rounded: true, class: 'col'}
    }
    $q.dialog(config).onOk(() => {
      doRemove()
    })
  } else {
    doRemove()
  }
}
const btns = ref([])
const listItems = ref([])
let disableProjectAndDashboardAndReopen

import {pointStore} from 'stores/point'
const pStore = pointStore()
async function main() {
  const {id, isPointMode} = route.params
  await session.get(id)

  attributes.value = getAttributes(session.one)
  disableProjectAndDashboardAndReopen = session.one.booking && session.status === 'Ended'
  if (route.query.isPointMode) {
    isPointMode.value = true
    await pStore.getClaimSetting()
  }

  btns.value = [
    {
      button: true,
      icon: 'o_remove',
      label: 'Cancel',
      fn: removeFn,
      show: true,
      dialog: true,
      disable: false,
      tips: session.isPremiumLecture
        ? 'You are about to cancel this session within 2 hours prior to the starting time, which results in any cost involved being deducted.'
        : 'You can no longer cancel the session within 12 hours prior to the start time',
    },
  ]

  listItems.value.push(
    {
      icon: 'o_remove_red_eye',
      label: 'Overview',
      to: session.viewRoute(session.one, 'overview'),
      overview: true,
      show: !session.isInterviewConsultant,
    },
    {
      icon: 'o_slideshow',
      kidIcon: 'slides',
      label: 'Slides',
      to: session.viewRoute(session.one, 'slides'),
      slides: true,
      show: true,
    },
    {
      icon: 'o_slideshow',
      kidIcon: 'slides',
      label: 'Questions',
      to: session.viewRoute(session.one, 'question'),
      question: true,
      show: true,
    },
    {icon: 'o_change_circle', label: 'Update slides', fn: upslidesFn, show: !session.one.booking && session.status === 'Scheduled', disable: false},
    {
      icon: 'o_devices',
      label: 'Projector view',
      href: session.toRoom(session.one, 't', route),
      show: true,
      disable: disableProjectAndDashboardAndReopen,
    },
    {
      icon: 'o_space_dashboard',
      label: 'Dashboard view',
      href: session.toRoom(session.one, 'd', route),
      show: true,
      disable: disableProjectAndDashboardAndReopen,
    },

    // {icon: 'o_article', label: 'Learning data', fn: () => {}, show: true, disable: false},
    {
      icon: 'o_format_list_bulleted',
      label: 'Class roster',
      href: session.toRoom(session.one, 'd', route, true),
      show: true,
      roster: true,
      disable: disableProjectAndDashboardAndReopen,
    },
    {
      icon: 'o_ballot',
      kidIcon: 'takeaway',
      label: session.takeaway?._id ? 'Takeaway' : 'Generate takeaway',
      fn: takeawayFn,
      show: true,
      takeaway: true,
    },
    {
      icon: 'o_pageview',
      label: route.query.back?.includes('sys') ? 'Evaluation' : 'Reflection',
      to: {path: `/reflect/${session.one.type}/${id}`, query: {back: route.fullPath}},
      show: true,
    },
    {
      icon: 'o_edit',
      label: 'Reschedule',
      fn: () => {
        router.push({
          path: `/com/reschedule/${id}`,
          query: {back: route.fullPath},
        })
      },
      show: false,
    }
  )

  loading.value = false
}
onMounted(main)
</script>
