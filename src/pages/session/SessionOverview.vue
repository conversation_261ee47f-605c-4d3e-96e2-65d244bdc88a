<template>
  <PubHeader />
  <div v-if="loading" class="text-center q-pa-xl text-grey">
    <q-spinner-ball color="primary" size="2em" class="full-width" />
  </div>
  <q-page-container class="bg-grey-1" v-else>
    <q-page :class="pub.isStudent ? 'student-theme' : 'q-pa-md pc-body'">
      <div v-if="pub.isStudent" class="q-pa-md student-bg rounded-borders-bottom-left-right-lg">
        <div class="pc-body q-py-sm">
          <div class="text-h5 text-weight-medium">
            <q-btn flat round dense size="lg" icon="arrow_back" @click="goBack"></q-btn>
            <q-icon size="lg" :name="`svguse:/v2/icons.svg#kid-overview`"> </q-icon>
            Overview
          </div>
        </div>
      </div>
      <div v-else class="text-h5 text-weight-medium">
        <q-btn flat round dense size="lg" icon="arrow_back" @click="goBack"></q-btn>
        Overview
      </div>
      <div class="pc-body q-pa-md">
        <OnePage v-if="session.task.mode == 'tool'" :isPreview="true" :assessmentToolOneId="session.task._id"></OnePage>
        <SessionOverviewItems v-else :content="session.task" :gradeOptions="gradeOptions" />
      </div>
    </q-page>
  </q-page-container>
</template>

<script setup>
/*
  imports 
*/
import {ref, onMounted} from 'vue'
import {useRoute, useRouter} from 'vue-router'
const route = useRoute()
const router = useRouter()

import {curriculumStore} from 'stores/curriculum'
const curriculum = curriculumStore()
import {sessionStore} from 'stores/session'
const session = sessionStore()

import SessionOverviewItems from './SessionOverviewItems.vue'
// import OnePage from 'pages/account/assessment-tool/OnePage.vue'

import {pubStore} from 'stores/pub'
const pub = pubStore()

const loading = ref(true)
const gradeOptions = ref(null)

const goBack = () => {
  router.replace(route.query.back || '/home/<USER>')
}

onMounted(async () => {
  await session.get(route.params.id)
  const {school, uid} = session.one
  gradeOptions.value = await curriculum.gradeOptions(school || uid)
  loading.value = false
})
</script>
