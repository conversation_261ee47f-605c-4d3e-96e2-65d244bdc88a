<template>
  <q-layout view="hHh LpR fFf">
    <PubHeader />
    <q-page-container class="pc-sm">
      <q-page class="q-pa-md">
        <div class="text-h5 text-weight-medium q-mb-md">
          <q-btn flat round dense size="lg" icon="arrow_back" @click="goBack"></q-btn>
          Slides
        </div>
        <div v-if="loading" class="text-center q-pa-xl text-grey">
          <q-spinner-ball color="primary" size="2em" class="full-width" />
        </div>
        <template v-else>
          <LibrarySlide @change="onSlideChanged" :lock-all="isScoreEnabledAndNotStartAndNotTeacher" :content="session.one" />
          <div class="row flex items-center">
            <q-space></q-space>
            <div class="text-h6 q-pa-sm">
              <span> {{ index + 1 }}/{{ session.one.pageNum }} </span>
            </div>
          </div>
          <div class="text-center rounded-borders border-1 q-pa-md q-mb-md" v-if="locked && !session.isMember">
            <div class="text-h5 q-pb-md">Please purchase to view the details</div>
            <q-btn rounded class="full-width" color="primary" label="Buy now" no-caps @click="buyFn"></q-btn>
          </div>
          <div class="rounded-borders border-1 q-my-md" v-else>
            <SlidesPageQuestion :question="session.question" :material="session.material" :numbersOfOutline="session.numbersOfOutline" />
          </div>
        </template>
      </q-page>
    </q-page-container>
  </q-layout>
</template>
<script setup>
import {ref, onMounted, computed} from 'vue'
import {useRoute, useRouter} from 'vue-router'
const route = useRoute()
const router = useRouter()
import {sessionStore} from 'stores/session'
const session = sessionStore()

import LibrarySlide from 'components/LibrarySlide.vue'
import SlidesPageQuestion from './SlidesPageQuestion.vue'

const loading = ref(true)
const index = ref(0)
const locked = ref(false)
const onSlideChanged = (i, slides) => {
  index.value = i
  locked.value = slides[i].locked
  session.pageId = slides[i].pageId
}

const isScoreEnabledAndNotStartAndNotTeacher = computed(() => {
  return session.one.questions.some((e) => e.scoreEnable) && new Date(session.one.start) > new Date() && !session.isOwner
})

const goBack = () => {
  router.replace(route.query.back || '/home/<USER>')
}

const buyFn = async () => {}

onMounted(async () => {
  const {id} = route.params
  if (!id) return
  await session.get(id)
  loading.value = false
})
</script>
