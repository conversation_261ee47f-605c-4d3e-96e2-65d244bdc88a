<template>
  <div class="q-pa-md">
    <BreadCrumbs title="Preview" v-if="$route.query.back"></BreadCrumbs>
    <div class="row no-wrap items-center text-h5 text-weight-medium">
      <div class="col-auto row items-center">
        <q-btn flat round dense size="lg" icon="arrow_back" @click="goBack" v-if="$route.query.back"></q-btn>
      </div>
      <div class="col items-center row">
        <!-- <q-icon v-if="serviceAuth?.isWorkshopAuth(session.one) && !session.one.booking" size="24px" name="svguse:/v2/icons.svg#premium">
          <q-tooltip>premium workshop</q-tooltip>
        </q-icon> -->
        <span class="q-pl-sm">{{ session.one.name || 'Untitled' }}</span>
      </div>
      <div class="row">
        <q-icon v-if="session.one.promotion" size="24px" name="svguse:/v2/icons.svg#promotion">
          <q-tooltip>promotional premium workshop</q-tooltip>
        </q-icon>
        <SessionStatus :status="session.status" v-if="session.status" />
      </div>
    </div>
    <OwnerBox class="q-mt-sm" :owner="session.one.owner" :updatedAt="session.one.createdAt" />

    <InviteBtn v-if="session.status" :detail="session.one" @cb="main" />
    <div class="q-my-md">
      <q-img
        spinner-color="white"
        fit="cover"
        class="fit rounded-borders-md"
        :ratio="16 / 7"
        :src="hashToUrl(session.packUserDoc?.snapshot?.cover || session.one.image) || '/v2/img/avatar.png'">
        <div class="absolute-bottom-left text-subtitle2 bg-transparent">
          <q-avatar size="1.8rem" :color="attributes.color" text-color="white" :icon="attributes.icon" />
        </div>
      </q-img>
    </div>
    <div class="text-left" v-if="!session.one?.booking">
      <SessionPrice
        :session="session.one"
        :isPointMode="!!route.query.isPointMode"
        :discountFinished="false"
        :is-promotion="session.one.promotion"></SessionPrice>
    </div>
    <ListItemCardChips :doc="session.one" />
  </div>

  <q-separator inset></q-separator>
  <q-list class="explicit-page-actions-list q-px-md">
    <q-expansion-item class="overflow-hidden" v-model="expanded" dense-toggle switch-toggle-side>
      <slot name="listItems"></slot>
      <slot name="btns"></slot>
    </q-expansion-item>
    <q-item clickable v-ripple @click="expanded = !expanded" v-if="!studentId">
      <q-item-section>
        <q-item-label class="row items-center justify-center">
          <q-icon :name="expanded ? 'arrow_drop_up' : 'arrow_drop_down'" size="2em"></q-icon>
          <div class="text-grey-7 q-pl-sm">{{ expanded ? 'Hide' : 'Show' }} Actions</div>
        </q-item-label>
      </q-item-section>
    </q-item>
  </q-list>
  <q-expansion-item
    class="shadow-2 rounded-borders-md overflow-hidden q-my-md"
    v-for="group in session.linkGroup.filter((v) => v.link.length > 0)"
    :key="group._id">
    <template v-slot:header>
      <q-item-section>
        {{ group.name }}
      </q-item-section>
      <q-item-section side>
        <div class="row items-center">
          <q-icon name="dynamic_feed"></q-icon>
          <span class="q-pl-sm">{{ group.link.length }} Lesson</span>
        </div>
      </q-item-section>
    </template>
    <template v-if="session.isUnit">
      <template v-for="link in group.link" :key="link._id">
        <q-separator />
        <ListItemCard v-if="link" :doc="link" :pid="session.one._id" dense />
      </template>
    </template>
    <template v-else>
      <q-expansion-item v-for="(link, i) in group.link" :key="i">
        <template v-slot:header>
          <q-item-section side>
            <q-avatar size="1.8rem" :color="UnitModeMap[link.task.mode].color" text-color="white" :icon="UnitModeMap[link.task.mode].icon" />
          </q-item-section>
          <q-item-section>
            <div class="column">
              <span>{{ link.name }}</span>
              <SessionEndBlock :doc="link" />
            </div>
          </q-item-section>
          <q-item-section side>
            <SessionStatus :status="session.statusFn(link)" />
          </q-item-section>
        </template>
        <div class="q-pa-md">
          <q-card bordered class="shadow-0 rounded-borders-md q-pa-md row">
            <q-btn
              class="col-xs-4 col-sm-3 col-md-3 q-pa-md"
              label="Enter class"
              stack
              flat
              icon="o_local_library"
              no-caps
              :href="session.toRoom(link, '', $route)"></q-btn>
            <q-btn
              class="col-xs-4 col-sm-3 col-md-3 q-pa-md"
              label="Overview"
              stack
              flat
              icon="o_visibility"
              no-caps
              :to="session.viewRoute(link, 'overview')"></q-btn>
            <q-btn
              v-if="link.task.mode.toLowerCase().includes('task')"
              class="col-xs-4 col-sm-3 col-md-3"
              label="Slides"
              stack
              flat
              icon="slideshow"
              no-caps
              :to="session.viewRoute(link, 'slides')"></q-btn>
          </q-card>
        </div>
      </q-expansion-item>
    </template>
  </q-expansion-item>
</template>

<script setup>
import {ref} from 'vue'
import BreadCrumbs from 'components/BreadCrumbs.vue'
import ListItemCard from './ListItemCard.vue'
import ListItemCardChips from './ListItemCardChips.vue'

import OwnerBox from 'components/detail/OwnerBox.vue'
import SessionEndBlock from './SessionEndBlock.vue'
import SessionPrice from 'components/SessionPrice.vue'
import SessionStatus from 'components/SessionStatus.vue'
import InviteBtn from 'src/pages/substitute/InviteBtn.vue'

import {useRoute, useRouter} from 'vue-router'
const route = useRoute()
const router = useRouter()

import {sessionStore} from 'stores/session'
const session = sessionStore()
const attributes = ref({}),
  expanded = ref(true)

const studentId = ref(route.params.sid)
defineProps(['main'])

const goBack = (hash) => {
  let path = route.query.back || '/home/<USER>'
  const query = route.query
  if (query?.back) delete query.back
  if (hash && query.back) {
    path = query.back.replace(/(#\w+)$/, hash)
  }
  //router.replace({path, query})
  router.replace(path)
}
</script>
