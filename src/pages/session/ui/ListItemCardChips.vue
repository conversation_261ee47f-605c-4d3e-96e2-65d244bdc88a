<template>
  <q-item-label class="text-weight-medium">
    <span class="text-yellow-9 q-pr-sm">{{ session._getTitle(doc) }}</span>
    <span class="text-primary">
      {{ curriculum.pubList[doc.curriculum]?.replace('curriculum', '') ?? 'Others' }}
    </span>
    <q-chip color="teal-1" text-color="primary">{{ doc.childSize + 1 }}</q-chip>
    {{ doc.sessionType.toFirstUpperCase() }}
  </q-item-label>
  <q-item-label v-if="!Acan.isEmpty(doc.subjects)">
    <b class="q-pr-sm">Subject</b>
    <q-chip v-for="(o, i) in doc.subjects" :key="i" :label="o.value" color="teal-1" text-color="primary"></q-chip>
  </q-item-label>
  <q-item-label v-if="doc.type && !Acan.isDefined(dense)">
    <b class="q-pr-sm"> Assessment type</b>
    <q-chip class="text-weight-medium" :ripple="false" :label="doc.unitType" color="teal-1" text-color="primary"></q-chip>
  </q-item-label>
  <q-item-label class="q-py-xs row items-center">
    <q-icon name="timer" color="primary"></q-icon>
    <span class="text-grey-7 q-pl-sm">Duration: {{ (new Date(doc.end).getTime() - new Date(doc.start).getTime()) / 60000 }} mins</span>
  </q-item-label>
  <SessionEndBlock :doc="doc" />
  <q-item-label class="q-py-xs row items-center" v-if="countDown">
    <q-icon name="timer" color="primary"></q-icon>
    <span class="text-red q-pl-sm">
      Enrollment ends in:
      {{ countDown }}
    </span>
    <q-icon v-if="doc.uid === pub.user?._id && (!doc.regNum || doc.regNum < doc.discount.size)" class="q-ml-sm" name="warning_amber" color="red" size="sm">
      <q-tooltip max-width="300px">Minimal number hasn't reached!</q-tooltip>
    </q-icon>
  </q-item-label>
</template>
<script setup>
import {ref, onMounted, onUnmounted, inject} from 'vue'
import {date} from 'quasar'
import SessionEndBlock from './SessionEndBlock.vue'
import {curriculumStore} from 'stores/curriculum'
const curriculum = curriculumStore()
import {pubStore} from 'stores/pub'
const pub = pubStore()
import {sessionStore} from 'stores/session'
const session = sessionStore()
const props = defineProps(['doc', 'dense'])

const countDownFormat = inject('countDownFormat')
const countDown = ref(null)
let cronId = null
onMounted(() => {
  const start = new Date(props.doc.start)
  cronId = setInterval(() => {
    const nt = Date.now()
    if (start > nt) countDown.value = countDownFormat((start - nt) / 1000)
    else {
      clearInterval(cronId)
      cronId = null
    }
  }, 1000)
})
onUnmounted(() => {
  if (cronId) {
    clearInterval(cronId)
  }
})
</script>
