<template>
  <q-card class="full-width">
    <q-item clickable class="q-pa-sm overflow-auto" :to="session.viewRoute(doc)">
      <div :class="$q.screen.gt.xs ? 'row' : 'column'" class="full-width">
        <div class="item-cover">
          <q-img :src="doc.image || '/v2/img/no-img.png'" class="rounded-borders" :ratio="16 / 9">
            <div class="absolute-bottom-left bg-transparent" style="padding: 8px">
              <q-avatar size="1.6rem" :color="UnitModeMap[doc.task.mode].color" text-color="white" :icon="UnitModeMap[doc.task.mode].icon" />
            </div>
          </q-img>
        </div>
        <div class="col column full-width q-pa-none relative-position" :class="$q.screen.gt.xs ? 'q-pl-md' : ''">
          <q-item-label class="q-py-xs text-h6 row">
            <span class="col ellipsis">
              {{ doc.name }}
            </span>
            <q-space></q-space>
            <q-badge color="primary" :label="session.statusFn(doc)" />
          </q-item-label>
          <ListItemCardChips :doc="doc" dense />
          <q-space></q-space>
          <div class="row items-center">
            <OwnerBox class="col" :owner="doc.owner" />
            <q-space></q-space>
            <div class="q-gutter-sm" v-if="true">
              <q-btn rounded label="Enter Class" color="primary" :to="session.viewRoute(doc)" no-caps></q-btn>
            </div>
          </div>
        </div>
      </div>
    </q-item>
  </q-card>
</template>
<script setup>
import OwnerBox from 'components/detail/OwnerBox.vue'
import ListItemCardChips from './ListItemCardChips.vue'
// import SessionTime from 'components/SessionTime.vue'
import {sessionStore} from 'stores/session'
const session = sessionStore()
defineProps(['doc', 'showBtn'])
</script>
<style scoped>
.item-cover {
  width: 22rem;
}
body.screen--xs .item-cover {
  width: 100%;
  padding-bottom: 1rem;
}
</style>
