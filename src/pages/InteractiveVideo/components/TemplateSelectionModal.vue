<template>
  <q-dialog ref="dialogRef">
    <q-card class="dialog-height q-pa-md">
      <div class="q-pb-md text-subtitle1">Choose {{ chooseCurrType === 'academic' ? 'Curriculum' : 'Service type' }}</div>
      <div v-if="loading">
        <div class="q-pa-md flex flex-center">
          <q-spinner size="40px" color="primary" />
        </div>
      </div>
      <div v-else>
        <q-card-section style="" class="scroll">
          <div class="flex items-center">
            <q-option-group :options="[{label: 'Academic', value: 'academic'}]" type="radio" v-model="chooseCurrType" :disable="!hasNormalCurr" />
            <q-option-group :options="[{label: 'Service', value: 'service'}]" type="radio" v-model="chooseCurrType" />
          </div>

          <!-- <pre>{{ selectedOption }}</pre> -->
          <!-- <pre>acList: {{ acList?.length }}</pre> -->

          <q-select
            v-if="chooseCurrType === 'academic' && acList?.length"
            v-model="selectedOption"
            :options="acList"
            option-label="label"
            option-value="value"
            label="Select Template"
            filled
            class="q-my-md"
            menu-anchor="bottom left"
            menu-self="top left">
            <template v-slot:option="scope">
              <q-item v-bind="scope.itemProps" class="column rounded-borders q-ma-xs q-pa-xs" style="border: 1px solid #ccc">
                <div class="">
                  {{ scope.opt.name }}
                  <q-chip v-if="scope.opt._id == defaultTaskTemplateId" color="primary" dense outline class="q-my-sm" text-color="white">Default</q-chip>
                </div>
                <div class="q-pl-md text-caption">
                  {{ getCurriculumLabel(scope.opt) }}
                  <q-chip color="grey" dense outline class="q-my-sm text-capitalize" text-color="white">{{ mode }}</q-chip>
                </div>
              </q-item>
            </template>

            <template v-slot:selected>
              <q-item v-bind="selectedOption" class="column">
                <div class="">
                  {{ selectedOption?.name ?? '-' }}
                  <q-chip v-if="selectedOption?._id == defaultTaskTemplateId" color="primary" dense outline class="q-my-sm" text-color="white">Default</q-chip>
                </div>
                <div class="q-pl-md text-caption">
                  {{ getCurriculumLabel(selectedOption) }}
                  <q-chip color="grey" dense outline class="q-my-sm text-capitalize" text-color="white">{{ mode }}</q-chip>
                </div>
              </q-item>
            </template>
          </q-select>

          <q-select
            v-if="chooseCurrType === 'service' && serviceList?.length"
            v-model="selectedOption"
            :options="serviceList"
            option-label="label"
            option-value="value"
            label="Select Service Type"
            filled
            class="q-my-md"
            menu-anchor="bottom left"
            menu-self="top left" />
        </q-card-section>

        <q-card-actions align="center">
          <div class="flex justify-between items-center q-mb-md full-width">
            <q-btn
              unelevated
              rounded
              class="text-primary bg-white"
              label="Cancel"
              icon="arrow_back"
              style="border: 1px solid #ccc; width: calc(50% - 1rem)"
              @click="onDialogCancel"
              no-caps />
            <q-btn style="width: calc(50% - 1rem)" unelevated rounded color="teal-4" label="Confirm" icon="done" @click="onOKClick" no-caps />
          </div>
          <q-btn
            v-if="isUserEditing"
            class="full-width"
            unelevated
            rounded
            color="teal-4"
            label="Go to template setting"
            @click="router.push({path: `/account/academic-setting/subject/templateSetting`, query: {back: route.fullPath}})"
            no-caps />
        </q-card-actions>
      </div>
    </q-card>
  </q-dialog>
</template>

<script setup>
import {useDialogPluginComponent} from 'quasar'
import {computed, onMounted, ref, watch} from 'vue'
import {pubStore} from 'stores/pub'
import {subjectsStore} from 'stores/subjects'
import {useRoute, useRouter} from 'vue-router'
import useUnitPlanTemplate from 'src/composables/account/unit-plan-template/useUnitPlanTemplate'
import useSchool from 'src/composables/common/useSchool'

import {acList, serviceList, selectedOption} from 'src/composables/useGlobalStates'

const {dialogRef, onDialogOK, onDialogCancel} = useDialogPluginComponent()
const pub = pubStore()
const subjects = subjectsStore()
const router = useRouter()
const route = useRoute()

const chooseCurrType = ref('academic')

const {isUserEditing} = useSchool()
const {
  getCurriculum,
  curriculumList: _curriculumList,
  curriculumIds,
  getOneById,
  defaultTaskTemplateId,
  pdTaskTemplate,
  unitPlanTemplateUserList,
} = useUnitPlanTemplate({isInit: true})

const props = defineProps({
  currentUnitData: {
    type: Object,
    default: null,
  },
})

const loading = ref(true)

const getCurriculumLabel = (template) => {
  const curriculum = template?.curriculum || ''
  const target = _curriculumList.value.find((e) => e.code === curriculum)
  if (!target) return ''
  return target?.name
}

const defaultTplId = ref('')
const mode = ref('task')

watch([unitPlanTemplateUserList, _curriculumList], async () => {
  if (!acList.length) {
    loading.value = true
    acList.value = []
    await getUnitPlanTemplateUserList()
    loading.value = false
  }
})
const getUnitPlanTemplateUserList = async () => {
  const data = unitPlanTemplateUserList.value.filter((e) => curriculumIds.value.includes(e.curriculum))
  if (_curriculumList.value.length && data?.length) {
    // Map over data to create an array of promises
    const promises = data.map(async (unitPlanTemplateUser) => {
      const taskId = unitPlanTemplateUser?.tpl?.task
      const taskTemplateData = await getOneById(taskId)
      const values = acList.value.map((e) => e.value)
      if (!values.includes(taskId) && !['others', 'pd'].includes(taskTemplateData?.curriculum)) {
        acList.value.push({
          ...taskTemplateData,
          value: taskId,
          label: taskTemplateData?.name,
        })
      }
    })
    await Promise.all(promises)
  }
  await setSelectedOption()
}

const getServices = async () => {
  serviceList.value = await subjects.getOptions('1', 'pd')
}

async function setSelectedOption() {
  const school = pub.user?.schoolInfo?._id
  const query = {}
  query.school = {$in: ['1', school || pub.user?._id]}
  query.$limit = 2000

  let tpl = await App.service('conf-user').get('TaskTplDefault')
  //for service
  await App.service('unit-tpl').get('task')

  if (tpl && tpl.val) {
    Object.keys(tpl.val).forEach((key) => {
      if (key.indexOf(':') == -1) {
        if ((!school && key == 'personal') || (school && key === school)) {
          defaultTplId.value = tpl.val[key]
        }
      }
    })
  }
  const rs = {data: unitPlanTemplateUserList.value}
  if (acList.value?.length) {
    if (defaultTplId.value) {
      const defaultTplExist = rs.data.find((d) => d.tpl?.['task']?._id === defaultTplId.value)
      if (defaultTplExist) {
        selectedOption.value = acList.value.find((e) => e._id === defaultTplId.value)
      }
    }
  }
  // console.log(props.currentUnitData)
  if (props.currentUnitData) {
    const curriculum = props.currentUnitData?.curriculum || ''
    if (curriculum !== 'pd') {
      const target = acList.value.find((e) => e.curriculum === curriculum)
      // console.warn('target', target)
      if (target) {
        selectedOption.value = target
      } else {
        selectedOption.value = acList.value[0]
      }
    } else {
      // const target = serviceList.value.find(e => e.value === curriculum)
      selectedOption.value = serviceList.value[0]
    }
    // console.log(selectedOption.value)
  }
}
const hasNormalCurr = computed(() => _curriculumList.value?.find((item) => item.value !== 'pd'))

onMounted(async () => {
  loading.value = true
  await getCurriculum(true)
  await Promise.all([getUnitPlanTemplateUserList(), getServices()]) // Wait for both functions

  if (props.currentUnitData?.curriculum === 'pd' || !hasNormalCurr.value) {
    chooseCurrType.value = 'service'
  } else {
    chooseCurrType.value = 'academic'
  }

  loading.value = false // Set loading to false when all API calls complete

  // dev: delete test unit
  // await App.service('unit').remove('6746ce703a6fecac4eb59568')
})

const onOKClick = () => {
  const data = selectedOption.value
  if (chooseCurrType.value === 'service') {
    data.curriculum = 'pd'
    // pdTaskId
    data._id = pdTaskTemplate.value._id
  }
  console.log(data)
  onDialogOK({data})
}

watch(chooseCurrType, (newCurr) => {
  if (newCurr === 'academic') {
    selectedOption.value = acList.value[0]
  } else if (newCurr === 'service') {
    selectedOption.value = serviceList.value[0]
  }
})
</script>

<style scoped>
.dialog-height {
  width: clamp(32rem, 50%, 60vh);
  overflow-y: auto;
}
</style>
