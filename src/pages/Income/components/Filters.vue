<template>
  <div class="fit">
    <q-btn flat color="primary" label="Filter" no-caps :icon-right="showMenu ? 'arrow_drop_up' : 'arrow_drop_down'">
      <q-menu max-width="770px" @show="onMenuShow" @hide="onMenuHide" fit :offset="[0, 4]" class="shadow-3 rounded-borders-md">
        <q-card>
          <q-card-section class="q-pa-sm q-pl-md">
            <div class="q-py-sm subtitle-2">Period</div>
            <q-input outlined dense v-model="formattedDateRange" readonly class="cursor-pointer bg-white solid-border" style="width: 250px">
              <template v-slot:prepend @show="clearDateFilter">
                <q-icon name="event" class="cursor-pointer">
                  <q-popup-proxy cover transition-show="scale" transition-hide="scale" @show="clearDateFilter">
                    <q-date v-model="filters.dateRange" range @update:model-value="onFilterUpdate">
                      <div class="row items-center justify-end">
                        <q-btn v-close-popup label="Close" color="primary" flat />
                      </div>
                    </q-date>
                  </q-popup-proxy>
                </q-icon>
              </template>
            </q-input>
          </q-card-section>

          <q-card-section v-for="item in options" :key="item.label" class="q-pt-none">
            <div class="q-py-sm subtitle-2">{{ item.label }}</div>
            <q-option-group inline :options="item.list" type="checkbox" v-model="filters[item.value]" @update:modelValue="onFilterUpdate">
              <template v-slot:label="opt">
                <div class="row items-center">
                  <span class="q-mr-lg">{{ opt.label }}</span>
                </div>
              </template>
            </q-option-group>
          </q-card-section>
        </q-card>
      </q-menu>
    </q-btn>
  </div>
</template>

<script setup>
import {ref, computed} from 'vue'

defineProps({
  options: Array, // Receives 'Status', 'Category', etc. from const.js
})
const emit = defineEmits(['update:modelValue'])

const filters = defineModel()

const showMenu = ref(false)

const formattedDateRange = computed(() => {
  const dateRange = filters.value.dateRange
  if (!dateRange) return ''
  if (typeof dateRange === 'string') return dateRange.replace(/\//g, '-')
  return `${dateRange.from.replace(/\//g, '-')} - ${dateRange.to.replace(/\//g, '-')}`
})

const clearDateFilter = () => {
  filters.value.dateRange = null
  onFilterUpdate()
}

const onMenuHide = () => {
  showMenu.value = false
}

const onMenuShow = () => {
  showMenu.value = true
}

const onFilterUpdate = () => {
  emit('update:modelValue', filters.value)
}
</script>
<style scoped>
.solid-border .q-field__control {
  border: 1px solid #c03e3e !important; /* change #ccc to your desired color */
}
</style>
