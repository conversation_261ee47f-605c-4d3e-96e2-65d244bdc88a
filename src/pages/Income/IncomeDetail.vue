<template>
  <q-layout view="hHh LpR fFf" class="bg-white">
    <q-header elevated class="bg-teal-1 text-black" height-hint="98">
      <q-toolbar>
        <q-btn flat round dense icon="navigate_before" @click="router.back()"></q-btn>
        <q-toolbar-title class="text-h6"> Income Details </q-toolbar-title>
      </q-toolbar>
    </q-header>

    <q-page-container class="pc-sm page-box">
      <q-page class="q-pa-md q-mt-md">
        <q-breadcrumbs>
          <q-breadcrumbs-el label="Account" to="/account/info" />
          <q-breadcrumbs-el label="Income" to="/home/<USER>" />
          <q-breadcrumbs-el label="Details" class="text-grey-6" />
        </q-breadcrumbs>

        <div class="text-h6 q-mt-sm q-mb-md">
          <q-btn flat round dense size="lg" icon="arrow_back" @click="router.back()"></q-btn>
          Income Details
        </div>

        <q-banner v-if="detail?.eventDetails?.takeaway" inline-actions class="rounded-borders shadow-1 items-center q-pa-sm">
          <div class="row items-center">
            <q-avatar icon="info" size="lg" text-color="primary" />

            <div class="q-ml-xs" style="font-size: 14px; color: #4e4e4e">
              You're just one step away from getting your income! Send takeaway report to proceed
            </div>
            <q-btn
              class="q-ml-sm"
              unelevated
              rounded
              color="primary"
              label="Send Takeaway"
              style="text-transform: capitalize; font-size: 14px; font-weight: 400; padding: 6px 16px"
              @click="sendToGenerateTakeaway(detail?.eventDetails?.takeaway)">
            </q-btn>
          </div>
        </q-banner>

        <div class="q-pb-xl">
          <div v-if="loading" class="text-center q-pa-xl">
            <q-spinner-dots color="primary" size="40px" />
          </div>

          <div v-else-if="detail" class="rounded-borders-sm q-mt-md q-border-1 q-pa-md bg-white shadow-1">
            <div class="row underline q-py-md items-center">
              <div class="col-2 text-grey-6">Updates</div>
              <div class="col-10" :class="detail.tab === 'earn' ? (detail?.value > 0 ? 'text-positive' : 'text-negative') : 'text-blue'">
                USD {{ detail?.value > 0 ? '+' : '' }}{{ (detail?.value / 100).toFixed(2) }}
              </div>
            </div>

            <div class="row underline q-py-sm items-center">
              <div class="col-2 text-grey-6">Status</div>
              <div class="col-10">
                <div class="row items-center" v-if="detail?.status == 0">
                  <div class="text-orange">Expected</div>
                  <q-avatar v-if="detail?.eventDetails?.takeaway" icon="help_outline" size="lg" text-color="grey-8" style="cursor: pointer">
                    <q-tooltip anchor="top middle" self="top middle" :offset="[0, 20]" style="background-color: #1d2129; font-size: 10px">
                      Please send takeaway report to receive income.
                    </q-tooltip>
                  </q-avatar>
                </div>

                <span class="text-blue" v-if="detail?.status == 1">Actual</span>
              </div>
            </div>
            <div v-if="detail.category === 'teaching_service'" class="row underline q-py-md items-center">
              <div class="col-2 text-grey-6">Session tracking</div>
              <div class="col-10 text-blue">{{ formatTitleCase(detail?.eventDetails?.sessionStatus) }}</div>
            </div>

            <div class="row underline q-py-md items-center">
              <div class="col-2 text-grey-6">Category</div>
              <div class="col-10">{{ incomeLabel(detail) }}</div>
            </div>

            <div class="row items-center underline">
              <div class="col-2 text-grey-6">Event</div>
              <div class="col-10">
                <div class="row items-center no-wrap full-width q-py-sm">
                  <div class="col row items-center no-wrap cursor-pointer">
                    <q-img
                      v-if="detail.eventDetails?.cover"
                      :src="detail.eventDetails?.cover"
                      width="76px"
                      height="50px"
                      fit="cover"
                      class="rounded-borders-sm q-mr-sm" />
                    <div>{{ detail.eventDetails?.name }}</div>
                  </div>

                  <q-btn v-if="childRecords.length > 0" flat dense round :icon="isMenuOpen ? 'arrow_drop_up' : 'arrow_drop_down'">
                    <q-menu
                      v-model="isMenuOpen"
                      fit
                      anchor="bottom right"
                      self="top right"
                      transition-show="jump-down"
                      transition-hide="jump-up"
                      :offset="[10, 14]">
                      <q-list separator style="min-width: 630px; background-color: white">
                        <q-item v-for="child in childRecords" :key="child._id" clickable v-ripple @click="toDetail(child)" class="q-pa-sm">
                          <q-item-section avatar>
                            <q-img :src="child.eventDetails?.cover" width="76px" height="50px" fit="cover" class="rounded-borders-sm" />
                          </q-item-section>
                          <q-item-section>
                            {{ child.eventDetails?.name }}
                          </q-item-section>
                          <q-item-section>
                            <span class="text-blue" v-if="child.status === 1">Actual</span>
                            <span class="text-orange" v-if="child.status === 0">Expected</span>
                          </q-item-section>
                          <q-item-section :class="child.value > 0 ? 'text-positive' : 'text-negative'">
                            USD {{ child.value > 0 ? '+' : '' }}{{ (child.value / 100).toFixed(2) }}
                          </q-item-section>
                          <q-item-section side>
                            <q-icon name="chevron_right" />
                          </q-item-section>
                        </q-item>
                      </q-list>
                    </q-menu>
                  </q-btn>
                </div>
              </div>
            </div>

            <div class="row underline q-py-md items-center">
              <div class="col-2 text-grey-6">Action on</div>
              <div class="col-10">{{ date.formatDate(detail?.updatedAt, 'MM/DD/YYYY HH:mm:ss') }}</div>
            </div>
          </div>
        </div>
      </q-page>
    </q-page-container>
  </q-layout>

  <MaterialDialog class="z-max" v-model="isPreviewDialogShow">
    <ImgPreview :item="currentPreviewData" style="width: 90%; height: 90%" :isPreviewBig="true" :isDialog="true" />
  </MaterialDialog>
</template>

<script setup>
import {onMounted, ref, watch} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {date} from 'quasar'
import {incomeLabel} from './consts'
import ImgPreview from 'src/components/material/ImgPreview.vue'
import MaterialDialog from 'src/pages/class/components/MaterialDialog.vue'

const route = useRoute()
const router = useRouter()

const detail = ref(null)
const loading = ref(true)
const childRecords = ref([])
const isPreviewDialogShow = ref(false)
const currentPreviewData = ref(null)
const isMenuOpen = ref(false)

const hashToUrl = (hash) => hash

function onPreview(item) {
  currentPreviewData.value = item
  isPreviewDialogShow.value = true
}

const fetchDetail = async () => {
  loading.value = true
  childRecords.value = []
  try {
    detail.value = await App.service('income-log').get(route.params.id)

    console.log('Fetched detail:', detail.value)

    if (detail.value && detail.value.isParent) {
      let res = await App.service('income-log').find({query: {parentId: detail.value._id}})
      childRecords.value = res?.data || []
    }
  } catch (error) {
    console.error('Failed to fetch details:', error)
  } finally {
    loading.value = false
  }
}

function formatTitleCase(inputString) {
  if (!inputString) {
    return ''
  }
  return inputString
    .split('_')
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ')
}

const sendToGenerateTakeaway = (takeaway) => {
  router.push({
    path: `/account/takeaway/${takeaway.session}/view/${takeaway.uid}`,
    query: {back: route.path},
  })
}

const toDetail = (item) => {
  router.push({
    path: `/account/earning-rewards/income/${item._id}`,
    query: {backUrl: `/account/earning-rewards/income`},
  })
}

watch(
  () => route.params.id,
  (newId) => {
    if (newId) {
      fetchDetail()
    }
  },
  {immediate: true}
)
</script>

<style lang="scss" scoped>
.page-box {
  padding-left: 0 !important;
}
.underline {
  border-bottom: 1px solid #e0e0e0;
}
.cursor-pointer {
  cursor: pointer;
}
.q-expansion-item {
  padding: 0;
}
</style>
