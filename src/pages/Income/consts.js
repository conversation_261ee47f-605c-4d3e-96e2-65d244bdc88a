export const incomeLabel = (item) => {
  // ... (this function remains the same)
  const labels = {
    library_content: 'Library Content',
    new_prompt_content: 'New Prompt Content',
    self_study_content: 'Self Study Content',
    premium_contents_unit_module: 'Premium Unit Module',
    premium_contents_task_sessions: 'Premium Task Sessions',
    premium_content_audit: 'Premium Content Audit',
    teaching_service: 'Teaching Service',
    correcting_service: 'Correcting Service',
    associated_task: 'Associated Task',
    certificate: 'Certificate',
  }
  return labels[item.category] || item.category
}

export const incomeFilterOptions = [
  {
    label: 'Status',
    value: 'status',
    list: [
      {label: 'Expected', value: 0}, // Removed 'count'
      {label: 'Actual', value: 1}, // Removed 'count'
    ],
  },
  {
    label: 'Category',
    value: 'category',
    list: [
      {label: 'Library Content', value: 'library_content'},
      {label: 'New Prompt Content', value: 'new_prompt_content'},
      {label: 'Self Study Content', value: 'self_study_content'},
      {label: 'Premium Unit Module', value: 'premium_contents_unit_module'},
      {label: 'Premium Task Sessions', value: 'premium_contents_task_sessions'},
      {label: 'Premium Content Audit', value: 'premium_content_audit'},
      {label: 'Teaching Service', value: 'teaching_service'},
      {label: 'Correcting Service', value: 'correcting_service'},
      {label: 'Associated Task', value: 'associated_task'},
      {label: 'Certificate', value: 'certificate'},
    ],
  },
  {
    label: 'Type',
    value: 'tab',
    list: [{label: 'Claimed', value: 'claim'}],
  },
]
