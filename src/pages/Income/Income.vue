<template>
  <q-layout view="hHh LpR fFf" class="bg-white">
    <q-page-container class="pc-sm q-pb-xl">
      <q-page class="q-pa-md" style="min-height: auto">
        <div class="rounded-borders-sm q-mt-md q-border-1 q-pa-md bg-white shadow-1">
          <div class="row justify-end">
            <div class="col-2 q-ml-xl">
              <Filters class="" v-model="filters" :options="filterOptions" @update:modelValue="resetFn" />
            </div>
          </div>

          <div class="q-mt-lg">
            <div class="row text-grey-6 table-header">
              <div class="col-3">Earning method/Claimed</div>
              <div class="col-2">Income(USD)</div>
              <div class="col-2">Status</div>
              <div class="col-3">Time</div>
              <div class="col-2">Balance(USD)</div>
            </div>
            <q-separator class="q-mt-sm" />

            <div v-if="loading && incomeData.length === 0" class="text-center q-pa-lg">
              <q-spinner-dots color="primary" size="40px" />
            </div>

            <div v-else-if="!loading && incomeData.length === 0">
              <NoData message="No income yet!" messageColor="text-grey" />
              <div class="text-center">
                <div class="text-grey-6">There is no income history.</div>
              </div>
            </div>

            <div v-else class="scrollable-table-area" ref="scrollArea">
              <div class="row items-center q-py-sm" v-for="item in incomeData" :key="item._id">
                <div class="col-3 text-primary text-capitalize cursor-pointer" @click="toDetail(item)">
                  {{ incomeLabel(item) }}
                </div>
                <div class="col-2" :class="item?.value > 0 ? 'text-positive' : 'text-negative'">
                  {{ item?.value > 0 ? '+' : '' }}{{ (item?.value / 100).toFixed(2) }}
                </div>
                <div class="col-2">
                  <span class="text-orange" v-if="item?.status == 0">Expected</span>
                  <span class="text-blue" v-if="item?.status == 1">Actual</span>
                </div>
                <div class="col-3">{{ date.formatDate(item.updatedAt, 'MM/DD/YYYY HH:mm:ss') }}</div>
                <q-avatar v-if="item?.needsWalletSync" icon="help_outline" size="lg" text-color="grey-8" style="cursor: pointer">
                  <q-tooltip anchor="top middle" self="top middle" :offset="[0, 20]">Processing</q-tooltip>
                </q-avatar>

                <template v-else>
                  {{ (item?.total / 100).toFixed(2) }}
                </template>
                <div class="col-12"><q-separator class="q-mt-sm" /></div>
              </div>
            </div>
          </div>
        </div>

        <div class="text-primary q-mt-lg cursor-pointer" v-if="incomeData.length < totalRecords" @click="loadMoreData">More</div>
      </q-page>
    </q-page-container>
  </q-layout>
</template>

<script setup>
import {onMounted, ref, computed, nextTick} from 'vue'
import {useRouter, useRoute} from 'vue-router'
import {date} from 'quasar'
import Filters from './components/Filters.vue'
import {pubStore} from 'stores/pub'
import NoData from '../../components/pub/NoData.vue'
import {incomeLabel, incomeFilterOptions} from './consts'

const pub = pubStore()
const router = useRouter()
const route = useRoute()

const incomeData = ref([])
const totalRecords = ref(0)
const loading = ref(false)
const skip = ref(0)
const scrollArea = ref(null)
const limit = 8

const filters = ref({
  dateRange: null,
  status: [], // Default to empty
  category: [], // Default to empty
  tab: [], // Default to empty for the new "Type" filter
})

const filterOptions = incomeFilterOptions

const loadMoreData = async () => {
  await fetchData()
  await nextTick()
  if (scrollArea.value) {
    scrollArea.value.scrollTop = scrollArea.value.scrollHeight
  }
}

const fetchData = async () => {
  if (loading.value) return
  loading.value = true
  try {
    let filterData = Acan.clone(filters.value)

    const query = {
      $limit: limit,
      $skip: skip.value,
      $sort: {updatedAt: -1},
      isParent: {$ne: false},
    }

    if (filterData.status.length > 0) {
      query.status = {$in: filterData.status}
    }

    if (filterData.category.length > 0) {
      query.category = {$in: filterData.category}
    }

    if (filterData.tab.includes('claim')) {
      query.tab = 'claim'
    }

    if (filterData.dateRange && filterData.dateRange.from && filterData.dateRange.to) {
      const startDate = new Date(filterData.dateRange.from)
      const endDate = new Date(filterData.dateRange.to)
      endDate.setHours(23, 59, 59, 999)
      query.updatedAt = {
        $gte: startDate.toISOString(),
        $lte: endDate.toISOString(),
      }
    }

    const result = await App.service('income-log').find({query})

    incomeData.value.push(...result.data)
    totalRecords.value = result.total
    skip.value += limit
  } catch (error) {
    console.error('Failed to fetch income data:', error)
  } finally {
    loading.value = false
  }
}

const toDetail = (item) => {
  router.push({
    path: `income/${item._id}`,
    query: {backUrl: route.fullPath},
  })
}

const resetFn = () => {
  incomeData.value = []
  skip.value = 0
  totalRecords.value = 0
  fetchData()
}

onMounted(fetchData)
</script>

<style scoped>
.scrollable-table-area {
  max-height: 350px;
  overflow-y: auto;
}
.table-header {
  position: sticky;
  top: 0;
  background: white;
  z-index: 10;
}
.q-page-container {
  max-width: none !important;
}
@media (max-width: 1023px) {
  .full-width-table {
    margin-left: 0;
  }
}
</style>
