<template>
  <q-dialog :maximized="$q.screen.lt.sm" ref="dialogRef" @hide="onDialogHide">
    <q-card class="q-dialog-plugin criteria-dialog" style="width: 700px; max-width: 80vw">
      <q-list v-if="!loading" class="q-pa-md">
        <NoData v-if="Acan.isEmpty(list)" />
        <q-item v-else v-for="(o, i) in list" :key="i">
          <q-item-section side> Session {{ i + 1 }} </q-item-section>
          <q-item-section class="text-right">
            <q-item-label> expires on {{ date.formatDate(o.expired, TIME_FORMAT_NZ + ' (ddd)') }} </q-item-label>
          </q-item-section>
        </q-item>
        <q-separator class="q-ma-md" />
        <q-item>
          <q-btn class="col" color="primary" rounded label="I got it" no-caps icon="done" @click="onDialogOK"> </q-btn>
        </q-item>
      </q-list>
    </q-card>
  </q-dialog>
</template>
<script setup>
import {ref, onMounted} from 'vue'
import {date, useDialogPluginComponent} from 'quasar'
defineEmits([...useDialogPluginComponent.emits])
const {dialogRef, onDialogHide, onDialogOK, onDialogCancel} = useDialogPluginComponent()
const props = defineProps(['packUser'])
const list = ref([])
const loading = ref(true)

onMounted(async () => {
  $q.loading.show()
  const {data} = await App.service('service-pack-user-data').find({query: {$limit: 1000, packUser: props.packUser, status: 0}})
  await sleep(1000)
  list.value = data
  loading.value = false
  $q.loading.hide()
})
</script>
