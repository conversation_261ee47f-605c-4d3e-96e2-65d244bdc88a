<template>
  <q-dialog :maximized="$q.screen.lt.sm" ref="dialogRef" @hide="onDialogHide">
    <q-card class="q-dialog-plugin criteria-dialog" style="width: 800px; max-width: 80vw">
      <q-list v-if="!loading" class="q-pa-md">
        <NoData v-if="Acan.isEmpty(list)" />
        <template v-else v-for="(o, i) in list" :key="i">
          <UnitItemCard :doc="o" dense class="q-ma-sm">
            <template v-slot:button>
              <!-- <q-btn color="primary" label="View" no-caps></q-btn> -->
            </template>
          </UnitItemCard>
        </template>
        <!-- <q-separator class="q-ma-md" /> -->
        <q-item>
          <q-btn class="col" color="primary" rounded label="I got it" no-caps icon="done" @click="onDialogOK"> </q-btn>
        </q-item>
      </q-list>
    </q-card>
  </q-dialog>
</template>
<script setup>
import UnitItemCard from './UnitItemCard.vue'

import {ref, onMounted} from 'vue'
import {date, useDialogPluginComponent} from 'quasar'
defineEmits([...useDialogPluginComponent.emits])
const {dialogRef, onDialogHide, onDialogOK, onDialogCancel} = useDialogPluginComponent()
const props = defineProps(['packUser'])
const list = ref([])
const loading = ref(true)

onMounted(async () => {
  $q.loading.show()
  const {snapshot, tasks, taskIndex} = await App.service('service-pack-user').get(props.packUser)
  await sleep(1000)
  list.value.length = 0
  const arr = []
  for (const id in snapshot.linkSnapshot) {
    if (!tasks.includes(id)) continue
    arr.push(snapshot.linkSnapshot[id])
  }
  list.value = arr
  loading.value = false
  console.log(tasks, taskIndex)

  $q.loading.hide()
})
</script>
