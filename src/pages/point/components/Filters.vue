<template>
  <div class="fit">
    <div class="row q-col-gutter-x-lg">
      <div class="col-12">
        <q-btn flat color="primary" label="Filter" no-caps :icon-right="showMenu ? 'arrow_drop_up' : 'arrow_drop_down'">
          <q-menu max-width="770px" @show="onMenuShow" @hide="onMenuHide" fit :offset="[0, 4]" class="shadow-3 rounded-borders-md">
            <q-card>
              <q-card-section class="scroll" v-for="item in options" :key="item.label">
                <div class="q-py-md text-h6">{{ item.label }}</div>
                <q-option-group inline :options="item.list" type="checkbox" v-model="filters[item.value]" @update:modelValue="onFilterUpdate">
                  <template v-slot:label="opt">
                    <div class="row items-center">
                      <span class="q-mr-lg"
                        >{{ opt.label }}<span class="q-ml-sm text-grey">{{ opt.count }}</span>
                      </span>
                    </div>
                  </template>
                </q-option-group>
              </q-card-section>
            </q-card>
          </q-menu>
        </q-btn>
      </div>
    </div>
    <div></div>
  </div>
</template>
<script setup>
/*
  imports
*/
import {ref, onMounted, watch, computed} from 'vue'

/*
  props && emit
*/
const props = defineProps({
  options: Array,
})

const emit = defineEmits(['update:modelValue'])
const filters = defineModel()

/*
  consts
*/

const showMenu = ref(false)

// const filters = ref({})

// props.options.forEach((item) => {
//   filters.value[item.value] = []
// })

/*
  computeds
*/

/*
  methods
*/

const onMenuHide = (evt) => {
  showMenu.value = false
}

const onMenuShow = (evt) => {
  showMenu.value = true
}

const onFilterUpdate = (val) => {
  emit('update:modelValue', filters.value)
}
</script>
