<template>
  <q-layout view="hHh LpR fFf" class="bg-white">
    <q-header elevated class="bg-teal-1 text-black" height-hint="98">
      <q-toolbar>
        <q-btn flat round dense icon="navigate_before" @click="router.back()"></q-btn>
        <q-toolbar-title class="text-h6"> Claiming cash details </q-toolbar-title>
      </q-toolbar>
    </q-header>
    <q-page-container class="pc-sm page-box">
      <q-page class="q-pa-md q-mt-md">
        <q-breadcrumbs>
          <q-breadcrumbs-el label="Account" to="/account/info" />
          <q-breadcrumbs-el label="Invitation history" to="/point/invite" />
          <q-breadcrumbs-el label="Points" class="text-grey-6" />
        </q-breadcrumbs>
        <div class="text-h6 q-mt-sm q-mb-md">
          <q-btn flat round dense size="lg" icon="arrow_back" @click="router.back()"></q-btn>
          Claiming cash details
        </div>
        <div class="q-px-md q-pb-xl">
          <DetailView ref="child" :load="handleLoad" class="q-mt-lg">
            <div class="rounded-borders-sm q-mt-md q-border-1 q-pa-md bg-white shadow-1">
              <!-- <div class="underline q-py-md">{{ productLabel(detail) }}</div> -->
              <div class="row underline q-py-md">
                <div class="col-2 text-grey-6">Updates</div>
                <div class="col-10" :class="detail?.value > 0 ? 'text-positive' : 'text-negative'">
                  USD
                  {{ detail?.value > 0 ? '+' : '' }}
                  {{ (detail?.value / 100).toFixed(2) }}
                </div>
              </div>
              <div class="row underline q-py-md">
                <div class="col-2 text-grey-6">Status</div>
                <div class="col-10">
                  <span class="text-orange" v-if="detail?.status == 0">Expected</span>
                  <span class="text-blue" v-if="detail?.status == 1">Actual</span>
                </div>
              </div>
              <!-- <div class="row underline q-py-md">
                <div class="col-2 text-grey-6">Notes</div>
                <div class="col-10">
                  <span v-if="detail.tab == 'earn' && detail?.expected && detail?.expected != detail?.value">Amount change due to user refund</span>
                </div>
              </div> -->
              <div class="row underline q-py-md">
                <div class="col-2 text-grey-6">Remaining</div>
                <div class="col-10">USD {{ (detail?.total / 100).toFixed(2) }}</div>
              </div>
              <div class="row underline q-py-md">
                <div class="col-2 text-grey-6">Category</div>
                <div class="col-10">
                  {{ productLabel(detail) }}
                </div>
              </div>
              <template v-if="detail?.tab === 'earn'">
                <div class="row underline q-py-md" v-if="detail?.category === 'verify' || detail?.category === 'invite'">
                  <div class="col-2 text-grey-6">Event</div>
                  <div class="col-10">
                    <PubAvatar class="q-mr-sm" size="1.5rem" :src="detail?.snapshot?.avatar" />
                    {{ detail?.snapshot?.name[0] }}
                  </div>
                </div>
                <div class="row underline q-py-md" v-else>
                  <div class="col-2 text-grey-6">Event</div>
                  <div class="col-10">
                    <q-img
                      class="q-mr-md cursor-pointer"
                      :src="
                        detail?.snapshot?.links?.[0]?.goods?.cover
                          ? hashToUrl(detail?.snapshot?.links?.[0]?.goods?.cover)
                          : detail?.snapshot?.links?.[0]?.goods?.image
                      "
                      width="100px"
                      height="50px"
                      @click="
                        onPreview(
                          detail?.snapshot?.links?.[0]?.goods?.cover
                            ? hashToUrl(detail?.snapshot?.links?.[0]?.goods?.cover)
                            : detail?.snapshot?.links?.[0]?.goods?.image
                        )
                      "></q-img>
                    {{ detail?.snapshot?.links?.[0]?.goods?.name }}
                  </div>
                </div>
              </template>
              <template v-if="detail?.tab === 'claim'">
                <div class="row underline q-py-md">
                  <div class="col-2 text-grey-6">Event</div>
                  <div class="col-10">
                    <q-img
                      class="q-mr-md cursor-pointer"
                      :src="
                        detail?.snapshot?.links?.[0]?.goods?.cover
                          ? hashToUrl(detail?.snapshot?.links?.[0]?.goods?.cover)
                          : detail?.snapshot?.links?.[0]?.goods?.image
                      "
                      width="100px"
                      height="50px"
                      @click="
                        onPreview(
                          detail?.snapshot?.links?.[0]?.goods?.cover
                            ? hashToUrl(detail?.snapshot?.links?.[0]?.goods?.cover)
                            : detail?.snapshot?.links?.[0]?.goods?.image
                        )
                      "></q-img>
                    {{ detail?.snapshot?.links?.[0]?.goods?.name }}
                  </div>
                </div>
              </template>
              <div class="row underline q-py-md">
                <div class="col-2 text-grey-6">Action on</div>
                <div class="col-10">{{ date.formatDate(detail?.createdAt, 'MM/DD/YYYY HH:mm:ss') }}</div>
              </div>
            </div>
          </DetailView>
        </div>
      </q-page>
    </q-page-container>
  </q-layout>
  <!-- dialogs -->
  <MaterialDialog class="z-max" v-model="isPreviewDialogShow">
    <ImgPreview :item="currentPreviewData" style="width: 90%; height: 90%" :isPreviewBig="true" :isDialog="true" />
  </MaterialDialog>
</template>
<script setup>
/*
  imports
*/
import {computed, onMounted, ref} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import DetailView from 'components/DetailView.vue'
import {date} from 'quasar'
import {productLabel} from './consts'
import ImgPreview from 'src/components/material/ImgPreview.vue'
import MaterialDialog from 'src/pages/class/components/MaterialDialog.vue'

const route = useRoute()
const router = useRouter()
const child = ref(null)

const isPreviewDialogShow = ref(false)
const currentPreviewData = ref(null)

function onPreview(item) {
  currentPreviewData.value = item
  isPreviewDialogShow.value = true
}

const detail = computed(() => child?.value?.data)

const onLoad = async () => {
  child.value.onLoad()
}

const handleLoad = async () => {
  return App.service('commission-log').get(route.params.id)
}

onMounted(() => {
  onLoad()
})
</script>
<style lang="scss" scope>
.page-box {
  padding-left: 0 !important;
}

.underline {
  border-bottom: 1px solid #ccc;
}
.cursor-pointer {
  cursor: pointer;
}
</style>
