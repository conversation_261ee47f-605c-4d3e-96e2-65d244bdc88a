<template>
  <div style="background-color: #fafafa">
    <PubTopBanner title="Gift cards" :isShowMenu="false" />

    <div class="row flex justify-center" style="background-color: #fafafa">
      <div class="q-mb-md q-pa-sm" style="max-width: 872px; width: auto">
        <div class="col-12 col-sm-6 col-md-4">
          <div class="row items-center">
            <q-card
              class="q-mt-md q-pa-md"
              style="height: auto; max-width: 516px; box-shadow: none; background-color: #fafafa"
              :style="$q.screen.lt.md ? {} : {minWidth: '516px'}">
              <div
                class="text-weight-regular"
                style="line-height: 40px; font-size: 32px"
                :style="$q.screen.lt.md ? {textAlign: 'center', marginLeft: 'auto', marginRight: 'auto'} : {}">
                Your USD {{ giftCardDetails.amount }} Gift card
              </div>

              <div
                class="q-mt-md q-mx-md"
                v-if="$q.screen.lt.lg"
                style="display: flex; justify-content: center; align-items: center; width: 100%; height: 245px; margin-left: auto; margin-right: auto">
                <GiftCardDesign style="width: 100%; max-width: 328px" :image="giftCardDetails.imageUrl" />
              </div>

              <div class="text-subtitle1 text-weight-medium q-pt-md" style="white-space: pre-wrap">{{ giftCardDetails.message }}</div>
              <div class="text-subtitle1 text-weight-medium q-pt-md">-- {{ giftCardDetails.senderName }}</div>
              <div class="text-weight-medium q-pt-lg" style="line-height: 28px; font-size: 22px">Ready to redeem your gift card?</div>
              <div class="text-subtitle1 text-weight-regular q-pt-md">Copy your unique PIN and redeem your card to spent it on a future purchase</div>

              <div class="row items-center no-wrap q-my-md">
                <q-input
                  class="q-px-md"
                  style="border: solid 1px #79747e; border-radius: 4px"
                  :style="$q.screen.lt.sm ? {width: '80%'} : {maxWidth: '400px', width: '100%'}"
                  v-model="giftCardDetails.pin"
                  label="Pin"
                  persistent-placeholder
                  flat
                  borderless
                  readonly
                  no-caps />
                <q-btn flat label="Copy" style="height: 22px" rounded class="q-ml-sm text-primary" @click="copyToClipboard(giftCardDetails.pin)" />
              </div>
              <q-btn label="Begin" class="full-width bg-primary q-mt-sm" style="text-transform: none; color: #fafafa" rounded @click="renderRedeemPage" />
            </q-card>
            <div class="q-mt-md q-ml-sm" v-if="$q.screen.gt.md" style="min-width: 324px; height: 218px">
              <GiftCardDesign :image="giftCardDetails.imageUrl" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import {ref, onMounted, watch, initCustomFormatter} from 'vue'
import {useRouter, useRoute} from 'vue-router'
import GiftCardCaorousel from './components/GiftCardCarousel.vue'
import GiftCardDesign from './components/GiftCardDesign.vue'

const router = useRouter()
const route = useRoute()

const giftCardDetails = ref({
  senderName: '',
  message: '',
  amount: 0,
  pin: '',
  imageUrl: {url: ''},
})

function copyToClipboard(value) {
  navigator.clipboard
    .writeText(value)
    .then(() => {
      $q.notify({
        type: 'positive',
        message: 'Copied to clipboard!',
        position: 'bottom-left',
      })
    })
    .catch(() => {
      $q.notify({
        type: 'negative',
        message: 'Failed to copy.',
        position: 'bottom-left',
      })
    })
}

function renderRedeemPage() {
  router.push('/account/gift-cards/redeem')
}

async function init() {}

onMounted(() => {
  const {senderName, giftMessage, amount, code, image} = route.query

  giftCardDetails.value.senderName = decodeURIComponent(senderName) || ''
  giftCardDetails.value.message = decodeURIComponent(giftMessage) || ''
  giftCardDetails.value.amount = Number(amount) || 0
  giftCardDetails.value.pin = code || ''
  giftCardDetails.value.imageUrl.url = image || ''
})
</script>
