<template>
  <q-card flat bordered class="cursor-pointer rounded-borders-lg" :class="{selectedcard: isSelected}" v-ripple tabindex="0">
    <q-img :src="image?.url" :alt="'Gift Card Design ' + image?.id" :ratio="16 / 9" class="rounded-borders-lg" />
  </q-card>
</template>

<script setup>
import {ref} from 'vue'

const props = defineProps({
  image: {
    type: Object,
    required: true,
  },

  isSelected: {
    type: Boolean,
    default: false,
  },
})
</script>

<style scoped>
.selectedcard {
  padding: 2px;
  border: 3px solid #4b4b4b;
  border-radius: 12px;
}
</style>
