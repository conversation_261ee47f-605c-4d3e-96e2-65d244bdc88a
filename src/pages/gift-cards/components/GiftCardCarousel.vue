<template>
  <q-card-section class="q-pa-none q-mt-md">
    <div class="row justify-between items-center q-mb-md">
      <div class="text-h6">Pick a design</div>
      <div class="row q-col-gutter-md items-start q-mr-md">
        <q-btn flat round dense bordered icon="chevron_left" @click="prevPage" :disable="currentPage === 0" aria-label="Previous designs" />
        <q-btn flat round dense bordered icon="chevron_right" @click="nextPage" :disable="currentPage >= totalPages - 1" aria-label="Next designs" />
      </div>
    </div>
  </q-card-section>

  <q-card-section v-if="paginatedImages.length > 0">
    <div class="row q-col-gutter-md">
      <div v-for="image in paginatedImages" :key="image.id" class="col-sm-4 col-xs-4" :style="$q.screen.lt.sm ? {height: '84px'} : {height: '146px'}">
        <GiftCardDesign :image="image" :isSelected="selectedGiftCardDesign?.id === image?.id" @click="emit('select', image)" />
      </div>
    </div>
  </q-card-section>
</template>

<script setup>
import {ref, computed} from 'vue'
import GiftCardDesign from './GiftCardDesign.vue'

const props = defineProps({
  modelValue: {
    type: Object,
    default: null,
  },
  imageObject: {
    type: Object,
    default: () => ({}),
  },
  selectedGiftCardDesign: {
    type: Object,
    default: null,
  },
})

const emit = defineEmits()

const ITEMS_PER_PAGE = 9
const currentPage = ref(0)

const allImagesArray = computed(() => {
  if (!props.imageObject || Object.keys(props.imageObject).length === 0) {
    return []
  }
  const imageEntries = Object.entries(props.imageObject)
  return imageEntries.map(([key, originalPath]) => {
    return {id: key, url: originalPath}
  })
})

const totalPages = computed(() => {
  if (!allImagesArray.value || allImagesArray.value.length === 0) return 0
  return Math.ceil(allImagesArray.value.length / ITEMS_PER_PAGE)
})

const paginatedImages = computed(() => {
  if (!allImagesArray.value || allImagesArray.value.length === 0) return []
  if (currentPage.value >= totalPages.value && totalPages.value > 0) {
    currentPage.value = totalPages.value - 1
  } else if (currentPage.value < 0 && totalPages.value > 0) {
    currentPage.value = 0
  } else if (totalPages.value === 0) {
    currentPage.value = 0
  }
  const start = currentPage.value * ITEMS_PER_PAGE
  const end = start + ITEMS_PER_PAGE
  return allImagesArray.value.slice(start, end)
})

const nextPage = () => {
  if (currentPage.value < totalPages.value - 1) {
    currentPage.value++
  }
}

const prevPage = () => {
  if (currentPage.value > 0) {
    currentPage.value--
  }
}
</script>

<style scoped lang="scss">
.rounded-borders-xl {
  border-radius: 16px;
}

.rounded-borders-lg {
  border-radius: 12px;
}

.rounded-borders-md {
  border-radius: 8px;
}

.image-card {
  transition:
    transform 0.2s ease-in-out,
    box-shadow 0.2s ease-in-out;
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
  }
}

.selected-card {
  border-color: var(--q-primary) !important;
  box-shadow:
    0 0 0 2.5px var(--q-primary),
    0 4px 10px rgba(var(--q-color-primary-rgb), 0.3);
  transform: translateY(-2px);
}
</style>
