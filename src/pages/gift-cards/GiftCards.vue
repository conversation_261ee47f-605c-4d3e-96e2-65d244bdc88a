<template>
  <div>
    <PubTopBanner title="Gift cards" :isShowMenu="false" />
    <q-spinner v-if="isLoading" color="primary" size="50px" class="absolute-center" :style="{zIndex: 1000}" />
    <div v-else class="flex items-center justify-center q-mt-xl">
      <div class="column q-pa-sm" style="min-width: 300px; max-width: 1096px; width: 100%">
        <div class="balance-box q-mb-md">
          <span class="label text-primary">Your gift card balance</span>
          <span class="balance"> $ {{ currentBalance }}</span>
        </div>
        <div class="row q-pa-sm" :class="$q.screen.lt.lg ? 'column items-center' : 'row'" style="gap: 16px">
          <q-btn
            label="Buy now"
            color="primary"
            rounded
            class="q-px-xl"
            style="width: 100%; max-width: 335px; height: 56px; text-transform: none"
            @click="renderBuyPage" />
          <q-btn
            label="Redeem"
            color="#EA4335"
            rounded
            style="width: 100%; max-width: 335px; height: 56px; background-color: #ea4335; text-transform: none"
            @click="renderRedeemPage" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import {ref, onMounted} from 'vue'
import {useRouter, useRoute} from 'vue-router'
import useSchool from 'src/composables/common/useSchool'

const router = useRouter()
const {isAdmin, isStudent, schoolId, isSubjectCoordinator} = useSchool()
const currentBalance = ref(0)
const isLoading = ref(true)

function renderBuyPage() {
  router.push('/account/gift-cards/buy')
}
function renderRedeemPage() {
  router.push('/account/gift-cards/redeem')
}

async function init() {
  let balance = 0

  if (schoolId.value) {
    const rs = await App.service('wallet-balance').get('balance', {query: {uid: schoolId.value, balanceType: 'giftCard'}})
    balance = rs.availableBalance
  } else {
    const res = await App.service('wallet-balance').get('balance', {query: {balanceType: 'giftCard'}})
    balance = res.availableBalance
  }
  currentBalance.value = (balance / 100).toFixed(2)
  isLoading.value = false
}

onMounted(() => {
  if (schoolId.value && !isAdmin.value && !isSubjectCoordinator.value) {
    router.push('/account/info')
    return
  }
  init()
})
</script>

<style scoped>
.gift-card-page {
  background-color: #fefefe;
  min-height: 100vh;
  font-family: sans-serif;
}

.balance-box {
  background-color: hsl(169, 33%, 90%);
  border-radius: 8px;
  padding: 16px 24px;
  display: flex;
  justify-content: space-between;
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 40px;
  color: #333;
}

.balance {
  color: #00bfa5;
  font-weight: bold;
}
</style>
