### GiftCards.vue

    - /account/gift-cards
    - main page with gift card balance, buy now and redeem buttons

### BuyGiftCards.vue

    - /account/gift-cards/buy
    - page to buy gift cards
    - include choose amount and custom amount
    - import GiftCardCarousel.vue and use here, send selected card
    - create an object for images object = {
        gift_card_1: "path1",
        gift_card_2: ........
    }

### RedeemGiftCards.vue

    - /account/gift-cards/redeem

### Order Page

    - changes to the existing order page for gift cards

### Components

     components
       - GiftCardDesign.vue
            - this component is to render a single gift card design
       - GiftCardCarousel.vue
            - this is the layout to render all available gift card designs
            - we need to import GiftCardDesign.vue here and loop through all the design to render it
            - it should accept selected image state passed from parent component BuyGiftCards.vue

### Gift card order intigration

    --use vue-router state to push to order confirm page: /gift_card/new-gift-card
