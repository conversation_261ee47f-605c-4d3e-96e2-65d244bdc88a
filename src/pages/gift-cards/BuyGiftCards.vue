<template>
  <div>
    <PubTopBanner title="Gift cards" :isShowMenu="false" />

    <div class="row flex justify-center" style="background-color: #fafafa">
      <div class="q-mb-md q-pa-sm" style="max-width: 1300px; width: auto">
        <div class="col-12 col-sm-6 col-md-4">
          <div class="row q-mt-md" :class="$q.screen.lt.md ? 'justify-center q-pt-none' : 'justify-start'">
            <q-btn
              label="Redeem"
              color="#EA4335"
              rounded
              class="q-mt-md"
              style="width: 100%; max-width: 350px; height: 56px; background-color: #ea4335"
              @click="renderRedeemPage" />
          </div>

          <div class="row">
            <q-card
              class="q-mt-md q-pa-md"
              style="height: auto; max-width: 742px; box-shadow: none"
              :style="$q.screen.lt.md ? {backgroundColor: '#fafafa'} : {minWidth: '742px'}">
              <div
                class="text-h5 text-bold q-pt-md"
                :style="$q.screen.lt.md ? {textAlign: 'center', fontSize: '32px', fontWeight: '700', marginLeft: 'auto', marginRight: 'auto'} : {}">
                Purchase a gift card
              </div>
              <div
                class="q-mt-md q-mx-md"
                v-if="$q.screen.lt.lg"
                style="display: flex; justify-content: center; align-items: center; width: 100%; height: 245px; margin-left: auto; margin-right: auto">
                <GiftCardDesign style="width: 100%; max-width: 364px" :image="selectedGiftCardDesign" />
              </div>
              <GiftCardCaorousel :selectedGiftCardDesign="selectedGiftCardDesign" :imageObject="imageObjectSource" @select="selectedImage" />
              <div class="text-h6 q-pt-md">Choose an amount</div>
              <div class="row q-gutter-sm q-pt-md q-pb-sm wrap">
                <div v-for="amount in amounts" :key="amount" :class="$q.screen.lt.sm ? 'col-4' : 'auto'">
                  <q-btn
                    :label="`NZD ${amount}`"
                    :color="selectedAmount === amount ? 'primary' : 'primary'"
                    :unelevated="selectedAmount === amount"
                    :outline="selectedAmount !== amount"
                    :text-color="selectedAmount === amount ? 'white' : 'primary'"
                    class="q-px-md amount-btn full-width"
                    @click="selectedAmount = amount" />
                </div>
              </div>
              <q-input
                class="q-py-md"
                style="width: 100%"
                :style="$q.screen.lt.sm ? {} : {maxWidth: '400px'}"
                v-model="selectedAmount"
                type="number"
                label="Custom"
                @blur="validateAmount"
                persistent-placeholder
                stack-label
                placeholder="From an amount between NZD 5 to NZD 1000"
                outlined
                no-caps
                flat
                unelevated
                clearable="false"
                :error="!!errorMessage"
                :error-message="errorMessage"
                hide-bottom-space />
            </q-card>
            <div class="q-mt-md q-mx-md" v-if="$q.screen.gt.md" style="min-width: 364px; height: 245px">
              <GiftCardDesign :image="selectedGiftCardDesign" />
            </div>
          </div>
        </div>
        <q-card
          class="q-pa-md q-mt-md"
          style="height: auto; max-width: 742px; margin-bottom: 67px; box-shadow: none"
          :style="$q.screen.lt.md ? {backgroundColor: '#fafafa'} : {}">
          <div class="row items-center q-gutter-sm">
            <q-checkbox v-model="showGiftForm" size="32px" />
            <q-icon name="mail_outline" size="24px" color="primary" class="q-ml-sm" />
            <div class="text-body1 text-weight-medium q-pl-sm">Send as gift</div>
          </div>

          <q-slide-transition style="width: auto">
            <div v-show="showGiftForm" class="q-mt-md">
              <q-input v-model="form.sender" label="Sender name" outlined class="q-mb-md" />
              <q-input v-model="form.recipient" label="Recipient name" outlined class="q-mb-md" style="height: 56px" />
              <q-input
                v-model="form.email"
                label="Recipient Email"
                type="email"
                placeholder="Enter the recipient’s email address"
                outlined
                class="q-mb-md"
                :required="true"
                @blur="validateInput(form.email, 'email')"
                :error="!!errorForEmail"
                :error-message="errorForEmail"
                hide-bottom-space />
              <q-input
                v-model="form.message"
                label="Gift message"
                type="textarea"
                outlined
                @blur="validateInput(form.message, 'length')"
                :error="!!errorForLength"
                :error-message="errorForLength"
                hide-bottom-space />
            </div>
          </q-slide-transition>
        </q-card>
      </div>
      <q-footer elevated class="bg-white text-white q-pa-md" style="position: fixed; bottom: 0; left: 0; right: 0; max-height: '66px'">
        <div class="row justify-end items-center">
          <q-btn label="Buy now" rounded color="primary" style="text-transform: none" :disable="!canProceed" @click="handleBuyNow" />
        </div>
      </q-footer>
    </div>
  </div>
</template>

<script setup>
import {ref, onMounted, watch, computed} from 'vue'
import {useRouter, useRoute} from 'vue-router'
import GiftCardCaorousel from './components/GiftCardCarousel.vue'
import GiftCardDesign from './components/GiftCardDesign.vue'

const router = useRouter()
const currentBalance = ref(501)
const amounts = ref([10, 20, 50, 100, 500])
const selectedAmount = ref(null)
const errorMessage = ref(null)
const errorForEmail = ref(null)
const errorForLength = ref(null)
const selectedGiftCardDesign = ref(null)
const form = ref({
  sender: '',
  recipient: '',
  email: '',
  message: '',
})

const canProceed = computed(() => {
  const hasCardAndAmount = selectedGiftCardDesign.value && selectedAmount.value

  if (!hasCardAndAmount) return false

  const email = form.value.email
  if (showGiftForm.value) {
    return email && !errorForEmail.value
  }

  return true
})

const validateAmount = () => {
  errorMessage.value = null
  if (selectedAmount.value === null || selectedAmount.value === '') {
    return
  }
  const val = parseFloat(selectedAmount.value)

  if (val < 5) {
    errorMessage.value = 'Error: Please note that the minimum amount should be 5 USD'
    return
  }
  if (val % 1 !== 0) {
    errorMessage.value = "Error: Please note that the amount can't be a decimal value"
    return
  }
  if (val > 1000) {
    errorMessage.value = 'Error: Please note that the maximum amount is 1000 USD'
    return
  }
}

function validateInput(value, type) {
  if (type === 'length') {
    const wordCount = value.length

    if (wordCount > 200) {
      errorForLength.value = `You cannot exceed 200 words:   ${wordCount}/200`
    } else {
      errorForLength.value = ''
    }
  }

  if (type === 'email') {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(value)) {
      errorForEmail.value = 'The mail id you entered is not valid'
    } else {
      errorForEmail.value = null
    }
  }
}

function renderRedeemPage() {
  router.push('/account/gift-cards/redeem')
}

const imageObjectSource = {
  gift_card_1: 'https://r2dev.classcipe.com/1b8f2943ef4ad6ec9f257a899b310688af9d0759',
  gift_card_2: 'https://r2dev.classcipe.com/f9d03f7995c8c0b00fb48f75c8a9db18f6493bc9',
  gift_card_3: 'https://r2dev.classcipe.com/b8ccf38c9e0b5270b12e27dc4113e6c909eac34d',
  gift_card_4: 'https://r2dev.classcipe.com/97562526a37565c9438dddc84a84b20aaacc75d5',
  gift_card_5: 'https://r2dev.classcipe.com/0202b8274523c3492d5375b3670ed7fded5ba0f6',
  gift_card_6: 'https://r2dev.classcipe.com/5d394c8c634e15ee32a05edc0f3289dc1db1dc84',
  gift_card_7: 'https://r2dev.classcipe.com/422b8d411c1c5cb3bea6c6c602e7a187b8bb0f01',
  gift_card_8: 'https://r2dev.classcipe.com/855223cc3574fe4342a7a448692897c3e4849f5d',
  gift_card_9: 'https://r2dev.classcipe.com/9ccff4898b32b71f096de1b8ed72864d5636784f',
  gift_card_10: 'https://r2dev.classcipe.com/a4b077c977e6b06ab5119ca766e9b5cc3fd4f3d5',
  gift_card_11: 'https://r2dev.classcipe.com/eb4419ea875ab3a9a3d4ec0f3cd930c55c258f8c',
  gift_card_12: 'https://r2dev.classcipe.com/a1bd1d3cdb6392c90226bcda29f0e10cf624787f',
  gift_card_13: 'https://r2dev.classcipe.com/f8fc40c652124f227bc1b252c70ecadf3932d964',
  gift_card_14: 'https://r2dev.classcipe.com/bfa8011ffefd950b73d1eedd40bd5b2f1c039d9b',
  gift_card_15: 'https://r2dev.classcipe.com/96259a6367c963f0904ea0cdb7117b8f591c0c80',
  gift_card_16: 'https://r2dev.classcipe.com/b4c4edc7dfe931523118eff7096902cadfc38ccd',
  gift_card_17: 'https://r2dev.classcipe.com/b9834b39ea9345bb5acf56cc2d906728e09bb50f',
  gift_card_18: 'https://r2dev.classcipe.com/6908bb515f44cfc7fba37c1488bd3a25fc312fa0',
  gift_card_19: 'https://r2dev.classcipe.com/6ce26afa939243039eee1b64fd9f54f7fc46a11e',
  gift_card_20: 'https://r2dev.classcipe.com/e37a77b06b219ffe292b94ef104416ac8fdd53c2',
  gift_card_21: 'https://r2dev.classcipe.com/19d88e585cb832f0c29ae7b8d2862956b4513c8d',
  gift_card_22: 'https://r2dev.classcipe.com/44fab0914216d55ff4530df03d74198d066308cd',
  gift_card_23: 'https://r2dev.classcipe.com/2724105e154522530df36f6456f7627d5a062448',
  gift_card_24: 'https://r2dev.classcipe.com/2d49ba7b617aca925b48963fe5989d60731edd9b',
  gift_card_25: 'https://r2dev.classcipe.com/178d29db9b7d6c141f6ca504ff79f12d0f92d7ff',
  gift_card_26: 'https://r2dev.classcipe.com/640979c785dd5be8cecb7c165dde2cce5a72051d',
  gift_card_27: 'https://r2dev.classcipe.com/bf952b775f1b98ee7918d4333a7347c4244f5bd7',
}

function selectedImage(image) {
  selectedGiftCardDesign.value = image
}

const showGiftForm = ref(false)

function handleBuyNow() {
  const type = 'gift_card'
  const unitId = 'new-gift-card'

  const payload = {
    selectedGiftCardDesign: selectedGiftCardDesign.value,
    selectedAmount: selectedAmount.value * 100,
    message: form.value.message,
    sender: form.value.sender,
    recipient: form.value.recipient,
    email: form.value.email,
    isGift: showGiftForm.value,
  }

  sessionStorage.setItem('giftCardOrderData', JSON.stringify(payload))

  router.push({
    path: `/order/confirm/${type}/${unitId}`,
  })
}

onMounted(() => {
  selectedGiftCardDesign.value = {
    id: Object.keys(imageObjectSource)[0],
    url: Object.values(imageObjectSource)[0],
  }
})
</script>

<style scoped>
.gift-card-page {
  background-color: #fefefe;
  min-height: 100vh;
  font-family: sans-serif;
}

.balance-box {
  background-color: hsl(169, 33%, 90%);
  border-radius: 8px;
  padding: 16px 24px;
  display: flex;
  justify-content: space-between;
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 40px;
  color: #333;
}

.balance {
  color: #00bfa5;
  font-weight: bold;
}
</style>
