<template>
  <div style="background-color: #fafafa">
    <PubTopBanner title="Gift cards" :isShowMenu="false" />

    <div class="row flex justify-center" style="background-color: #fafafa">
      <div class="q-mb-md q-pa-sm" style="max-width: 872px; width: auto">
        <div class="col-12 col-sm-6 col-md-4">
          <div class="row items-center">
            <q-card
              class="q-mt-md q-pa-md"
              style="height: auto; max-width: 516px; box-shadow: none; background-color: #fafafa"
              :style="$q.screen.lt.md ? {} : {minWidth: '516px'}">
              <div
                class="text-weight-regular"
                style="line-height: 28px; font-size: 22px"
                :style="$q.screen.lt.lg ? {textAlign: 'center', marginLeft: 'auto', marginRight: 'auto'} : {}">
                Let's redeem your Gift card
              </div>

              <div
                class="q-mt-md q-mx-md"
                v-if="$q.screen.lt.lg"
                style="display: flex; justify-content: center; align-items: center; width: 100%; height: 245px; margin-left: auto; margin-right: auto">
                <GiftCardDesign style="width: 100%; max-width: 328px" :image="imageUrl" />
              </div>

              <q-input
                class="q-mt-md"
                v-model="pin"
                label="Pin"
                placeholder="Please enter the pin"
                persistent-placeholder
                stack-label
                flat
                outlined
                no-caps
                :error="pinError" />

              <div class="text-weight-medium q-pt-md" style="line-height: 24px; font-size: 16px">
                By redeeming, you agree to the Classcipe
                <span style="text-decoration: underline; cursor: pointer" @click="redirectToTerms">card terms</span>.
              </div>

              <q-btn
                label="Redeem gift card"
                class="full-width bg-primary q-mt-lg"
                style="text-transform: none; color: #fafafa"
                rounded
                :disable="!pin || buttonLoading"
                @click="redeemGiftCard"
                :loading="buttonLoading" />
            </q-card>
            <div class="q-mt-xl q-ml-sm" v-if="$q.screen.gt.md" style="min-width: 324px; height: 218px">
              <GiftCardDesign :image="imageUrl" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import {ref, onMounted, watch, initCustomFormatter} from 'vue'
import {useRouter, useRoute} from 'vue-router'
import GiftCardCaorousel from './components/GiftCardCarousel.vue'
import GiftCardDesign from './components/GiftCardDesign.vue'
import {pubStore} from '../../stores/pub'

const pub = pubStore()
const router = useRouter()
const pin = ref('')
const pinError = ref(false)
const buttonLoading = ref(false)
const imageUrl = ref({
  name: 'gift-card',
  url: 'https://r2dev.classcipe.com/422b8d411c1c5cb3bea6c6c602e7a187b8bb0f01',
})

async function redeemGiftCard() {
  const pinStr = String(pin.value).trim()

  if (!pinStr) {
    return
  }

  try {
    buttonLoading.value = true
    const res = await App.service('gift-card').get('redeem', {
      query: {
        code: pinStr,
      },
    })

    if (res.status === 'failure') {
      throw new Error(res.message || 'Invalid PIN or already redeemed')
    }

    router.push('/account/gift-cards')
  } catch (err) {
    $q.notify({
      type: 'negative',
      message: err.message || 'Something went wrong',
      position: 'bottom-right',
    })
  } finally {
    buttonLoading.value = false
  }
}

function redirectToTerms() {}

async function init() {}

onMounted(async () => {
  if (!pub.user?._id) {
    router.push('/login', {query: {back: '/account/gift-cards/redeem'}})
    return
  }
  try {
    const text = await navigator.clipboard.readText()
    if (/^[A-Z0-9]{6,}$/.test(text)) {
      pin.value = text
    }
  } catch (err) {
    console.warn('Clipboard access denied or failed', err)
  }
})
</script>
<style scoped>
.error-message {
  background-color: #26a69a14;
}
</style>
