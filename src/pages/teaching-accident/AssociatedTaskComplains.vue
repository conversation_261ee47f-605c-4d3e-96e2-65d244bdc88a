<template>
  <div>
    <PubTopBanner :title="`Teaching accident`" :useBack="true" :back="`/sys/teaching-accident`" />

    <q-page class="">
      <div class="pc-max q-px-md q-mt-xs">
        <main class="column no-wrap full-width relative-position">
          <q-card v-if="data?.status === 'pending'" class="bg-blue full-width q-pa-md flex justify-between items-center q-my-md">
            <div>Teaching accident status: <b>Pending</b></div>
            <div class="flex q-gutter-md">
              <!-- <q-btn rounded flat no-caps label="Approve" class="bg-green text-white" @click=";(isApproveDialogShow = true), clearApproveReason()" /> -->
              <!-- <q-btn rounded flat no-caps label="Reject" class="bg-red text-white" @click=";(isRejectDialogShow = true), (rejectReason = '')" /> -->
              <q-btn rounded flat no-caps label="Reject" class="bg-red text-white" @click="selectDialog('reject')" />
              <q-btn rounded flat no-caps label="Approve" class="bg-green text-white" @click="selectDialog('approve')" />
            </div>
            <DecisionDialog
              v-if="showDialog"
              :curMode="dialogMode"
              :sectionData="sectionData"
              @close="showDialog = false"
              :pathDataId="data?._id"
              @init="handleDialogInit" />
          </q-card>

          <div v-if="data?.status === 'approved'">
            <q-card class="bg-green full-width q-pa-md flex justify-between items-center q-my-md">
              <div>Teaching accident status: <b>Approved</b></div>
              <!-- <div class="flex q-gutter-md">
                <q-btn rounded flat no-caps label="Rejected" class="bg-red text-white" @click=";(isRejectDialogShow = true), (rejectReason = '')" />
              </div> -->
            </q-card>
            <div v-if="data?.checkReason" class="text-teal text-subtitle2 q-my-md" style="font-size: 1rem">
              <div>Reasons for approving</div>
              <div>{{ data.checkReason }}</div>
            </div>
          </div>

          <div v-if="data?.status === 'rejected'">
            <q-card class="bg-red full-width q-pa-md flex justify-between items-center q-my-md">
              <div>Teaching accident status: <b>Rejected</b></div>
              <!-- <div class="flex q-gutter-md">
                <q-btn rounded flat no-caps label="Approve" class="bg-green text-white" @click=";(isApproveDialogShow = true), clearApproveReason()" />
              </div> -->
            </q-card>
            <div v-if="data?.checkReason" class="text-red text-subtitle2 q-my-md" style="font-size: 1rem">
              <div>Reasons for rejecting</div>
              <div>{{ data.checkReason }}</div>
            </div>
          </div>

          <q-tabs
            v-model="tab"
            dense
            class="text-grey full-width flex justify-between q-my-md q-pa-none"
            active-color="primary"
            indicator-color="primary"
            align="justify"
            narrow-indicator>
            <div class="flex items-center">
              <q-tab
                v-for="item in Object.values(tabOptions)"
                :key="item.value"
                :name="item.value"
                no-caps
                :label="item.label"
                :class="tab === item.value ? 'text-primary' : 'text-black'" />
            </div>
          </q-tabs>

          <!-- serviceProvider -->
          {{ console.log('laoding', loading) }}

          <div v-if="loading" style="display: flex; justify-content: center; align-items: center; height: 100%">
            <q-spinner color="primary" size="2em" />
          </div>
          <q-card v-else-if="tab === 'serviceProvider'" class="rounded-borders-md">
            {{ console.log('data', data) }}
            <div class="coloumn q-pa-md">
              <div class="row items-center justify-between q-mt-sm">
                <div class="flex items-center">
                  <PubAvatar :src="data?.teacherInfo?.avatar" size="3rem" />
                  <div class="q-ml-sm">
                    <div class="text-subtitle2">{{ nameFormatter(data?.teacherInfo) }}</div>
                    <div class="text-grey-8">{{ data?.teacherInfo?.email ?? '0' }}</div>
                  </div>
                </div>

                <div class="text-grey-8">
                  create at
                  {{ date.formatDate(data?.createdAt, TIME_FORMAT) }}
                </div>
              </div>

              <div v-if="!(userData?.introduction || userData?.audio)" style="display: flex; justify-content: center; align-items: center; height: 100%">
                <q-spinner color="primary" size="2em" />
              </div>
              <div v-else class="flex justify-between items-start q-my-md">
                <div class="bg-teal-1 q-pa-sm q-my-none rounded-md text-subtitle1" style="max-width: calc(100% - 17rem); white-space: pre-line">
                  {{ userData?.introduction ?? '-' }}
                </div>
                <div v-if="userData?.audio" class="q-border-1 rounded-md overflow-hidden" style="width: 16rem; height: 8rem">
                  <AudioRecord :src="userData?.audio" :length="userData?.audioTime" :player="true"></AudioRecord>
                </div>
              </div>

              <hr class="divider" />

              <div class="q-my-md">
                <div class="text-h6">Student's impressions</div>
                <div v-if="data?.teacherInfo?._id && allTeacherRatingTags?.length" class="flex items-center">
                  <q-chip v-for="(item, index) in allTeacherRatingTags" :key="index" :ripple="false" color="teal-1" text-color="primary"
                    >{{ item.tags }}({{ item.count }})</q-chip
                  >
                  <q-btn :to="`/booking/students-impressions/${data?.teacherInfo?._id}`" no-caps dense flat rounded color="primary"
                    >See all evaluations ({{ evaluationsCount }})</q-btn
                  >
                </div>
                <NoData v-else message="No evaluations" messageColor="grey-8" />
              </div>
            </div>
          </q-card>

          <q-page v-if="tab === 'complainDetails'">
            <div v-if="!data" style="display: flex; justify-content: center; align-items: center; height: 100%">
              <q-spinner color="primary" size="2em" />
            </div>
            <div v-else class="q-px-md q-py-sm rounded-xl" style="border: 1px solid #f55c44; min-height: 100px">
              <div class="flex text-h6 q-my-lg items-center">{{ data?.sessionName }}</div>

              <div>
                <q-expansion-item
                  expand-separator
                  default-opened
                  header-class="flex items-center q-ml-none q-pl-none justify-between text-subtitle1 text-weight-medium "
                  content-class="q-pa-none"
                  class="q-ma-none q-pa-none">
                  <template #header>
                    <div>
                      <span>Prompt</span>
                    </div>
                  </template>
                  <pre style="white-space: pre-wrap; word-wrap: break-word; max-height: 200px; overflow-y: auto; min-height: 100px">{{
                    data?.sessionPrompt
                  }}</pre>
                </q-expansion-item>
              </div>
            </div>
            <!-- student complain card -->
            <div v-if="!data?.studentInfo" style="display: flex; justify-content: center; align-items: center; height: 100%">
              <q-spinner color="primary" size="2em" />
            </div>

            <q-card v-else class="q-mt-md q-pa-md rounded-lg">
              <div class="row items-center">
                <div class="text-h6 q-mr-md">Complain by</div>

                <PubAvatar :src="data?.studentInfo?.avatar" size="24px" />

                <div class="q-ml-xs">{{ nameFormatter(data?.studentInfo) }}</div>

                <div class="text-grey-8 q-ml-md">created at {{ date.formatDate(data?.createdAt, TIME_FORMAT) }}</div>
              </div>

              <div class="q-mt-md">
                <div v-if="data?.evidencesStudent?.[0]?.content">{{ data?.evidencesStudent?.[0]?.content }}</div>
                <div v-else class="text-grey-8">Not content</div>
              </div>

              <!-- Evidence preview Section -->
              <div v-if="data?.evidencesStudent?.at(-1)?.attachments?.length" class="q-mt-md">
                {{ console.log('evidencesStudent', data?.evidencesStudent) }}
                <div class="text-subtitle2 q-my-none">Evidences for Associated task complain</div>
                <div class="row q-gutter-sm">
                  <div v-for="(file, index) in data?.evidencesStudent?.at(-1)?.attachments" :key="index" style="max-width: fit-content; border-radius: 10px">
                    <div class="q-pa-xs" style="display: inline-block; border-radius: 10px">
                      <div style="position: relative; display: inline-block">
                        <q-img
                          v-if="file.mime.includes('image')"
                          :src="hashToUrl(file.hash)"
                          style="width: 60px; height: 45px; object-fit: cover; border-radius: 10px; overflow: hidden" />
                        <q-video v-else :src="hashToUrl(file.hash)" style="width: 60px; height: 45px; border-radius: 10px" :controls="true" autoplay="false" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </q-card>
            <!-- teacher complain card -->
            <q-card class="q-mt-md q-pa-md rounded-lg" v-if="data?.evidencesTeacher?.length">
              <div class="row items-center">
                <div class="text-h6 q-mr-md">Appeal by</div>

                <PubAvatar :src="data?.teacherInfo?.avatar" size="24px" />

                <div class="q-ml-xs">{{ nameFormatter(data?.teacherInfo) }}</div>

                <div class="text-grey-8 q-ml-md">created at {{ date.formatDate(data?.createdAt, TIME_FORMAT) }}</div>
              </div>

              <div class="q-mt-md">
                <div v-if="data?.evidencesTeacher?.[0]?.content">{{ data?.evidencesTeacher?.[0]?.content }}</div>
                <div v-else class="text-grey-8">Not content</div>
              </div>

              <!-- Evidence preview Section -->

              <div v-if="data?.evidencesTeacher?.at(-1)?.attachments?.length" class="q-mt-md">
                {{ console.log('evidence teacher', data?.evidencesTeacher?.at(-1)?.attachments) }}

                <div class="text-subtitle2 q-my-none">Appealing evidence</div>
                <div class="row q-gutter-sm">
                  <div v-for="(file, index) in data?.evidencesTeacher?.at(-1)?.attachments" :key="index" style="max-width: fit-content; border-radius: 10px">
                    <div class="q-pa-xs" style="display: inline-block; border-radius: 10px">
                      <div style="position: relative; display: inline-block">
                        <q-img
                          v-if="file.mime.includes('image')"
                          :src="hashToUrl(file.hash)"
                          style="width: 60px; height: 45px; object-fit: cover; border-radius: 10px; overflow: hidden" />
                        <q-video v-else :src="hashToUrl(file.hash)" style="width: 60px; height: 45px; border-radius: 10px" :controls="true" autoplay="false" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </q-card>
            <q-card class="q-pa-xs rounded-lg q-mt-md"
              ><q-expansion-item
                expand-separator
                default-opened
                header-class="flex items-center q-ml-none q-pl-none justify-between text-subtitle1 text-weight-medium "
                content-class="q-pa-none"
                class="q-ma-none q-pa-none">
                <template #header>
                  <div>
                    <span class="q-ml-md text-h6">Task details</span>
                  </div>
                </template>

                <!-- product detail page -->

                <q-card class="q-pa-md rounded-lg">
                  <div class="text-h6 text-center">Product detail</div>
                  <q-img
                    v-if="task?.cover"
                    class="q-my-md"
                    contain
                    :src="hashToUrl(task?.cover)"
                    style="width: 90%; max-width: 580px; height: auto; border-radius: 4px" />

                  <div class="text-weight-medium q-pa-sm">
                    <div class="text-subtitle1 text-weight-medium q-mb-sm">
                      Service type:
                      <q-chip square color="teal-1" text-color="teal-7" style="font-weight: 500" :ripple="false">
                        {{ task?.mentoringType }}
                      </q-chip>
                    </div>

                    <div class="text-subtitle1 text-weight-medium q-mb-sm">
                      Service provider qualification:
                      <q-chip square color="teal-1" text-color="teal-7" style="font-weight: 500" :ripple="false">
                        {{ task?.qualification }}
                      </q-chip>
                    </div>

                    <div v-if="task?.name" class="text-subtitle1 text-weight-medium q-mt-md">
                      {{ task?.name }}
                    </div>
                    <div v-if="task?.description" class="text-subtitle2 text-weight-regular">
                      {{ task?.description }}
                    </div>
                  </div>
                </q-card>

                <div v-if="!sectionData?.length" style="display: flex; justify-content: center; align-items: center; height: 100%">
                  <q-spinner color="primary" size="2em" />
                </div>

                <SectionListTeachingAccident v-else :currentTaskSections="sectionData" />
              </q-expansion-item>
            </q-card>
          </q-page>
        </main>
      </div>
    </q-page>
  </div>
</template>

<script setup>
import {ref, computed, onMounted, watch} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {date} from 'quasar'

// import {useConfStore} from 'stores/service-conf'
// const confStore = useConfStore()
// confStore.init()
// {{ confStore?.doc.introduction }}
import {TIME_FORMAT, GradeGroupMap, SatisfiedOptions, UnsatisfiedOptions} from 'src/boot/const'
import {AccidentMap} from 'src/pages/teaching-accident/utils'
import nameFormatter from 'src/utils/formatters/nameFormatter'
import MaterialItem from 'src/components/material/MaterialItem.vue'
import MaterialDialog from 'src/pages/class/components/MaterialDialog.vue'
import SuspendedLogTable from 'src/pages/teaching-accident/components/tabs/SuspendedLogTable.vue'

import TextItem from 'src/components/utils/TextItem.vue'
import AudioRecord from 'src/components/AudioRecord.vue'
import useAcademicSetting from 'src/composables/account/academic/useAcademicSetting'
import useSubject from 'src/composables/account/academic/useSubject'
import useTeacherVerificationAuth from 'src/composables/account/teacher-verification/useTeacherVerificationAuth'
import useTeachingAccident from 'src/composables/account/teaching-accident/useTeachingAccident'
import useSuspendedLogList from 'src/composables/account/teaching-accident/useSuspendedLogList'
import SectionListTeachingAccident from './SectionListTeachingAccident.vue'
import DecisionDialog from './DecisionDialog.vue'

const $route = useRoute()
const $router = useRouter()

const {getUserServiceConfigById} = useTeacherVerificationAuth()
const {getOneById, patchOneById, patchReadById, getCount} = useTeachingAccident()
const {sysCurriculumMap} = useAcademicSetting()
const {sysMap: sysSubjectMap} = useSubject()
const {suspendedUid, suspendedAccidentId, getList} = useSuspendedLogList()

const currentId = computed(() => $route.params.id)
const userData = ref(null)
const data = ref(null)

const defaultImpressions = 4
const allTeacherRatingTags = ref([])
const evaluationsCount = ref(0)

const tab = ref('serviceProvider')
const task = ref(null)
const sectionData = ref(null)
const loading = ref(true)
const showDialog = ref(false)
const dialogMode = ref('')

const tabOptions = {
  serviceProvider: {
    label: 'Service provider',
    value: 'serviceProvider',
  },
  complainDetails: {
    label: 'Complain details',
    value: 'complainDetails',
  },
}

async function getStudentImpressment() {
  const teacherId = data.value?.teacherInfo?._id
  await App.service('service-rating')
    .get('tagsCount', {
      query: {servicer: teacherId},
    })
    .then((res) => {
      // console.log(res)
      allTeacherRatingTags.value = res
        // .filter((item) => SatisfiedOptions.some((e) => e.label == item.tags))
        .sort((a, b) => {
          return b.count - a.count
        })
        .splice(0, defaultImpressions)
    })
  await App.service('service-rating')
    .find({
      query: {servicer: teacherId, $limit: 2000},
    })
    .then((res) => {
      // console.log(res)
      evaluationsCount.value = res?.total ?? 0
    })
}

function selectDialog(mode) {
  dialogMode.value = mode
  showDialog.value = true
}

onMounted(async () => {
  // await init()
  await onReadAccident()
  const userRes = await getUserServiceConfigById(data.value?.teacherInfo?._id)
  userData.value = userRes
})

async function init() {
  console.log('calling init function')
  try {
    await getCount(true)
    const res = await getOneById(currentId.value)
    const serviceBookingId = res?.serviceBooking
    const serviceTaskId = res?.servicePackUser
    if (serviceBookingId) {
      try {
        const bookingRes = await App.service('service-booking').get(serviceBookingId)

        task.value = bookingRes?.servicePackUser?.snapshot
      } catch (bookingError) {
        console.error('Error fetching service booking:', bookingError)
      }
    }
    if (serviceTaskId) {
      try {
        const sedata = await App.service('section').find({
          query: {
            serviceTaskId: serviceTaskId,
            $limit: 10,
          },
        })
        sectionData.value = sedata.data
      } catch (bookingError) {
        console.error('Error fetching service booking:', bookingError)
      }
    }
    data.value = res
    loading.value = false
    if (data.value?.days) {
      newSuspensionDays.value = data.value.days
    }
    await getStudentImpressment()
  } catch (error) {
    console.error('Error in init function:', error)
  }
}

async function onReadAccident() {
  console.log('read', currentId.value)
  // await patchOneById(currentId.value, {read: true})
  await patchReadById(currentId.value)
  await init()
}

async function onResetVerification(id) {
  await patchOneById(id, {status: 'pending', read: false})
  await init()
}

const rejectReason = ref('')
const isRejectDialogShow = ref(false)
async function onRejectAccident(id) {
  await patchOneById(id, {status: 'rejected', read: true, checkReason: rejectReason.value})
  await init()
  isRejectDialogShow.value = false
}

const suspensionOptions = [
  {label: 'No suspension', value: 0},
  {label: '2 week', value: 14},
  {label: '4 week', value: 28},
]
const suspensionDays = ref(14)
const approveReason = ref('The complain has been approved.')
function clearApproveReason() {
  approveReason.value = 'The complain has been approved.'
}
const isApproveDialogShow = ref(false)
async function onApproveAccident(id) {
  await patchOneById(id, {status: 'approved', read: true, checkReason: approveReason.value, days: suspensionDays.value})
  await init()
  isApproveDialogShow.value = false
}

const isSuspensionDialogShow = ref(false)
const newSuspensionDays = ref(0)
async function updateSuspensionDays(id) {
  await patchOneById(id, {status: 'approved', days: newSuspensionDays.value})
  await init()
  isSuspensionDialogShow.value = false
}
function getSuspensionLabel(days = 0) {
  const target = suspensionOptions.find((e) => e.value === days)
  if (target) return target.label
  else return `${days} days`
}

async function handleDialogInit() {
  console.log('dffbrhfbyrgb')
  await init()
  showDialog.value = false
}

const isPreviewDialogShow = ref(false)
const currentPreviewData = ref(null)
function onPreview(item) {
  currentPreviewData.value = item
  isPreviewDialogShow.value = true
}

const isLogDialogShow = ref(false)
function onLogClick() {
  suspendedAccidentId.value = data.value?._id || ''
  suspendedUid.value = data.value?.suspend?.uid || ''
  isLogDialogShow.value = true
}
</script>

<style lang="scss" scoped>
.item-wrapper {
  border: 1px solid #ddd;
  border-radius: 0.5rem;
  background: #fafafa;
  overflow: hidden;
  width: 10rem;
}

.spinner-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

pre {
  all: unset;
  white-space: pre-wrap;
}
</style>
