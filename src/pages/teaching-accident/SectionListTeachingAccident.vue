<template>
  {{ console.log('section in section list', currentTaskSections) }}
  <div class="row">
    <!-- Left part -->
    <div class="col q-ma-md" style="min-width: 0">
      <q-tab-panels v-model="tab" animated swipeable vertical transition-prev="jump-up" transition-next="jump-up" style="z-index: 9; width: 100%">
        <q-tab-panel v-for="(section, index) in currentTaskSections" :key="section._id" :name="section._id" style="padding: 0; margin: 0">
          <q-card class="q-px-md q-py-sm rounded-xl" style="border: 1px solid #6b6b6b; min-height: 350px">
            <div class="row items-center q-mt-sm q-pl-na q-gutter-x-na">
              <q-space />
              <div
                v-if="section.status !== 'completed' && section.complain && section.complainStatus == 'pending'"
                style="color: #f55c44; font-weight: bold; font-size: 12px">
                Compain Pending
              </div>
              <div class="text-right q-ml-sm text-teal">
                {{ `${index + 1}/${currentTaskSections.length}` }}
              </div>
            </div>

            <div class="flex text-h6 q-my-lg items-center">
              {{ section.name }}
            </div>

            <q-card
              ><q-expansion-item
                expand-separator
                default-opened
                header-class="flex items-center q-ml-none q-pl-none justify-between text-subtitle1 text-weight-medium "
                content-class="q-pa-none"
                class="q-ma-none q-pa-none">
                <template #header>
                  <div>
                    <span>Prompt</span>
                  </div>
                </template>

                <pre style="white-space: pre-wrap; word-wrap: break-word; max-height: 200px; overflow-y: auto; min-height: 100px">{{ section.prompt }}</pre>
              </q-expansion-item></q-card
            >
            <Comments
              :currentSection="section"
              :isReadOnly="true"
              :isReadonlyforProvider="true"
              :servicerInfo="section.servicerInfo"
              :bookerInfo="section.bookerInfo" />
          </q-card>
        </q-tab-panel>
      </q-tab-panels>
    </div>

    <!-- Right part -->
    <div style="width: 235px">
      <div style="position: relative; z-index: 999; width: 100%; height: 100%">
        <div class="flex justify-end text-bold text-teal q-mr-xl text-subtitle1 q-mb-sm">Total {{ currentTaskSections?.length }}</div>
        <div style="max-height: 500px; overflow-y: auto; position: relative">
          <div style="position: absolute; top: 0; bottom: 0; left: 7px; border-left: 2px solid grey; height: auto"></div>
          <q-tabs v-model="tab" vertical indicator-color="transparent" stretch no-caps style="height: 100%; width: 100%">
            <q-tab v-for="(section, index) in currentTaskSections" :key="index" :name="section._id" class="q-py-sm q-mb-lg">
              <div class="row">
                <span style="position: absolute; left: -23px; top: 12px; z-index: 999">
                  <q-avatar color="teal" text-color="white" size="16px" style="z-index: 999">
                    {{ index + 1 }}
                  </q-avatar>
                </span>

                <div
                  :style="{
                    width: '3px',
                    borderTopLeftRadius: '8px',
                    borderBottomLeftRadius: '8px',
                    backgroundColor: section._id === tab ? getTextColor(section.status) : 'transparent',
                  }" />
                <div class="coloumn">
                  <div
                    class="row items-center justify-between q-py-sm"
                    :style="{
                      backgroundColor: getBackgroundColor(section.status),
                      width: '180px',
                    }">
                    <div class="text-caption q-ml-sm" :style="{color: getTextColor(section.status)}">
                      <q-tooltip v-if="section.name.length > 8">
                        {{ section.name }}
                      </q-tooltip>
                      {{ truncate(section.name) }}
                    </div>

                    <q-chip
                      square
                      :ripple="false"
                      :label="section.status"
                      :style="{
                        width: '61px',
                        height: '20px',
                        gap: '10px',
                        borderRadius: '4px',
                        padding: '2px 4px',
                        backgroundColor: getChipColor(section.status),
                        color: getTextColor(section.status),
                        fontWeight: 500,
                        fontSize: '10px',
                        textTransform: 'capitalize',
                      }" />
                  </div>
                  <div class="row">
                    <div
                      class="q-mx-sm"
                      :style="{
                        height: '37px',
                        fontSize: '14px',
                        textTransform: 'none',
                        display: 'flex',
                        alignItems: 'center',
                        color: 'black',
                      }">
                      <div class="row items-center justify-end" style="width: 100%">
                        <div style="display: flex; align-items: center; gap: 10px">
                          <div>Credit: {{ section.status === 'completed' ? section.costPrice : 0 }}/{{ section.costPrice }}</div>
                          <!-- <div v-if="section.availableCredits < section.salesPrice" class="text-subtitle2 q-ml-md" style="color: red">
                            Deficit: {{ section.salesPrice - section.availableCredits }}
                          </div> -->
                        </div>
                      </div>
                    </div>
                  </div>

                  <div
                    :style="{
                      borderBottom: section.status !== 'refunded' ? '1px solid lightgray' : 'none',
                    }" />
                  <div v-if="section.status !== 'refunded' || isUnassigned" class="row q-mx-sm items-center justify-between q-py-sm">
                    <div class="text-subtitle2" style="padding: 0; color: black; text-transform: none; margin: 0; font-weight: normal">
                      {{ section.servicerInfo?.name?.join(' ') }}
                    </div>
                    <q-avatar size="24px">
                      <img :src="section.servicerInfo?.avatar" />
                      <q-tooltip>
                        {{ section.servicerInfo?.name[0] }}
                      </q-tooltip>
                    </q-avatar>
                  </div>
                </div>
              </div>
            </q-tab>
          </q-tabs>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import {ref, computed, watch, onMounted} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import Comments from 'src/components/ServiceTask/Comments.vue'

const route = useRoute()
const router = useRouter()

const props = defineProps({
  currentTaskSections: {
    type: Array,
    default: () => [],
  },
})

const tab = ref(props.currentTaskSections[0]?._id || '')
const defaultSection = computed(() => {
  return props.currentTaskSections?.find((section) => section.status === 'ongoing')
})
const isCollapsed = ref(false)

function toggle() {
  isCollapsed.value = !isCollapsed.value
}

function getBackgroundColor(status) {
  if (status === 'pending') return '#FFFBE6'
  if (status === 'ongoing') return '#E6F8FF'
  if (status === 'completed') return '#D5F0D8'
  if (status === 'refunded') return '#FFEEEF'
  return '#ffffff'
}

function getTextColor(status) {
  if (status === 'pending') return '#DFA208'
  if (status === 'ongoing') return '#10A4DF'
  if (status === 'completed') return '#0F8D49'
  if (status === 'refunded') return '#E2452D'
  return '#000000'
}

function getChipColor(status) {
  if (status === 'pending') return '#F8EEBD'
  if (status === 'ongoing') return '#BDE8F8'
  if (status === 'completed') return '#A0E5A7'
  if (status === 'refunded') return '#FEDEDC'
  return '#000000'
}

watch(
  defaultSection,
  (newVal) => {
    if (newVal) tab.value = newVal._id
  },
  {immediate: true}
)

function truncate(text, length = 8) {
  return text.length > length ? text.slice(0, length) + '…' : text
}
</script>

<style scoped>
.q-tabs .q-tab__content {
  width: calc(100% - 20px) !important;
  align-items: stretch;
}
.q-tabs--left .q-tab__indicator {
  left: 0 !important;
  right: auto !important;
  width: 3px !important;
}
::v-deep(.q-splitter__separator) {
  width: 3px !important;
  z-index: 1 !important;
  pointer-events: none;
  position: relative;
  left: 10px;
}
pre {
  all: unset;
  white-space: pre-wrap;
}
</style>
