<template>
  <q-dialog v-model="showDialog" persistent>
    <q-card class="q-pa-md" style="width: 450px; max-width: 90vw; border-radius: 12px; background-color: #e0f2f1">
      <!-- Title Section -->
      <q-card-section v-if="props.curMode === 'approve'">
        <div class="text-subtitle1 q-mb-md">Approve reason</div>
        <RecordInput v-model="reasonForDecision" :counter="true" :maxlength="1000" outlined />
      </q-card-section>

      <q-card-section v-else-if="props.curMode === 'reject'">
        <div class="text-subtitle1 q-mb-md">Reject reason</div>
        <RecordInput v-model="reasonForDecision" :counter="true" :maxlength="1000" outlined placeholder="Please enter the reason rejection" />
      </q-card-section>

      <!-- Actions button -->
      <q-card-actions class="q-pa-none">
        <div class="row q-col-gutter-sm full-width">
          <div class="col-6">
            <q-btn flat label="Cancel" color="primary" class="full-width" style="border-radius: 100px; border: 1px solid black" @click="showDialog = false" />
          </div>
          <div class="col-6">
            <q-btn
              label="Confirm"
              color="primary"
              class="full-width"
              style="border-radius: 100px"
              @click="curMode === 'approve' ? onApproveAccident(pathDataId) : onRejectAccident(pathDataId)" />
          </div>
        </div>
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import {ref, watch, computed} from 'vue'
import {serviceTaskStore} from 'stores/serviceTask'
import {pubStore} from 'stores/pub'
import RecordInput from 'src/components/utils/inputs/RecordInput.vue'

const emit = defineEmits(['close', 'init'])

const showDialog = ref(true)

const fileInput = ref(null)
const serviceTask = serviceTaskStore()
const pub = pubStore()
const files = ref([])

const props = defineProps({
  curMode: {
    type: String,
    default: 'approve',
  },
  pathDataId: {
    type: String,
    default: '',
  },
  sectionData: {
    type: Array,
    deafault: () => [],
  },
})

const reasonForDecision = ref(props.curMode === 'approve' ? 'Your complain has been approved' : '')

async function handleFileChange() {
  const rs = await Fn.fileUpLoadUiX('image/*, video/*')
  if (!rs) {
    return
  }
  console.log('value of rs:', rs)
  if (rs.message) {
    return $q.notify({type: 'negative', message: rs.message})
  }
  console.log(pub.user)
  const isVideo = rs.mime.includes('video')

  console.log('dbfyh', isVideo)

  files.value.push({
    cover: rs._id, // your “hash”
    mime: isVideo ? 'video' : 'image',
    filename: rs.originalName || rs.filename || 'unknown',
  })
}

function removeFile(index) {
  files.value.splice(index, 1)
}

async function onApproveAccident(id) {
  //   await patchOneById(id, {status: 'approved', read: true, checkReason: reasonForDecision.value})
  serviceTask.currentSection = props.sectionData?.find((section) => section.status === 'ongoing')
  console.log('fbrhfgyrgur')

  try {
    await App.service('teaching-accident').patch(id, {status: 'approved', read: true, checkReason: reasonForDecision.value})
    await App.service('section').patch(serviceTask.currentSection._id, {markAsCompleted: false, isViewAppeal: true})
  } catch (error) {
    console.error('Failed to Approve :', error)
  } finally {
    emit('init')
  }
}

async function onRejectAccident(id) {
  // await patchOneById(id, {status: 'rejected', read: true, checkReason: reasonForDecision.value})
  serviceTask.currentSection = props.sectionData?.find((section) => section.status === 'ongoing')
  try {
    const nextSection = props.sectionData?.find((section) => section.sectionNo === serviceTask.currentSection?.sectionNo + 1)
    console.log('gkgkglkglgl', nextSection, props.sectionData)
    await serviceTask.handleConfirmAsCompleted(serviceTask.currentSection?._id, true)
    await App.service('section').patch(serviceTask.currentSection._id, {isViewAppeal: true})
    await serviceTask.updateNextSectionStatus(nextSection, 'ongoing')
    await App.service('teaching-accident').patch(id, {status: 'rejected', read: true, checkReason: reasonForDecision.value})
  } catch (error) {
    console.error('Failed to reject:', error)
  } finally {
    emit('init')
  }
}
// async function submitComplaint() {
//   if (!reasonForDecision.value.trim()) {
//     $q.notify({type: 'negative', message: 'Please provide complain reasonForDecision'})
//     return
//   }

//   const data = {
//     student: serviceTask.currentSection.uid,
//     teacher: serviceTask.currentSection.servicer,
//     session: serviceTask.currentSection._id,
//     sessionName: serviceTask.currentSection.name,
//     sessionPrompt: serviceTask.currentSection.prompt,
//     booking: serviceTask.currentSection.bookingId,
//     servicePackUser: serviceTask.currentSection.serviceTaskId,
//     tags: 'Associated task complains',
//     evidencesStudent: [
//       {
//         content: reasonForDecision.value,
//         attachments: files.value.map((f) => ({
//           filename: f.filename,
//           mime: f.mime,
//           hash: f.cover,
//         })),
//       },
//     ],
//     status: 'pending',
//   }

//   console.log('data', data)

//   const accident = await App.service('teaching-accident').create(data)

//   $q.notify({type: 'positive', message: 'Complain submitted successfully'})
//   emit('close')
// }

watch(showDialog, (val) => {
  if (!val) emit('close')
})
</script>

<style scoped>
.absolute-top-right {
  position: absolute;
  top: -8px;
  right: -8px;
  z-index: 1;
}
</style>
