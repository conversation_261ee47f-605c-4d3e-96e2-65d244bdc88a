<template>
  <q-page class="full-height pc-max">
    <div class="q-pl-md q-pr-md q-mt-lg">
      <div class="text-subtitle2 q-mb-md row justify-between items-center" v-if="child?.data?.length > 0">Bonus for ambassador</div>
      <div class="text-subtitle2 q-mb-md row justify-between items-center" v-else>
        Please set bonus for ambassador
        <q-btn class="" color="primary" label="Setting" no-caps rounded size="" style="width: 200px" @click="add()"></q-btn>
      </div>
    </div>
    <DetailView ref="child" :load="handleLoad" class="">
      <div class="q-pa-md">
        <div class="" v-for="item in child.data" :key="item._id">
          <div>
            <span>Accumulated Sales Figure (monthly)</span>
            <span class="text-primary"> ${{ item.accumulated }} </span>
            <span class="q-ml-md q-mr-md">, </span>
            <span>Bonus Rate</span>
            <span class="text-primary q-ml-md"> {{ item.value }} {{ item.mode === 'percentage' ? '%' : '' }}</span>
            <q-btn flat dense text-color="grey-8" icon="edit" @click="add(item)"></q-btn>
          </div>
        </div>
      </div>

      <template v-slot:noData>
        <NoData title="No ways of bonus set" message="Please add new" messageColor="text-grey" />
      </template>
    </DetailView>
  </q-page>
</template>

<script setup>
import {ref, onMounted, computed} from 'vue'
import {useRoute} from 'vue-router'
import DetailView from 'components/DetailView.vue'

import BonusDialog from './BonusDialog.vue'

const route = useRoute()
// const router = useRouter()

const child = ref(null)

const tab = ref(route.query.tab || 'agency')

const add = (item) => {
  $q.dialog({
    component: BonusDialog,
    componentProps: {
      item,
      isEdit: !!item,
      list: child.value?.data,
    },
  }).onOk(() => {
    $q.notify({type: 'positive', message: 'Add successfully'})
    onLoad()
  })
}

const handleLoad = async () => {
  const query = {
    $limit: 100,
    $sort: {createdAt: -1},
    tab: 'earn',
    category: 'ambassador_school',
  }
  return App.service('bonus-setting').find({query})
}

const onLoad = async () => {
  child.value.onLoad()
}

onMounted(() => {
  onLoad()
})
</script>
<style lang="scss" scope>
.close_pos {
  top: -12px;
  right: -12px;
  display: none;
}
.hover_close {
  &:hover {
    .close_pos {
      display: block;
    }
  }
}
</style>
