<template>
  <q-dialog ref="dialogRef" @hide="onDialogHide" maximized>
    <q-card class="q-pa-lg" style="width: 900px; height: 420px">
      <div class="text-h6">Bonus Setting for Ambassador</div>
      <q-form @submit="onSubmit">
        <q-card-section>
          <div class="text-subtitle1">Accumulated Sales Figure (Monthly)</div>
          <div class="row q-col-gutter-md">
            <div class="col-6">
              <q-input v-model="form.accumulated" :rules="[(val) => (val && val >= 0) || 'Value must be non-negative']" outlined stack-label type="number" />
            </div>
          </div>
        </q-card-section>
        <q-card-section>
          <div class="text-subtitle1">Bonus rate</div>
          <div class="row q-col-gutter-md">
            <div class="col-6">
              <q-input v-model="form.value" :rules="[(val) => (val && val >= 0) || 'Value must be non-negative']" outlined stack-label type="number">
                <template v-slot:after>
                  <q-icon name="percent" />
                </template>
              </q-input>
            </div>
          </div>
        </q-card-section>
        <q-card-actions align="right" class="text-primary">
          <q-btn flat label="Cancel" no-caps rounded v-close-popup />
          <q-btn color="primary" :disable="form.value && form.accumulated" :loading="loading" no-caps rounded label="Save" type="submit" />
        </q-card-actions>
      </q-form>
    </q-card>
  </q-dialog>
</template>

<script setup>
import {ref, onMounted} from 'vue'

import {useDialogPluginComponent} from 'quasar'
import useRequest from 'components/hooks/useRequest'

const form = ref({
  accumulated: '',
  value: '',
})

const props = defineProps({
  isEdit: Boolean,
  item: Object,
})

defineEmits([...useDialogPluginComponent.emits])

const {dialogRef, onDialogHide, onDialogOK} = useDialogPluginComponent()

const {loading, run: onSubmit} = useRequest(
  async () => {
    const data = {
      tab: 'earn',
      category: 'ambassador_school',
      accumulated: +form.value.accumulated,
      mode: 'percentage',
      value: +form.value.value,
    }
    return props?.item?._id ? App.service('bonus-setting').patch(props?.item?._id, data) : App.service('bonus-setting').create(data)
  },
  {
    onSuccess: () => {
      onDialogOK()
    },
  }
)

const main = async () => {
  if (props.isEdit) {
    form.value = {
      value: props.item.value,
      accumulated: props.item.accumulated,
    }
  }
}

onMounted(main)
</script>
