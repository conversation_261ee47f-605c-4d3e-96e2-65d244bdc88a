<template>
  <div class="order-disputes-container">
    <div class="page-header">
      <h4 class="text-h4 text-weight-medium text-grey-8 q-mb-sm">Order Disputes</h4>
      <p class="text-body2 text-grey-6">Manage incomplete orders and refund processing</p>
    </div>

    <q-tabs v-model="activeTab" class="text-teal-7 q-mb-lg" indicator-color="teal" active-color="teal" align="left" dense no-caps="no-caps">
      <q-tab name="incomplete-orders" label="Incomplete Orders" />
      <q-tab name="incomplete-refunds" label="Incomplete Refunds" />
    </q-tabs>

    <q-tab-panels v-model="activeTab" animated>
      <q-tab-panel name="incomplete-orders" class="q-pa-none">
        <q-card class="shadow-2">
          <q-card-section class="bg-grey-1">
            <div class="row items-center justify-between">
              <div>
                <div class="text-h6 text-weight-medium text-grey-8">Incomplete Orders</div>
                <div class="text-caption text-grey-6">Orders with successful payment but failed completion</div>
              </div>
              <div>
                <q-checkbox v-model="showCompleted" label="Show completed orders" color="primary" class="q-pr-md" />
                <q-btn
                  color="teal"
                  icon="refresh"
                  label="Refresh"
                  @click="handleIncompleteOrdersRequest({pagination: incompleteOrdersPagination})"
                  :loading="loadingIncompleteOrders"
                  unelevated />
              </div>
            </div>
          </q-card-section>

          <q-separator />

          <q-card-section class="q-pa-none">
            <q-table
              :rows="incompleteOrders"
              :columns="incompleteOrdersColumns"
              row-key="_id"
              :loading="loadingIncompleteOrders"
              :rows-per-page-options="[10, 25, 50]"
              v-model:pagination="incompleteOrdersPagination"
              @request="handleIncompleteOrdersRequest"
              class="admin-table"
              flat
              bordered>
              <template v-slot:body-cell-paidAt="props">
                <q-td :props="props">
                  <div class="text-caption text-grey-7">
                    {{ formatDate(props.value) }}
                  </div>
                </q-td>
              </template>

              <template v-slot:body-cell-status="props">
                <q-td :props="props">
                  <q-chip :color="getStatusColor(props.value)" text-color="white" size="sm">
                    {{ props.value }}
                  </q-chip>
                </q-td>
              </template>

              <template v-slot:body-cell-price="props">
                <q-td :props="props">
                  <span class="text-weight-medium">${{ (props.value / 100).toFixed(2) }}</span>
                </q-td>
              </template>

              <template v-slot:body-cell-payMethod="props">
                <q-td :props="props">
                  <q-chip v-for="method in props.value" :key="method" color="blue-grey-2" text-color="blue-grey-8" size="sm" class="q-mr-xs">
                    {{ method }}
                  </q-chip>
                </q-td>
              </template>

              <template v-slot:body-cell-retryInfo="props">
                <q-td :props="props">
                  <q-chip :color="props.value > 3 ? 'red' : props.value > 1 ? 'orange' : 'green'" text-color="white" size="sm">
                    {{ props.value?.count || 0 }}
                  </q-chip>
                  <div v-if="props.value?.updatedAt" class="text-caption text-grey-7">
                    {{ formatDate(props.value?.updatedAt) }}
                  </div>
                  <div v-if="props.value?.reason" class="text-caption text-grey-7">
                    {{ props.value?.reason }}
                  </div>
                </q-td>
              </template>

              <template v-slot:no-data>
                <div class="full-width row flex-center text-grey-6 q-gutter-sm q-pa-lg">
                  <q-icon size="2em" name="inbox" />
                  <span class="text-subtitle1">No incomplete orders found</span>
                </div>
              </template>
            </q-table>
          </q-card-section>
        </q-card>
      </q-tab-panel>

      <q-tab-panel name="incomplete-refunds" class="q-pa-none">
        <q-card class="shadow-2">
          <q-card-section class="bg-grey-1">
            <div class="row items-center justify-between">
              <div>
                <div class="text-h6 text-weight-medium text-grey-8">Incomplete Refunds</div>
                <div class="text-caption text-grey-6">Orders requiring refund processing</div>
              </div>
              <q-btn
                color="teal"
                icon="refresh"
                label="Refresh"
                @click="handleIncompleteRefundsRequest({pagination: incompleteRefundsPagination})"
                :loading="loadingIncompleteRefunds"
                unelevated />
            </div>
          </q-card-section>

          <q-separator />

          <q-card-section class="q-pa-none">
            <q-table
              :rows="incompleteRefunds"
              :columns="incompleteRefundsColumns"
              row-key="_id"
              :loading="loadingIncompleteRefunds"
              :rows-per-page-options="[10, 25, 50]"
              v-model:pagination="incompleteRefundsPagination"
              @request="handleIncompleteRefundsRequest"
              class="admin-table"
              flat
              bordered>
              <template v-slot:body-cell-status="props">
                <q-td :props="props">
                  <q-chip :color="getStatusColor(props.value)" text-color="white" size="sm">
                    {{ props.value }}
                  </q-chip>
                </q-td>
              </template>

              <template v-slot:body-cell-price="props">
                <q-td :props="props">
                  <span class="text-weight-medium">${{ (props.value / 100).toFixed(2) }}</span>
                </q-td>
              </template>

              <template v-slot:body-cell-payMethod="props">
                <q-td :props="props">
                  <q-chip v-for="method in props.value" :key="method" color="blue-grey-2" text-color="blue-grey-8" size="sm" class="q-mr-xs">
                    {{ method }}
                  </q-chip>
                </q-td>
              </template>

              <template v-slot:body-cell-refundFailedTime="props">
                <q-td :props="props">
                  <div class="text-caption text-grey-7">
                    {{ formatDate(props.value) }}
                  </div>
                </q-td>
              </template>

              <template v-slot:body-cell-refundAmount="props">
                <q-td :props="props">
                  <span :class="formatAmount(props.value) === 'N/A' ? 'text-caption text-grey-7' : 'text-weight-medium text-red-7'">{{
                    formatAmount(props.value)
                  }}</span>
                </q-td>
              </template>

              <template v-slot:body-cell-escalated="props">
                <q-td :props="props">
                  <q-icon :name="props.value ? 'warning' : 'check_circle'" :color="props.value ? 'orange' : 'green'" size="sm" @click="retryRefund()" />
                  <span class="q-ml-xs text-caption">
                    {{ props.value ? 'Escalated' : 'Normal' }}
                  </span>
                </q-td>
              </template>

              <template v-slot:no-data>
                <div class="full-width row flex-center text-grey-6 q-gutter-sm q-pa-lg">
                  <q-icon size="2em" name="inbox" />
                  <span class="text-subtitle1">No incomplete refunds found</span>
                </div>
              </template>
            </q-table>
          </q-card-section>
        </q-card>
      </q-tab-panel>
    </q-tab-panels>
  </div>
</template>
<script setup>
import {ref, onMounted, watch} from 'vue'
import {date} from 'quasar'

const activeTab = ref('incomplete-orders')
const showCompleted = ref(false)
const incompleteOrders = ref([])
const incompleteRefunds = ref([])
const loadingIncompleteOrders = ref(false)
const loadingIncompleteRefunds = ref(false)

const incompleteOrdersPagination = ref({
  sortBy: 'paidAt',
  descending: true,
  page: 1,
  rowsPerPage: 10,
  rowsNumber: 0,
})

const incompleteRefundsPagination = ref({
  sortBy: 'refundFailedTime',
  descending: true,
  page: 1,
  rowsPerPage: 10,
  rowsNumber: 0,
})

const incompleteOrdersColumns = [
  {
    name: 'orderId',
    required: true,
    label: 'Order ID',
    align: 'left',
    field: '_id',
    sortable: true,
  },
  {
    name: 'paidAt',
    align: 'center',
    label: 'Paid At',
    field: 'paidAt',
    sortable: true,
  },
  {
    name: 'status',
    align: 'center',
    label: 'Status Code',
    field: 'status',
    sortable: true,
  },
  {
    name: 'price',
    align: 'right',
    label: 'Amount',
    field: 'price',
    sortable: true,
  },
  {
    name: 'payMethod',
    align: 'center',
    label: 'Pay Method',
    field: 'payMethod',
  },
  {
    name: 'retryInfo',
    align: 'center',
    label: 'Retry Count',
    field: (row) => row.retryInfo || {},
    sortable: true,
  },
]

const incompleteRefundsColumns = [
  {
    name: 'orderId',
    required: true,
    label: 'Order ID',
    align: 'left',
    field: '_id',
    sortable: true,
  },
  {
    name: 'status',
    align: 'center',
    label: 'Status Code',
    field: 'status',
    sortable: true,
  },
  {
    name: 'price',
    align: 'right',
    label: 'Amount',
    field: 'price',
    sortable: true,
  },
  {
    name: 'payMethod',
    align: 'center',
    label: 'Pay Method',
    field: 'payMethod',
  },
  {
    name: 'refundFailedTime',
    align: 'center',
    label: 'Refund Failed Time',
    field: (row) => row.refundRequired?.createdAt,
    sortable: true,
  },
  {
    name: 'refundAmount',
    align: 'right',
    label: 'Refund Amount',
    field: (row) => row.refundRequired?.amount,
    sortable: true,
  },
  {
    name: 'escalated',
    align: 'center',
    label: 'Escalated',
    field: (row) => row.refundRequired?.escalated,
    sortable: true,
  },
]

const retryRefund = async () => {
  console.log('Retry Refund')
  await App.service('order').get('retryFailedRefunds')
}

watch(showCompleted, () => {
  incompleteOrdersPagination.value.page = 1
  handleIncompleteOrdersRequest({pagination: incompleteOrdersPagination.value})
})

const handleIncompleteOrdersRequest = async (props) => {
  const {page, rowsPerPage, sortBy, descending} = props.pagination
  loadingIncompleteOrders.value = true

  const limit = rowsPerPage
  const skip = (page - 1) * rowsPerPage
  const sort = {}
  if (sortBy) {
    sort[sortBy] = descending ? -1 : 1
  }

  try {
    const fiveMinutesAgo = date.subtractFromDate(new Date(), {minutes: 5})
    const query = {
      $sys: 1,
      $limit: limit,
      $skip: skip,
      $sort: sort,
    }
    if (showCompleted.value) {
      query['retryInfo.count'] = {$gt: 0}
    } else {
      query['status'] = 110
      query['paidAt'] = {$lt: fiveMinutesAgo}
    }
    const result = await App.service('order').find({
      query,
    })
    incompleteOrders.value = result.data || []

    incompleteOrdersPagination.value.page = page
    incompleteOrdersPagination.value.rowsPerPage = rowsPerPage
    incompleteOrdersPagination.value.sortBy = sortBy
    incompleteOrdersPagination.value.descending = descending
    incompleteOrdersPagination.value.rowsNumber = result.total
  } catch (error) {
    console.error('Error fetching incomplete orders:', error)
  } finally {
    loadingIncompleteOrders.value = false
  }
}

const handleIncompleteRefundsRequest = async (props) => {
  const {page, rowsPerPage, sortBy, descending} = props.pagination
  loadingIncompleteRefunds.value = true

  const limit = rowsPerPage
  const skip = (page - 1) * rowsPerPage
  const sort = {}
  const sortField = sortBy === 'refundFailedTime' ? 'refundRequired.createdAt' : sortBy
  if (sortField) {
    sort[sortField] = descending ? -1 : 1
  }

  try {
    const tenMinutesAgo = date.subtractFromDate(new Date(), {minutes: 10})
    const result = await App.service('order').find({
      query: {
        $sys: 1,
        'refundRequired.createdAt': {$lt: tenMinutesAgo},
        $limit: limit,
        $skip: skip,
        $sort: sort,
      },
    })
    incompleteRefunds.value = result.data || []

    incompleteRefundsPagination.value.page = page
    incompleteRefundsPagination.value.rowsPerPage = rowsPerPage
    incompleteRefundsPagination.value.sortBy = sortBy
    incompleteRefundsPagination.value.descending = descending
    incompleteRefundsPagination.value.rowsNumber = result.total
  } catch (error) {
    console.error('Error fetching incomplete refunds:', error)
  } finally {
    loadingIncompleteRefunds.value = false
  }
}

const getStatusColor = (status) => {
  switch (status) {
    case 100:
      return 'orange'
    case 200:
      return 'green'
    case 400:
      return 'red'
    default:
      return 'grey'
  }
}

const formatDate = (dateString) => {
  if (!dateString) return 'N/A'
  const d = new Date(dateString)
  return d.toLocaleDateString() + ' ' + d.toLocaleTimeString()
}

const formatAmount = (amount) => {
  return Number(amount) && !isNaN(parseInt(amount)) ? '$' + (amount / 100).toFixed(2) : 'N/A'
}

onMounted(() => {
  handleIncompleteOrdersRequest({pagination: incompleteOrdersPagination.value})
  handleIncompleteRefundsRequest({pagination: incompleteRefundsPagination.value})
})
</script>

<style scoped>
.order-disputes-container {
  padding: 24px;
  max-width: 1600px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 32px;
}

.admin-table {
  border-radius: 8px;
  overflow: hidden;
}

.admin-table .q-table__top {
  background: #f5f5f5;
}

.admin-table .q-table thead th {
  background: #f8f9fa;
  font-weight: 600;
  color: #495057;
  border-bottom: 2px solid #dee2e6;
}

.admin-table .q-table tbody tr:hover {
  background-color: #f8f9fa;
}

.admin-table .q-table td {
  padding: 12px;
  border-bottom: 1px solid #dee2e6;
}

.q-tab-panels {
  background: transparent;
}

.q-card {
  border-radius: 12px;
  border: 1px solid #e0e0e0;
}

.q-chip {
  font-weight: 500;
}
</style>
