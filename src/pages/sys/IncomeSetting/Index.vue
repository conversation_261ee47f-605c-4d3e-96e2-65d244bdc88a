<template>
  <q-page class="q-pa-md">
    <div class="q-mb-lg">
      <h4 class="text-h5 text-weight-bold">Ways of earning Income</h4>
    </div>

    <div v-if="loading" class="q-gutter-md">
      <q-skeleton v-for="n in 6" :key="n" type="rect" height="60px" class="rounded-borders" />
    </div>

    <div v-else class="">
      <div v-for="item in incomeData" :key="item.key" class="q-pa-sm">
        <div class="row items-center">
          <div class="text-subtitle1 text-weight-medium q-pa-md" style="border-radius: 8px; border: 1px solid #aeaaae">
            {{ item.label }}
          </div>

          <div v-if="item.children" class="row q-ml-sm q-gutter-x-md">
            <div v-for="child in item.children" :key="child.key" class="text-subtitle1">
              <span class="text-primary">{{ child.label }}</span>

              <span class="text-primary text-weight-bold q-ml-sm">{{ child.value }}<span v-if="child.value !== 'N/A'">%</span></span>
            </div>
          </div>

          <div v-else class="text-primary text-weight-bold text-body1 q-ml-sm">{{ item.value }}<span v-if="item.value !== 'N/A'">%</span></div>

          <div>
            <q-btn @click="handleEdit(item)" icon="edit" flat round color="black" size="md">
              <q-tooltip>Edit {{ item.label }}</q-tooltip>
            </q-btn>
          </div>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup>
import {ref, onMounted} from 'vue'
import {incomeModel} from './const'
import IncomeDialog from './IncomeDialog.vue'

const incomeData = ref({})
const loading = ref(true)

const fetchIncomeSettings = async () => {
  try {
    loading.value = true

    const categories = incomeModel.flatMap((item) => (item.children ? item.children.map((c) => c.key) : [item.key]))

    const response = await App.service('income-setting').find({
      query: {
        category: {$in: categories},
      },
    })

    const apiMap = Object.fromEntries(response.data.map((i) => [i.category, i]))

    incomeData.value = incomeModel.map((item) => {
      if (item.children) {
        return {
          ...item,
          children: item.children.map((c) => ({
            ...c,
            _id: apiMap[c.key]?._id ?? null,
            value: apiMap[c.key]?.value ?? 'N/A',
          })),
        }
      } else {
        return {
          ...item,
          _id: apiMap[item.key]?._id ?? null,
          value: apiMap[item.key]?.value ?? 'N/A',
        }
      }
    })
  } catch (error) {
    console.error('Error fetching income settings:', error)
    $q.notify({
      type: 'negative',
      message: 'Failed to load income settings',
      position: 'top',
    })
  } finally {
    loading.value = false
  }
}

const handleEdit = (item) => {
  $q.dialog({
    component: IncomeDialog,
    componentProps: {
      item,
    },
  }).onOk(() => {
    $q.notify({type: 'positive', message: 'Add successfully'})
    fetchIncomeSettings()
  })
}

onMounted(() => {
  fetchIncomeSettings()
})
</script>

<style scoped>
.q-card {
  transition: all 0.3s ease;
}

.q-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
</style>
