<template>
  <q-dialog ref="dialogRef" @hide="onDialogHide">
    <q-card class="q-pa-sm" style="width: 700px; max-width: 80vw; border-radius: 12px; background-color: #eef4f6">
      <q-card-section>
        <div class="text-h6">Edit Income Rate</div>
      </q-card-section>

      <q-card-section>
        <div class="text-body1 q-pa-na text-weight-medium">{{ item.label }}</div>
      </q-card-section>

      <q-form @submit="onSubmit">
        <q-card-section class="q-pt-none">
          <div v-if="item.children && item.children.length > 0" class="q-gutter-md">
            <div v-for="child in item.children" :key="child.key" class="q-mb-md">
              <div class="text-body2 q-mb-sm">{{ child.label }}</div>
              <div class="col-6" style="width: 75%">
                <q-input
                  :model-value="getFormValue(child.key)"
                  @update:model-value="updateFormValue(child.key, $event)"
                  type="number"
                  inputmode="numeric"
                  outlined
                  stack-label
                  min="0"
                  :rules="[
                    (val) => (val !== null && val !== '' && Number(val) >= 0) || 'Value must be non-negative',
                    (val) => /^\d*\.?\d{0,1}$/.test(val) || 'Only one decimal place is allowed',
                  ]"
                  :label="
                    child.key === 'teacher_verification_referral_associated_task'
                      ? 'Percentage of (sales price - cost price) for each session'
                      : 'Percentage of sales price'
                  ">
                  <template v-slot:after>
                    <q-icon name="percent" />
                  </template>
                </q-input>
              </div>
            </div>

            <div v-if="item.key === 'teaching_service'" class="q-mt-sm">
              <q-icon name="info_outline" size="sm" color="grey-7" class="q-mr-xs" />
              <span class="text-body2 text-grey-7">
                Income percentage for teaching session using own content must be set higher than that using Classcipe provided Content
              </span>
            </div>
          </div>

          <div v-else>
            <div class="col-6" style="width: 75%">
              <q-input
                :model-value="getFormValue(item.key)"
                @update:model-value="updateFormValue(item.key, $event)"
                inputmode="numeric"
                outlined
                stack-label
                type="number"
                min="0"
                :rules="[
                  (val) => (val !== null && val !== '') || 'Field is required',
                  (val) => Number(val) >= 0 || 'Value must be non-negative',
                  (val) => /^\d*\.?\d{0,1}$/.test(val) || 'Only one decimal place is allowed',
                ]"
                :label="getLabel(item.key)">
                <template v-slot:after>
                  <q-icon name="percent" />
                </template>
              </q-input>
            </div>
          </div>
        </q-card-section>

        <q-card-actions align="right" class="q-pr-md q-pb-md">
          <q-btn outline rounded color="primary" label="Cancel" no-caps v-close-popup />
          <q-btn unelevated rounded color="primary" label="Save" no-caps type="submit" :loading="loading" :disable="!isFormValid" style="width: 60px" />
        </q-card-actions>
      </q-form>
    </q-card>
  </q-dialog>
</template>

<script setup>
import {ref, onMounted, computed} from 'vue'
import {useDialogPluginComponent} from 'quasar'
import useRequest from 'components/hooks/useRequest'

const props = defineProps({item: {type: Object, required: true}})
defineEmits([...useDialogPluginComponent.emits])

const {dialogRef, onDialogHide, onDialogOK} = useDialogPluginComponent()
const form = ref({})

const getFormValue = (key) => {
  return form.value[key] ?? null
}

const updateFormValue = (key, value) => {
  form.value[key] = value
}

const getLabel = (key) => {
  if (key === 'premium_contents_unit_module' || key === 'associated_task') return 'Percentage of sales price for each session'
  return 'Percentage of sales price'
}

const normalizeValue = (val) => (val === 'N/A' ? null : val)

const isFormValid = computed(() => {
  if (!form.value || Object.keys(form.value).length === 0) {
    return false
  }

  return Object.values(form.value).every((val) => {
    const isNonNegative = val !== null && val !== '' && !isNaN(val) && Number(val) >= 0

    const hasOneDecimalPlace = /^\d*\.?\d{0,1}$/.test(val)

    return isNonNegative && hasOneDecimalPlace
  })
})

onMounted(() => {
  const formValues = {}

  if (props.item.children && props.item.children.length) {
    props.item.children.forEach((child) => {
      formValues[child.key] = normalizeValue(child.value)
    })
  } else {
    formValues[props.item.key] = normalizeValue(props.item.value)
  }

  form.value = formValues
})

const {loading, run: onSubmit} = useRequest(
  async () => {
    const service = App.service('income-setting')
    const apiPromises = []

    if (props.item.children) {
      props.item.children.forEach((child) => {
        const newValue = Number(normalizeValue(form.value[child.key])) || 0

        if (child._id) {
          apiPromises.push(service.patch(child._id, {value: newValue}))
        } else {
          const payload = {
            category: child.key,
            value: newValue,
          }
          apiPromises.push(service.create(payload))
        }
      })
    } else {
      const newValue = Number(normalizeValue(form.value[props.item.key])) || 0

      if (props.item._id) {
        const payload = {value: newValue}
        apiPromises.push(service.patch(props.item._id, payload))
      } else {
        const payload = {
          category: props.item.key,
          value: newValue,
        }
        apiPromises.push(service.create(payload))
      }
    }

    return await Promise.all(apiPromises)
  },
  {
    onSuccess: () => {
      onDialogOK()
    },
  }
)
</script>

<style scoped></style>
