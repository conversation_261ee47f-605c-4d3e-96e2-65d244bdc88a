<template>
  <div>
    <PubTopBanner :title="`Teaching accident`" :useBack="true" :back="`/sys/teaching-accident`" />

    <q-page class="">
      <div class="pc-max q-px-md q-mt-xs">
        <main class="column no-wrap full-width relative-position">
          <!-- Pending state -->
          <h1>gbijhujbiungidfhgughguthu</h1>
          <q-card v-if="data.status === 'pending'" class="bg-blue full-width q-pa-md flex justify-between items-center q-my-md">
            <div>Teaching accident status: <b>Pending</b></div>
            <div class="flex q-gutter-md">
              <q-btn
                rounded
                flat
                no-caps
                label="Rejected"
                class="bg-red text-white"
                @click="
                  () => {
                    isRejectDialogShow = true
                    rejectReason = ''
                  }
                " />
              <q-btn
                rounded
                flat
                no-caps
                label="Approved"
                class="bg-green text-white"
                @click="
                  () => {
                    isApproveDialogShow = true
                    clearApproveReason()
                  }
                " />
            </div>
          </q-card>

          <!-- Approved state -->
          <q-card v-if="data.status === 'approved'" class="bg-green full-width q-pa-md flex justify-between items-center q-my-md">
            <div>Teaching accident status: <b>Approved</b></div>
            <div class="flex q-gutter-md">
              <q-btn
                rounded
                flat
                no-caps
                label="Rejected"
                class="bg-red text-white"
                @click="
                  () => {
                    isRejectDialogShow = true
                    rejectReason = ''
                  }
                " />
            </div>
          </q-card>

          <!-- Rejected state -->
          <q-card v-if="data.status === 'rejected'" class="bg-red full-width q-pa-md flex justify-between items-center q-my-md">
            <div>Teaching accident status: <b>Rejected</b></div>
            <div class="flex q-gutter-md">
              <q-btn
                rounded
                flat
                no-caps
                label="Approve"
                class="bg-green text-white"
                @click="
                  () => {
                    isApproveDialogShow = true
                    clearApproveReason()
                  }
                " />
            </div>
          </q-card>

          <!-- Display approval/rejection reason -->
          <div v-if="data.checkReason" class="text-subtitle2 q-my-md" :class="data.status === 'approved' ? 'text-teal' : 'text-red'" style="font-size: 1rem">
            <div>{{ data.status === 'approved' ? 'Reasons for approving' : 'Rejection Reason' }}</div>
            <div>{{ data.checkReason }}</div>
          </div>

          <!-- Approve Dialog -->
          <q-dialog v-model="isApproveDialogShow">
            <q-card>
              <q-card-section>
                <div class="text-h6">Provide approval reason (optional)</div>
                <q-input v-model="approveReason" type="textarea" label="Reason" auto-grow />
              </q-card-section>
              <q-card-actions align="right">
                <q-btn flat label="Cancel" v-close-popup />
                <q-btn flat label="Confirm" color="green" @click="onApprove" />
              </q-card-actions>
            </q-card>
          </q-dialog>

          <!-- Reject Dialog -->
          <q-dialog v-model="isRejectDialogShow">
            <q-card>
              <q-card-section>
                <div class="text-h6">Provide rejection reason</div>
                <q-input v-model="rejectReason" type="textarea" label="Reason" auto-grow />
              </q-card-section>
              <q-card-actions align="right">
                <q-btn flat label="Cancel" v-close-popup />
                <q-btn flat label="Confirm" color="red" @click="onReject" />
              </q-card-actions>
            </q-card>
          </q-dialog>
        </main>
      </div>
    </q-page>
  </div>
</template>

<script setup>
import {ref, onMounted} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {useQuasar} from 'quasar'

import PubTopBanner from 'components/PubTopBanner.vue'
// adjust this import to match your service path:
import teachingAccidentService from 'src/services/teaching-accident'

const $q = useQuasar()
const route = useRoute()
const router = useRouter()

// --- Reactive state ---
const data = ref({})
const isRejectDialogShow = ref(false)
const isApproveDialogShow = ref(false)
const rejectReason = ref('')
const approveReason = ref('')

// Extract the record ID from route (params or query)
const id = route.params.id || route.query.id

// Fetch the record
async function fetchData() {
  if (!id) return
  try {
    data.value = await teachingAccidentService.get(id)
  } catch (err) {
    $q.notify({type: 'negative', message: 'Failed to load record'})
  }
}

// Clear approval reason
function clearApproveReason() {
  approveReason.value = ''
}

// Handle approve
async function onApprove() {
  try {
    await teachingAccidentService.patch(data.value._id, {
      status: 'approved',
      checkReason: approveReason.value || '',
    })
    isApproveDialogShow.value = false
    $q.notify({type: 'positive', message: 'Accident approved'})
    fetchData()
  } catch {
    $q.notify({type: 'negative', message: 'Approval failed'})
  }
}

// Handle reject
async function onReject() {
  if (!rejectReason.value) {
    $q.notify({type: 'warning', message: 'Please provide a rejection reason'})
    return
  }
  try {
    await teachingAccidentService.patch(data.value._id, {
      status: 'rejected',
      checkReason: rejectReason.value,
    })
    isRejectDialogShow.value = false
    $q.notify({type: 'positive', message: 'Accident rejected'})
    fetchData()
  } catch {
    $q.notify({type: 'negative', message: 'Rejection failed'})
  }
}

// (Optional) Reset verification back to pending
async function onResetVerification(accidentId) {
  try {
    await teachingAccidentService.patch(accidentId, {
      status: 'pending',
      checkReason: '',
    })
    $q.notify({type: 'info', message: 'Verification reset to pending'})
    fetchData()
  } catch {
    $q.notify({type: 'negative', message: 'Reset failed'})
  }
}

onMounted(fetchData)
</script>
