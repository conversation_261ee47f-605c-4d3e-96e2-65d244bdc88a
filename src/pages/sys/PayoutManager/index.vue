<template>
  <q-page padding>
    <div class="q-mb-md">
      <div class="text-h5 q-mb-md">Payout Transactions Analysis</div>

      <!-- Filters -->
      <q-card flat bordered class="q-pa-md">
        <div class="row q-col-gutter-md">
          <div class="col-12 col-sm-6 col-md-3">
            <q-select
              v-model="filters.status"
              :options="Object.keys(statusOptions)"
              :option-label="(item) => statusOptions[item]"
              label="Status"
              outlined
              dense
              @update:model-value="onFilterChange" />
          </div>

          <div class="col-12 col-sm-6 col-md-3">
            <q-select
              v-model="filters.payoutMonth"
              :options="payoutMonthOptions"
              label="Payout Month"
              outlined
              dense
              clearable
              @update:model-value="onFilterChange" />
          </div>

          <div class="col-12 col-sm-6 col-md-3">
            <q-select
              v-model="filters.payoutYear"
              :options="payoutYearOptions"
              label="Payout Year"
              outlined
              dense
              clearable
              @update:model-value="onFilterChange" />
          </div>

          <div class="col-12 col-sm-6 col-md-3">
            <q-input v-model="filters.uid" label="Search by UID" outlined dense clearable @keyup.enter="onFilterChange">
              <template v-slot:prepend>
                <q-icon name="search" />
              </template>
            </q-input>
          </div>
        </div>
      </q-card>
    </div>

    <!-- Table -->
    <q-table
      :rows="transactions"
      :columns="columns"
      row-key="_id"
      :loading="loading"
      :pagination="pagination"
      @request="onTableRequest"
      :table-row-class-fn="getRowClass"
      binary-state-sort
      flat
      bordered>
      <!-- UID Column -->
      <template v-slot:body-cell-uid="props">
        <q-td :props="props">
          <span>{{ props.row.uid }}</span>
          <!-- <q-btn v-if="props.row.isSchool" color="primary" icon="school" outline="true" size="xs" class="q-pa-xs q-ml-xs" /> -->
        </q-td>
      </template>

      <!-- Amount Column -->
      <template v-slot:body-cell-amount="props">
        <q-td :props="props">
          ${{ (props.row.amount / 100).toFixed(2) }}
          <q-btn flat dense color="primary" icon="info" size="sm">
            <q-tooltip max-width="300px">
              <div v-for="(item, idx) in props.row.amountBreakUp" :key="idx">
                <strong>{{ item.balanceType.toFirstUpperCase() }}:</strong> ${{ (item.amount / 100).toFixed(2) }}
              </div>
            </q-tooltip>
          </q-btn>
        </q-td>
      </template>

      <!-- Status Column -->
      <template v-slot:body-cell-status="props">
        <q-td :props="props">
          <q-badge :color="getStatusColor(props.row.status)" class="q-py-xs q-px-sm" :label="statusOptions[props.row.status]" />
          <q-btn v-if="props.row.adminNotes" flat dense color="red" icon="info" size="sm" class="q-ml-xs">
            <q-tooltip max-width="300px">
              <div>{{ props.row.adminNotes }}</div>
            </q-tooltip>
          </q-btn>
        </q-td>
      </template>

      <!-- Airwallex Response Column -->
      <template v-slot:body-cell-airwallexResponse="props">
        <q-td :props="props">
          <q-btn
            v-if="props.row.airwallexResponse"
            flat
            dense
            color="primary"
            icon="code"
            size="sm"
            @click="showAirwallexResponse(props.row.airwallexResponse)">
            <q-tooltip>View Response</q-tooltip>
          </q-btn>
          <span v-else class="text-grey">N/A</span>
        </q-td>
      </template>

      <!-- Transfer ID Column -->
      <template v-slot:body-cell-airwallexTransferId="props">
        <q-td :props="props">
          <span v-if="props.row.airwallexTransferId">{{ props.row.airwallexTransferId }}</span>
          <span v-else class="text-grey">N/A</span>
        </q-td>
      </template>

      <!-- Sent At Column -->
      <template v-slot:body-cell-sentAt="props">
        <q-td :props="props">
          <span v-if="props.row.sentAt">{{ new Date(props.row.sentAt).toLocaleString() }}</span>
          <span v-else class="text-grey">N/A</span>
        </q-td>
      </template>

      <!-- Actions Column -->
      <template v-slot:body-cell-actions="props">
        <q-td :props="props">
          <q-btn v-if="allowRetry(props.row)" flat dense color="primary" icon="refresh" size="md" @click="retryTransaction(props.row._id)">
            <q-tooltip>Retry Transaction</q-tooltip>
          </q-btn>
          <q-btn
            v-else-if="props.row.status === 'pending'"
            unelevated
            dense
            color="negative"
            icon="cancel"
            label="Cancel"
            size="sm"
            class="q-py-xs q-px-sm"
            @click="cancelTransaction(props.row)">
            <q-tooltip>Cancel Payout for this month: {{ props.row.payoutMonth }}</q-tooltip>
          </q-btn>
          <q-btn
            v-else-if="props.row.status === 'cancelled'"
            unelevated
            dense
            color="positive"
            label="Recover"
            size="sm"
            class="q-py-xs q-px-sm"
            @click="recoverTransaction(props.row)">
            <q-tooltip>Recover Payout for this month: {{ props.row.payoutMonth }}</q-tooltip>
          </q-btn>
          <span v-else class="text-grey">N/A</span>
        </q-td>
      </template>
    </q-table>

    <!-- Airwallex Response Dialog -->
    <q-dialog v-model="responseDialog">
      <q-card style="min-width: 500px; max-width: 800px">
        <q-card-section class="row items-center q-pb-none">
          <div class="text-h6">Airwallex Response</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-card-section>
          <pre class="bg-grey-2 q-pa-md" style="overflow-x: auto; border-radius: 4px">{{ formattedResponse }}</pre>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="Copy" color="primary" @click="copyToClipboard" icon="content_copy" />
          <q-btn flat label="Close" color="primary" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup>
import {ref, onMounted, computed} from 'vue'
import {useQuasar} from 'quasar'

const $q = useQuasar()

const transactions = ref([])
const loading = ref(false)
const responseDialog = ref(false)
const currentResponse = ref(null)
const highlightRow = ref(null)
const pendingPayoutSummary = ref({})

const filters = ref({
  status: 'pending',
  payoutMonth: null,
  payoutYear: null,
  uid: '',
})

const pagination = ref({
  sortBy: 'updatedAt',
  descending: true,
  page: 1,
  rowsPerPage: 10,
  rowsNumber: 0,
})

const statusOptions = {
  all: 'All',
  pending: 'Pending',
  cancelled: 'Cancelled',
  processing: 'Processing',
  completed: 'Completed',
  failed: 'Failed',
}

// Generate payout month options from 2025-10 to current month
const payoutMonthOptions = computed(() => {
  const startYear = 2025
  const startMonth = 10
  const currentDate = new Date()
  const currentYear = currentDate.getFullYear()
  const currentMonth = currentDate.getMonth() + 1

  const months = []

  for (let year = startYear; year <= currentYear; year++) {
    const monthStart = year === startYear ? startMonth : 1
    const monthEnd = year === currentYear ? currentMonth : 12

    for (let month = monthStart; month <= monthEnd; month++) {
      const monthStr = month.toString().padStart(2, '0')
      months.push(`${year}-${monthStr}`)
    }
  }

  return months.reverse() // Most recent first
})

// Generate payout year options from 2025 to current year
const payoutYearOptions = computed(() => {
  const startYear = 2025
  const currentYear = new Date().getFullYear()
  const years = []

  for (let year = startYear; year <= currentYear; year++) {
    years.push(year)
  }

  return years.reverse() // Most recent first
})

const columns = [
  {
    name: 'uid',
    label: 'UID',
    align: 'left',
    field: 'uid',
    sortable: true,
  },
  {
    name: 'amount',
    label: 'Amount',
    align: 'center',
    field: 'amount',
    sortable: true,
  },
  {
    name: 'payoutMonth',
    label: 'Payout Month',
    align: 'center',
    field: 'payoutMonth',
    sortable: true,
  },
  {
    name: 'status',
    label: 'Status',
    align: 'left',
    field: 'status',
    sortable: true,
  },
  {
    name: 'airwallexTransferId',
    label: 'Transfer ID',
    align: 'left',
    field: 'airwallexTransferId',
  },
  {
    name: 'airwallexResponse',
    label: 'Response',
    align: 'center',
    field: 'airwallexResponse',
  },
  {
    name: 'sentAt',
    label: 'Sent At',
    align: 'left',
    field: 'sentAt',
    sortable: true,
  },
  {
    name: 'updatedAt',
    label: 'Last Updated At',
    align: 'left',
    field: (row) => new Date(row.updatedAt).toLocaleString(),
    sortable: true,
  },
  {
    name: 'actions',
    label: 'Actions',
    align: 'center',
  },
]

const formattedResponse = computed(() => {
  return currentResponse.value ? JSON.stringify(currentResponse.value, null, 2) : ''
})

const getStatusColor = (status) => {
  const colors = {
    pending: 'orange',
    cancelled: 'grey',
    processing: 'blue',
    completed: 'green',
    failed: 'red',
  }
  return colors[status] || 'grey'
}

const getRowClass = (row) => {
  if (highlightRow.value && row._id === highlightRow.value._id) {
    return 'highlighted-table-row'
  }
  return ''
}

const buildQuery = (props) => {
  const {page, rowsPerPage, sortBy, descending} = props.pagination

  const query = {
    $limit: rowsPerPage,
    $skip: (page - 1) * rowsPerPage,
  }

  if (sortBy) {
    query.$sort = {[sortBy]: descending ? -1 : 1}
  }

  if (filters.value.status && filters.value.status !== 'all') {
    query.status = filters.value.status
  }

  if (filters.value.payoutMonth) {
    query.payoutMonth = filters.value.payoutMonth
  }

  if (filters.value.payoutYear) {
    query.payoutYear = filters.value.payoutYear
  }

  if (filters.value.uid) {
    query.uid = {$regex: filters.value.uid, $options: 'i'}
  }

  return query
}

const fetchTransactions = async (props = {pagination: pagination.value}, highlight = null) => {
  loading.value = true

  try {
    const query = buildQuery(props)
    const response = await App.service('payout-transactions').find({query})

    transactions.value = response.data
    pagination.value.rowsNumber = response.total
    pagination.value.page = props.pagination.page
    pagination.value.rowsPerPage = props.pagination.rowsPerPage
    pagination.value.sortBy = props.pagination.sortBy
    pagination.value.descending = props.pagination.descending
    highlightRow.value = highlight ? transactions.value[0] : null
  } catch (error) {
    $q.notify({
      type: 'negative',
      message: 'Failed to fetch transactions',
      caption: error.message,
    })
  } finally {
    loading.value = false
  }
}

const getThisMonthPendingSummary = async () => {
  const rs = await App.service('payout-transactions').get('PendingPayoutSummary')
  pendingPayoutSummary.value = rs
}

const onFilterChange = () => {
  // Reset to page 1 when filters change
  pagination.value.page = 1
  fetchTransactions()
}

const onTableRequest = (props) => {
  fetchTransactions(props)
}

const showAirwallexResponse = (response) => {
  currentResponse.value = response
  responseDialog.value = true
}

const copyToClipboard = () => {
  navigator.clipboard.writeText(formattedResponse.value)
  $q.notify({
    type: 'positive',
    message: 'Copied to clipboard',
  })
}

const allowRetry = (transaction) => {
  // allow retry if transaction is failed or (processing and updatedAt is more than 30 minutes ago )
  return (
    transaction.status === 'failed' ||
    (transaction.status === 'processing' && new Date().getTime() - new Date(transaction.updatedAt).getTime() > 10 * 60 * 1000)
  )
}

const retryTransaction = async (transactionId) => {
  $q.dialog({
    title: 'Retry Transaction',
    message: 'Are you sure you want to retry this failed transaction?',
    cancel: true,
    persistent: true,
  }).onOk(async () => {
    loading.value = true
    try {
      await App.service('payout-transactions').get('retryPayoutById', {
        query: {id: transactionId},
      })

      $q.notify({
        type: 'positive',
        message: 'Transaction retry initiated successfully',
      })

      filters.value.status = 'all'
      fetchTransactions(undefined, true)
    } catch (error) {
      $q.notify({
        type: 'negative',
        message: 'Failed to retry transaction',
        caption: error.message,
      })
    } finally {
      loading.value = false
    }
  })
}

const cancelTransaction = async (transaction) => {
  $q.dialog({
    title: 'Cancel Transaction',
    message: `Are you sure you want to cancel transaction for ${transaction.uid}?`,
    prompt: {
      model: '',
      type: 'text',
      label: 'Admin Notes (optional)',
      outlined: true,
    },
    cancel: true,
    persistent: true,
  }).onOk(async (adminNotes) => {
    loading.value = true
    try {
      await App.service('payout-transactions').patch(transaction._id, {
        status: 'cancelled',
        adminNotes: adminNotes || undefined,
      })

      $q.notify({
        type: 'positive',
        message: 'Transaction cancelled successfully',
      })

      filters.value.status = 'all'
      fetchTransactions(undefined, true)
    } catch (error) {
      $q.notify({
        type: 'negative',
        message: 'Failed to cancel transaction',
        caption: error.message,
      })
    } finally {
      loading.value = false
    }
  })
}

const recoverTransaction = async (transaction) => {
  $q.dialog({
    title: 'Recover Transaction',
    message: `Are you sure you want to recover transaction for ${transaction.uid}?`,
    cancel: true,
    persistent: true,
  }).onOk(async () => {
    loading.value = true
    try {
      await App.service('payout-transactions').patch(transaction._id, {
        status: 'pending',
      })
      $q.notify({
        type: 'positive',
        message: 'Transaction recovered successfully',
      })

      filters.value.status = 'all'
      fetchTransactions(undefined, true)
    } catch (error) {
      $q.notify({
        type: 'negative',
        message: 'Failed to recover transaction',
        caption: error.message,
      })
    } finally {
      loading.value = false
    }
  })
}

onMounted(() => {
  fetchTransactions()
  getThisMonthPendingSummary()
})
</script>

<style scoped>
pre {
  margin: 0;
  font-size: 12px;
  line-height: 1.4;
}
</style>
