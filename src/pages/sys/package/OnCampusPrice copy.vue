<template>
  <q-item-label class="text-h6 q-pt-md" :class="{ 'text-negative': error }">
    <div class="row items-center">
      <div class="col">*On campus service
        <q-icon class="cursor-pointer q-ml-sm" size="1.5rem" name="help_outline">
          <q-tooltip>
            The organisations who purchase this product will be able to book offline teaching assistants <br>
            in the chosen locations
          </q-tooltip>
        </q-icon>
      </div>
      <q-btn color="primary" class="q-ml-md" no-caps rounded label="Add new" @click="addNew()" :disable="readonly" />
    </div>
  </q-item-label>
  <div class="row items-center q-mt-md">
    <div class="text-bold q-mr-md">Sold to</div>
    <div class="col">
      <q-option-group disable color="primary" :options="targetSoldOptions" type="checkbox" inline
        v-model="servicePackage.salesTarget" />
    </div>
  </div>
  <q-card class="q-my-md bg-teal-1" v-for="(item, index) in servicePackage.onCampusPrice" :key="index">
    <q-card-section class="relative-position">
      <q-item class="row">
        <q-item-section class="col-9">
          <div class="row items-center">
            <q-img class="q-mr-sm" style="width: 124px; height: 90px" fit="fill"
              :src="hashToUrl(cityInfo(item?.city)?.attachmentsCity?.[0]?.hash)" />
            <div class="col">
              <div class="text-bold">
                {{ item?.city }}, {{ cityInfo(item?.city)?.country }}
                <q-btn flat dense color="primary" no-caps label="data detail" rounded class="q-ml-md"
                  @click="showStatistics(item?.city)" />
              </div>
              <div class="text-grey-6">
                Hourly rate income of service provider
                <b class="text-primary q-ml-sm">
                  <template v-for="(value, key, index) in getGradesRateSort(cityInfo(item?.city)?.experienceRate)">
                    <template v-if="index < 1">
                      USD {{ key }}({{ value.join(',') }})
                    </template>
                  </template>
                </b>
                <b class="text-black q-ml-sm  cursor-pointer">
                  +{{
                    (Object.keys(getGradesRateSort(cityInfo(item?.city)?.experienceRate)).length - 1) +
                    Object.keys(getGradesRateSort(cityInfo(item?.city)?.tutorRate)).length
                  }} more
                  <q-popup-proxy>
                    <div class="q-pa-md">
                      <div class="text-grey-6 text-ellipsis-2 ">
                        Experienced teacher:
                        <template v-if="cityInfo(item?.city)?.experienceRate">
                          <div class="text-primary q-mt-xs q-ml-sm" :key="key"
                            v-for="(value, key) in getGradesRateSort(cityInfo(item?.city)?.experienceRate)">
                            USD {{ key }}({{ value.join(',') }})
                          </div>
                        </template>
                      </div>
                      <div class="text-grey-6 text-ellipsis-1 q-mt-md">
                        Student tutor:
                        <template v-if="cityInfo(item?.city)?.tutorRate">
                          <div class="text-primary q-mt-xs q-ml-sm" :key="key"
                            v-for="(value, key) in getGradesRateSort(cityInfo(item?.city)?.tutorRate)">
                            USD {{ key }}({{ value.join(',') }})
                          </div>
                        </template>
                      </div>

                    </div>
                  </q-popup-proxy>
                </b>
              </div>
              <div class="text-grey-6">
                Compensation hours for each service:
                <b class="text-primary q-ml-sm">{{ cityInfo(item?.city)?.compensationHour }} hours</b>
              </div>
            </div>
          </div>
        </q-item-section>
        <q-item-section>
          <q-input v-model.number="item.price" :disable="readonly" outlined stack-label prefix="$" label="per hour"
            type="number" :rules="[val => val > 0 || 'Number must be greater than 0']"></q-input>
        </q-item-section>
      </q-item>
      <template v-if="item?.price > 0">
        <div class="bg-teal-2 rounded-md q-mt-md q-py-sm shadow-2" v-for="(itemDiscount, i) in item.discount" :key="i">
          <q-item class="row items-center">
            <q-item-section>
              <q-input v-model.number="itemDiscount.count" :rules="[val => val > 0 || 'Number must be greater than 0']"
                :disable="readonly" type="number" outlined stack-label label="No of hours"></q-input>
            </q-item-section>
            <q-item-section side>
              <q-item-label class="text-h5 text-teal">
                ${{ (calPrice(itemDiscount, item.price).price / 100).toFixed(2) }}
              </q-item-label>
            </q-item-section>
            <q-item-section avatar v-if="item.discount.length > 1">
              <q-btn flat icon="close" @click="deleteDiscountClick(item, i)" :disable="readonly"></q-btn>
            </q-item-section>
          </q-item>
        </div>
        <q-btn class="q-mt-sm text-teal" flat outlint no-caps icon="add" label="Add bundle sales choice"
          :disable="readonly" @click="addDiscountClick(item)"></q-btn>
      </template>
      <q-btn dense class="absolute-top-right q-mr-md q-mt-md" v-if="servicePackage.onCampusPrice?.length > 1"
        color="red" icon="delete_outline" flat no-caps @click="deleteOnCampusPrice(index)" :disable="readonly"></q-btn>
    </q-card-section>
  </q-card>
  <q-dialog v-model="dialog">
    <q-card class="q-pa-lg" style="width: 600px">
      Choose a service location
      <q-card-section class="shadow-2 rounded-borders-md q-my-md">
        <div class="text-bold">
          On campus service
          <q-icon class="cursor-pointer q-ml-sm" size="1.5rem" name="help_outline">
            <q-tooltip>
              The organisations who purchase this product will be able to book offline teaching assistants <br>
              in the chosen locations
            </q-tooltip>
          </q-icon>
        </div>
        <q-select class="q-mt-md" v-model="chooseCity" :options="cityOptions" color="teal" label-color="teal" emit-value
          map-options outlined label="Please choose the offline service locations" icon="search" />
      </q-card-section>
      <q-card-actions class="row q-col-gutter-md">
        <div class="col-6">
          <q-btn class="full-width" icon="arrow_back" color="primary" outline no-caps rounded label="Not now"
            v-close-popup />
        </div>
        <div class="col-6">
          <q-btn class="full-width" :disable="!chooseCity" icon="done" color="primary" label="Save" no-caps rounded
            @click="addOnCampusPrice" />
        </div>
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>
<script setup>
/*
  imports
*/
import { computed, onMounted, ref, watch } from 'vue'
import { servicePackageStore } from 'stores/service-package'
import { calPremiumMentor } from 'src/pages/order/consts'
import { date } from 'quasar'
import { getGradesRateSort } from 'src/pages/sys/CampusLocation/const'
import StatisticalDialog from './StatisticalDialog.vue'
import {GradeGroupMap} from 'src/boot/const'

const targetSoldOptions = [
  {
    label: 'Organization',
    value: 'school',
  },
]

const dialog = ref(false)
const chooseCity = ref(null)

const cityOptions = ref([])
const cityAllOptions = ref([])
const teacherStatistics = ref([])
const readonly = ref(false)
const props = defineProps({
  error: Boolean,
  // readonly: Boolean,
  currentSubjectList: Array,
})

const emit = defineEmits(['update'])
const servicePackage = servicePackageStore()


const calPrice = (item, prePrice) => {
  const price = prePrice * 100
  return {
    price: item?.count * price,
  }
}


const addNew = () => {
  chooseCity.value = null
  dialog.value = true
  cityOptions.value = cityAllOptions.value?.filter(item => !servicePackage.onCampusPrice.some(price => price.city === item.value))
  // cityOptions.value = cityOptions.value.map(item => ({
  //   ...item,
  //   disable: servicePackage.onCampusPrice.some(price => price.city === item.value)
  // }))

}


const addOnCampusPrice = () => {
  console.log('chooseCity', chooseCity.value)
  servicePackage.onCampusPrice.push({
    city: chooseCity.value,
    price: 0,
    hash: cityInfo(chooseCity.value)?.attachmentsCity?.[0]?.hash,
    discount: [
      {
        count: 0,
        discount: 0,
        gifts: 0,
      }
    ]
  })
  console.log('servicePackage.onCampusPrice', servicePackage.onCampusPrice)
  dialog.value = false
}

const deleteOnCampusPrice = (index) => {
  servicePackage.onCampusPrice.splice(index, 1)
}

const deleteDiscountClick = (item, index) => {
  item.discount.splice(index, 1)
}

const addDiscountClick = (item) => {
  item.discount.push({
    count: 0,
    discount: 0,
    gifts: 0,
  })
}


const onValidateUpdate = () => {
  let isError = false
  if (servicePackage?.onCampusPrice?.length === 0) {
    isError = true
  } else {
    servicePackage?.onCampusPrice?.forEach((item) => {
      if ((item.price === '' || item.price <= 0) || item.discount.some(discount => discount.count === '' || discount.count <= 0)) {
        isError = true
      }
    })
  }

  console.log('subCampusErrorValidate', isError)
  emit('update', isError)
}


const cityInfo = (city) => cityAllOptions.value?.find(item => item.value === city)

const getCityOptions = async () => {
  const res = await App.service('campus-location').find({
    query: {
      $limit: 1000,
      $sort: { updatedAt: -1 },
      country: servicePackage?.country,
      archive: false,
    }
  })

  if (res) {
    cityAllOptions.value = res?.data?.map((item) => {
      return {
        ...item,
        label: item?.city,
        value: item?.city,
      }
    })
    console.log('cityAllOptions', cityAllOptions.value)
  }
  console.log('res', res)

}



const initData = () => {
  servicePackage.salesTarget = ['school']
  getCityOptions()
}


const getTeacherStatistics = async () => {
  const mentoringType = servicePackage.mentoringType
  const isAcademic = mentoringType === 'academic'
  const subject = props.currentSubjectList?.filter(v => v.selected).map(v => v._id)
  await App.service('campus-location').get('cityTeacherStatistics', { query: {
    country: servicePackage?.country,
     subject : isAcademic ? subject : [],
     mentoringType,
     topic: isAcademic ? [] : subject,
     } }).then(res => {
    console.log('res', res)
    teacherStatistics.value = res
  })
}

const showStatistics = async (city) => {

  const mentoringType = servicePackage.mentoringType
  const isAcademic = mentoringType === 'academic'
  const subject = props.currentSubjectList?.filter(v => v.selected).map(v => v._id)
  $q.loading.show()
  await App.service('campus-location').get('cityTeacherStatistics', { query: {
    country: servicePackage?.country,
     subject : isAcademic ? subject : [],
     mentoringType,
     topic: isAcademic ? [] : subject,
     } }).then(res => {
    console.log('res', res)
    teacherStatistics.value = res
  }).finally(() => {
    $q.loading.hide()
  })



  // const mentoringType = servicePackage.mentoringType
  const curriculum = servicePackage.curriculum?.value

  const gradeGroup = servicePackage.gradeGroup?.grades || []

  let column = []



  const list = teacherStatistics.value?.find( v=> v.city === city)?.statistics

  // const subject = props.currentSubjectList?.filter(v => v.selected).map(v => v._id)
  // const isAcademic = servicePackage.mentoringType === 'academic'
  gradeGroup.forEach(item => {

    const years = GradeGroupMap[item]?.grades[isAcademic ? curriculum : 'classcipe'] || []
    years.forEach(v => {
      column.push({
        name: v,
        label: v,
        align: 'center',
        field: row => row?.grades?.[v]?.count || '',
      })
    })
  })

  // return
  $q.dialog({
    component: StatisticalDialog,
    componentProps: {
      column,
      list: list?.filter(v => subject.includes(isAcademic ? v.subject : v.topic)) || [],
    },
  })
}

watch(
  () => servicePackage.onCampusPrice,
  (val) => {
    onValidateUpdate()
  },
  { deep: true }
)



onMounted(async () => {
  initData()
  // getTeacherStatistics()
  onValidateUpdate()
})
</script>
