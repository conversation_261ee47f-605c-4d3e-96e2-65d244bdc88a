<template>
  <div class="row">
    <div class="col-12 q-px-md" v-for="(item, lidx) in defaultTpl" :key="lidx">
      <div class="text-h6 text-teal-6">{{ item.name }}</div>
      <div class="q-mb-lg inline-block" v-if="item.type == 'choice-mark' || item.type == 'choice'">
        <div v-for="(choice, cidx) in rearrangeObject(item.value)" :key="cidx">
          <div class="text-weight-medium q-mt-sm">
            <span v-html="convertToLink(choice.value)"></span>
          </div>
          <div class="q-pl-md">
            <span v-html="convertToLink(choice.mark)"></span>
          </div>
          <div v-for="(item, iidx) in choice.child" :key="iidx">
            <div class="q-pl-md">
              <span v-html="convertToLink(item.value)"></span>
            </div>
            <div class="q-pl-lg">
              <span v-html="convertToLink(item.mark)"></span>
            </div>
          </div>
        </div>
      </div>
      <div class="q-mb-lg inline-block" v-else-if="['string', 'number'].includes(typeof item.value)">
        <span v-html="convertToLink(item.value)"></span>
      </div>
      <div class="q-mb-lg inline-block" v-else v-for="(b, bi) in item.value" :key="bi">
        <template v-if="item.code == 'words'">
          <q-chip outline square>{{ b }}</q-chip>
        </template>
        <span v-else v-html="convertToLink(b)"></span>
      </div>
      <div v-if="((defaultTpl.length > 2 && lidx == 2) || (defaultTpl.length < 3 && lidx + 1 == defaultTpl.length)) && taskOutline">
        <OutlinePreview :outline="taskOutline" :gradeOptions="gradeOptions" />
      </div>
    </div>
  </div>
  {{ console.log('linkList', linkList) }}
  <div class="q-pa-md" v-if="linkList?.length">
    <div class="text-h6 text-teal-6 q-pb-sm">Link content</div>
    <template v-for="(group, index) in groups" :key="index">
      <q-expansion-item v-if="getMainLinksByGroup(group)?.length" group="somegroup" class="overflow-hidden q-border-1 rounded-borders-md q-mb-md">
        <template v-slot:header>
          <q-item-section>
            <div class="row">
              <div class="col">{{ group.name }}</div>
              <div class="row items-center">
                <q-icon name="dynamic_feed" color="grey" size="1rem" />
                <span class="q-pl-sm text-grey"> {{ getMainLinksByGroup(group).length }} Content{{ getMainLinksByGroup(group).length > 1 ? 's' : '' }} </span>
              </div>
            </div>
          </q-item-section>
        </template>
        <template v-for="(link, index) in getMainLinksByGroup(group)" :key="index">
          <q-separator />
          <SessionBoard small isMyContent isPreviewPage isLimit no-border no-price :session="link"></SessionBoard>
        </template>
        <NoData v-if="!getMainLinksByGroup(group).length" size="9rem"></NoData>
      </q-expansion-item>
    </template>
  </div>
</template>

<script setup>
import {ref, watch, onMounted, computed} from 'vue'
import OutlinePreview from 'components/OutlinePreview.vue'
import SessionBoard from 'components/SessionBoard.vue'
import {useRoute} from 'vue-router'
import {pubStore} from 'stores/pub'
const pub = pubStore()
const props = defineProps({
  content: Object,
  taskOutline: Object,
  linkGroup: Boolean,
  gradeOptions: Object,
  linkSnapshot: Array,
})
const emit = defineEmits(['loaded'])
const defaultTpl = ref({})
const linkList = ref([])
const groups = ref([])
const mergeData = async () => {
  console.log('props', props)

  defaultTpl.value = {}
  const sectionData = {}
  sectionData.data = props.content.template

  const dataFiltered = []
  sectionData?.data?.forEach((data) => {
    if (data.code && !Acan.isEmpty(props.content[data.code]) && !['cover', 'name', 'grades', 'subjects', 'type'].includes(data.code)) {
      data.value = props.content[data.code]
    } else if (data.tags && props.content.ext?.[data.tags]) {
      //data.value = props.content.ext[data.tags]
    } else if (props.content.ext?.[data._id]) {
      //data.value = props.content.ext[data._id]
    } else if (data.code && props.content.ext?.[data.code]) {
      data.value = props.content.ext[data.code] //Real World Connection(s)
    }
    if (!Acan.isEmpty(data.value)) {
      dataFiltered.push(data)
    }
  })

  defaultTpl.value = dataFiltered.reverse().sort((a, b) => {
    if (a.code && ['inquiry', 'overview', 'idea'].includes(a.code)) {
      return -1
    } else {
      return 1
    }
  })

  getRelateList()
}

const convertToLink = (text) => {
  const urlRegex = /(https?:\/\/[^\s]+)/g
  const newText = text.toString()?.replace(urlRegex, '<a href="$1" target="_blank">$1</a>')
  return newText.split('\n').join('<br/>')
}

const rearrangeObject = (obj) => {
  for (let key in obj) {
    if (!Array.isArray(obj[key])) {
      delete obj[key]
    }
  }
  //moves items ahead if their keys do not contain a semicolon
  const sortedObj = Object.fromEntries(
    Object.entries(obj).sort(([keyA], [keyB]) => {
      if (keyA.includes(':') && !keyB.includes(':')) {
        return 1
      } else if (!keyA.includes(':') && keyB.includes(':')) {
        return -1
      } else {
        return 0
      }
    })
  )
  const item = []
  for (const [index, [key, value]] of Object.entries(Object.entries(sortedObj))) {
    if (!Array.isArray(value)) continue
    if (index == 0) {
      item.push(...value)
    } else if (item[index - 1]) {
      item[index - 1]['child'] = value
    }
  }
  return item
}

const getRelateList = async () => {
  linkList.value = []

  //const rs = await App.service('unit').get('relateLinkList', {query: {rid: props.content._id}})
  const rs = props.linkSnapshot

  console.log('relateLinkList', rs)
  let liveCount = []
  props.content.link.forEach((link, index) => {
    let meta = rs.find((e) => e._id == link.id)
    if (meta?._id) {
      linkList.value[index] = {...meta, ...{linkid: link._id, group: link.group, id: link.id, main: true}}
      if (meta.sessionType == 'live') {
        liveCount.push(meta?._id)
      }
    }
  })
  // isSysPremium
  const count = [...new Set(liveCount)].length
  console.log('liveCo', liveCount)
  emit('loaded', count)
  groups.value = props.content.linkGroup
}

const getMainLinksByGroup = (group) => {
  return linkList.value.filter((item) => {
    return item.group == group._id && item.main
  })
}

watch(
  () => props.content,
  (val) => {
    mergeData()
  }
)

onMounted(mergeData)
</script>
