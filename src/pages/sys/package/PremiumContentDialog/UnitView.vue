<template>
  <div class="library-detail pc-max">
    <div class="column overflow-hidden" v-if="Acan.isEmpty(content)">
      <div class="col text-center q-pa-xl text-grey" v-if="loading">
        <q-spinner-ball color="primary" size="2em" class="full-width" />
      </div>
      <NoData v-else></NoData>
    </div>
    <div v-else>
      <div class="q-pa-lg">
        <q-img
          class="full-width rounded-borders-md"
          :ratio="$q.screen.gt.xs ? 16 / 12 : 16 / 9"
          fit="cover"
          :src="hashToUrl(content.cover || content.image || content.unitSnapshot?.cover) || '/v2/img/no-img.png'" />
      </div>
      <div class="q-pa-md">
        <div class="q-pb-md text-h6">{{ content.name }}</div>
        <q-separator></q-separator>
      </div>
      <OverviewPage
        @loaded="onOverviewPageLoaded"
        :linkSnapshot="linkSnapshot"
        linkGroup
        :content="content"
        :taskOutline="outline"
        :gradeOptions="gradeOptions"></OverviewPage>
    </div>
  </div>
</template>

<script setup>
/*
  imports
*/
import {ref, watch, onMounted} from 'vue'
import {useRoute} from 'vue-router'
import {pubStore} from 'stores/pub'
import {curriculumStore} from 'stores/curriculum'
import OverviewPage from './OverviewPage.vue'

/*
  consts
*/
const curriculum = curriculumStore()
const gradeOptions = ref(null)
const route = useRoute()
const pub = pubStore()
const content = ref(null)
const linkSnapshot = ref([])
const props = defineProps({
  id: String,
})
const emit = defineEmits(['change', 'loaded'])
const outline = ref({})
const loading = ref(false)

watch(
  () => props.id,
  () => {
    if (!props.id) return
    content.value = {}
    loadUnit()
  }
)

async function loadUnit() {
  const id = props.id
  if (!id) return
  loading.value = true
  const query = {}

  let rs = await App.service('service-auth').get(id, {query})
  console.log('service-auth', rs)
  if (!rs) {
    loading.value = false
    return
  }

  const links = rs.linkSnapshot
  rs = rs.unitSnapshot

  if (pub.user._id) {
    gradeOptions.value = await curriculum.gradeOptions(pub.user.school || pub.user._id)
    //  outline.value = (await App.service('task-outline').get('byRid', {query: {_id: rs._id}})) || {}
    outline.value = rs?.outline
  }
  loading.value = false
  content.value = rs

  linkSnapshot.value = Object.values(links)

  console.log('links', Object.values(links))

  // let liveCount = 0
  // for (const key in links) {
  //   if (links[key].sessionType === 'live') {
  //     liveCount++
  //   }
  // }
  // console.log('liveCount', liveCount)
  // emit('loaded', liveCount)
}

const onOverviewPageLoaded = (val) => {
  console.log('onOverviewPageLoaded', val)
  emit('loaded', val)
}

onMounted(loadUnit)
</script>
<style lang="sass">
.border-1
  border: 1px solid $grey-3
.video-item
  width:100%
</style>
