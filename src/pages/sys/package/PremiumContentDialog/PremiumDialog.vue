<template>
  <q-dialog ref="dialogRef" @hide="onDialogHide" full-width full-height :maximized="$q.screen.lt.sm" class="content-dialog">
    <q-card class="column">
      <q-card-actions class="lt-sm" align="between">
        <div class="row">
          <q-btn class="q-mr-md" flat round dense icon="close" v-close-popup />
          <div class="text-h6">{{ title }}</div>
        </div>
        <q-btn flat round dense no-caps icon="check" color="primary" @click="onOKClick" />
      </q-card-actions>
      <q-card-actions class="gt-xs" align="between">
        <div class="text-h6">{{ title }}</div>
        <q-btn flat round dense icon="close" v-close-popup />
      </q-card-actions>

      <q-separator />

      <q-card-section class="col pc-max row position-relative q-pt-none">
        <div class="col-xs-12 col-sm-7 full-height">
          <div class="q-px-md q-pt-md full-width row q-col-gutter-md">
            <div class="col">
              <GeneralFilters v-if="mounted" v-model="filters" input-only :options="filterOptions" @update:modelValue="onFilterUpdate"></GeneralFilters>
            </div>
            <div class="col-xs-12 col-sm-6" v-if="serviceType == 'academic'">
              <q-select
                dense
                outlined
                v-model="topic"
                :options="topicOptions"
                @filter="filterFn"
                @update:modelValue="requery()"
                use-input
                emit-value
                map-options
                input-debounce="0"
                label="Search by topic content tag" />
            </div>
          </div>
          <div v-if="!isEmpty(linkList)" class="overflow-auto q-px-md q-pt-sm content-list-height" id="scroll-area-with-virtual-scroll-1">
            <q-virtual-scroll
              scroll-target="#scroll-area-with-virtual-scroll-1"
              component="q-list"
              class="col full-width overflow-auto q-pb-xl q-mb-xl"
              :items="linkList"
              @virtual-scroll="onVirtualScroll">
              <template v-slot:after>
                <div v-if="list.data.length === list.limit" class="row justify-center q-my-md">
                  <q-spinner-ball color="primary" size="2em" class="full-width" />
                </div>
                <div v-else class="q-pa-md text-grey text-center hidden">It's over</div>
              </template>
              <template v-slot="{item: o, index: i}">
                <div class="row q-pb-md" :key="i">
                  <q-radio v-model="activedLink" :val="o" color="teal" :disable="exceptions?.includes(o._id)" />
                  <div class="col q-pl-md">
                    <AuthUnitCard :item="o" />
                  </div>
                </div>
              </template>
            </q-virtual-scroll>
          </div>
          <template v-else>
            <div v-if="loading" class="text-center q-pa-xl text-grey">
              <q-spinner-ball color="primary" size="2em" class="full-width" />
            </div>
            <NoData v-else />
          </template>
        </div>
        <div class="col-xs-12 col-sm-5 gt-xs full-height" v-if="linkList?.length > 0">
          <q-scroll-area ref="scrollAreaRef" class="full-height" style="">
            <div class="bg-white border-1-grey rounded-borders q-ma-md q-px-md q-mb-xl q-pb-xl">
              <UnitView v-if="activedLink && $q.screen.gt.xs" @loaded="onUnitLoad" :id="activedLink._id" />
              <NoData v-else messageColor="grey" size="9rem" message="Please choose one content from the left list to view the details"></NoData>
            </div>
          </q-scroll-area>
        </div>
      </q-card-section>

      <q-separator class="gt-xs" />

      <q-card-actions align="right" class="q-px-md q-gutter-md items-center">
        <div class="col-4 q-pt-md">
          <q-input
            outlined
            dense
            v-model.trim="times"
            disable
            label="Required to complete this course"
            class="full-width"
            type="number"
            :rules="[(val) => !!val || '', (val) => val > 0 || '']"
            lazy-rules>
            <template v-slot:append>
              <span class="text-body1"> sessions </span>
            </template>
          </q-input>
        </div>
        <q-btn class="q-pb-lg" :disable="times < 1" rounded no-caps color="primary" label="Confirm" @click="onOKClick"> </q-btn>
        <q-btn class="q-pb-lg" rounded no-caps outline color="primary" label="Cancel" @click="onDialogCancel"> </q-btn>
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import {ref, computed, onMounted} from 'vue'
import {useDialogPluginComponent} from 'quasar'
import PackageCard from 'components/PackageCard.vue'

import GeneralFilters from 'components/GeneralFilters.vue'
import AuthUnitCard from 'src/pages/account/teacher-auth/AuthUnitCard.vue'
import UnitView from './UnitView.vue'

import {LEARNING_STAGE_OPTION, LEARNING_STYLE_OPTION, OTHER_LEARNING_STYLE_OPTION} from 'src/boot/const'
import useSubject from 'src/composables/account/academic/useSubject.js'

/*
  props & emits
*/
const props = defineProps({
  serviceType: String,
  label: String,
  curriculum: String,
  countryCode: Array,
  subject: Array,
  gradeGroup: Array,
  exceptions: Array,
})
defineEmits([...useDialogPluginComponent.emits])

/*
  consts
*/
const {dialogRef, onDialogHide, onDialogOK, onDialogCancel} = useDialogPluginComponent()

const {getOneById} = useSubject()
const title = ref('Add premium content')
const loading = ref(false)
const scrollAreaRef = ref(null)
const topic = ref('')
const activedLink = ref(null)
const linkList = ref([])
const list = ref([])
const times = ref(null)
const filters = ref({})
const mounted = ref(false)
const topicOptions = ref([])
const subjectData = ref({})
const topicCounts = ref({})

/*
  computeds
*/
const filterOptions = computed(() => {
  const options = []
  if (props.serviceType == 'academic') {
    options.push({
      key: 'ability',
      label: "Suitable for students' learning ability",
      options: LEARNING_STAGE_OPTION,
      default: 'catchUp',
      type: 'checkbox',
    })
    options.push({
      key: 'styles',
      label: 'Suitable for students with recognition style',
      options: LEARNING_STYLE_OPTION,
      default: '',
      type: 'checkbox',
    })
    options.push({
      key: 'otherStyles',
      label: 'Suitable for students with other recognition style',
      options: OTHER_LEARNING_STYLE_OPTION,
      default: '',
      type: 'checkbox',
    })
  }
  return options
})
/*
  methods
*/
const mapSelectOption = (e) => {
  const value = e?._id ?? ''
  const label = e?.name ?? ''
  let count = topicCounts.value[e._id] ?? 'No data'
  if (count > 99) {
    count = '99+'
  }
  return {value, label: label + ' ( ' + count + ' )'}
}

const filterFn = (val, update) => {
  if (val === '') {
    update(() => {
      if (subjectData.value?.topic?.length) topicOptions.value = subjectData.value.topic.map(mapSelectOption)
      else topicOptions.value = []
    })
    return
  }

  update(() => {
    if (subjectData.value?.topic?.length) {
      const needle = val.toLowerCase()
      topicOptions.value = subjectData.value.topic.map(mapSelectOption).filter((v) => v.label.toLowerCase().indexOf(needle) > -1)
    } else {
      topicOptions.value = []
    }
  })
}

const onUnitLoad = (count) => {
  times.value = count
}

const onFilterUpdate = () => {
  requery()
}

const onVirtualScroll = async ({index, to}) => {
  if (index !== to) return // console.warn(index, to, direction)
  const {limit = 10, skip = 0, data} = list.value
  if (Acan.isEmpty(data)) return // has last page
  console.warn('need load more')
  await find(skip + limit)
}

const find = async ($skip = 0) => {
  loading.value = true
  const query = {status: 2, 'unitSnapshot.mode': {$in: ['unit', 'pdUnit']}, $sort: {updatedAt: -1}}
  query.$skip = $skip
  if (filters.value.search) {
    query.name = {}
    query.name.$search = filters.value.search
  }

  query.curriculum = props.serviceType == 'academic' ? props.curriculum : 'pd'

  if (props.countryCode?.length) {
    query.countryCode = {$in: props.countryCode}
  }

  if (props.gradeGroup?.length) {
    query.gradeGroup = {$in: props.gradeGroup}
  }

  if (props.serviceType && props.subject?.length) {
    if (props.curriculum == 'pd') {
      query['topic._id'] = {$all: props.subject}
    } else {
      query.subject = {$all: props.subject}
      if (topic.value) {
        query['topic._id'] = topic.value
      }
    }
  }

  if (filters.value.ability?.length) {
    query.ability = {$in: filters.value.ability}
  }

  if (filters.value.styles?.length) {
    query.styles = {$in: filters.value.styles}
  }

  if (filters.value.otherStyles?.length) {
    query.otherStyles = {$in: filters.value.otherStyles}
  }

  list.value = await App.service('service-auth').get('unit', {query})
  linkList.value.push(...list.value.data)

  loading.value = false
}

const requery = async () => {
  activedLink.value = null
  linkList.value.length = 0
  list.value = {}
  times.value = null
  await find()
}

const onOKClick = () => {
  onDialogOK({premium: activedLink.value, times: times.value})
}

onMounted(async () => {
  if (props.serviceType == 'academic') {
    const rs = await App.service('service-auth').get('groupTopic', {
      query: {type: 'content', curriculum: props.curriculum, subject: {$in: props.subject}},
    })
    topicCounts.value = rs
    const res = await getOneById(props.subject[0], true)
    subjectData.value = res
  }
  mounted.value = true
})
</script>

<style lang="sass" scope>
.content-dialog
 .content-list-height
   height: calc(100vh - 300px)
   body.screen--xs &
      height: calc(100vh - 250px)
 .scroll
   body.screen--xs &
      max-height: 100vh
      width: auto
      height: auto
</style>
