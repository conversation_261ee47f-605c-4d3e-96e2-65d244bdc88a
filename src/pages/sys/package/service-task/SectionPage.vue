<template>
  <div class="shadow-1">
    <q-sticky>
      <q-toolbar>
        <q-btn flat round icon="chevron_left" @click="goBack" />
        <q-toolbar-title class="text-bold">Editing Sections</q-toolbar-title>
      </q-toolbar>
    </q-sticky>
  </div>
  <q-card class="q-pa-md q-my-md q-mx-md rounded-borders-md">
    <q-card-section>
      <div class="q-mb-md">
        <div class="text-subtitle2 q-mb-xs">Name of the Section</div>
        <q-input outlined v-model="sectionName" />
      </div>

      <div class="q-mb-md">
        <div class="text-subtitle2 q-mb-xs">Prompt</div>
        <q-input
          outlined
          type="textarea"
          v-model="prompt"
          rows="10"
          class="scroll-textarea"
          placeholder="Please describe the requirements of the service and set the benchmarks for completing the service" />
      </div>

      <div class="row justify-between q-gutter-md q-mb-md items-center">
        <div class="flex-col">
          <div class="text-subtitle2 q-mb-xs">Price setting</div>
          <div class="flex items-center">
            <span class="text-bold text-h4 q-mr-sm">$</span>
            <q-input v-model.number="salesPrice" type="number" min="0" label="Sales price" outlined style="width: 100px" />
          </div>
        </div>
        <div class="flex-col">
          <div class="text-subtitle2 q-mb-xs">Cost of Section = Sales price * Commission rate</div>
          <div class="flex items-center">
            <span class="text-bold text-h6 q-mr-sm"
              >$ {{ salesPrice }}
              <span>&#215;</span>
            </span>
            <q-input
              v-model.number="commissionRate"
              type="number"
              min="0"
              max="100"
              @blur="commissionRate = Math.min(100, Math.max(0, commissionRate))"
              label-slot
              outlined
              style="width: 100px">
              <template v-slot:label>
                <span class="" style="font-size: x-small">Commission rate</span>
              </template>
            </q-input>
            <span class="text-bold text-h6 q-mr-sm"> % &nbsp;<span class="text-h5">=</span></span>
            <div class="text-bold text-h6 q-mr-sm">$ {{ calculatedCost.toFixed(2) }}</div>
          </div>
        </div>
      </div>

      <div class="row q-col-gutter-sm q-mt-md">
        <div class="col">
          <q-btn
            class="full-width"
            rounded
            label="Save"
            icon="check"
            color="primary"
            :disable="!sectionName || !salesPrice || !commissionRate"
            @click="handleSaveSection" />
        </div>
        <div class="col">
          <q-btn v-if="!isCreate" class="full-width" rounded label="Delete" icon="delete" color="negative" @click="handleDeleteSection" />
        </div>
      </div>
    </q-card-section>
  </q-card>
</template>

<script setup>
import {ref, computed, onMounted} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {servicePackageStore} from 'stores/service-package'

const route = useRoute()
const router = useRouter()
const servicePackage = servicePackageStore()
const sectionName = ref('')
const prompt = ref('')
const salesPrice = ref(0)
const commissionRate = ref(0)
const isCreate = ref(route.path.includes('create-section'))

const calculatedCost = computed(() => {
  return (salesPrice.value * commissionRate.value) / 100
})

const goBack = () => {
  router.replace(route.query.back || `/sys/package/edit?tab=${route.query.tab}`)
}

const handleSaveSection = async () => {
  const dto = {
    name: sectionName.value,
    prompt: prompt.value,
    salesPrice: salesPrice.value,
    costPrice: calculatedCost.value,
    serviceTaskId: route.query.id,
  }
  if (isCreate.value) {
    servicePackage.sections.push(dto)
    servicePackage.price += dto.salesPrice * 100
  } else {
    servicePackage.price = 0
    servicePackage.sections = servicePackage.sections.map((section) => {
      if (section._id === route.params.id) {
        return {...section, ...dto}
      }
      return section
    })
    servicePackage.price = servicePackage.sections.reduce((total, section) => total + section.salesPrice * 100, 0)
  }
  try {
    await servicePackage.saveSections(route.query.id)
  } catch (error) {
    console.error('Failed to save section:', error)
  }
  goBack()
}

const handleDeleteSection = async () => {
  servicePackage.sections = servicePackage.sections.filter((section) => section._id !== route.params.id)
  try {
    await servicePackage.saveSections()
  } catch (error) {
    console.error('Failed to delete section:', error)
  }
  goBack()
}

onMounted(async () => {
  if (!isCreate.value) {
    $q.loading.show()
    const section = servicePackage.sections.find((section) => section._id === route.params.id)
    if (section) {
      sectionName.value = section.name
      prompt.value = section.prompt
      salesPrice.value = section.salesPrice
      commissionRate.value = ((section.costPrice / salesPrice.value) * 100).toFixed(2)
    } else {
      goBack()
    }
    $q.loading.hide()
  }
})
</script>

<style scoped>
.scroll-textarea textarea {
  overflow-y: auto;
}
</style>
