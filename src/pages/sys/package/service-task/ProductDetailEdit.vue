<template>
  <q-card class="q-my-md rounded-borders-md">
    <q-card-section>
      <q-item class="q-pa-sm">
        <q-item-section>
          <div class="text-h6 row items-center justify-between q-gutter-x-sm">
            <div>
              <span v-if="!task.status">*</span>Product detail
              <q-chip
                square
                :color="task.status ? 'green-2' : 'red-2'"
                :text-color="task.status ? 'green-7' : 'red-7'"
                style="font-weight: 500"
                :ripple="false"
                :label="task.status ? 'Published' : `Unpublished`"></q-chip>
            </div>
            <div v-if="task.status" class="text-right text-teal-5 text-bold text-h6">Total price: USD {{ (task.price / 100).toFixed(2) ?? 0 }}</div>
          </div>
        </q-item-section>
        <q-item-section side>
          <q-btn v-if="!task.status" flat dense text-color="grey-8" icon="edit" @click="openEditProductDetail()"></q-btn>
        </q-item-section>
      </q-item>
      <q-img
        v-if="task?.cover"
        class="q-pa-sm"
        style="width: 90%; max-width: 580px; aspect-ratio: 580 / 264; border-radius: 4px"
        contain
        :src="hashToUrl(task.cover)" />
      <div class="text-weight-medium q-pa-sm">
        <div class="text-subtitle1 text-weight-medium">
          Service type:
          <q-chip square color="teal-1" text-color="teal-7" style="font-weight: 500" :ripple="false">{{
            servicePackage.mentoringTypeList.find((e) => e.value == task.mentoringType)?.label
          }}</q-chip>
        </div>
        <div class="text-subtitle1 text-weight-medium">
          Service provider qualification:
          <q-chip square color="teal-1" text-color="teal-7" style="font-weight: 500" :ripple="false">{{
            servicePackage.qualificationList.find((e) => e.value == task.qualification)?.label
          }}</q-chip>
        </div>
        <div v-if="task.name" class="text-subtitle1 text-weight-medium q-mt-md">
          {{ task.name }}
        </div>
        <div v-if="task.description" class="text-subtitle2 text-weight-regular" style="max-height: 200px; overflow-y: auto">
          {{ task.description }}
        </div>
        <div v-if="task.points?.[0].trim()" class="text-subtitle1 text-weight-medium q-mt-md">
          Selling Points
          <div v-for="(point, i) in task.points" :key="i" class="text-subtitle2 text-weight-regular">
            <div>{{ `${i + 1}. ${task.points[i]}` }}</div>
          </div>
        </div>
      </div>
    </q-card-section>
  </q-card>
  {{ console.log('jcjhchjcj', taskData) }}
</template>

<script setup>
import {ref, computed} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {servicePackageStore} from 'stores/service-package'

const servicePackage = servicePackageStore()
const route = useRoute()
const router = useRouter()
const props = defineProps({
  openEditProductDetail: {
    type: Function,
    default: () => ({}),
  },
  taskData: {
    type: Object,
    default: () => ({}),
  },
})

const task = computed(() => {
  return servicePackage
})
</script>
