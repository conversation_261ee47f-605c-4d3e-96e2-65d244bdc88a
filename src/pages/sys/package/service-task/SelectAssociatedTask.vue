<template>
  <q-dialog ref="dialogRef" @hide="onDialogHide" @before-show="beforeDialogShow" persistent maximized>
    <q-card style="width: 1000px; height: 700px; max-width: 90vw; max-height: 100vh" class="bg-teal-1">
      <q-card-section>
        <q-item class="row justify-between">
          <q-item-section v-if="mounted">
            <GeneralFilters v-model="filters" :pagination="pagination" :options="filterOptions" @update:modelValue="onFilterUpdate" />
          </q-item-section>
          <q-item-section style="width: 200px" class="col-6">
            <q-btn class="text-teal" outline rounded label="Go to template setting" no-caps @click="onTeamplateClick"></q-btn>
          </q-item-section>
        </q-item>
      </q-card-section>
      <div style="overflow: auto; height: calc(100% - 180px)">
        <template v-if="isEmpty(listData)">
          <NoData v-if="!loading" />
          <div v-else class="text-center q-pa-xl text-grey">
            <q-spinner-ball color="primary" size="2em" class="full-width" />
          </div>
        </template>
        <q-virtual-scroll v-else separator class="col q-px-sm full-width overflow-auto" :items="listData" @virtual-scroll="scrollFn">
          <template v-slot:after>
            <div v-if="list.data.length === list.limit" class="row justify-center q-my-md">
              <q-spinner-ball color="primary" size="2em" />
            </div>
            <div v-else class="q-pa-md text-grey text-center hidden">It's over</div>
          </template>
          <template v-slot="{item: o, index: i}">
            <div :key="i" class="q-ma-sm q-mb-md">
              <q-radio v-model="servicePackage.associatedTask._id" :val="o._id" color="primary" class="full-width">
                <TaskCard isView isSelect :task="o" :key="i" />
              </q-radio>
            </div>
          </template>
        </q-virtual-scroll>
      </div>
      <q-card-section>
        <q-item class="row justify-end">
          <q-item-section style="width: 200px" class="col-6">
            <q-btn class="text-teal" outline rounded label="Cancel" no-caps @click="onDialogCancel"></q-btn>
          </q-item-section>
          <q-item-section style="width: 200px" class="col-6">
            <q-btn class="text-white bg-teal" :disabled="confirmDisabled" rounded label="Confirm" no-caps @click="onOKClick"></q-btn>
          </q-item-section>
        </q-item>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script setup>
import {ref, computed, onMounted} from 'vue'
import {useDialogPluginComponent} from 'quasar'
import {servicePackageStore} from 'stores/service-package'
import {useRouter} from 'vue-router'
import GeneralFilters from '../../../../components/GeneralFilters.vue'
import TaskCard from '../../../../components/ServiceTask/TaskCard.vue'

const servicePackage = servicePackageStore()
const router = useRouter()
const loading = ref(false)
const filters = ref()
const listData = ref([])
const list = ref({})
const mounted = ref(false)
const virtualListIndex = ref(1)

defineEmits([
  // REQUIRED; need to specify some events that your
  // component will emit through useDialogPluginComponent()
  ...useDialogPluginComponent.emits,
])

const {dialogRef, onDialogHide, onDialogOK, onDialogCancel} = useDialogPluginComponent()

/*
    computeds
  */
const pagination = computed(() => {
  return {start: virtualListIndex.value + 1, end: listData.value.length, total: list.value.total}
})

const filterOptions = computed(() => {
  const options = []
  return options
})

const confirmDisabled = computed(() => {
  return !servicePackage.associatedTask._id
})

/*
    methods
  */

const find = async ($skip = 0) => {
  const query = {del: false, $skip}
  query.type = 'serviceTask'
  query.$sort = {createdAt: -1}

  if (filters.value?.search) {
    query.name = {$regex: filters.value.search, $options: 'i'}
  }

  if (servicePackage.serviceRoles == 'consultant') {
    query.mentoringType = 'thesis-defense'
    query.qualification = 'seniorConsultant'
  } else if (servicePackage.serviceRoles == 'mentoring') {
    query.mentoringType = servicePackage.mentoringType
    if (servicePackage.qualification === 'studentTutor') {
      query.qualification = 'consultant'
    } else {
      query.qualification = 'seniorConsultant'
    }
  }

  query.contentOrientatedEnable = false
  query.status = true

  const rs = (list.value = await App.service('service-pack').find({query}))
  loading.value = false
  listData.value.push(...rs.data)

  return rs.data.length
}

const scrollFn = async ({index, to}) => {
  virtualListIndex.value = index
  if (index !== to) return // console.warn(index, to, direction)
  const {limit = 10, skip = 0, data} = list.value
  if (Acan.isEmpty(data)) return // has last page
  console.warn('need load more')
  find(skip + limit)
}

const resetFn = async () => {
  listData.value.length = 0
  list.value = {}
  loading.value = true
  await find()
}

function onFilterUpdate() {
  resetFn()
}

const onTeamplateClick = () => {
  const url = router.resolve({
    path: '/sys/package',
    query: {
      tab: 'service-task',
    },
  }).href

  window.open(url, '_blank')
}

const onOKClick = async () => {
  await servicePackage.setAssociatedTask()
  onDialogOK()
}

onMounted(() => {
  loading.value = true
  mounted.value = true
})
</script>

<style scoped>
::v-deep(.q-radio__label) {
  width: 100% !important;
}
</style>
