<template>
  <div>
    <div class="row">
      <div class="col q-ma-md" style="min-width: 0">
        <q-tab-panels v-model="tab" animated swipeable vertical transition-prev="jump-up" transition-next="jump-up" style="z-index: 9; width: 100%">
          <q-tab-panel v-for="(section, index) in currentTaskSections" :key="index" :name="section._id" style="padding: 0; margin: 0">
            <q-card class="q-px-md q-py-sm q-ma-md rounded-xl" style="box-shadow: 1px 1px 3px 1px #00000026, 1px 1px 2px 0px #00000026">
              <div class="flex text-h6 q-my-lg items-center">
                {{ section.name }}
                <div>
                  <span class="q-ml-xl text-h5" style="font-weight: 500"> $ </span>
                  <span style="font-weight: 400; opacity: 63%">
                    &nbsp;{{ section.salesPrice }} <q-tooltip anchor="top middle" self="bottom middle">Sales price for this Section</q-tooltip></span
                  >
                  <span class="q-ml-xl text-h5" style="font-weight: 500">$</span>
                  <span style="font-weight: 400; opacity: 63%"
                    >&nbsp;{{ section.costPrice }} <q-tooltip anchor="top middle" self="bottom middle">Cost for this Section</q-tooltip></span
                  >
                </div>
              </div>
              <div class="text-h6 q-mb-sm">{{ prompt }}</div>
              <pre style="white-space: pre-wrap; word-wrap: break-word; max-height: 200px; overflow-y: auto; min-height: 100px">{{ section.prompt }}</pre>
              <div v-if="!servicePackage.status" class="text-right q-mr-md q-mb-sm">
                <q-btn
                  class="q-py-sm"
                  icon="edit"
                  size="sm"
                  color="white"
                  text-color="primary"
                  rounded
                  no-caps
                  style="width: 140px; background: #fffbfe; font-weight: 500; box-shadow: 0px 1px 3px 1px #00000026, 0px 1px 2px 0px #00000026"
                  @click="editSectionClick(section._id)">
                  <span style="font-size: 14px">&nbsp;Edit</span>
                </q-btn>
              </div>
            </q-card>
          </q-tab-panel>
        </q-tab-panels>
      </div>

      <div style="width: 184px">
        <div style="position: relative; z-index: 999; width: 100%; height: 100%">
          <div class="flex justify-end text-bold text-teal q-mr-xl text-subtitle1 q-mb-sm">Total {{ currentTaskSections.length }}</div>
          <q-scroll-area class="scroll-height">
            <div style="position: absolute; left: 7px; border-left: 2px solid grey; height: 100%"></div>
            <q-tabs v-model="tab" vertical indicator-color="transparent" stretch no-caps>
              <q-tab v-for="(section, index) in currentTaskSections" :key="index" :name="section._id" class="q-py-sm q-mb-lg">
                <div class="row">
                  <span style="position: absolute; left: -20px; top: 12px; z-index: 999">
                    <q-avatar color="teal" text-color="white" size="16px" style="z-index: 999">
                      {{ index + 1 }}
                    </q-avatar>
                  </span>
                  <div style="width: 3px" :style="section._id === tab ? 'background-color: #26A69A' : ''"></div>
                  <div class="row justify-between q-py-sm" style="background-color: #26a69a1a; width: 140px">
                    <div class="text-caption">
                      &nbsp;<q-tooltip v-if="section.name.length > 8">{{ section.name }}</q-tooltip>
                      {{ truncate(section.name) }}
                    </div>
                    <div class="text-right text-caption text-bold text-teal">USD {{ section.salesPrice }}&nbsp;</div>
                  </div>
                </div>
              </q-tab>
            </q-tabs>
          </q-scroll-area>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import {computed, onMounted, ref, watch} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {servicePackageStore} from 'stores/service-package'

const props = defineProps({
  currentTaskSections: {
    type: Array,
    default: () => [],
  },
})

const router = useRouter()
const route = useRoute()
const servicePackage = servicePackageStore()

const isSys = computed(() => route.path.includes('sys'))
const defaultSection = computed(() => {
  return props.currentTaskSections?.[0]
})

const tab = ref(props.currentTaskSections[0]?._id || '')

watch(
  defaultSection,
  (newVal) => {
    if (newVal) {
      tab.value = newVal._id
    }
  },
  {immediate: true}
)

const splitterModel = ref(580)

function truncate(text, length = 6) {
  return text.length > length ? text.slice(0, length) + '…' : text
}

const editSectionClick = (id) => {
  const fullQueryString = Object.entries(route.query)
    .map(([key, value]) => `${key}=${value}`)
    .join('&')
  const currentPath = route.path
  router.push({
    path: `${currentPath}/${id}`,
    query: {
      ...route.query,
      back: `/sys/package/edit?${fullQueryString}`,
    },
  })
}

onMounted(() => {
  if (defaultSection.value) {
    tab.value = defaultSection.value._id
  }
})
</script>

<style scoped>
.q-tabs .q-tab__content {
  width: calc(100% - 20px) !important;
  align-items: stretch;
}

.q-tabs--left .q-tab__indicator {
  left: 0 !important;
  right: auto !important;
  width: 3px !important;
}

::v-deep(.q-splitter__separator) {
  width: 3px !important;
  z-index: 1 !important;
  pointer-events: none;
  position: relative;
  left: 10px;
}

.scroll-height {
  height: 360px;
  overflow-y: auto;
}
</style>
