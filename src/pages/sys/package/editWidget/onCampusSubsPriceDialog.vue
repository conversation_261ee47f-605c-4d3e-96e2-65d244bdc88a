<template>
  <q-dialog ref="dialogRef" @hide="onDialogHide">
    <q-card class="q-my-md rounded-borders-md bg-teal-1" style="width: 800px; max-width: 80vw">
      <q-form ref="myForm" @submit="onConfirm" class="q-gutter-md">
        <q-card-section>
          <div class="row items-center q-mt-md">
            <div class="text-bold q-mr-md">Sold to</div>
            <div class="col">
              <q-option-group disable color="primary" :options="targetSoldOptions" type="checkbox" inline v-model="servicePackage.salesTarget" />
            </div>
            <q-btn color="primary" class="q-ml-md" no-caps rounded label="Add new" @click="addNew()" :disable="readonly" />
          </div>
          <q-card class="q-my-md bg-teal-1" v-for="(item, index) in onCampusPrice" :key="index">
            <q-card-section class="relative-position">
              <q-item>
                <q-item-section>
                  <div class="text-grey-6">
                    Hourly rate income of service provider
                    <span class="text-primary text-bold">
                      ${{ servicePackage.hourRateList?.[index]?.toFixed(2) }}
                      <span class="text-teal">
                        * {{ servicePackage.inflationRate }}%
                        <q-tooltip> Inflation rate </q-tooltip>
                      </span>
                    </span>
                  </div>
                </q-item-section>
                <q-item-section side>
                  <div class="text-grey-6">
                    Compensation hours for each service
                    <span class="text-primary text-bold"> {{ cityInfo(item?.city)?.compensationHour }} hours </span>
                  </div>
                </q-item-section>
              </q-item>
              <div class="bg-teal-2 q-pa-md q-mb-md rounded-borders-sm row items-center">
                <div class="text-bold">
                  Qualification:
                  {{ servicePackage.qualificationList.find((e) => e.value == servicePackage.qualification)?.label }}
                </div>
                <div class="col text-right text-grey-6 text-bold">
                  cost per session = {{ servicePackage.hourRateList?.[index] }} * {{ 60 }} / 60 =
                  <span class="text-primary">${{ ((servicePackage.hourRateList?.[index] * 60) / 60).toFixed(2) }}</span>
                </div>
              </div>
              <q-item class="row">
                <q-item-section class="col-8">
                  <div class="row items-center">
                    <q-img class="q-mr-sm" style="width: 124px; height: 90px" fit="fill" :src="hashToUrl(cityInfo(item?.city)?.attachmentsCity?.[0]?.hash)" />
                    <div class="col">
                      <div class="text-bold">{{ item?.city }}, {{ cityInfo(item?.city)?.country }}</div>
                    </div>
                  </div>
                </q-item-section>
                <q-item-section>
                  <q-input
                    v-model.number="item.price"
                    :disable="readonly"
                    outlined
                    stack-label
                    prefix="$"
                    label="per hour"
                    type="number"
                    :rules="[validateInteger, valueRequired]"></q-input>
                </q-item-section>
                <q-item-section>
                  <q-btn
                    dense
                    class="absolute-top-right q-mr-md q-mt-md"
                    v-if="onCampusPrice?.length > 1"
                    color="red"
                    icon="delete_outline"
                    flat
                    no-caps
                    @click="deleteOnCampusPrice(index)"
                    :disable="readonly"></q-btn>
                </q-item-section>
              </q-item>
              <template v-if="item?.price > 0">
                <div class="bg-teal-2 rounded-md q-mt-md q-py-sm shadow-2" v-for="(itemDiscount, i) in item.discount" :key="i">
                  <q-item class="row items-center">
                    <q-item-section>
                      <q-input
                        v-model.number="itemDiscount.count"
                        :rules="[validateInteger, valueRequired]"
                        :disable="readonly"
                        type="number"
                        outlined
                        stack-label
                        label="No of hours"></q-input>
                    </q-item-section>
                    <q-item-section side>
                      <q-item-label class="text-h5 text-teal"> ${{ (calPrice(itemDiscount, item.price).price / 100).toFixed(2) }} </q-item-label>
                    </q-item-section>
                    <q-item-section avatar v-if="item.discount.length > 1">
                      <q-btn flat icon="close" @click="deleteDiscountClick(item, i)" :disable="readonly"></q-btn>
                    </q-item-section>
                  </q-item>
                </div>
                <q-btn
                  class="q-mt-sm text-teal"
                  flat
                  outlint
                  no-caps
                  icon="add"
                  label="Add bundle sales choice"
                  :disable="readonly"
                  @click="addDiscountClick(item)"></q-btn>
              </template>
            </q-card-section>
          </q-card>
        </q-card-section>
        <q-card-section>
          <q-item class="row justify-end">
            <q-btn class="text-teal" outline rounded label="Cancel" no-caps v-close-popup></q-btn>
            <q-btn class="text-white bg-teal q-ml-md" rounded label="Confirm" no-caps type="submit"></q-btn>
          </q-item>
        </q-card-section>
      </q-form>
    </q-card>
  </q-dialog>
  <q-dialog v-model="dialog">
    <q-card class="q-pa-lg" style="width: 600px">
      Choose a service location
      <q-card-section class="shadow-2 rounded-borders-md q-my-md">
        <div class="text-bold">
          On campus service
          <q-icon class="cursor-pointer q-ml-sm" size="1.5rem" name="help_outline">
            <q-tooltip>
              The organisations who purchase this product will be able to book offline teaching assistants <br />
              in the chosen locations
            </q-tooltip>
          </q-icon>
        </div>
        <q-select
          class="q-mt-md"
          v-model="chooseCity"
          :options="cityOptions"
          color="teal"
          label-color="teal"
          emit-value
          map-options
          outlined
          label="Please choose the offline service locations"
          icon="search" />
      </q-card-section>
      <q-card-actions class="row q-col-gutter-md">
        <div class="col-6">
          <q-btn class="full-width" icon="arrow_back" color="primary" outline no-caps rounded label="Not now" v-close-popup />
        </div>
        <div class="col-6">
          <q-btn class="full-width" :disable="!chooseCity" icon="done" color="primary" label="Save" no-caps rounded @click="addOnCampusPrice" />
        </div>
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>
<script setup>
import {ref, onMounted, computed, watch} from 'vue'
import {pubStore} from 'stores/pub'
import {useRoute, useRouter} from 'vue-router'
import {useDialogPluginComponent} from 'quasar'
import {servicePackageStore} from 'stores/service-package'
import {OverseasStudyList} from 'src/pages/teacher-verification/utils'
/*
  consts
*/
const route = useRoute()
const router = useRouter()
const pub = pubStore()
const servicePackage = servicePackageStore()

const props = defineProps({})

const dialog = ref(false)
const chooseCity = ref(null)

const cityOptions = ref([])
const cityAllOptions = ref([])
const teacherStatistics = ref([])
const readonly = ref(false)
const onCampusPrice = ref(Acan.clone(servicePackage.onCampusPrice))

const targetSoldOptions = [
  {
    label: 'Organization',
    value: 'school',
  },
]

const myForm = ref(null)

const isFormValid = ref(false)

defineEmits([...useDialogPluginComponent.emits])
const {dialogRef, onDialogHide, onDialogOK, onDialogCancel} = useDialogPluginComponent()

/*
  computeds
*/

/*
  methods
*/

const valueRequired = (val) => val?.toString()?.length > 0 || 'Value is required'

const validateInteger = (val) => {
  return (val != null && val > 0 && Number.isInteger(val)) || 'value must be a positive integer'
}

// const hourRate = (data) => {
//   const gradeGroup = servicePackage.gradeGroup?.grades || []
//   const qualification = servicePackage.qualification
//   const rateType = qualification === 'experiencedTeacher' ? 'experienceRate' : 'tutorRate'
//   const rates = data?.[rateType] || {}

//   const matchedGradeKeys = Object.keys(rates).filter((key) => gradeGroup.includes(key))

//   if (matchedGradeKeys.length > 0) {
//     return rates[matchedGradeKeys[0]]
//   }

//   return 0
// }

const calPrice = (item, prePrice) => {
  const price = prePrice * 100
  return {
    price: item?.count * price,
  }
}

const addNew = () => {
  chooseCity.value = null
  dialog.value = true
  cityOptions.value = cityAllOptions.value?.filter((item) => !onCampusPrice.value.some((price) => price.city === item.value))
  // cityOptions.value = cityOptions.value.map(item => ({
  //   ...item,
  //   disable: servicePackage.onCampusPrice.some(price => price.city === item.value)
  // }))
}

const addOnCampusPrice = () => {
  console.log('chooseCity', chooseCity.value)
  onCampusPrice.value.push({
    city: chooseCity.value,
    price: 0,
    hash: cityInfo(chooseCity.value)?.attachmentsCity?.[0]?.hash,
    discount: [
      {
        count: 0,
        discount: 0,
        gifts: 0,
      },
    ],
  })
  console.log('onCampusPrice.value', onCampusPrice.value)
  dialog.value = false
}

const deleteOnCampusPrice = (index) => {
  onCampusPrice.value.splice(index, 1)
}

const deleteDiscountClick = (item, index) => {
  item.discount.splice(index, 1)
}

const addDiscountClick = (item) => {
  item.discount.push({
    count: 0,
    discount: 0,
    gifts: 0,
  })
}

const cityInfo = (city) => cityAllOptions.value?.find((item) => item.value === city)

const getCityOptions = async () => {
  const res = await App.service('campus-location').find({
    query: {
      $limit: 1000,
      $sort: {updatedAt: -1},
      country: servicePackage?.country,
      archive: false,
    },
  })

  if (res) {
    cityAllOptions.value = res?.data?.map((item) => {
      return {
        ...item,
        label: item?.city,
        value: item?.city,
      }
    })
    console.log('cityAllOptions', cityAllOptions.value)
  }
  console.log('res', res)
}

const onConfirm = () => {
  servicePackage.onCampusPrice = onCampusPrice.value
  onDialogOK()
}

const main = () => {
  getCityOptions()
}

onMounted(main)

const checkFormValid = async () => {
  myForm.value?.validate()?.then((result) => {
    isFormValid.value = result
  })
}

// watch(
//   () => myForm.value,
//   (val) => {
//     checkFormValid()
//   }
// )

// watch(
//   () => onCampusPrice.value,
//   (val) => {
//     checkFormValid()
//   },
//   {deep: true}
// )
</script>
