<template>
  <q-dialog ref="dialogRef" @hide="onDialogHide">
    <q-card class="q-my-md rounded-borders-md " style="width: 500px">
      <q-card-section>
        <div class="text-h6">* Package tag setting</div>
        <div v-if="servicePackage.mentoringType">
          <q-item-label class="text-subtitle1 text-weight-medium q-my-md">* Curriculum</q-item-label>
          <q-select
            class="q-pa-xs"
            v-if="servicePackage.mentoringType === 'academic'"
            v-model="curriculum"
            :options="filteredCurriculumList"
            @update:modelValue="onCurriculumUpdate"
            outlined
            label="Search and select curriculum"
            icon="search"
            use-input
            @filter="filterCurriculumList"></q-select>
          <q-chip v-else color="grey-5" disabled square no-caps label="Service"></q-chip>
        </div>
        <!-- <div v-if="servicePackage.countryCodeRequired">
          <q-item-label class="text-subtitle1 text-weight-medium q-my-md">* Country</q-item-label>
          <q-chip color="grey-5" disabled square no-caps :label="OverseasStudyList.find((e) => e.value == servicePackage.countryCode?.[0])?.label"></q-chip>
        </div> -->
        <div v-if="servicePackage.type && servicePackage.mentoringType && curriculum.value && servicePackage.mentoringType !== 'overseasStudy'">
          <q-item-label class="text-subtitle1 text-weight-medium q-my-md"
            >* {{ subjectBlockTitle }} ({{ isMultipleSubjectChoice ? 'Multiple choice' : 'Single choice' }})</q-item-label
          >
          <div v-if="servicePackage.topicWithChilds">
            <q-select
              class="q-pa-xs"
              v-model="essayOrTeacherTrainingSubjectLists"
              :options="currentSubjectList"
              :multiple="isMultipleSubjectChoice"
              outlined
              label="Search and select field"
              icon="search"
              use-input
              @update:model-value="essayOrTSubChange"></q-select>

            <template v-for="item in essayOrTeacherTrainingSubjectLists" :key="item?.label">
              <q-card class="q-mt-md ">
                <q-card-section>
                  <div class="row items-center q-my-sm">
                    <div class="col">{{ item?.label }}</div>
                    <q-btn flat icon="o_close" class="q-pa-sm"  @click="closeSubject(item)"></q-btn>
                  </div>
                  <q-chip
                    v-for="(subject, i) in item?.value"
                    :key="i"
                    color="primary"
                    text-color="white"
                    :outline="!subjectSelected?.includes(subject?._id)"
                    square
                    clickable
                    :label="subject.name"
                    @click="subjectClick(subject, i)" />
                </q-card-section>
              </q-card>
            </template>
          </div>
          <div v-else>
            <span v-for="(subject, i) in currentSubjectList" :key="i" style="display: inline-block">
              <span>
                <q-chip
                  color="primary"
                  text-color="white"
                  :outline="!subjectSelected?.includes(subject?._id)"
                  square
                  clickable
                  :label="subject.name"
                  @click="subjectClick(subject, i)">
                </q-chip>
              </span>
              <br />
              <span class="q-mr-md text-caption text-grey-5" style="float: right">{{ subject.subtitle }}</span>
            </span>
          </div>
        </div>
      </q-card-section>
      <q-card-section>
        <q-item class="row">
          <q-item-section class="col-6">
            <q-btn class="text-teal" outline rounded label="Cancel" no-caps v-close-popup></q-btn>
          </q-item-section>
          <q-item-section class="col-6">
            <q-btn class="text-white bg-teal" rounded label="Confirm" :disabled="!subjectSelected.length" no-caps @click="onConfirm"></q-btn>
          </q-item-section>
        </q-item>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>
<script setup>
import {ref, onMounted, computed} from 'vue'
import {pubStore} from 'stores/pub'
import {useRoute, useRouter} from 'vue-router'
import {useDialogPluginComponent} from 'quasar'
import {servicePackageStore} from 'stores/service-package'
import {OverseasStudyList} from 'src/pages/teacher-verification/utils'
/*
  consts
*/
const route = useRoute()
const router = useRouter()
const pub = pubStore()
const servicePackage = servicePackageStore()

const props = defineProps({
  tagSettingDisable: Boolean,
  subjectBlockTitle: String,
  isMultipleSubjectChoice: Boolean,
  propsEssayOrTeacherTrainingSubjectLists: Array,
  curriculumList: Array,
  userCurriculumCodes: Array,
  isMultipleContents: Boolean,
})

const filteredCurriculumList = ref([])
const essayOrTeacherTrainingSubjectLists = ref()

const subjectSelected = ref([])
const subjectSelectedOld = ref([])

const curriculum = ref({
  ...servicePackage.curriculum,
})

defineEmits([...useDialogPluginComponent.emits])
const {dialogRef, onDialogHide, onDialogOK, onDialogCancel} = useDialogPluginComponent()

/*
  computeds
*/

const currentSubjectList = computed(() => {
  let subjects = servicePackage.subjectList?.filter((subject) => subject.curriculum[0] === curriculum?.value?.value)

  const subjectsDetail = servicePackage.serviceSubjects[servicePackage.mentoringType]
  if (servicePackage.mentoringType !== 'academic' && subjectsDetail?.topic) {
    if (servicePackage.topicWithChilds) {
      subjects = []
      subjectsDetail.topic.map((topic) => {
        subjects.push({
          label: topic.name,
          value: topic.child,
        })
      })
    } else {
      subjects = subjectsDetail.topic
    }
  }

  return subjects
})

/*
  methods
*/

const essayOrTSubChange = (value) => {
  const valueList = essayOrTeacherTrainingSubjectLists.value.map((s) => s.value)?.flat()?.map((s) => s?._id)
  subjectSelected.value = subjectSelected.value?.filter((s) => valueList.includes(s))
}

const closeSubject = (item) => {
  essayOrTeacherTrainingSubjectLists.value = essayOrTeacherTrainingSubjectLists.value.filter((s) => s.label !== item.label)
  essayOrTSubChange()
}

const isEqualArray = (arr1, arr2) => {
  if (arr1.length !== arr2.length) return false
  return arr1.slice().sort().toString() === arr2.slice().sort().toString()
}

const subjectClick = (subject, index) => {

  if (!props?.isMultipleSubjectChoice) {
    subjectSelected.value = [subject._id]
  } else {
    if (subjectSelected.value.includes(subject._id)) {
      subjectSelected.value = subjectSelected.value.filter((s) => s !== subject._id)
    } else {
      subjectSelected.value.push(subject._id)
    }
  }
  // subject.selected = !subject.selected
}

const onCurriculumUpdate = (value) => {
  subjectSelected.value = []
}

function filterCurriculumList(value, update) {
  const text = value.toLowerCase()
  update(() => {
    let list = props.curriculumList.filter((e) => !props.userCurriculumCodes.includes(e.value))
    //list = list.filter((e) => e.value !== 'others')
    list = list.filter((e) => e.label.toLowerCase().includes(text) || e.value.toLowerCase().includes(text))
    if (servicePackage.type === 'substituteAcademic') {
      list = list.filter((e) => e.value !== 'pd')
    }

    if (servicePackage.mentoringType === 'academic') {
      list = list.filter((e) => e.value !== 'pd')
    }
    filteredCurriculumList.value = list
  })
}

const onConfirm = () => {
  if (subjectSelected.value.length < 1) {
    $q.notify({type: 'negative', message: 'Please select a subject'})
    return
  }

  let isTagSame = isEqualArray(subjectSelectedOld.value, subjectSelected.value)

  if (!props.isMultipleContents) {
    isTagSame =
      subjectSelected.value.every((item) => subjectSelectedOld.value.includes(item)) && subjectSelected.value.length <= subjectSelectedOld.value.length
  }


  onDialogOK({
    subjectSelected: subjectSelected.value,
    curriculum: curriculum.value,
    essayOrTeacherTrainingSubjectLists: essayOrTeacherTrainingSubjectLists.value,
    isContentChanged: !isTagSame,
  })
}

const main = () => {
  console.log('props', props)
  essayOrTeacherTrainingSubjectLists.value = props.propsEssayOrTeacherTrainingSubjectLists
    ? Acan.clone(props.propsEssayOrTeacherTrainingSubjectLists)
    : props.propsEssayOrTeacherTrainingSubjectLists

  if (servicePackage.topicWithChilds && props.propsEssayOrTeacherTrainingSubjectLists) {
    const valueList = props.propsEssayOrTeacherTrainingSubjectLists?.map((list) => list.value).flat()

    subjectSelected.value = valueList?.filter((s) => s.selected).map((s) => s._id)
  } else {
    subjectSelected.value = currentSubjectList.value?.filter((s) => s.selected).map((s) => s._id)
  }

  subjectSelectedOld.value = [...subjectSelected.value]
}

onMounted(main)
</script>
