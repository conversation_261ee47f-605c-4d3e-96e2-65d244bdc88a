<template>
  <q-dialog ref="dialogRef" @hide="onDialogHide">
    <q-card class="q-my-md rounded-borders-md bg-teal-1" style="width: 800px; max-width: 80vw">
      <q-form ref="myForm" @submit="onConfirm" class="q-gutter-md">
        <q-card-section>
          <div class="row items-center q-mt-md">
            <div class="text-bold q-mr-md">Sold to</div>
            <div class="col">
              <q-option-group disable color="primary" :options="targetSoldOptions" type="checkbox" inline v-model="servicePackage.salesTarget" />
            </div>
          </div>
          <div class="row items-center q-my-md">
            <div class="text-grey-6">
              Hourly rate income of service provider
              <span class="text-primary text-bold">
                ${{ servicePackage.hourRate.toFixed(2) }}
                <span class="text-teal">
                  * {{ servicePackage.inflationRate }}%
                  <q-tooltip> Inflation rate </q-tooltip>
                </span>
              </span>
            </div>
          </div>
          <div class="bg-teal-2 q-pa-md q-mb-md rounded-borders-sm row items-center">
            <div class="text-bold">
              Qualification:
              {{ servicePackage.qualificationList.find((e) => e.value == servicePackage.qualification)?.label }}
            </div>
            <div class="col text-right text-grey-6 text-bold">
              cost per session = {{ servicePackage.hourRate }} * {{ 60 }} / 60 =
              <span class="text-primary">${{ ((servicePackage.hourRate * 60) / 60).toFixed(2) }}</span>
            </div>
          </div>
          <q-input v-model.number="price" outlined stack-label prefix="$" label="per hour" type="number" :rules="[validateInteger, valueRequired]" />
          <div v-if="price > 0">
            <q-card class="q-my-md bg-teal-1" v-for="(item, i) in discount" :key="i">
              <q-card-section>
                <q-item>
                  <q-item-section>
                    <q-input
                      v-model.number="item.count"
                      :rules="[validateInteger, valueRequired]"
                      type="number"
                      outlined
                      stack-label
                      label="No of hours"></q-input>
                  </q-item-section>
                  <q-item-section side>
                    <q-item-label class="text-h5 text-teal"> ${{ (calPrice(item)?.price / 100).toFixed(2) }} </q-item-label>
                  </q-item-section>
                  <q-item-section avatar v-if="discount.length > 1">
                    <q-btn flat icon="close" @click="deleteDiscountClick(i)"></q-btn>
                  </q-item-section>
                </q-item>
              </q-card-section>
            </q-card>
            <q-btn
              v-if="price > 0"
              class="q-mt-sm text-teal"
              flat
              outlint
              no-caps
              icon="add"
              label="Add bundle sales choice"
              @click="addDiscountClick()"></q-btn>
          </div>
        </q-card-section>
        <q-card-section>
          <q-item class="row justify-end">
            <q-btn class="text-teal" outline rounded label="Cancel" no-caps v-close-popup></q-btn>
            <q-btn class="text-white bg-teal q-ml-md" rounded label="Confirm" no-caps type="submit"></q-btn>
          </q-item>
        </q-card-section>
      </q-form>
    </q-card>
  </q-dialog>
</template>
<script setup>
import {ref, onMounted, computed, watch} from 'vue'
import {pubStore} from 'stores/pub'
import {useRoute, useRouter} from 'vue-router'
import {useDialogPluginComponent} from 'quasar'
import {servicePackageStore} from 'stores/service-package'
import {OverseasStudyList} from 'src/pages/teacher-verification/utils'
/*
  consts
*/
const route = useRoute()
const router = useRouter()
const pub = pubStore()
const servicePackage = servicePackageStore()

const props = defineProps({})

const price = ref(servicePackage.price)
const discount = ref(Acan.clone(servicePackage.discount))

const targetSoldOptions = [
  {
    label: 'Organization',
    value: 'school',
  },
]

const myForm = ref(null)

const isFormValid = ref(false)

defineEmits([...useDialogPluginComponent.emits])
const {dialogRef, onDialogHide, onDialogOK, onDialogCancel} = useDialogPluginComponent()

/*
  computeds
*/

/*
  methods
*/

const valueRequired = (val) => val?.toString()?.length > 0 || 'Value is required'

const validateInteger = (val) => {
  return (val > 0 && Number.isInteger(val)) || 'value must be a positive integer'
}

const calPrice = (item) => ({
  price: item?.count * price.value * 100,
})

const addDiscountClick = () => {
  discount.value.push({
    count: 0,
    discount: 0,
    gifts: 0,
  })
}

const deleteDiscountClick = (index) => {
  discount.value.splice(index, 1)
}

const onConfirm = () => {
  servicePackage.price = parseInt(price.value)
  servicePackage.discount = discount.value
  onDialogOK()
}

const main = () => {}

onMounted(main)

const checkFormValid = async () => {
  myForm.value?.validate()?.then((result) => {
    isFormValid.value = result
  })
}

// watch(
//   () => myForm.value,
//   (val) => {
//     checkFormValid()
//   }
// )

// watch(
//   [() => price.value, () => discount.value],
//   (val) => {
//     checkFormValid()
//   },
//   {deep: true}
// )
</script>
