<template>
  <q-dialog ref="dialogRef" @hide="onDialogHide">
    <q-card>
      <q-toolbar class="q-px-lg q-pt-lg">
        <q-item-label class="text-h6"> Ratio of Valid sold package / Total sold packages </q-item-label>
      </q-toolbar>
      <q-card-section>
        <q-item class="row">
          <q-item-section class="col-1">
            <q-icon name="done" class="text-teal" size="2rem"></q-icon>
          </q-item-section>
          <q-item-section>
            <q-item-label class="text-subtitle1 text-weight-medium"> Valid package </q-item-label>
          </q-item-section>
          <q-item-section side class="col-2">
            <q-item-label class="text-weight-bold text-h6">
              {{ `${pack.count?.valid ? pack.count?.valid : 0} / ${pack.count?.sold ? pack.count?.sold : 0}` }}
            </q-item-label>
          </q-item-section>
        </q-item>
      </q-card-section>
      <q-card-section class="q-px-lg text-weight-medium"> The service package can only be deleted when all the sold packages are invalid </q-card-section>
      <q-separator inset />
      <q-card-section>
        <q-btn class="full-width bg-red text-white" rounded no-caps label="Confirm to delete" @click="deletePackage()"></q-btn>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>
<script setup>
import {ref, onMounted, computed, watch, nextTick} from 'vue'
import {pubStore} from 'stores/pub'
import {useRoute, useRouter} from 'vue-router'
import {useDialogPluginComponent} from 'quasar'
import {servicePackageStore} from 'stores/service-package'
/*
  consts
*/
const route = useRoute()
const router = useRouter()
const pub = pubStore()

const props = defineProps({
  pack: Object,
})

defineEmits([...useDialogPluginComponent.emits])
const {dialogRef, onDialogHide, onDialogOK, onDialogCancel} = useDialogPluginComponent()

const deletePackage = () => {
  onDialogOK()
}
</script>
