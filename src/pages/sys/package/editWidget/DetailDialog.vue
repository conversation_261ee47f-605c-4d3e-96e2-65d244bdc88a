<template>
  <q-dialog ref="dialogRef" @hide="onDialogHide">
    <q-card class="q-pa-md rounded-borders-md bg-teal-1" style="width: 500px">
      <q-form ref="myForm" @submit="onConfirm" class="q-gutter-md">
        <q-card-section>
          <q-toolbar class="text-h6"> Package detail</q-toolbar>
          <div id="error-cover">
            <div class="text-subtitle1 text-weight-medium">* Upload image</div>
            <br />
            <div class="row justify-center">
              <q-img fit="cover" v-if="form.cover" width="200px" class="rounded-borders-md q-mt-md" :src="hashToUrl(form.cover)"></q-img>
            </div>
            <div class="row q-mt-md">
              <q-card
                class="no-shadow bg-teal-2 text-teal cursor-pointer col-12"
                style="border: 2px dotted turquoise; border-radius: 10px"
                @click="uploadCoverImage">
                <q-card-section>
                  <div class="q-my-sm flex justify-center items-center">
                    <q-icon name="upload" size="1.5rem"></q-icon>
                  </div>
                  <div class="text-center">Click to select image to upload</div>
                </q-card-section>
              </q-card>
            </div>
          </div>
          <div id="error-name">
            <br />
            <div class="text-subtitle1 text-weight-medium">* Package description</div>
            <br />
            <q-input class="q-mx-md" :rules="[valueRequired]" :disable="!editable" outlined v-model.trim="form.name" label="Package name" stack-label></q-input>
          </div>
          <div id="error-points">
            <br />

            <q-item-label class="text-subtitle1 text-weight-medium">* Selling points</q-item-label>
            <q-item v-for="(point, i) in form.points" :key="i">
              <q-item-section>
                <q-input class="q-mt-sm" :rules="[valueRequired]" v-model="form.points[i]" outlined placeholder="E.g. Essay tutoring 10 times"></q-input>
              </q-item-section>
              <q-item-section avatar v-if="form.points.length > 1">
                <q-btn flat icon="close" @click="deleteSellingPointClick(i)"></q-btn>
              </q-item-section>
            </q-item>
            <q-btn
              v-if="sellingPointAddShow"
              class="q-mt-md text-teal"
              flat
              outlint
              no-caps
              icon="add"
              label="Add selling point"
              @click="addSellingPoint()"></q-btn>
          </div>
        </q-card-section>

        <q-card-section>
          <q-item class="row">
            <q-item-section class="col-6">
              <q-btn class="text-teal" outline rounded label="Cancel" no-caps v-close-popup></q-btn>
            </q-item-section>
            <q-item-section class="col-6">
              <q-btn class="text-white bg-teal" rounded label="Confirm" no-caps type="submit"></q-btn>
            </q-item-section>
          </q-item>
        </q-card-section>
      </q-form>
    </q-card>
  </q-dialog>
</template>
<script setup>
import {ref, onMounted, computed, watch, nextTick} from 'vue'
import {pubStore} from 'stores/pub'
import {useRoute, useRouter} from 'vue-router'
import {useDialogPluginComponent} from 'quasar'
import {servicePackageStore} from 'stores/service-package'
/*
  consts
*/
const route = useRoute()
const router = useRouter()
const pub = pubStore()
const servicePackage = servicePackageStore()

const props = defineProps({
  editable: Boolean,
})

const form = ref({
  points: [''],
  name: '',
  cover: '',
  coverName: '',
})

const myForm = ref(null)

const isFormValid = ref(false)

defineEmits([...useDialogPluginComponent.emits])
const {dialogRef, onDialogHide, onDialogOK, onDialogCancel} = useDialogPluginComponent()

/*
  computeds
*/

const sellingPointAddShow = computed(() => {
  let AddPoints = true
  if (form.value.points.length === 1) {
    if (form.value.points[0] === '') {
      AddPoints = false
    }
  } else {
    const lastPoint = form.value.points[form.value.points.length - 1]
    if (lastPoint === '') {
      AddPoints = false
    }
  }
  return AddPoints
})

/*
  methods
*/

const valueRequired = (val) => val.length > 0 || 'Value is required'
const uploadCoverImage = async () => {
  $q.loading.show()
  const rs = await Fn.fileUpLoadUiX('image/*')
  $q.loading.hide()
  if (!rs) {
    return
  }
  console.log(rs)
  if (rs.message) {
    pageLoading.value = false
    return $q.notify({type: 'negative', message: rs.message})
  }
  console.log(pub.user)
  form.value.coverName = rs.title?.[pub.user._id]
  form.value.cover = rs._id
}

const addSellingPoint = () => {
  form.value.points.push('')
}

const deleteSellingPointClick = (index) => {
  form.value.points.splice(index, 1)
}

const onConfirm = () => {
  const {points, name, cover, coverName} = form.value
  console.log('cover', cover)
  if (!cover) {
    return $q.notify({type: 'negative', message: 'Please upload image'})
  }

  servicePackage.points = points
  servicePackage.name = name
  servicePackage.cover = cover
  servicePackage.coverName = coverName
  onDialogOK()
}

const main = async () => {
  const {points, name, cover, coverName} = servicePackage
  console.log('servicePackage', servicePackage)
  form.value = {points: [...points], name, cover, coverName}
}

onMounted(main)

const checkFormValid = async () => {
  myForm.value?.validate()?.then((result) => {
    isFormValid.value = result && form.value.cover?.length > 0
  })
}

// watch(
//   () => myForm.value,
//   (val) => {
//     checkFormValid()
//   }
// )

// watch(
//   () => form.value,
//   (val) => {
//     checkFormValid()
//   },
//   {deep: true}
// )
</script>
