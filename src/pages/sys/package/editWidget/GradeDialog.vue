<template>
  <q-dialog ref="dialogRef" @hide="onDialogHide">
    <q-card class="q-my-md rounded-borders-md bg-teal-1" style="width: 500px">
      <q-card-section>
        <div class="text-h6">* Grade {{ `(${isMultipleGradeChoice ? 'Multiple choice' : 'Single choice'})` }}</div>
        <div v-if="servicePackage.isEducatorServiceType">
          <q-chip
            v-for="(label, i) in gradeList"
            :key="i"
            :outline="!gradeGroup.grades?.includes(label)"
            :label="label"
            color="primary"
            text-color="white"
            no-caps
            square
            clickable
            @click="gradeClick(label, i)"></q-chip>
        </div>
        <div class="col row" v-else>
          <div class="q-my-sm">
            <div v-for="(gradesGroup, i) in gradeList" :key="i">
              <div class="q-py-xs" v-if="gradesGroup.grades[servicePackage.curriculum.value]?.length || servicePackage.mentoringType !== 'academic'">
                <q-chip
                  color="primary"
                  text-color="white"
                  square
                  clickable
                  :outline="!gradeGroup.grades?.includes(i)"
                  :label="gradesGroup.label"
                  @click="gradeClick(gradesGroup, i)"></q-chip>
                <template v-if="servicePackage.mentoringType === 'academic'">
                  <span v-for="(grade, i) in gradesGroup.grades[servicePackage.curriculum.value]" :key="i" class="text-grey-5 q-ma-sm">{{ grade }}</span>
                </template>
                <template v-else>
                  <span v-for="(grade, i) in gradesGroup.grades.classcipe" :key="i" class="text-grey-5 q-ma-sm">{{ grade }}</span>
                </template>
              </div>
            </div>
          </div>
        </div>
      </q-card-section>
      <q-card-section>
        <q-item class="row">
          <q-item-section class="col-6">
            <q-btn class="text-teal" outline rounded label="Cancel" no-caps v-close-popup></q-btn>
          </q-item-section>
          <q-item-section class="col-6">
            <q-btn class="text-white bg-teal" rounded label="Confirm" :disabled="!gradeGroup.grades.length" no-caps @click="onConfirm"></q-btn>
          </q-item-section>
        </q-item>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>
<script setup>
import {ref, onMounted, computed} from 'vue'
import {pubStore} from 'stores/pub'
import {useRoute, useRouter} from 'vue-router'
import {useDialogPluginComponent} from 'quasar'
import {servicePackageStore} from 'stores/service-package'
/*
  consts
*/
const route = useRoute()
const router = useRouter()
const pub = pubStore()
const servicePackage = servicePackageStore()

const props = defineProps({
  isMultipleSubjectChoice: Boolean,
  gradeList: Array,
  isMultipleGradeChoice: Boolean,
})


const gradeGroup = ref(Acan.clone(servicePackage.gradeGroup))

defineEmits([...useDialogPluginComponent.emits])
const {dialogRef, onDialogHide, onDialogOK, onDialogCancel} = useDialogPluginComponent()

/*
  computeds
*/

/*
  methods
*/

const onConfirm = () => {
  servicePackage.gradeGroup = gradeGroup.value
  onDialogOK()
}

function gradeClick(label, key) {
  console.log('label', label)
  console.log('key', key)
  if (props.isMultipleGradeChoice) {
    if (gradeGroup.value.grades.some((e) => e === (servicePackage.isEducatorServiceType ? label : key))) {
      gradeGroup.value.grades = gradeGroup.value.grades.filter((e) => e !== (servicePackage.isEducatorServiceType ? label : key))
    } else {
      gradeGroup.value.grades.push(servicePackage.isEducatorServiceType ? label : key)
    }
  } else {
    if (servicePackage.isEducatorServiceType) {
      gradeGroup.value.grades = [label]
    } else {
      gradeGroup.value.label = label.label
      gradeGroup.value.grades = [key]
    }
  }
}

const main = () => {
  console.log('gradeGroup', servicePackage.gradeGroup)
  console.log('gradeList', props.gradeList)
}

onMounted(main)
</script>
