<template>
  <q-dialog ref="dialogRef" @hide="onDialogHide">
    <q-card class="q-my-md rounded-borders-md bg-white" style="width: 1000px; max-width: 80vw">
      <q-form @submit="onConfirm" ref="myForm">
        <q-card-section>
          <q-item-label class="text-subtitle1 text-weight-medium">* Price setting</q-item-label>
          <div class="row items-center q-mt-md">
            <div class="text-bold q-mr-md">Sold to</div>
            <div class="col">
              <q-option-group
                disable
                color="primary"
                :options="[
                  {
                    label: 'Individual user',
                    value: 'personal',
                  },
                ]"
                type="checkbox"
                inline
                v-model="servicePackage.salesTarget" />
            </div>
          </div>
          <div class="row items-center q-mt-md">
            <div class="text-grey-6">
              Hourly rate income of service provider
              <span class="text-primary text-bold">
                ${{ servicePackage.hourRate.toFixed(2) }}
                <span class="text-teal">
                  * {{ servicePackage.inflationRate }}%
                  <q-tooltip> Inflation rate </q-tooltip>
                </span>
              </span>
            </div>
            <div class="col text-grey-6 text-right">
              Length of service
              <span class="text-primary text-bold"> {{ servicePackage.duration }} mins </span>
            </div>
          </div>
          <div class="bg-teal-2 q-pa-md q-mt-md rounded-borders-sm row items-center">
            <div class="text-bold">
              Qualification:
              {{ servicePackage.qualificationList.find((e) => e.value == servicePackage.qualification)?.label }}
            </div>
            <div class="col text-right text-grey-6 text-bold">
              cost per session = {{ servicePackage.hourRate }} * {{ servicePackage.duration }} / 60 =
              <span class="text-primary">${{ ((servicePackage.hourRate * servicePackage.duration) / 60).toFixed(2) }}</span>
            </div>
          </div>
          <div class="q-mt-md">
            <q-input
              v-model.number="showPrice"
              style="width: 200px"
              type="number"
              outlined
              stack-label
              label="Per Session"
              :rules="[validateInteger, valueRequired]">
              <template v-slot:prepend>
                <q-icon name="attach_money" />
              </template>
            </q-input>
          </div>
          <div v-if="showPrice > 0">
            <q-card class="q-my-md bg-teal-1" v-for="(discount, i) in pgDiscount" :key="i">
              <q-card-section>
                <q-item>
                  <q-item-section>
                    <q-input
                      v-model.number="discount.count"
                      type="number"
                      :rules="[validateInteger, (val) => validateCount(val, pgDiscount?.[i - 1]?.count || 0), valueRequired]"
                      outlined
                      stack-label
                      label="No. of session"></q-input>
                  </q-item-section>
                  <q-item-section class="relative-position col-2">
                    <span class="bg-white q-px-sm top_tips">Rate/Session</span>
                    <q-item-label class="">$ {{ wholesalePrice(discount) }}</q-item-label>
                  </q-item-section>
                  <q-item-section class="q-ml-md">
                    <q-input
                      v-model.number="discount.discount"
                      outlined
                      stack-label
                      label="Percentage of retail price"
                      type="number"
                      :rules="[validatePercent, (val) => validateDiscount(val, pgDiscount?.[i - 1]?.discount || 101), valueRequired]">
                      <template v-slot:append>
                        <q-icon name="percent" />
                      </template>
                    </q-input>
                  </q-item-section>
                  <q-item-section class="relative-position col-1">
                    <span class="bg-white q-px-sm top_tips">Discount</span>
                    <q-item-label class="">{{ pgDiscountConfig.discount }} %</q-item-label>
                  </q-item-section>
                  <q-item-section side class="">
                    <q-item-label>${{ (wholesalePrice(discount) * discount.count).toFixed(2) }}</q-item-label>
                    <q-item-label class="text-h5 text-teal">
                      ${{
                        ((wholesalePrice(discount) * discount.count * (100 - (pgDiscountConfig.enable ? pgDiscountConfig.discount || 0 : 0))) / 100).toFixed(2)
                      }}
                    </q-item-label>
                  </q-item-section>
                  <q-item-section avatar v-if="pgDiscount.length > 1">
                    <q-btn flat icon="close" @click="deleteDiscountClick(i)"></q-btn>
                  </q-item-section>
                </q-item>
                <q-item class="row" v-if="discount.gifts || discount.enableGift">
                  <q-item-section class="col-3">
                    <q-input v-model="discount.gifts" outlined stack-label label="No of gifts"></q-input>
                  </q-item-section>
                  <q-item-section class="col-5">
                    <div>
                      <q-btn class="q-mx-md text-red" flat no-caps label="Cancel gifts" @click="deleteGift(discount)"> </q-btn>
                    </div>
                  </q-item-section>
                </q-item>
                <div>
                  <q-btn class="q-mx-md text-teal" flat no-caps icon="add" label="Add gifts" @click="enableGift(discount)"> </q-btn>
                </div>
              </q-card-section>
            </q-card>
          </div>
          <q-btn
            v-if="showPrice > 0"
            class="q-mt-sm text-teal"
            flat
            outlint
            no-caps
            icon="add"
            label="Add bundle sales choice"
            @click="addDiscountClick()"></q-btn>
          <q-item class="row">
            <q-item-section side>
              <q-item-label class="q-pb-md">Active discount</q-item-label>
            </q-item-section>
            <q-item-section side class="q-pb-md">
              <q-toggle v-model="pgDiscountConfig.enable"></q-toggle>
            </q-item-section>
            <q-item-section class="col-3">
              <q-input
                v-model.number="pgDiscountConfig.discount"
                :rules="[validatePercent, valueRequired]"
                :disable="!pgDiscountConfig.enable"
                outlined
                stack-label
                type="number"
                label="Discount"
                hint="">
                <template v-slot:append>
                  <q-icon name="percent" />
                </template>
              </q-input>
            </q-item-section>
            <q-item-section :class="$q.screen.lt.sm ? 'col-6' : 'col-3'">
              <q-input
                v-model="pgDiscountConfig.end"
                outlined
                stack-label
                :disable="!pgDiscountConfig.enable"
                type="date"
                label="Discount ends"
                hint="YYYY/MM/DD"
                :min="new Date().toISOString().split('T')[0]"></q-input>
            </q-item-section>
          </q-item>
        </q-card-section>
        <q-card-section>
          <q-item class="row justify-end">
            <q-btn class="text-teal" outline rounded label="Cancel" no-caps v-close-popup></q-btn>
            <q-btn class="text-white bg-teal q-ml-md" rounded label="Confirm" no-caps type="submit"></q-btn>
          </q-item>
        </q-card-section>
      </q-form>
    </q-card>
  </q-dialog>
</template>
<script setup>
import {ref, onMounted, computed, watch} from 'vue'
import {pubStore} from 'stores/pub'
import {useRoute, useRouter} from 'vue-router'
import {useDialogPluginComponent} from 'quasar'
import {servicePackageStore} from 'stores/service-package'
import {OverseasStudyList} from 'src/pages/teacher-verification/utils'
/*
  consts
*/
const route = useRoute()
const router = useRouter()
const pub = pubStore()
const servicePackage = servicePackageStore()

const props = defineProps({})

const showPrice = ref(servicePackage.price / 100)
const pgDiscount = ref(Acan.clone(servicePackage.discount))
const pgDiscountConfig = ref(Acan.clone(servicePackage.discountConfig))

const myForm = ref(null)

const isFormValid = ref(false)

defineEmits([...useDialogPluginComponent.emits])
const {dialogRef, onDialogHide, onDialogOK, onDialogCancel} = useDialogPluginComponent()

/*
  computeds
*/

/*
  methods
*/

const valueRequired = (val) => val?.toString()?.length > 0 || 'Value is required'

const validateInteger = (val) => {
  return (val > 0 && Number.isInteger(val)) || 'value must be a positive integer'
}
const validateCount = (val, pre) => {
  return val - pre >= 0 || `The value must exceed  ${pre}`
}

const validateRetailPrice = (val) => {
  return (val > 0 && val < 100 && Number.isInteger(val)) || 'value must be a number between 0 and 100'
}

const validatePercent = (val) => {
  return (val > 0 && val <= 100 && Number.isInteger(val)) || 'value must be a number between 0 and 100'
}

const validateDiscount = (val, pre) => {
  return val - pre < 0 || `The value must no exceed  ${pre}`
}

const wholesalePrice = (item) => {
  return ((showPrice.value * item.discount) / 100).toFixed(2)
}

function addDiscountClick() {
  pgDiscount.value.push({
    count: 0,
    discount: 0,
    gift: 0,
    // orderCount: 0,
  })
}

function deleteDiscountClick(index) {
  pgDiscount.value.splice(index, 1)
}

function enableGift(discount) {
  if (Acan.isEmpty(discount.count) || Acan.isEmpty(discount.discount)) {
    $q.notify({type: 'negative', message: 'Please fill the value first'})
  } else {
    discount.enableGift = true
  }
}

function deleteGift(discount) {
  discount.gifts = 0
  discount.enableGift = false
}

const onConfirm = () => {
  servicePackage.price = parseInt(showPrice.value * 100)
  servicePackage.discount = pgDiscount.value
  servicePackage.discountConfig = pgDiscountConfig.value
  onDialogOK({})
}

const main = () => {
  console.log('props', props)
}

onMounted(main)

const checkFormValid = async () => {
  myForm.value?.validate()?.then((result) => {
    isFormValid.value = result
  })
}

// watch(
//   () => myForm.value,
//   (val) => {
//     checkFormValid()
//   }
// )

// watch(
//   [() => showPrice.value, () => pgDiscount.value, () => pgDiscountConfig.value],
//   (val) => {
//     checkFormValid()
//   },
//   {deep: true}
// )
</script>
<style lang="scss" scoped>
.top_tips {
  position: absolute;
  top: -20px;
  left: 0;
  height: 20px;
}
</style>
