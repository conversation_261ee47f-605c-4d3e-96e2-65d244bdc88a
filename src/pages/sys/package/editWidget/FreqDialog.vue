<template>
  <q-dialog ref="dialogRef" @hide="onDialogHide">
    <q-card class="q-pa-md rounded-borders-md bg-teal-1" style="width: 500px">
      <q-card-section>
        <q-toolbar class="text-h6"> * Minimal frequency of the service</q-toolbar>
        <div id="error-freq">
          <q-item-label class="text-subtitle1 text-weight-medium"> </q-item-label>
          <q-btn class="q-ma-sm" :class="freq === 7 ? 'bg-teal-4 text-white' : ''" rounded outline no-caps label="Weekly" @click="frequencyClick(7)"></q-btn>
          <q-btn
            class="q-ma-sm"
            :class="freq === 14 ? 'bg-teal-4 text-white' : ''"
            rounded
            outline
            no-caps
            label="Fortnightly"
            @click="frequencyClick(14)"></q-btn>
          <q-btn class="q-ma-sm" :class="freq === 30 ? 'bg-teal-4 text-white' : ''" rounded outline no-caps label="Monthly" @click="frequencyClick(30)"></q-btn>
          <!--
              <q-btn
                class="q-ma-sm"
                :class="freq === 120 ? 'bg-teal-4 text-white' : ''"
                rounded
                outline
                no-caps
                label="Quarterly"
                @click="frequencyClick(120)"></q-btn>
              -->
        </div>
      </q-card-section>

      <q-card-section>
        <q-item class="row">
          <q-item-section class="col-6">
            <q-btn class="text-teal" outline rounded label="Cancel" no-caps v-close-popup></q-btn>
          </q-item-section>
          <q-item-section class="col-6">
            <q-btn class="text-white bg-teal" :disabled="!freq" rounded label="Confirm" no-caps @click="onConfirm"></q-btn>
          </q-item-section>
        </q-item>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>
<script setup>
import {ref, onMounted, computed} from 'vue'
import {pubStore} from 'stores/pub'
import {useRoute, useRouter} from 'vue-router'
import {useDialogPluginComponent} from 'quasar'
import {servicePackageStore} from 'stores/service-package'
/*
  consts
*/
const route = useRoute()
const router = useRouter()
const pub = pubStore()
const servicePackage = servicePackageStore()

const props = defineProps({

})

const freq = ref(0)

defineEmits([...useDialogPluginComponent.emits])
const {dialogRef, onDialogHide, onDialogOK, onDialogCancel} = useDialogPluginComponent()

/*
  computeds
*/

/*
  methods
*/

const frequencyClick = (val) => {
  freq.value = val
}

const onConfirm = () => {
  servicePackage.freq = freq.value
  onDialogOK()
}

const main = () => {
  freq.value = servicePackage.freq
}

onMounted(main)
</script>
