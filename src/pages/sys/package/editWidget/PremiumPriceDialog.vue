<template>
  <q-dialog ref="dialogRef" @hide="onDialogHide">
    <q-card class="q-my-md rounded-borders-md bg-white" style="width: 800px; max-width: 80vw">
      <q-form ref="myForm" @submit="onConfirm" class="q-gutter-md">
        <q-card-section>
          <div class="row items-center q-mt-md">
            <div class="text-bold q-mr-md">Sold to</div>
            <div class="col">
              <q-option-group color="primary" :options="soldOptions" type="checkbox" inline v-model="salesTarget" @update:model-value="salesChange" />
            </div>
          </div>
          <q-card class="rounded-borders-md q-mt-md bg-white" v-for="(item, index) in contentOrientated || []" :key="index">
            <div class="row items-center q-pa-md">
              <div class="text-grey-6">
                Hourly rate income of service provider
                <span class="text-primary text-bold">
                  ${{ servicePackage.hourRate.toFixed(2) }}
                  <span class="text-teal">
                    * {{ servicePackage.inflationRate }}%
                    <q-tooltip> Inflation rate </q-tooltip>
                  </span>
                </span>
              </div>
              <div class="col text-grey-6 text-right">
                Length of service
                <span class="text-primary text-bold"> {{ servicePackage.duration }} mins </span>
              </div>
            </div>
            <q-card-section>
              <div class="bg-teal-2 q-pa-md rounded-borders-md row items-center">
                <div class="q-mr-md" style="width: 96px; height: 50px">
                  <q-img
                    :ratio="16 / 7"
                    class="fit rounded-borders-md"
                    fit="cover"
                    spinner-color="white"
                    :src="hashToUrl(item?.premium?.cover || item?.premium?.image || item?.premium?.unitSnapshot?.cover) || '/v2/img/no-img.png'" />
                </div>
                <div class="text-bold ellipsis-2-lines" style="width: 250px">{{ item?.premium?.unit?.name }}</div>
                <div class="col text-right q-pr-md">
                  <div class="text-bold">
                    Qualification:
                    {{ servicePackage.qualificationList.find((e) => e.value == servicePackage.qualification)?.label }}
                  </div>
                  <div class="text-right text-grey-6 text-bold q-mt-sm">
                    cost per session = {{ servicePackage?.hourRateList?.[index] }} * {{ servicePackage.duration }} / 60 =
                    <span class="text-primary">${{ ((servicePackage?.hourRateList?.[index] * servicePackage.duration) / 60).toFixed(2) }}</span>
                  </div>
                </div>
              </div>
              <div class="row items-center" v-if="salesTarget?.includes('school')">
                <div class="text-bold sold_left">Organization</div>
                <div class="col bg-teal-1 q-pa-md rounded-borders-md row items-center q-mt-md">
                  <q-item class="col-12">
                    <q-item-section>
                      <q-input
                        v-model.number="item.schoolPrice"
                        class="q-mr-lg"
                        outlined
                        stack-label
                        label="Rate/Session"
                        type="number"
                        style="width: 150px"
                        :rules="[validateInteger, valueRequired]">
                        <template v-slot:prepend>
                          <q-icon name="attach_money" />
                        </template>
                      </q-input>
                    </q-item-section>

                    <q-item-section class="relative-position">
                      <div class="bg-white top_tips q-px-sm">No of service</div>
                      <q-item-label>{{ item?.times }}</q-item-label>
                    </q-item-section>
                    <q-item-section class="relative-position">
                      <div class="bg-white top_tips q-px-sm">Discount</div>
                      <q-item-label> {{ totalDiscount }}% </q-item-label>
                    </q-item-section>
                    <q-item-section side>
                      <div class="text-grey-6 text-body2 text-strike">${{ (schoolPrice(item)?.price / 100).toFixed(2) }}</div>
                      <div class="text-bold text-primary">${{ (schoolPrice(item)?.discountPrice / 100).toFixed(2) }}</div>
                    </q-item-section>
                  </q-item>
                </div>
              </div>
              <div class="row items-center" v-if="salesTarget?.includes('personal') && salesTarget?.length === 1">
                <div class="text-bold sold_left">Individual</div>
                <div class="col bg-teal-1 q-pa-md rounded-borders-md row items-center q-mt-md">
                  <q-item class="col-12">
                    <q-item-section>
                      <q-input
                        v-model.number="item.price"
                        class="q-mr-lg"
                        outlined
                        stack-label
                        label="Rate/Session"
                        type="number"
                        style="width: 150px"
                        :rules="[validateInteger]">
                        <template v-slot:prepend>
                          <q-icon name="attach_money" />
                        </template>
                      </q-input>
                    </q-item-section>
                    <q-item-section class="relative-position">
                      <div class="bg-white top_tips q-px-sm">No of service</div>
                      <q-item-label>{{ item?.times }}</q-item-label>
                    </q-item-section>
                    <q-item-section class="relative-position">
                      <div class="bg-white top_tips q-px-sm">Discount</div>
                      <q-item-label> {{ totalDiscount }}% </q-item-label>
                    </q-item-section>
                    <q-item-section side>
                      <q-item-label class="text-grey-6 text-body2 text-strike">${{ (personalPrice(item)?.price / 100).toFixed(2) }}</q-item-label>
                      <q-item-label class="text-bold text-primary">${{ (personalPrice(item)?.discountPrice / 100).toFixed(2) }}</q-item-label>
                    </q-item-section>
                  </q-item>
                </div>
              </div>
              <div class="row items-center" v-if="salesTarget?.includes('personal') && salesTarget?.includes('school')">
                <div class="text-bold sold_left">Individual</div>
                <div class="col bg-teal-1 q-pa-md rounded-borders-md row items-center q-mt-md">
                  <q-item class="col-12">
                    <q-item-section class="relative-position">
                      <div class="bg-white top_tips q-px-sm">Rate/Session</div>
                      <q-item-label class="text-bold">$ {{ (item.percentPrice ? item?.price : item?.schoolPrice)?.toFixed(2) }}</q-item-label>
                    </q-item-section>
                    <q-item-section class="relative-position col-4">
                      <span class="text-primary" style="font-size: 12px"> Percentage of organization rate</span>
                      <q-input
                        v-model.number="item.percentPrice"
                        class="q-mr-lg"
                        :disable="!item?.schoolPrice"
                        outlined
                        stack-label
                        type="number"
                        :rules="[validateInteger, validate100, valueRequired]"
                        @update:model-value="percentUp(item)">
                        <template v-slot:after>
                          <q-icon name="percent" />
                        </template>
                      </q-input>
                    </q-item-section>
                    <q-item-section class="relative-position">
                      <div class="bg-white top_tips q-px-sm">No of service</div>
                      <q-item-label>{{ item?.times }}</q-item-label>
                    </q-item-section>
                    <q-item-section class="relative-position">
                      <div class="bg-white top_tips q-px-sm">Discount</div>
                      <q-item-label> {{ totalDiscount }}% </q-item-label>
                    </q-item-section>
                    <q-item-section side>
                      <q-item-label class="text-grey-6 text-body2 text-strike">${{ (personalPercentPrice(item)?.price / 100).toFixed(2) }}</q-item-label>
                      <q-item-label class="text-bold text-primary">${{ (personalPercentPrice(item)?.discountPrice / 100).toFixed(2) }}</q-item-label>
                    </q-item-section>
                  </q-item>
                </div>
              </div>
              <div class="bg-teal-2 q-pa-md rounded-borders-md row items-center q-mt-md" v-if="item?.servicePack">
                <q-item class="col-12">
                  <q-item-section class="relative-position">
                    <div class="q-mr-md" style="width: 96px; height: 50px">
                      <q-img
                        :ratio="16 / 7"
                        class="fit rounded-borders-md"
                        fit="cover"
                        spinner-color="white"
                        :src="
                          hashToUrl(item?.servicePack?.cover || item?.servicePack?.image || item?.servicePack?.unitSnapshot?.cover) || '/v2/img/no-img.png'
                        " />
                    </div>
                  </q-item-section>
                  <q-item-section class="relative-position col-3">
                    <div class="text-bold">
                      <div class="">
                        {{ item?.servicePack?.name || item?.servicePack?.desc }}
                      </div>
                      <div class="text-pink-10">
                        Qualification:
                        {{ servicePackage.qualificationList.find((e) => e.value == item?.servicePack?.qualification)?.label }}
                      </div>
                    </div>
                  </q-item-section>
                  <q-item-section class="relative-position">
                    <div class="bg-white top_tips q-px-sm">Rate/service</div>
                    <div class="">${{ (servicePrice(item)?.rate / 100)?.toFixed(2) }}</div>
                  </q-item-section>
                  <q-item-section class="relative-position">
                    <div class="bg-white top_tips q-px-sm">No of service</div>
                    <div class="">
                      {{ servicePrice(item)?.totalCount }}
                      <div class="text-yellow-9" style="font-size: 12px" v-if="servicePrice(item).gifts">
                        <q-icon name="redeem" size="xs"></q-icon>
                        {{ ` Additional ${servicePrice(item).gifts} free sessions as gifts` }}
                      </div>
                    </div>
                  </q-item-section>
                  <q-item-section class="relative-position">
                    <div class="bg-white top_tips q-px-sm">Discount</div>
                    <div>{{ totalDiscount }}%</div>
                  </q-item-section>
                  <q-item-section side>
                    <div class="q-ml-md">
                      <div class="text-grey-6 text-body2 text-strike">${{ (servicePrice(item)?.oriPrice / 100).toFixed(2) }}</div>
                      <div class="text-bold text-primary">${{ (servicePrice(item)?.price / 100).toFixed(2) }}</div>
                    </div>
                  </q-item-section>
                </q-item>
              </div>
              <div class="q-mt-md row items-center justify-end">
                <q-card class="q-ml-md" v-if="salesTarget?.includes('school')">
                  <q-card-section class="row items-center">
                    <div class="text-right text-bold">Sub total for organisation</div>
                    <div class="q-ml-md">
                      <div class="text-grey-6 text-body2 text-strike">${{ (subTotal(item, 'school')?.price / 100).toFixed(2) }}</div>
                      <div class="text-bold text-primary">${{ (subTotal(item, 'school')?.discountPrice / 100).toFixed(2) }}</div>
                    </div>
                  </q-card-section>
                </q-card>
                <q-card class="q-ml-md">
                  <q-card-section class="row items-center" v-if="salesTarget?.includes('personal')">
                    <div class="text-right text-bold">Sub total for individual</div>
                    <div class="q-ml-md">
                      <div class="text-grey-6 text-body2 text-strike">${{ (subTotal(item, 'personal')?.price / 100).toFixed(2) }}</div>
                      <div class="text-bold text-primary">${{ (subTotal(item, 'personal')?.discountPrice / 100).toFixed(2) }}</div>
                    </div>
                  </q-card-section>
                </q-card>
              </div>
            </q-card-section>
          </q-card>

          <q-item class="row bg-white q-mt-md">
            <q-item-section side>
              <q-item-label class="q-pb-md">Active discount</q-item-label>
            </q-item-section>
            <q-item-section side class="q-pb-md">
              <q-toggle v-model="discountConfig.enable"></q-toggle>
            </q-item-section>
            <q-item-section class="col-3" v-if="servicePackage.contentOrientatedEnable">
              <q-input
                v-model.number="discountConfig.discount"
                :disable="!discountConfig.enable"
                outlined
                stack-label
                type="number"
                label="Discount"
                hint=""
                :rules="[validateInteger, validateBetween]">
                <template v-slot:append>
                  <q-icon name="percent" />
                </template>
              </q-input>
            </q-item-section>
            <q-item-section :class="$q.screen.lt.sm ? 'col-6' : 'col-3'">
              <q-input
                v-model="discountConfig.end"
                outlined
                stack-label
                :disable="!discountConfig.enable"
                type="date"
                label="Discount ends"
                hint="YYYY/MM/DD"
                :min="new Date().toISOString().split('T')[0]"></q-input>
            </q-item-section>
          </q-item>
          <div class="row items-center" v-if="servicePackage.contentOrientatedEnable && servicePackage.contentOrientated.length > 1">
            <div class="text-bold">Allow premium content selection at the time of purchase</div>
            <q-toggle v-model="servicePackage.splitSale" size="xl" color="primary" :disable="!servicePackage?.salesTarget?.includes('school')" />
          </div>

          <template v-if="carerPack && carerPack?._id">
            <div class="text-bold">* Bundled carer service package</div>
            <div class="bg-teal-2 q-pa-md rounded-borders-md row items-center q-mt-md">
              <q-item class="col-12">
                <q-item-section class="relative-position">
                  <div class="q-mr-md" style="width: 96px; height: 50px">
                    <q-img
                      :ratio="16 / 7"
                      class="fit rounded-borders-md"
                      fit="cover"
                      spinner-color="white"
                      :src="hashToUrl(carerPack?.cover || carerPack?.image || carerPack?.unitSnapshot?.cover) || '/v2/img/no-img.png'" />
                  </div>
                </q-item-section>
                <q-item-section class="relative-position col-3">
                  <div class="text-bold">
                    <div class="">
                      {{ carerPack?.name || carerPack?.desc }}
                    </div>
                    <div class="text-pink-10">
                      Qualification:
                      {{ servicePackage.qualificationList.find((e) => e.value == servicePackage?.qualification)?.label }}
                    </div>
                  </div>
                </q-item-section>
                <q-item-section class="relative-position">
                  <div class="bg-white top_tips q-px-sm">Rate/service</div>
                  <div class="">${{ (carerServicePrice()?.rate / 100)?.toFixed(2) }}</div>
                </q-item-section>
                <q-item-section class="relative-position">
                  <div class="bg-white top_tips q-px-sm">No of service</div>
                  <div class="">
                    {{ carerServicePrice()?.totalCount }}
                    <div class="text-yellow-9" style="font-size: 12px" v-if="carerServicePrice().gifts">
                      <q-icon name="redeem" size="xs"></q-icon>
                      {{ ` Additional ${carerServicePrice().gifts} free sessions as gifts` }}
                    </div>
                  </div>
                </q-item-section>
                <q-item-section class="relative-position">
                  <div class="bg-white top_tips q-px-sm">Discount</div>
                  <div>{{ totalDiscount }}%</div>
                </q-item-section>
                <q-item-section side>
                  <div class="q-ml-md">
                    <div class="text-grey-6 text-body2 text-strike">${{ (carerServicePrice()?.oriPrice / 100).toFixed(2) }}</div>
                    <div class="text-bold text-primary">${{ (carerServicePrice()?.price / 100).toFixed(2) }}</div>
                  </div>
                </q-item-section>
              </q-item>
            </div>
          </template>
        </q-card-section>
        <q-card-section>
          <q-item class="row justify-end">
            <q-btn class="text-teal" outline rounded label="Cancel" no-caps v-close-popup></q-btn>
            <q-btn class="text-white bg-teal q-ml-md" rounded label="Confirm" no-caps type="submit"></q-btn>
          </q-item>
        </q-card-section>
      </q-form>
    </q-card>
  </q-dialog>
</template>
<script setup>
import {ref, onMounted, computed, watch} from 'vue'
import {pubStore} from 'stores/pub'
import {calPremiumMentor} from 'src/pages/order/consts'
import {useRoute, useRouter} from 'vue-router'
import {useDialogPluginComponent} from 'quasar'
import {servicePackageStore} from 'stores/service-package'
import {calServiceDiscount} from '../const'
/*
  consts
*/
const route = useRoute()
const router = useRouter()
const pub = pubStore()
const servicePackage = servicePackageStore()
import {date} from 'quasar'
const props = defineProps({})

const salesTarget = ref(Acan.clone(servicePackage.salesTarget))
const contentOrientated = ref(Acan.clone(servicePackage.contentOrientated))
const discountConfig = ref(Acan.clone(servicePackage.discountConfig))

const myForm = ref(null)

const isFormValid = ref(false)

defineEmits([...useDialogPluginComponent.emits])
const {dialogRef, onDialogHide, onDialogOK, onDialogCancel} = useDialogPluginComponent()

/*
  computeds
*/

/*
  methods
*/
const valueRequired = (val) => val?.toString()?.length > 0 || 'Value is required'

const validateInteger = (val) => {
  return (val > 0 && Number.isInteger(val)) || 'value must be a positive integer'
}

const validate100 = (val) => {
  return val > 100 || 'value must exceed 100'
}

const validateBetween = (val) => {
  return (val >= 0 && val <= 100) || 'value must be a number between 0 and 100'
}

const targetSoldOptions = [
  {
    label: 'Organization',
    value: 'school',
  },
  {
    label: 'Individual user',
    value: 'personal',
  },
]

const carerPack = computed(() => {
  return servicePackage?.carerPack
})

const totalDiscount = computed(() => {
  return calServiceDiscount(servicePackage?.discountConfig)
})

const soldOptions = computed(() => {
  let result = [...targetSoldOptions]
  if (salesTarget.value?.length === 1) {
    result = [
      {
        label: 'Organization',
        value: 'school',
        disable: salesTarget.value?.includes('school'),
      },
      {
        label: 'Individual user',
        value: 'personal',
        disable: salesTarget.value?.includes('personal'),
      },
    ]
  }

  return result
})

const carerServicePrice = () => {
  return calPremiumMentor({
    count: servicePackage?.totalTimes,
    servicePack: servicePackage?.carerPack,
    discount: totalDiscount.value,
  })
}

const servicePrice = (item) => {
  return calPremiumMentor({
    count: +item?.times,
    servicePack: item?.servicePack,
    discount: totalDiscount.value,
  })
}

const subTotal = (item, type) => {
  let inPrice = type === 'school' ? item?.schoolPrice : item?.price
  let price = 0
  let discountPrice = 0
  price += calPrice(inPrice, item.times)?.price
  discountPrice += calPrice(inPrice, item.times)?.discountPrice

  if (item?.servicePack) {
    let serPrice = calPremiumMentor({
      count: +item?.times,
      servicePack: item?.servicePack,
      discount: totalDiscount.value,
    })
    price += serPrice?.oriPrice
    discountPrice += serPrice?.price
  }

  return {
    price,
    discountPrice,
  }
}

const schoolPrice = (item) => {
  return calPrice(item?.schoolPrice, item.times)
}

const personalPrice = (item) => {
  return calPrice(item?.price, item.times)
}

const personalPercentPrice = (item) => {
  const price = (item?.schoolPrice * item.percentPrice) / 100
  return calPrice(price, item.times)
}

const calPrice = (itemPrice, times) => {
  let price = itemPrice * 100 * times
  let discountPrice =
    discountConfig.value?.enable && discountConfig.value?.discount ? itemPrice * 100 * ((100 - discountConfig.value?.discount) / 100) * times : price

  return {
    price: isNaN(price) ? 0 : price,
    discountPrice: isNaN(discountPrice) ? 0 : discountPrice,
  }
}

const percentUp = (o) => {
  if (+o.percentPrice <= 100) {
    o.percentPrice = undefined
  }
  contentOrientated.value = [...contentOrientated.value].map((item) => {
    let price = item?.price
    if (item?._id === o?._id) {
      price = (o?.schoolPrice * o?.percentPrice) / 100
    }
    return {
      ...item,
      price,
    }
  })
}

const salesChange = () => {
  if (salesTarget.value?.length === 2) {
    contentOrientated.value?.map((item) => {
      item.price = undefined
      item.percentPrice = undefined
    })
  }
}

const onConfirm = () => {
  servicePackage.salesTarget = salesTarget.value
  servicePackage.contentOrientated = contentOrientated.value
  servicePackage.discountConfig = discountConfig.value
  onDialogOK()
}

const main = () => {
  console.log(servicePackage.carerPack)
}

onMounted(main)

const checkFormValid = async () => {
  myForm.value?.validate()?.then((result) => {
    isFormValid.value = result
  })
}

// watch(
//   () => myForm.value,
//   (val) => {
//     checkFormValid()
//   }
// )

// watch(
//   [() => contentOrientated.value, () => discountConfig.value],
//   (val) => {
//     checkFormValid()
//   },
//   {deep: true}
// )
</script>
<style lang="scss" scoped>
.sold_left {
  width: 90px;
}

.top_tips {
  position: absolute;
  top: -20px;
  left: 0;
}
</style>
