<template>
  <q-item-label class="text-h6 q-pt-md" :class="{ 'text-negative': error }">
    <div class="row items-center">
      <div class="col">* Price setting</div>
    </div>
  </q-item-label>
  <div class="row items-center q-mt-md">
    <div class="text-bold q-mr-md">Sold to</div>
    <div class="col">
      <q-option-group disable color="primary" :options="targetSoldOptions" type="checkbox" inline
        v-model="servicePackage.salesTarget" />
    </div>
  </div>
  <q-input v-model.number="servicePackage.price" :disable="readonly" outlined stack-label prefix="$" label="per hour"
    type="number" :rules="[val => val > 0 || 'Number must be greater than 0']" />
  <div v-if="servicePackage.price > 0">
    <q-card class="q-my-md bg-teal-1" v-for="(item, i) in servicePackage.discount" :key="i">
      <q-card-section>
        <q-item>
          <q-item-section>
            <q-input v-model.number="item.count" :disable="readonly" outlined stack-label label="No of hours"></q-input>
          </q-item-section>
          <q-item-section side>
            <q-item-label class="text-h5 text-teal">
              ${{ (calPrice(item)?.price / 100).toFixed(2) }}
            </q-item-label>
          </q-item-section>
          <q-item-section  avatar v-if="servicePackage.discount.length > 1">
            <q-btn :disable="readonly" flat icon="close" @click="deleteDiscountClick(i)"></q-btn>
          </q-item-section>
        </q-item>
      </q-card-section>
    </q-card>
    <q-btn :disable="readonly"  v-if="servicePackage.price > 0" class="q-mt-sm text-teal" flat outlint no-caps icon="add"
      label="Add bundle sales choice" @click="addDiscountClick()"></q-btn>
  </div>
</template>
<script setup>
/*
  imports
*/
import { computed, onMounted, ref, watch } from 'vue'
import { servicePackageStore } from 'stores/service-package'
import { calPremiumMentor } from 'src/pages/order/consts'
import { date } from 'quasar'


const targetSoldOptions = [
  {
    label: 'Organization',
    value: 'school',
  },
]

const dialog = ref(false)
const chooseCity = ref(null)

const cityOptions = ref([])

const props = defineProps({
  error: Boolean,
  readonly: Boolean,
})

const emit = defineEmits(['update'])
const servicePackage = servicePackageStore()


const calPrice = (item) => ({
  price: item?.count * servicePackage.price * 100,
})


const addDiscountClick = () => {
  servicePackage.discount.push({
    count: 0,
    discount: 0,
    gifts: 0,
  })
}

const deleteDiscountClick = (index) => {
  servicePackage.discount.splice(index, 1)
}


const onValidateUpdate = () => {
  const isError = !servicePackage?.discount.every(item => item.count >  0)
  console.log('subOnlineErrorValidate', isError)
  emit('update', isError)
}


const initData = () => {
  servicePackage.salesTarget = ['school']
}

watch(
  () => servicePackage.discount,
  (val) => {
    onValidateUpdate()
  },
  {deep: true}
)


onMounted(async () => {
  initData()
  onValidateUpdate()
})
</script>
