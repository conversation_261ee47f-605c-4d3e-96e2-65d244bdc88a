<template>
  <q-item-label class="text-h6 q-pt-md" :class="{'text-negative': error}">*Price setting & Target sales group</q-item-label>
  <div class="row items-center q-mt-md">
    <div class="text-bold q-mr-md">Sold to</div>
    <div class="col">
      <q-option-group
        :disable="readonly"
        color="primary"
        :options="soldOptions"
        type="checkbox"
        inline
        v-model="servicePackage.salesTarget"
        @update:model-value="salesChange" />
    </div>
  </div>
  <q-card class="rounded-borders-md q-mt-md bg-white" v-for="(item, index) in servicePackage?.contentOrientated || []" :key="index">
    <q-card-section>
      <div class="bg-teal-2 q-pa-md rounded-borders-md row items-center">
        <div class="q-mr-md" style="width: 96px; height: 50px">
          <q-img
            :ratio="16 / 7"
            class="fit rounded-borders-md"
            fit="cover"
            spinner-color="white"
            :src="hashToUrl(item?.premium?.cover || item?.premium?.image || item?.premium?.unitSnapshot?.cover) || '/v2/img/no-img.png'" />
        </div>
        <div class="text-bold ellipsis-2-lines" style="width: 250px">{{ item?.premium?.unit?.name }}</div>
        <div class="col text-right q-pr-md">
          <div class="text-bold">
            Qualification:
            {{ servicePackage.qualificationList.find((e) => e.value == servicePackage.qualification)?.label }}
          </div>
          <div class="">
            <HourlyRate
              :topic="topic"
              :duration="servicePackage.duration"
              :curriculum="servicePackage.curriculum"
              :type="servicePackage.serviceRoles"
              :mentoringType="servicePackage.mentoringType"
              :qualification="servicePackage.qualification" />
          </div>
        </div>
      </div>
      <div class="row items-center" v-if="servicePackage?.salesTarget?.includes('school')">
        <div class="text-bold sold_left">Organization</div>
        <div class="col bg-teal-1 q-pa-md rounded-borders-md row items-center q-mt-md">
          <div class="col">
            <q-input
              v-model="item.schoolPrice"
              :disable="readonly"
              class="q-mr-lg"
              outlined
              stack-label
              label="Rate/Session"
              type="number"
              style="width: 150px"
              @update:model-value="onValidateUpdate">
              <template v-slot:prepend>
                <q-icon name="attach_money" />
              </template>
            </q-input>
          </div>

          <div class="relative-position text-left top_tips_box row items-center sold_right_content">
            <div class="bg-white top_tips" style="width: 85px">No of service</div>
            <div>{{ item?.times }}</div>
          </div>
          <div class="relative-position text-left top_tips_box row items-center sold_right_content">
            <div class="bg-white top_tips" style="width: 62px">Discount</div>
            <div>
              {{ servicePackage?.discountConfig?.enable && servicePackage?.discountConfig?.discount ? servicePackage?.discountConfig?.discount : 0 }}%
              <div class="text-pink-14" v-if="servicePackage?.discountConfig?.enable && servicePackage?.discountConfig?.end">
                Ends
                {{ date.formatDate(servicePackage?.discountConfig?.end, 'YYYY-MM-DD') }}
              </div>
            </div>
          </div>

          <div class="q-ml-md">
            <div class="text-grey-6 text-body2 text-strike">${{ (schoolPrice(item)?.price / 100).toFixed(2) }}</div>
            <div class="text-bold text-primary">${{ (schoolPrice(item)?.discountPrice / 100).toFixed(2) }}</div>
          </div>
        </div>
      </div>
      <div class="row items-center" v-if="servicePackage?.salesTarget?.includes('personal') && servicePackage?.salesTarget?.length === 1">
        <div class="text-bold sold_left">Individual</div>
        <div class="col bg-teal-1 q-pa-md rounded-borders-md row items-center q-mt-md">
          <div class="col">
            <q-input
              :disable="readonly"
              v-model="item.price"
              class="q-mr-lg"
              outlined
              stack-label
              label="Rate/Session"
              type="number"
              style="width: 150px"
              @update:model-value="onValidateUpdate">
              <template v-slot:prepend>
                <q-icon name="attach_money" />
              </template>
            </q-input>
          </div>

          <div class="relative-position text-left top_tips_box row items-center sold_right_content">
            <div class="bg-white top_tips" style="width: 85px">No of service</div>
            <div>{{ item?.times }}</div>
          </div>
          <div class="relative-position text-left top_tips_box row items-center sold_right_content">
            <div class="bg-white top_tips" style="width: 62px">Discount</div>
            <div>
              {{ servicePackage?.discountConfig?.enable && servicePackage?.discountConfig?.discount ? servicePackage?.discountConfig?.discount : 0 }}%
              <div class="text-pink-14" v-if="servicePackage?.discountConfig?.enable && servicePackage?.discountConfig?.end">
                Ends
                {{ date.formatDate(servicePackage?.discountConfig?.end, 'YYYY-MM-DD') }}
              </div>
            </div>
          </div>

          <div class="q-ml-md">
            <div class="text-grey-6 text-body2 text-strike">${{ (personalPrice(item)?.price / 100).toFixed(2) }}</div>
            <div class="text-bold text-primary">${{ (personalPrice(item)?.discountPrice / 100).toFixed(2) }}</div>
          </div>
        </div>
      </div>
      <div class="row items-center" v-if="servicePackage?.salesTarget?.includes('personal') && servicePackage?.salesTarget?.includes('school')">
        <div class="text-bold sold_left">Individual</div>
        <div class="col bg-teal-1 q-pa-md rounded-borders-md row items-center q-mt-md">
          <div class="col">
            <div class="row relative-position">
              <div class="relative-position text-left top_tips_box row items-center" style="width: 80px">
                <div class="bg-white top_tips" style="width: 85px">Rate/Session</div>
                <div class="text-bold">$ {{ item.percentPrice ? item?.price :item?.schoolPrice }}</div>
              </div>
              <div class="col q-ml-md">
                <span class="text-primary" style="font-size: 12px"> Percentage of organization rate</span>
                <q-input
                  style="width: 180px"
                  v-model="item.percentPrice"
                  class="q-mr-lg"
                  :disable="!item?.schoolPrice || readonly"
                  outlined
                  stack-label
                  type="number"
                  :rules="[val => val > 100 || 'The value must exceed 100']"
                  @update:model-value="percentUp(item)">
                  <template v-slot:after>
                    <q-icon name="percent" />
                  </template>
                </q-input>
              </div>
            </div>
          </div>

          <div class="relative-position text-left top_tips_box row items-center" style="width: 120px">
            <div class="bg-white top_tips" style="width: 85px">No of service</div>
            <div>{{ item?.times }}</div>
          </div>
          <div class="relative-position text-left top_tips_box row items-center" style="width: 120px">
            <div class="bg-white top_tips" style="width: 62px">Discount</div>
            <div>
              {{ servicePackage?.discountConfig?.enable && servicePackage?.discountConfig?.discount ? servicePackage?.discountConfig?.discount : 0 }}%
              <div class="text-pink-14" v-if="servicePackage?.discountConfig?.enable && servicePackage?.discountConfig?.end">
                Ends
                {{ date.formatDate(servicePackage?.discountConfig?.end, 'YYYY-MM-DD') }}
              </div>
            </div>
          </div>

          <div class="q-ml-md">
            <div class="text-grey-6 text-body2 text-strike">${{ (personalPercentPrice(item)?.price / 100).toFixed(2) }}</div>
            <div class="text-bold text-primary">${{ (personalPercentPrice(item)?.discountPrice / 100).toFixed(2) }}</div>
          </div>
        </div>
      </div>
      <div class="bg-teal-2 q-pa-md rounded-borders-md row items-center q-mt-md" v-if="item?.servicePack">
        <div class="q-mr-md" style="width: 96px; height: 50px">
          <q-img
            :ratio="16 / 7"
            class="fit rounded-borders-md"
            fit="cover"
            spinner-color="white"
            :src="hashToUrl(item?.servicePack?.cover || item?.servicePack?.image || item?.servicePack?.unitSnapshot?.cover) || '/v2/img/no-img.png'" />
        </div>
        <div class="col">
          <div class="text-bold">
            <div class="ellipsis">
              {{ item?.servicePack?.name || item?.servicePack?.desc }}
            </div>
            <div class="text-pink-10">
              Qualification:
              {{ servicePackage.qualificationList.find((e) => e.value == item?.servicePack?.qualification)?.label }}
            </div>
          </div>
        </div>
        <div class="relative-position text-left top_tips_box row items-center" style="width: 220px">
          <div class="bg-white top_tips" style="width: 85px">No of service</div>
          <div class="">
            {{ servicePrice(item)?.totalCount }}
            <div class="text-yellow-9" style="font-size: 12px" v-if="servicePrice(item).gifts">
              <q-icon name="redeem" size="xs"></q-icon>
              {{ ` Additional ${servicePrice(item).gifts} free sessions as gifts` }}
            </div>
          </div>
        </div>
        <div class="relative-position text-left top_tips_box row items-center sold_right_content">
          <div class="bg-white top_tips" style="width: 62px">Discount</div>
          <div>
            {{ servicePrice(item).discount }}%
            <div class="text-pink-14" v-if="servicePrice(item).hasDiscount && item?.servicePack?.discountConfig?.end">
              Ends
              {{ date.formatDate(item?.servicePack?.discountConfig?.end, 'YYYY-MM-DD') }}
            </div>
          </div>
        </div>
        <div class="q-ml-md">
          <div class="text-grey-6 text-body2 text-strike">${{ (servicePrice(item)?.oriPrice / 100).toFixed(2) }}</div>
          <div class="text-bold text-primary">${{ (servicePrice(item)?.price / 100).toFixed(2) }}</div>
        </div>
      </div>
      <div class="q-mt-md row items-center justify-end">
        <q-card class="q-ml-md" v-if="servicePackage?.salesTarget?.includes('school')">
          <q-card-section class="row items-center">
            <div class="text-right text-bold">Sub total for organisation</div>
            <div class="q-ml-md">
              <div class="text-grey-6 text-body2 text-strike">${{ (subTotal(item, 'school')?.price / 100).toFixed(2) }}</div>
              <div class="text-bold text-primary">${{ (subTotal(item, 'school')?.discountPrice / 100).toFixed(2) }}</div>
            </div>
          </q-card-section>
        </q-card>
        <q-card class="q-ml-md">
          <q-card-section class="row items-center" v-if="servicePackage?.salesTarget?.includes('personal')">
            <div class="text-right text-bold">Sub total for individual</div>
            <div class="q-ml-md">
              <div class="text-grey-6 text-body2 text-strike">${{ (subTotal(item, 'personal')?.price / 100).toFixed(2) }}</div>
              <div class="text-bold text-primary">${{ (subTotal(item, 'personal')?.discountPrice / 100).toFixed(2) }}</div>
            </div>
          </q-card-section>
        </q-card>
      </div>
    </q-card-section>
  </q-card>
</template>
<script setup>
/*
  imports
*/
import {computed, onMounted, ref, watch} from 'vue'
import {servicePackageStore} from 'stores/service-package'
import {calPremiumMentor} from 'src/pages/order/consts'
import HourlyRate from './HourlyRate.vue'
import {date} from 'quasar'

const props = defineProps({
  error: Boolean,
  topic: Object,
  // readonly: Boolean,
})

const readonly = ref(false)
const emit = defineEmits(['update'])
const servicePackage = servicePackageStore()

const targetSoldOptions = [
  {
    label: 'Organization',
    value: 'school',
  },
  {
    label: 'Individual user',
    value: 'personal',
  },
]

const soldOptions = computed(() => {
  let result = [...targetSoldOptions]
  if (servicePackage.salesTarget.length === 1) {
    result = [
      {
        label: 'Organization',
        value: 'school',
        disable: servicePackage.salesTarget?.includes('school'),
      },
      {
        label: 'Individual user',
        value: 'personal',
        disable: servicePackage.salesTarget?.includes('personal'),
      },
    ]
  }

  return result
})

const servicePrice = (item) => {
  return calPremiumMentor({
    count: +item?.times,
    servicePack: item?.servicePack,
  })
}

const subTotal = (item, type) => {
  let inPrice = type === 'school' ? item?.schoolPrice : item?.price
  let price = 0
  let discountPrice = 0
  price += calPrice(inPrice, item.times)?.price
  discountPrice += calPrice(inPrice, item.times)?.discountPrice

  if (item?.servicePack) {
    let serPrice = calPremiumMentor({
      count: +item?.times,
      servicePack: item?.servicePack,
    })
    price += serPrice?.oriPrice
    discountPrice += serPrice?.price
  }

  return {
    price,
    discountPrice,
  }
}

const schoolPrice = (item) => {
  return calPrice(item?.schoolPrice, item.times)
}

const personalPrice = (item) => {
  return calPrice(item?.price, item.times)
}

const personalPercentPrice = (item) => {
  const price = (item?.schoolPrice * item.percentPrice) / 100
  return calPrice(price, item.times)
}

const calPrice = (itemPrice, times) => {
  let price = itemPrice * 100 * times
  let discountPrice =
    servicePackage?.discountConfig?.enable && servicePackage?.discountConfig?.discount
      ? itemPrice * 100 * ((100 - servicePackage?.discountConfig?.discount) / 100) * times
      : price

  return {
    price: isNaN(price) ? 0 : price,
    discountPrice: isNaN(discountPrice) ? 0 : discountPrice,
  }
}

const percentUp = (o) => {
  if(+o.percentPrice <= 100){
    o.percentPrice = undefined
  }
  servicePackage.contentOrientated = [...servicePackage.contentOrientated].map((item) => {
    let price = item?.price
    if (item?._id === o?._id) {
      price = (o?.schoolPrice * o?.percentPrice) / 100
    }
    return {
      ...item,
      price,
    }
  })
  onValidateUpdate()
}


const salesChange = () => {
  if(servicePackage.salesTarget?.length === 2){
    servicePackage.contentOrientated?.map(item => {
      item.price = undefined;
      item.percentPrice = undefined
    })
  }
  onValidateUpdate()
}

const onValidateUpdate = () => {
  let isError = false
  if (servicePackage.salesTarget?.length === 0 || servicePackage?.contentOrientated?.length === 0) {
    isError = true
  } else {
    servicePackage?.contentOrientated?.forEach((item) => {
      let {price, schoolPrice, percentPrice} = item
      if (servicePackage?.salesTarget?.includes('personal') && !(price >= 0)) {
        isError = true
      }
      if (servicePackage?.salesTarget?.includes('school') && !(schoolPrice >= 0)) {
        isError = true
      }
      if (servicePackage.salesTarget?.length === 2) {
        if (percentPrice < 100) {
          isError = true
        }
      }
    })
  }

  console.log('premiumErrorValidate', isError)
  emit('update', isError)
}

watch(
  () => servicePackage.contentOrientated,
  (val) => {
    onValidateUpdate()
  },
  {deep: true}
)

onMounted(async () => {
  servicePackage.salesTarget = ['school', 'personal']
  onValidateUpdate()
})
</script>

<style lang="scss" scoped>
.top_tips_box {
  height: 56px;

  .top_tips {
    position: absolute;
    top: -16px;
    left: 0;
    height: 20px;
  }
}

.sold_left {
  width: 90px;
}

.sold_right_content {
  width: 120px;
}
</style>
