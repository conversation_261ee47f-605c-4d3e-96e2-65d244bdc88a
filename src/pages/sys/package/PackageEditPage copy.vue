<template>
  <q-layout view="hHh LpR fFf">
    <q-page-container class="pc-sm">
      <q-page class="q-ma-md">
        <q-card class="q-my-md rounded-borders-md">
          <q-card-section>
            <div class="text-h6">Basic setting for this package</div>
            <div class="text-weight-medium">
              <div>Service role: {{ servicePackage.serviceRolesList.find((e) => e.value == servicePackage.serviceRoles)?.label }}</div>
              <div>Service type: {{ servicePackage.mentoringTypeList.find((e) => e.value == servicePackage.mentoringType)?.label }}</div>
              <div>Service provider qualification: {{ servicePackage.qualificationList.find((e) => e.value == servicePackage.qualification)?.label }}</div>
              <div v-if="servicePackage.serviceRoles === 'mentoring'">
                Premium content orientated: {{ servicePackage.contentOrientatedEnable ? 'Yes' : 'No' }}
              </div>
              <div v-if="servicePackage.serviceRoles === 'consultant'">
                Consultant type: {{ servicePackage.consultantTypeList.find((e) => e.value == servicePackage.consultant?.type)?.label }}
              </div>
            </div>
          </q-card-section>
        </q-card>
        <q-card class="q-my-md rounded-borders-md">
          <q-card-section>
            <div class="text-h6">Package detail</div>
            <div id="error-cover">
              <div class="text-subtitle1 text-weight-medium" :class="{'text-negative': errorPositions.cover && isPublishAction}">* Upload image</div>
              <br />
              <div class="row">
                <div class="q-mr-sm">
                  <q-card
                    class="rounded-borders-md bg-teal-1"
                    style="width: 300px; max-width: 300px; height: 200px; max-height: 200px"
                    @click="uploadCoverImage()">
                    <div v-if="!servicePackage.cover" class="absolute-center text-center text-teal">
                      <q-icon flat name="upload" size="2rem"></q-icon>
                      <q-item-label>Add cover photo</q-item-label>
                    </div>
                    <q-img
                      v-else
                      fit="cover"
                      :src="hashToUrl(servicePackage.cover)"
                      style="width: 300px; max-width: 300px; height: 200px; max-height: 200px"></q-img>
                  </q-card>
                </div>
                <div class="column reverse" v-if="servicePackage.cover">
                  <div class="self-end q-ml-sm">{{ servicePackage.coverName }}</div>
                  <div v-if="editable">
                    <q-btn flat round color="teal" icon="change_circle" @click="uploadCoverImage()"></q-btn>
                    <q-btn flat round color="red" icon="delete" @click="deleteCover()"></q-btn>
                  </div>
                </div>
              </div>
            </div>
            <div id="error-name">
              <br />
              <div class="text-subtitle1 text-weight-medium" :class="{'text-negative': errorPositions.name && isPublishAction}">* Package description</div>
              <br />
              <q-input
                class="q-mx-md"
                :disable="!editable"
                outlined
                @change="checkDataFinished(false)"
                v-model.trim="servicePackage.name"
                label="Package name"
                stack-label></q-input>
            </div>
            <div id="error-points">
              <br />
              <q-item-label class="text-subtitle1 text-weight-medium" :class="{'text-negative': errorPositions.points && isPublishAction}"
                >* Selling points</q-item-label
              >
              <q-item v-for="(point, i) in servicePackage.points" :key="i">
                <q-item-section>
                  <q-input
                    class="q-mt-sm"
                    @change="checkDataFinished(false)"
                    v-model="servicePackage.points[i]"
                    outlined
                    placeholder="E.g. Essay tutoring 10 times"></q-input>
                </q-item-section>
                <q-item-section avatar v-if="servicePackage.points.length > 1">
                  <q-btn flat icon="close" @click="deleteSellingPointClick(i)"></q-btn>
                </q-item-section>
              </q-item>
              <q-btn
                v-if="sellingPointAddShow"
                class="q-mt-md text-teal"
                flat
                outlint
                no-caps
                icon="add"
                label="Add selling point"
                @click="addSellingPoint()"></q-btn>
            </div>
            <div id="error-duration" v-if="['mentoring', 'consultant'].includes(servicePackage.serviceRoles)">
              <br />
              <q-separator />
              <br />
              <q-item-label class="text-subtitle1 text-weight-medium" :class="{'text-negative': errorPositions.duration && isPublishAction}">
                * Service block setting
              </q-item-label>
              <br />
              <q-btn
                rounded
                no-caps
                color="primary"
                class="q-mb-md"
                :label="servicePackage.duration && servicePackage.break ? 'View the service block' : 'Set the service block'"
                @click="openBlockClick()"></q-btn>
            </div>
          </q-card-section>
        </q-card>
        <q-card class="q-my-md rounded-borders-md">
          <q-card-section>
            <div class="text-h6">* Package tag setting</div>
            <div v-if="servicePackage.mentoringType" id="error-curriculum">
              <br />
              <q-item-label class="text-subtitle1 text-weight-medium">* Curriculum</q-item-label>
              <br />
              <q-select
                class="q-pa-xs"
                v-if="servicePackage.mentoringType == 'academic'"
                v-model="servicePackage.curriculum"
                :options="filteredCurriculumList"
                @update:modelValue="onCurriculumUpdate"
                outlined
                :disable="!editable || tagSettingDisable || readonly"
                label="Search and select curriculum"
                icon="search"
                use-input
                @filter="filterCurriculumList"></q-select>
              <q-chip v-else color="grey-5" disabled square no-caps label="Service"></q-chip>
            </div>
            <div v-if="servicePackage.countryCodeRequired" id="error-countryCode">
              <br />
              <q-item-label class="text-subtitle1 text-weight-medium" :class="{'text-negative': errorPositions.countryCode && isPublishAction}"
                >* Country</q-item-label
              >
              <br />
              <q-chip color="grey-5" disabled square no-caps :label="OverseasStudyList.find((e) => e.value == servicePackage.countryCode?.[0])?.label"></q-chip>
            </div>
            <div
              id="error-subject"
              v-if="servicePackage.type && servicePackage.mentoringType && servicePackage.curriculum.value && servicePackage.mentoringType !== 'overseasStudy'">
              <br />
              <q-item-label class="text-subtitle1 text-weight-medium" :class="{'text-negative': errorPositions.subject && isPublishAction}"
                >* {{ subjectBlockTitle }} ({{ isMultipleSubjectChoice ? 'Multiple choice' : 'Single choice' }})</q-item-label
              >
              <br />
              <div v-if="servicePackage.topicWithChilds">
                <!-- {{ currentSubjectList }} -->
                  2211{{essayOrTeacherTrainingSubjectLists}}
                <q-select
                  class="q-pa-xs"
                  v-model="essayOrTeacherTrainingSubjectLists"
                  :options="currentSubjectList"
                  outlined
                  label="Search and select field"
                  icon="search"
                  :disable="!editable || tagSettingDisable || readonly"
                  use-input
                  @filter="filterCurriculumList"></q-select>
                <q-chip
                  v-for="(subject, i) in essayOrTeacherTrainingSubjectLists?.value"
                  :key="i"
                  color="primary"
                  text-color="white"
                  :outline="subject.selected !== true"
                  square
                  clickable
                  :disable="!editable || tagSettingDisable || readonly"
                  :label="subject.name"
                  @click="subjectClick(subject, i)"></q-chip>
              </div>
              <div v-else>
                <span v-for="(subject, i) in currentSubjectList" :key="i" style="display: inline-block">
                  <span>
                    <q-chip
                      color="primary"
                      text-color="white"
                      :outline="subject.selected !== true"
                      square
                      clickable
                      :disable="!editable || tagSettingDisable || readonly"
                      :label="subject.name"
                      @click="subjectClick(subject, i)">
                    </q-chip>
                  </span>
                  <br />
                  <span class="q-mr-md text-caption text-grey-5" style="float: right">{{ subject.subtitle }}</span>
                </span>
              </div>
            </div>
            <div v-if="servicePackage.type && servicePackage.mentoringType && servicePackage.curriculum" id="error-grades">
              <br />
              <q-item-label class="text-subtitle1 text-weight-medium" :class="{'text-negative': errorPositions.grades && isPublishAction}"
                >* Grade {{ `(${isMultipleGradeChoice ? 'Multiple choice' : 'Single choice'})` }}</q-item-label
              >
              <br />
              <div v-if="servicePackage.isEducatorServiceType">
                <q-chip
                  v-for="(label, i) in gradeList"
                  :key="i"
                  :disable="!editable || tagSettingDisable || readonly"
                  :outline="!servicePackage.gradeGroup.grades?.includes(label)"
                  :label="label"
                  color="primary"
                  text-color="white"
                  no-caps
                  square
                  clickable
                  @click="gradeClick(label, i)"></q-chip>
              </div>
              <div class="col row" v-else>
                <div class="q-my-sm">
                  <div v-for="(gradesGroup, i) in gradeList" :key="i">
                    <div class="q-py-xs" v-if="gradesGroup.grades[servicePackage.curriculum.value]?.length || servicePackage.mentoringType !== 'academic'">
                      <q-chip
                        color="primary"
                        text-color="white"
                        square
                        clickable
                        :outline="!servicePackage.gradeGroup.grades?.includes(i)"
                        :disable="!editable || tagSettingDisable || readonly"
                        :label="gradesGroup.label"
                        @click="gradeClick(gradesGroup, i)"></q-chip>
                      <template v-if="servicePackage.mentoringType === 'academic'">
                        <span v-for="(grade, i) in gradesGroup.grades[servicePackage.curriculum.value]" :key="i" class="text-grey-5 q-ma-sm">{{ grade }}</span>
                      </template>
                      <template v-else>
                        <span v-for="(grade, i) in gradesGroup.grades.classcipe" :key="i" class="text-grey-5 q-ma-sm">{{ grade }}</span>
                      </template>
                    </div>
                  </div>
                </div>
              </div>
              <!-- <q-btn v-else class="q-ma-sm bg-teal-4 text-white" disabled outline no-caps label="Sample"></q-btn> -->
            </div>
          </q-card-section>
        </q-card>
        <template v-if="servicePackage.contentOrientatedEnable">
          <q-card class="q-my-md rounded-borders-md" id="error-content">
            <q-card-section>
              <div class="text-h6" :class="{'text-negative': errorPositions.content && isPublishAction}">* Premium content setting</div>
              <div class="rounded-borders q-mt-md" v-for="(subject, i) in selectedSubjectsOrCountry" :key="i">
                <template v-if="isMultipleContents || (!isMultipleContents && i == 0)">
                  <div class="row items-center q-pa-md justify-between">
                    <div class="col row" :class="`${isPublishAction && addContentEnable(subject) ? 'text-negative' : 'text-primary'}`">
                      <template v-if="servicePackage.topicWithChilds">
                        <q-chip color="teal-1" text-color="primary" :ripple="false" :label="essayOrTeacherTrainingSubjectLists.label"></q-chip>
                        <q-chip color="primary" outline square :ripple="false" :label="subject.name"></q-chip>
                      </template>
                      <template v-else-if="isMultipleContents">
                        <div class="q-pr-md">{{ subject.name }}</div>
                      </template>
                      <template v-else-if="servicePackage.mentoringType == 'overseasStudy'">
                        <div class="q-pr-md">{{ subject.label }}</div>
                      </template>
                      <template v-else>
                        <div v-for="(e, i) in selectedSubjectsOrCountry" :key="i" class="q-pr-md">{{ e.name }}</div>
                      </template>
                    </div>
                    <q-btn
                      v-if="addContentEnable(subject)"
                      rounded
                      no-caps
                      color="primary"
                      :disabled="addPremiumContentDisable"
                      label="Add content"
                      icon="o_add"
                      @click="onUploadClick(subject)"></q-btn>
                  </div>
                  <q-expansion-item
                    default-opened
                    v-for="(item, index) in servicePackage.contentOrientated.filter(
                      (e) => (isMultipleContents && e.subject == subject._id) || !isMultipleContents
                    )"
                    :key="index">
                    <template v-slot:header>
                      <q-item-section @click.stop="onPremiumContentClick(item)">
                        <PackageCard
                          v-if="item.premium?._id"
                          :pack="item.premium"
                          :header="`${item.premium.status === 0 ? 'This content has been withdrawn by the publisher.' : ''}`"
                          header-class="bg-amber-2 text-black"
                          tag="Premium lecture"
                          is-educator
                          no-price
                          non-clickable
                          remove
                          @remove="onDeleteClick(item, true)"></PackageCard>
                      </q-item-section>
                      <q-item-section side> </q-item-section>
                    </template>

                    <div class="bg-teal-1 rounded-borders q-pa-md">
                      <div class="row q-pb-md items-center">
                        <div class="col-8">
                          <div>No. of sessions required to complete this course</div>
                        </div>
                        <div class="col-2 text-body1 text-weight-medium">{{ item.times }}</div>
                        <div class="col-2 hidden">
                          <q-btn dense rounded flat color="primary" icon="o_edit" @click.stop="onTimesClick(item)"></q-btn>
                        </div>
                      </div>
                      <q-btn
                        v-if="!item.servicePack"
                        rounded
                        color="primary"
                        label="Add bundled mentor service package"
                        no-caps
                        @click.stop="onAddClick(item)"></q-btn>
                      <div v-if="item.servicePack" class="row">
                        <div class="col">
                          <PackageCard
                            v-if="item.servicePack?._id"
                            :is-educator="false"
                            :service-no="parseInt(item.times)"
                            remove
                            header="Bundled mentor service package"
                            @remove="onDeleteClick(item, false)"
                            :pack="item.servicePack"
                            orientated
                            non-clickable
                            category="featured" />
                        </div>
                      </div>
                    </div>
                  </q-expansion-item>
                </template>
              </div>
            </q-card-section>
          </q-card>
        </template>
        <q-card class="q-my-md rounded-borders-md" id="error-price">
          <q-card-section>
            <template v-if="servicePackage.contentOrientatedEnable">
              <PremiumContent
                :readonly="readonly"
                :error="errorPositions.price && isPublishAction"
                @update="onPremiumContentUpdate"
                :topic="essayOrTeacherTrainingSubjectLists" />
            </template>
            <template v-else-if="isSubstitute">
              <template v-if="servicePackage.isOnCampus">
                <OnCampusPrice
                  :readonly="readonly"
                  @update="substituteUpdate"
                  :currentSubjectList="currentSubjectList"
                  :error="errorPositions.price && isPublishAction" />
              </template>
              <template v-else>
                <OnlineSubs :readonly="readonly" @update="substituteUpdate" :error="errorPositions.price && isPublishAction" />
              </template>
            </template>
            <template v-else>
              <q-item-label class="text-subtitle1 text-weight-medium" :class="{'text-negative': errorPositions.price && isPublishAction}"
                >* Price setting</q-item-label
              >

              <div class="row items-center q-mt-md">
                <div class="text-bold q-mr-md">Sold to</div>
                <div class="col">
                  <q-option-group
                    disable
                    color="primary"
                    :options="[
                      {
                        label: 'Individual user',
                        value: 'personal',
                      },
                    ]"
                    type="checkbox"
                    inline
                    v-model="servicePackage.salesTarget" />
                </div>
              </div>
              <HourlyRate
                v-if="servicePackage.serviceRoles === 'mentoring' || servicePackage.serviceRoles === 'consultant'"
                :topic="essayOrTeacherTrainingSubjectLists"
                :duration="servicePackage.duration"
                :curriculum="servicePackage.curriculum"
                :type="servicePackage.serviceRoles"
                :mentoringType="servicePackage.mentoringType"
                :qualification="servicePackage.qualification" />
              <div class="q-ma-md">
                <q-input v-model="displpayPrice" outlined stack-label label="Rate/Session" @update:model-value="inputPrice">
                  <template v-slot:prepend>
                    <q-icon name="attach_money" />
                  </template>
                </q-input>
              </div>
              <div v-if="displpayPrice > 0">
                <q-card class="q-my-md bg-teal-1" v-for="(discount, i) in servicePackage.discount" :key="i">
                  <q-card-section>
                    <q-item>
                      <q-item-section>
                        <q-input v-model="discount.count" outlined stack-label label="No. of session"></q-input>
                      </q-item-section>
                      <q-item-section avatar>
                        <q-icon size="3rem" color="teal-3" name="close"></q-icon>
                      </q-item-section>
                      <q-item-section class="q-ml-md">
                        <q-input v-model="discount.discount" :disable="!servicePackage.discountConfig.enable" outlined stack-label label="Discount">
                          <template v-slot:append>
                            <q-icon name="percent" />
                            OFF
                          </template>
                        </q-input>
                      </q-item-section>
                      <q-item-section side>
                        <q-item-label>{{ servicePackage.discountConfig.enable ? 'Discounted total price' : 'Total price' }}</q-item-label>
                        <q-item-label class="text-h5 text-teal">
                          ${{ ((displpayPrice * discount.count * (100 - (servicePackage.discountConfig.enable ? discount.discount : 0))) / 100).toFixed(3) }}
                        </q-item-label>
                      </q-item-section>
                      <q-item-section avatar v-if="servicePackage.discount.length > 1">
                        <q-btn flat icon="close" @click="deleteDiscountClick(i)"></q-btn>
                      </q-item-section>
                    </q-item>
                    <div v-if="discount.gifts || discount.enableGift">
                      <q-item class="row">
                        <q-item-section class="col-3">
                          <q-input v-model="discount.gifts" outlined stack-label label="No of gifts"></q-input>
                        </q-item-section>
                        <q-item-section class="col-2 on-left">
                          <q-btn class="q-mx-md text-red" icon="close" flat no-caps label="Cancel gifts" @click="deleteGift(discount)"> </q-btn>
                        </q-item-section>
                      </q-item>
                    </div>
                    <div>
                      <q-btn class="q-mx-md text-teal" flat no-caps icon="add" label="Add gifts" @click="enableGift(discount)"> </q-btn>
                    </div>
                  </q-card-section>
                </q-card>
              </div>
              <q-btn
                v-if="displpayPrice > 0"
                class="q-mt-sm text-teal"
                flat
                outlint
                no-caps
                icon="add"
                label="Add bundle sales choice"
                @click="addDiscountClick()"></q-btn>
            </template>

            <q-item class="row" v-if="!isSubstitute">
              <q-item-section side>
                <q-item-label class="q-pb-md">Active discount</q-item-label>
              </q-item-section>
              <q-item-section side class="q-pb-md">
                <q-toggle v-model="servicePackage.discountConfig.enable"></q-toggle>
              </q-item-section>
              <q-item-section class="col-3" v-if="servicePackage.contentOrientatedEnable">
                <q-input
                  v-model="servicePackage.discountConfig.discount"
                  :disable="!servicePackage.discountConfig.enable"
                  outlined
                  stack-label
                  type="number"
                  label="Discount"
                  hint="">
                  <template v-slot:append>
                    <q-icon name="percent" />
                  </template>
                </q-input>
              </q-item-section>
              <q-item-section :class="$q.screen.lt.sm ? 'col-6' : 'col-3'">
                <q-input
                  v-model="servicePackage.discountConfig.end"
                  outlined
                  stack-label
                  :disable="!servicePackage.discountConfig.enable"
                  type="date"
                  label="Discount ends"
                  hint="YYYY/MM/DD"></q-input>
              </q-item-section>
            </q-item>

            <div class="row items-center q-mt-md" v-if="servicePackage.contentOrientatedEnable && servicePackage.contentOrientated.length > 1">
              <div class="text-bold">Allow premium content selection at the time of purchase</div>
              <q-toggle v-model="servicePackage.splitSale" size="xl" color="primary" :disable="!servicePackage?.salesTarget?.includes('school')" />
            </div>

            <div id="error-freq" v-if="!isSubstitute">
              <br />
              <q-item-label class="text-subtitle1 text-weight-medium" :class="{'text-negative': errorPositions.freq && isPublishAction}"
                >* Minimal frequency of the service</q-item-label
              >
              <br />
              <q-btn
                class="q-ma-sm"
                :class="servicePackage.freq === 7 ? 'bg-teal-4 text-white' : ''"
                :disable="!editable || readonly"
                rounded
                outline
                no-caps
                label="Weekly"
                @click="frequencyClick(7)"></q-btn>
              <q-btn
                class="q-ma-sm"
                :class="servicePackage.freq === 14 ? 'bg-teal-4 text-white' : ''"
                :disable="!editable || readonly"
                rounded
                outline
                no-caps
                label="Fortnightly"
                @click="frequencyClick(14)"></q-btn>
              <q-btn
                class="q-ma-sm"
                :class="servicePackage.freq === 30 ? 'bg-teal-4 text-white' : ''"
                :disable="!editable || readonly"
                rounded
                outline
                no-caps
                label="Monthly"
                @click="frequencyClick(30)"></q-btn>
              <!--
              <q-btn
                class="q-ma-sm"
                :disable="!editable || readonly"
                :class="servicePackage.freq === 120 ? 'bg-teal-4 text-white' : ''"
                rounded
                outline
                no-caps
                label="Quarterly"
                @click="frequencyClick(120)"></q-btn>
              -->
            </div>
          </q-card-section>
        </q-card>
        <q-card id="error-carerPack" class="q-my-md rounded-borders-md" v-if="addCarerPackageEnable">
          <q-card-section>
            <div class="row items-center">
              <div class="text-h6" :class="{'text-negative': errorPositions.carerPack && servicePackage.bundledCarer && isPublishAction}">
                <span v-if="servicePackage.bundledCarer">*</span>
                Bundled carer service package
              </div>
              <q-toggle v-model="servicePackage.bundledCarer" @update:modelValue="onBundledCarerUpdate" size="lg" color="primary" />
            </div>
            <q-btn
              v-if="servicePackage.bundledCarer && !servicePackage.carerPack?._id"
              rounded
              color="primary"
              label="Add package"
              icon="add"
              no-caps
              @click.stop="onAddPackageClick('carer')"></q-btn>
          </q-card-section>
          <template v-if="servicePackage.carerPack?._id && servicePackage.bundledCarer">
            <q-card-section class="row">
              <div class="col">
                <PackageCard
                  :is-educator="false"
                  remove
                  @remove="onRemoveClick('carer')"
                  :pack="servicePackage.carerPack"
                  :serviceNo="servicePackage.totalTimes"
                  orientated
                  non-clickable
                  category="featured" />
              </div>
            </q-card-section>
          </template>
        </q-card>
        <q-card id="error-interviewPack" class="q-my-md rounded-borders-md" v-if="interviewOrAcademicCheckEnable">
          <q-card-section>
            <div class="row items-center">
              <div class="text-h6" :class="{'text-negative': errorPositions.interviewPack && servicePackage.bundledInterview && isPublishAction}">
                <span v-if="servicePackage.bundledInterview">*</span>
                Bundled interview for enrolment
              </div>
              <q-toggle v-model="servicePackage.bundledInterview" @update:modelValue="onBundledInterviewUpdate" size="lg" color="primary" />
            </div>
            <q-btn
              v-if="servicePackage.bundledInterview && !servicePackage.interviewPack?._id"
              rounded
              color="primary"
              label="Add package"
              icon="add"
              no-caps
              @click.stop="onAddPackageClick('interview')"></q-btn>
          </q-card-section>
          <q-card-section v-if="servicePackage.interviewPack?._id && servicePackage.bundledInterview" class="row">
            <div class="col">
              <PackageCard
                :is-educator="false"
                remove
                @remove="onRemoveClick('interview')"
                :pack="servicePackage.interviewPack"
                orientated
                non-clickable
                category="featured" />
            </div>
          </q-card-section>
        </q-card>
        <AcBgCheck v-if="interviewOrAcademicCheckEnable" />
        <q-card class="q-my-md rounded-borders-md">
          <q-card-section id="error-attachments">
            <div class="text-h6" :class="{'text-negative': errorPositions.attachments && isPublishAction}">* Promotional material</div>
            <div>
              The materials inserted here will be displayed during the workshop when this service packages is added as an item to be promoted to the
              participants.
            </div>
            <div>
              <div>
                <q-toolbar>
                  <div class="text-subtitle2 text-weight-medium">Academic value</div>
                  <q-space />
                  <q-btn v-if="!academicValueVideo" flat round icon="upload" class="text-grey" @click="uploadMaterial('AcademicValue')"></q-btn>
                </q-toolbar>
              </div>
              <div class="row">
                <div class="text-center" style="width: 300px" @click="showMaterialDetail(academicValueVideo)">
                  <div v-if="academicValueVideo?.mime.includes('image')">
                    <q-img :src="hashToUrl(academicValueVideo?.hash)" fit="fill"></q-img>
                  </div>
                  <div v-else-if="academicValueVideo?.mime.includes('video')">
                    <video class="full-width vertical-middle" id="record-audio" muted preload="metadata" :src="hashToUrl(academicValueVideo?.hash)"></video>
                  </div>
                  <div v-else-if="academicValueVideo?.mime.includes('audio')">
                    <q-icon class="bg-teal" color="white" name="mic" size="5rem" />
                  </div>
                  <div v-else-if="academicValueVideo?.mime.includes('pdf')">
                    <q-icon color="teal" name="picture_as_pdf" size="5em" />
                  </div>
                </div>
                <div v-if="academicValueVideo && editable" class="q-mx-md column reverse">
                  <q-icon color="red cursor-pointer" size="1.5rem" name="delete" @click="deleteMaterial(academicValueVideo)"></q-icon>
                </div>
              </div>
            </div>

            <div v-if="servicePackage.contentOrientatedEnable">
              <div>
                <q-toolbar>
                  <div class="text-subtitle2 text-weight-medium">Features</div>
                  <q-space />
                  <q-btn v-if="!featuresVideo" flat round icon="upload" class="text-grey" @click="uploadMaterial('Features')"></q-btn>
                </q-toolbar>
              </div>
              <div class="row">
                <div class="text-center" style="width: 300px" @click="showMaterialDetail(featuresVideo)">
                  <div v-if="featuresVideo?.mime.includes('image')">
                    <q-img :src="hashToUrl(featuresVideo?.hash)" fit="fill"></q-img>
                  </div>
                  <div v-else-if="featuresVideo?.mime.includes('video')">
                    <video class="full-width vertical-middle" id="record-audio" muted preload="metadata" :src="hashToUrl(featuresVideo?.hash)"></video>
                  </div>
                  <div v-else-if="featuresVideo?.mime.includes('audio')">
                    <q-icon class="bg-teal" color="white" name="mic" size="5rem" />
                  </div>
                  <div v-else-if="featuresVideo?.mime.includes('pdf')">
                    <q-icon color="teal" name="picture_as_pdf" size="5em" />
                  </div>
                </div>
                <div v-if="featuresVideo && editable" class="q-mx-md column reverse">
                  <q-icon color="red cursor-pointer" size="1.5rem" name="delete" @click="deleteMaterial(featuresVideo)"></q-icon>
                </div>
              </div>
            </div>

            <div>
              <div>
                <q-toolbar>
                  <div class="text-subtitle2 text-weight-medium">Q&A</div>
                  <q-space />
                  <q-btn v-if="!QAVideo" flat round icon="upload" class="text-grey" @click="uploadMaterial('QA')"></q-btn>
                </q-toolbar>
              </div>
              <div class="row">
                <div class="text-center" style="width: 300px" @click="showMaterialDetail(QAVideo)">
                  <div v-if="QAVideo?.mime.includes('image')">
                    <q-img :src="hashToUrl(QAVideo?.hash)" fit="fill"></q-img>
                  </div>
                  <div v-else-if="QAVideo?.mime.includes('video')">
                    <video class="full-width vertical-middle" id="record-audio" muted preload="metadata" :src="hashToUrl(QAVideo?.hash)"></video>
                  </div>
                  <div v-else-if="QAVideo?.mime.includes('audio')">
                    <q-icon class="bg-teal" color="white" name="mic" size="5rem" />
                  </div>
                  <div v-else-if="QAVideo?.mime.includes('pdf')">
                    <q-icon color="teal" name="picture_as_pdf" size="5em" />
                  </div>
                </div>
                <div v-if="QAVideo && editable" class="q-mx-md column reverse">
                  <q-icon color="red cursor-pointer" size="1.5rem" name="delete" @click="deleteMaterial(QAVideo)"></q-icon>
                </div>
              </div>
            </div>
          </q-card-section>
        </q-card>

        <q-toolbar id="bottom-publish">
          <q-space />
          <div v-if="isPublishAction && !servicePackage.filled" class="text-negative">
            There are sections needed to be completed before publishing the package
          </div>
          <q-btn
            class="q-mx-sm"
            color="primary"
            icon="check"
            rounded
            no-caps
            :label="isPublishAction ? 'Publish' : servicePackage?._id ? 'Save' : 'Create'"
            :loading="loading"
            @click="createClick()"></q-btn>
          <q-btn class="q-mx-sm text-teal" outline rounded no-caps label="Cancel" @click="cancelClick()"></q-btn>
        </q-toolbar>
        <br />
      </q-page>
      <q-dialog v-model="setBlock">
        <q-card>
          <q-card-section>
            <span class="text-h5"> Set time</span>
            <!-- <q-btn flat icon="arrow_left"></q-btn> -->
          </q-card-section>
          <q-card-section>
            <q-toolbar>
              <q-item-label class="text-h6">Length of each service</q-item-label>
              <q-space />
              <q-item-label v-if="tempDuration || servicePackage.duration" class="text-subtitle2 text-teal">{{
                `${tempDuration ? tempDuration : servicePackage.duration} mins`
              }}</q-item-label>
            </q-toolbar>
          </q-card-section>
          <q-card-section class="col row">
            <q-btn
              class="col-3 q-ma-sm"
              :class="tempDuration == 30 ? 'bg-teal-4 text-white' : ''"
              outline
              no-caps
              label="30 mins"
              @click="durationClick(30)"></q-btn>
            <q-btn
              class="col-3 q-ma-sm"
              :class="tempDuration == 60 ? 'bg-teal-4 text-white' : ''"
              outline
              no-caps
              label="60 mins"
              @click="durationClick(60)"></q-btn>
            <q-btn
              class="col-3 q-ma-sm"
              :class="tempDuration == 90 ? 'bg-teal-4 text-white' : ''"
              outline
              no-caps
              label="90 mins"
              @click="durationClick(90)"></q-btn>
            <q-btn
              class="col-3 q-ma-sm"
              :class="tempDuration == 120 ? 'bg-teal-4 text-white' : ''"
              outline
              no-caps
              label="120 mins"
              @click="durationClick(120)"></q-btn>
            <q-btn class="col-3 q-ma-sm text-teal" flat no-caps label="Customize" @click="customizeDuration()"></q-btn>
          </q-card-section>
          <q-card-section>
            <q-toolbar>
              <q-item-label class="text-h6">Length of break</q-item-label>
              <q-space />
              <q-item-label v-if="tempBreak || servicePackage.break" class="text-subtitle2 text-teal">{{
                `${tempBreak ? tempBreak : servicePackage.break} mins`
              }}</q-item-label>
            </q-toolbar>
          </q-card-section>
          <q-card-section class="col row">
            <q-btn class="col-3 q-ma-sm" :class="tempBreak == 5 ? 'bg-teal-4 text-white' : ''" outline no-caps label="5 mins" @click="breakClick(5)"></q-btn>
            <q-btn class="col-3 q-ma-sm" :class="tempBreak == 10 ? 'bg-teal-4 text-white' : ''" outline no-caps label="10 mins" @click="breakClick(10)"></q-btn>
            <q-btn class="col-3 q-ma-sm" :class="tempBreak == 15 ? 'bg-teal-4 text-white' : ''" outline no-caps label="15 mins" @click="breakClick(15)"></q-btn>
            <q-btn class="col-3 q-ma-sm" :class="tempBreak == 20 ? 'bg-teal-4 text-white' : ''" outline no-caps label="20 mins" @click="breakClick(20)"></q-btn>
            <q-btn class="col-3 q-ma-sm text-teal" flat no-caps label="Customize" @click="customizeBreak()"></q-btn>
          </q-card-section>
          <q-card-section>
            <q-btn class="full-width bg-teal text-white" rounded no-caps label="Confirm" @click="saveDurationBreak()"></q-btn>
          </q-card-section>
        </q-card>
      </q-dialog>
      <q-dialog v-model="viewBlock" class="full-height">
        <div class="q-pa-md full-width bg-white">
          <q-toolbar class="full-width">
            <q-toolbar-title>Service blocks</q-toolbar-title>
          </q-toolbar>
          <div class="full-width" style="height: 500px">
            <BlockList preview :block="block" />
          </div>
          <q-separator inset></q-separator>
          <div>
            <q-item class="col row q-mt-md">
              <q-item-section class="col" v-if="!readonly">
                <q-btn class="q-mx-md text-teal" rounded outline no-caps label="Edit" @click="editBlockClick()"></q-btn>
              </q-item-section>
              <q-item-section class="col">
                <q-btn class="q-mx-md bg-teal text-white" rounded outline no-caps label="Close" @click="viewBlock = false"></q-btn>
              </q-item-section>
            </q-item>
          </div>
        </div>
      </q-dialog>

      <q-dialog v-model="customizeDurationDialog">
        <q-card>
          <q-card-section>
            <q-item-label class="text-h6">Length of each service</q-item-label>
          </q-card-section>
          <q-card-section>
            <q-input v-model="tempDuration" outlined>
              <template v-slot:append> mins </template>
            </q-input>
          </q-card-section>
          <q-separator inset />
          <q-card-section>
            <q-btn class="full-width bg-teal text-white" rounded no-caps label="Confirm" @click="closeCustomize()"></q-btn>
          </q-card-section>
        </q-card>
      </q-dialog>

      <q-dialog v-model="customizeBreakDialog">
        <q-card>
          <q-card-section>
            <q-item-label class="text-h6">Length of break</q-item-label>
          </q-card-section>
          <q-card-section>
            <q-input v-model="tempBreak" outlined>
              <template v-slot:append> mins </template>
            </q-input>
          </q-card-section>
          <q-separator inset />
          <q-card-section>
            <q-btn class="full-width bg-teal text-white" rounded no-caps label="Confirm" @click="closeCustomize()"></q-btn>
          </q-card-section>
        </q-card>
      </q-dialog>

      <q-dialog v-model="viewMaterialDialog">
        <q-card style="width: 500px">
          <q-card-section>
            <q-toolbar>
              <q-toolbar-title>{{ viewMaterialDetail.filename ? viewMaterialDetail.filename : 'Material Detail' }}</q-toolbar-title>
              <q-space />
              <q-btn flat round icon="close" @click="() => (viewMaterialDialog = false)"></q-btn>
            </q-toolbar>
            <q-separator></q-separator>
            <br />
            <div class="text-center">
              <div v-if="viewMaterialDetail.mime.includes('image')">
                <q-img :src="hashToUrl(viewMaterialDetail.hash)"></q-img>
              </div>
              <div v-else-if="viewMaterialDetail.mime.includes('video')">
                <video class="full-width vertical-middle" id="record-audio" controls muted controlslist="nodownload">
                  <source :src="hashToUrl(viewMaterialDetail.hash)" />
                </video>
              </div>
              <div v-else-if="viewMaterialDetail.mime.includes('audio')">
                <video
                  class="full-width vertical-middle"
                  id="record-audio"
                  controls
                  muted
                  controlslist="nodownload"
                  style="height: 3rem"
                  :src="hashToUrl(viewMaterialDetail.hash)"></video>
              </div>
              <div v-else-if="viewMaterialDetail.mime.includes('pdf')">
                <q-btn flat class="text-teal" icon="picture_as_pdf" size="4rem" @click="openPDF(viewMaterialDetail)">
                  <q-tooltip>Open PDF</q-tooltip>
                </q-btn>
              </div>
            </div>
            <br />
            <q-separator></q-separator>
            <q-toolbar>
              <q-space />
              <q-btn class="text-white bg-red" label="Delete Material" no-caps rounded outline @click="deleteMaterial(viewMaterialDetail)"></q-btn>
            </q-toolbar>
          </q-card-section>
        </q-card>
      </q-dialog>
    </q-page-container>
  </q-layout>
</template>

<script setup>
import {ref, onMounted, inject, watch, computed, onUnmounted, openBlock} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {servicePackageStore} from 'stores/service-package'
import {pubStore} from 'stores/pub'

import {subjectsStore} from 'stores/subjects'
import useAcademicSetting from 'src/composables/account/academic/useAcademicSetting'
import {OverseasStudyList} from 'src/pages/teacher-verification/utils'
import {GradeGroupMap, EducatorGrades} from 'src/boot/const'
import SubjectNavBarItem from '../../account/academic-setting/components/SubjectNavBarItem.vue'
import {identity} from '@vueuse/core'
import BlockList from 'components/BlockList.vue'
import PremiumDialog from 'src/components/PremiumDialog.vue'
import RecommendDialog from 'src/components/RecommendDialog.vue'
import PackageDialog from 'components/PackageDialog.vue'
import PackageCard from 'src/components/PackageCard.vue'
import HourlyRate from './HourlyRate.vue'
import AcBgCheck from './AcBgCheck.vue'
import PremiumContent from './PremiumContent copy.vue'
import OnCampusPrice from './OnCampusPrice copy.vue'
import OnlineSubs from './OnlineSubs copy.vue'
import PackagePublish from 'components/PackagePublish.vue'

/*
  consts
*/
const servicePackage = servicePackageStore()
const pub = pubStore()
const route = useRoute()
const router = useRouter()

const {curriculumList, createCurriculum, userCurriculumList, getUserCurriculumList, sysCurriculumMap} = useAcademicSetting()
const filteredCurriculumList = ref([])
const subjectsList = ref()
const setBlock = ref(false)
const viewBlock = ref(false)
const customizeDurationDialog = ref(false)
const customizeBreakDialog = ref(false)
const tempDuration = ref(0)
const tempBreak = ref(0)
const essayOrTeacherTrainingSubjectLists = ref()
const viewMaterialDialog = ref()
const viewMaterialDetail = ref()
const errorPositions = ref({})
const isPublishAction = ref(route.query.action == 'publish')
const premiumContentError = ref(true)
const subsPriceError = ref(true)
const loading = ref(false)

/*
  computeds
*/

const selectedSubjectsOrCountry = computed(() => {
  return servicePackage.mentoringType == 'overseasStudy' ? OverseasStudyList.filter((e) => e.value == servicePackage.countryCode?.[0]) : selectedSubjects.value
})

const isMultipleContents = computed(() => {
  return ['essay', 'foundation', 'bachelor', 'diploma', 'master'].includes(servicePackage.mentoringType) // === 'university'
})

//https://docs.google.com/spreadsheets/d/1BsKOa4C6ren0zoOAedhoZcBKCnFjmeRsb7DyaFfBieQ/edit?pli=1&gid=456157691#gid=456157691
const isMultipleSubjectChoice = computed(() => {
  //personalStatement: 'Application and visa assistance'
  //https://github.com/zran-nz/bug/issues/5132#issuecomment-2378289953
  //https://github.com/zran-nz/bug/issues/5396#issue-2535721501
  const isSubstituteAcademic = servicePackage.serviceRoles === 'substitute' && servicePackage.mentoringType === 'academic'
  return servicePackage.serviceRoles === 'consultant' || ['steam', 'essay'].includes(servicePackage.mentoringType) || isSubstituteAcademic
})

const isMultipleGradeChoice = computed(() => {
  return servicePackage.serviceRoles === 'consultant' || servicePackage.isEducatorServiceType
})

const addCarerPackageEnable = computed(() => {
  return (
    servicePackage.serviceRoles == 'mentoring' &&
    servicePackage.contentOrientatedEnable &&
    servicePackage.contentOrientated?.length &&
    !servicePackage.isEducatorServiceType
  )
})

const interviewOrAcademicCheckEnable = computed(() => {
  //https://github.com/zran-nz/bug/issues/4967#issuecomment-2242243613
  //https://github.com/zran-nz/bug/issues/4616#issue-2286748959 Academic check学术审核
  //https://github.com/zran-nz/bug/issues/5416#issue-2544170851
  return (
    servicePackage.serviceRoles == 'mentoring' &&
    servicePackage.contentOrientatedEnable &&
    servicePackage.contentOrientated?.length &&
    (servicePackage.mentoringType == 'academic' || servicePackage.mentoringTypeList.some((e) => e.value == servicePackage.mentoringType && e.educator))
  )
})

const block = computed(() => {
  return {duration: servicePackage.duration, break: servicePackage.break}
})

const editable = computed(() => {
  return !(servicePackage.serviceRoles == 'consultant' && servicePackage.lastPublished)
})

const readonly = computed(() => {
  return !!servicePackage.count?.sold
})

const addPremiumContentDisable = computed(() => {
  return (!selectedSubjects.value?.length && servicePackage.mentoringType !== 'overseasStudy') || !servicePackage.gradeGroup.grades?.length
})

const tagSettingDisable = computed(() => {
  return servicePackage.contentOrientatedEnable && !!servicePackage.contentOrientated?.length
})

const displpayPrice = computed(() => servicePackage.price / 100)

const subjectBlockTitle = computed(() => {
  let title = 'Subject'
  if (servicePackage.isEducatorServiceType) {
    title = 'Teaching area'
  }
  if (servicePackage.mentoringType === 'essay') {
    title = 'University researching field'
  }
  if (servicePackage.serviceRoles === 'consultant') {
    title = 'Service areas'
  }
  return title
})

const userCurriculumCodes = computed(() => userCurriculumList.value.map((e) => e.code))

const sellingPointAddShow = computed(() => {
  let AddPoints = true
  if (servicePackage.points.length === 1) {
    if (servicePackage.points[0] === '') {
      AddPoints = false
    }
  } else {
    const lastPoint = servicePackage.points[servicePackage.points.length - 1]
    if (lastPoint === '') {
      AddPoints = false
    }
  }
  return AddPoints
})

const currentSubjectList = computed(() => {
  let subjects = servicePackage.subjectList?.filter((subject) => subject.curriculum[0] === servicePackage.curriculum?.value)

  const subjectsDetail = servicePackage.serviceSubjects[servicePackage.mentoringType]
  if (servicePackage.mentoringType !== 'academic' && subjectsDetail?.topic) {
    if (servicePackage.topicWithChilds) {
      subjects = []
      subjectsDetail.topic.map((topic) => {
        subjects.push({
          label: topic.name,
          value: topic.child,
        })
      })
    } else {
      subjects = subjectsDetail.topic
    }
  }
  return subjects
})

const selectedSubjects = computed(() => {
  let selected
  if (servicePackage.topicWithChilds) {
    selected = essayOrTeacherTrainingSubjectLists.value?.value.filter((e) => e.selected)
  } else {
    selected = currentSubjectList.value?.filter((e) => e.selected)
  }
  return selected
})

const gradeList = computed(() => {
  let gradeMapNeeded = Acan.clone(GradeGroupMap)
  if (servicePackage.mentoringType === 'academic') {
    delete gradeMapNeeded.tertiary
  } else if (servicePackage.isEducatorServiceType) {
    gradeMapNeeded = Acan.clone(EducatorGrades)
  } else if (servicePackage.mentoringType === 'essay') {
    gradeMapNeeded = {
      lowerHighSchool: gradeMapNeeded['lowerHighSchool'],
      upperHighSchool: gradeMapNeeded['upperHighSchool'],
      tertiary: gradeMapNeeded['tertiary'],
    }
  }
  return gradeMapNeeded
})

const academicValueVideo = computed(() => {
  let video = {}
  video = servicePackage.attachments.find((a) => a.videoType === 'AcademicValue')
  return video
})

const featuresVideo = computed(() => {
  let video = {}
  video = servicePackage.attachments.find((a) => a.videoType === 'Features')
  return video
})

const QAVideo = computed(() => {
  let video = {}
  video = servicePackage.attachments.find((a) => a.videoType === 'QA')
  return video
})

const isSubstitute = computed(() => {
  return servicePackage.serviceRoles === 'substitute'
})

// ---------------functions-------------------------

// --------------create functions -------------------
/*
  methods
*/

const addContentEnable = (subject) => {
  return (
    (isMultipleContents.value && !servicePackage.contentOrientated.some((e) => e.subject == subject._id)) ||
    (!isMultipleContents.value && servicePackage.contentOrientated.length === 0)
  )
}
function categoryClick(type) {
  servicePackage.serviceRoles = type
  // clearGrade()
  // checkCurriculumService()
}

function qualificationClick(type) {
  servicePackage.qualification = type
}

function contentOrientatedClick(value) {
  servicePackage.contentOrientatedEnable = value
}

function onMentoringChange() {
  if (servicePackage.mentoringType === 'academic') {
    servicePackage.curriculum = {value: 'au', label: 'AU curriculum'}
  } else {
    servicePackage.curriculum = {value: 'pd', label: 'Service'}
  }
}

// --------------create functions end-------------------

function onPremiumContentUpdate(invalid) {
  premiumContentError.value = invalid
  checkDataFinished(false)
}

function substituteUpdate(invalid) {
  subsPriceError.value = invalid
  checkDataFinished(false)
}

async function uploadCoverImage() {
  if (!editable.value) return
  const rs = await Fn.fileUpLoadUiX('image/*')
  if (!rs) {
    return
  }
  console.log(rs)
  if (rs.message) {
    pageLoading.value = false
    return $q.notify({type: 'negative', message: rs.message})
  }
  console.log(pub.user)
  servicePackage.coverName = rs.title?.[pub.user._id]
  servicePackage.cover = rs._id
  checkDataFinished(false)
}

function deleteCover() {
  servicePackage.cover = ''
  servicePackage.coverName = ''
  checkDataFinished(false)
}

function addSellingPoint() {
  servicePackage.points.push('')
  checkDataFinished(false)
}

function deleteSellingPointClick(index) {
  servicePackage.points.splice(index, 1)
  checkDataFinished(false)
}

function filterCurriculumList(value, update) {
  const text = value.toLowerCase()
  update(() => {
    let list = curriculumList.value.filter((e) => !userCurriculumCodes.value.includes(e.value))
    //list = list.filter((e) => e.value !== 'others')
    list = list.filter((e) => e.label.toLowerCase().includes(text) || e.value.toLowerCase().includes(text))
    if (servicePackage.type === 'substituteAcademic') {
      list = list.filter((e) => e.value !== 'pd')
    }
    filteredCurriculumList.value = list
  })
}

function checkCurriculumService() {
  if (servicePackage.mentoringType === 'academic') {
    if (servicePackage.curriculum.value === 'pd') {
      servicePackage.curriculum = {value: 'au', label: 'AU curriculum'}
    }
  } else {
    servicePackage.curriculum = {value: 'pd', label: 'Service'}
  }
}

function subjectClick(subject, index) {
  if (!isMultipleSubjectChoice.value) {
    if (servicePackage.topicWithChilds) {
      essayOrTeacherTrainingSubjectLists.value.value.map((s) => {
        if (s.selected) s.selected = !s.selected
      })
    } else {
      currentSubjectList.value.map((s) => {
        if (s.selected) s.selected = !s.selected
      })
    }
  }

  subject.selected = !subject.selected

  checkDataFinished(false)
}

function countryCodeClick(code, index) {
  servicePackage.countryCode = [code.value]
}

function gradeClick(label, key) {
  if (isMultipleGradeChoice.value) {
    if (servicePackage.gradeGroup.grades.some((e) => e === (servicePackage.isEducatorServiceType ? label : key))) {
      servicePackage.gradeGroup.grades = servicePackage.gradeGroup.grades.filter((e) => e !== (servicePackage.isEducatorServiceType ? label : key))
    } else {
      servicePackage.gradeGroup.grades.push(servicePackage.isEducatorServiceType ? label : key)
    }
  } else {
    servicePackage.gradeGroup.label = label.label
    servicePackage.gradeGroup.grades = [key]
  }

  checkDataFinished(false)
}

function clearGrade() {
  servicePackage.gradeGroup = {
    label: '',
    grades: [],
  }
}

function editBlockClick() {
  viewBlock.value = false
  setBlock.value = true
}

function onPremiumContentClick(item) {
  const path = `/detail/content/limit/${item.premium.unit._id}`
  const query = {isSysView: 1, authId: item.premium._id}
  window.open(
    router.resolve({
      path,
      query,
    }).href,
    '_blank'
  )
}

function onUploadClick(subject) {
  const existingPremiumContent = servicePackage.contentOrientated.map((e) => e.premium?._id)
  const _subject = isMultipleContents.value ? subject._id : null
  $q.dialog({
    component: PremiumDialog,
    componentProps: {
      label: servicePackage.mentoringTypeList.find((e) => e.value == servicePackage.mentoringType)?.label,
      serviceType: servicePackage.mentoringType,
      curriculum: servicePackage.curriculum.value,
      countryCode: servicePackage.countryCode,
      subject: _subject ? [_subject] : selectedSubjects.value.map((e) => e._id),
      gradeGroup: servicePackage.gradeGroup.grades,
      exceptions: existingPremiumContent,
    },
  }).onOk(async (obj) => {
    obj.subject = _subject
    servicePackage.contentOrientated.push(obj)
    delete errorPositions.value.content
  })
}

function onCarerPackTimesClick() {
  $q.dialog({
    title: servicePackage.carerPack.name,
    message: 'No. of sessions required to completed this course',
    prompt: {
      model: servicePackage.carerPack.times,
      isValid: (val) => val > 0,
      type: 'number', // optional
    },
    cancel: true,
    persistent: true,
  }).onOk((data) => {
    servicePackage.carerPack.times = data
    checkDataFinished(false)
  })
}

function onTimesClick(item) {
  $q.dialog({
    title: item.premium?.name,
    message: 'No. of sessions required to completed this course',
    prompt: {
      model: item.times,
      isValid: (val) => val > 0,
      type: 'number', // optional
    },
    cancel: true,
    persistent: true,
  }).onOk((data) => {
    item.times = data
  })
}

function onRemoveClick(type) {
  $q.dialog({
    title: 'Are you sure to remove it?',
    cancel: true,
    persistent: true,
  }).onOk(() => {
    if (type == 'carer') {
      servicePackage.carerPack = null
    } else {
      servicePackage.interviewPack = null
    }
    checkDataFinished(false)
  })
}

function onDeleteClick(item, all) {
  $q.dialog({
    title: 'Are you sure to remove it?',
    message: all ? 'Please notice that the bundled 1v1 mentor service package will also be deleted if the premium content is removed' : '',
    cancel: true,
    persistent: true,
  }).onOk(() => {
    if (all) {
      servicePackage.contentOrientated = servicePackage.contentOrientated.filter((e) => e.premium._id !== item.premium._id)
      if (servicePackage.contentOrientated?.length == 0) {
        servicePackage.carerPack = null
        servicePackage.bundledCarer = false
        servicePackage.interviewPack = null
        servicePackage.bundledInterview = false
        checkDataFinished(false)
      }
    } else {
      delete item.servicePack
    }
  })
}

function onBundledCarerUpdate() {
  checkDataFinished(false)
}

function onBundledInterviewUpdate() {
  checkDataFinished(false)
}

function onAddPackageClick(type) {
  $q.dialog({
    component: RecommendDialog,
    componentProps: {
      consultantType: type,
      educators: servicePackage.isEducatorServiceType,
      curriculum: servicePackage.curriculum?.value,
      serviceType: servicePackage.mentoringType,
      subject: selectedSubjects.value.map((e) => e._id),
      grades: servicePackage.gradeGroup.grades,
    },
  }).onOk((obj) => {
    if (obj?.package) {
      if (type == 'carer') {
        servicePackage.carerPack = obj.package
      } else {
        servicePackage.interviewPack = obj.package
      }
    }
    checkDataFinished(false)
  })
}

function onAddClick(item) {
  $q.dialog({
    component: RecommendDialog,
    componentProps: {
      curriculum: item.curriculum,
      orientated: true,
      educators: servicePackage.isEducatorServiceType,
      subject: item.premium?.curriculum == 'pd' ? item.premium.topic.map((e) => e._id) : item.premium.subject,
      serviceType: servicePackage.mentoringType,
    },
  }).onOk((obj) => {
    item.servicePack = obj.package
  })
}

function openBlockClick() {
  if (servicePackage.duration && servicePackage.break) {
    viewBlock.value = true
  } else {
    setBlock.value = true
  }
}

function durationClick(duration) {
  tempDuration.value = duration
}

function breakClick(breakTime) {
  tempBreak.value = breakTime
}

function customizeDuration() {
  customizeDurationDialog.value = true
}

function customizeBreak() {
  customizeBreakDialog.value = true
}

function closeCustomize() {
  customizeDurationDialog.value = false
  customizeBreakDialog.value = false
}

function saveDurationBreak() {
  servicePackage.duration = parseInt(tempDuration.value)
  servicePackage.break = parseInt(tempBreak.value)
  setBlock.value = false
  checkDataFinished(false)
}

function addDiscountClick() {
  servicePackage.discount.push({
    count: 0,
    discount: 0,
    gift: 0,
    orderCount: 0,
  })
}

function deleteDiscountClick(index) {
  servicePackage.discount.splice(index, 1)
}

function inputPrice(value) {
  servicePackage.price = parseInt(value * 100)
  checkDataFinished(false)
}

function enableGift(discount) {
  discount.enableGift = true
}

function deleteGift(discount) {
  discount.gifts = 0
  discount.enableGift = false
}

function frequencyClick(freq) {
  servicePackage.freq = freq
}

async function uploadMaterial(type) {
  //const rs = await Fn.fileUpLoadUiX('image/*,.pdf,.mp4,.webm,audio/*')
  const rs = await Fn.fileUpLoadUiX('.mp4,.webm')
  if (!rs) {
    return
  }
  console.log(rs)
  if (rs.message) {
    return $q.notify({type: 'negative', message: rs.message})
  }
  if (!servicePackage.attachments) servicePackage.attachments = []
  servicePackage.attachments.push({
    filename: rs.title?.[pub.user._id],
    mime: rs.mime,
    hash: rs._id,
    videoType: type,
  })
  checkDataFinished(false)
}

function openPDF(data) {
  window.open(Fn.hashToUrl(data.hash), '_blank')
}

function deleteMaterial(data) {
  let deleteIndex
  deleteIndex = servicePackage.attachments.findIndex((a) => a.videoType === data.videoType)
  servicePackage.attachments.splice(deleteIndex, 1)
  viewMaterialDialog.value = false
}

function showMaterialDetail(data) {
  viewMaterialDialog.value = true
  viewMaterialDetail.value = data
}

function saveSubjectsData() {
  const subjectList = []
  selectedSubjects.value?.map((subject) => {
    subjectList.push(subject._id)
  })

  if (servicePackage.mentoringType == 'academic') {
    servicePackage.subject = subjectList
    servicePackage.topic = []
  } else {
    servicePackage.subject = []
    if (servicePackage.mentoringType !== 'overseasStudy') {
      servicePackage.topic = subjectList
    }
  }
}

function savePriceData() {
  servicePackage.price = displpayPrice.value * 100
}

function saveKeywords() {
  servicePackage.keywords = selectedSubjects.value?.map((e) => e.name)
}

async function createClick() {
  loading.value = true
  saveSubjectsData()
  savePriceData()
  saveKeywords()

  checkDataFinished(true)

  if (!(isPublishAction.value && !servicePackage.filled)) {
    const rs = await servicePackage.packageSubmit(gradeList.value)
    if (rs) {
      if (isPublishAction.value) {
        if (servicePackage.isConsultantInterview) {
          const query = {
            status: true,
            'consultant.type': servicePackage.consultant.type,
            mentoringType: servicePackage.mentoringType,
            qualification: servicePackage.qualification,
            curriculum: servicePackage.curriculum.value,
          }
          if (servicePackage.countryCodeRequired) {
            query.countryCode = {$in: servicePackage.countryCode}
          }

          const res = await App.service('service-pack').find({query})

          if (res?.data?.length) {
            loading.value = false
            $q.dialog({
              title: 'Alert',
              message: 'You can not publish this service package due to the existence of same-type product under the published status. ',
              ok: {label: 'I got it', noCaps: true},
            })
            return
          }
        }
        loading.value = false
        $q.dialog({
          component: PackagePublish,
          componentProps: {pack: rs},
        }).onOk((res) => {
          router.back()
        })
      } else {
        router.back()
      }
    }
  }
}

function scrollIntoViewWithOffset(id, offset) {
  window.scrollTo({
    behavior: 'smooth',
    top: document.getElementById(id)?.getBoundingClientRect().top - document.body.getBoundingClientRect().top - offset,
  })
}

function checkDataFinished(scroll) {
  errorPositions.value = {}
  if (!servicePackage.cover) {
    errorPositions.value.cover = true
  } else {
    delete errorPositions.value.cover
  }

  if (!servicePackage.name) {
    errorPositions.value.name = true
  } else {
    delete errorPositions.value.name
  }

  if (servicePackage.points.length === 1 && !servicePackage.points[0]) {
    errorPositions.value.points = true
  } else {
    delete errorPositions.value.points
  }

  if (!servicePackage.type) {
    errorPositions.value.type = true
  }

  if (!servicePackage.mentoringType) {
    errorPositions.value.mentoringType = true
  }

  if (!servicePackage.curriculum.value) {
    errorPositions.value.curriculum = true
  }

  if (servicePackage.countryCodeRequired) {
    if (!servicePackage.countryCode?.length) {
      errorPositions.value.countryCode = true
    }
  }

  if (servicePackage.mentoringType == 'academic') {
    if (!servicePackage.subject.length || !selectedSubjects.value?.length) {
      errorPositions.value.subject = true
    } else {
      delete errorPositions.value.subject
    }
  } else if (servicePackage.mentoringType !== 'overseasStudy') {
    if (!servicePackage.topic.length || !selectedSubjects.value?.length) {
      errorPositions.value.subject = true
    } else {
      delete errorPositions.value.subject
    }
  }

  if (!servicePackage.gradeGroup?.grades?.length) {
    errorPositions.value.grades = true
  }

  if (['mentoring', 'consultant'].includes(servicePackage.serviceRoles) && (servicePackage.duration == 0 || servicePackage.break == 0)) {
    errorPositions.value.duration = true
  } else {
    delete errorPositions.value.duration
  }

  if (
    servicePackage.contentOrientatedEnable &&
    (!servicePackage.contentOrientated?.length || servicePackage.contentOrientated.some((e) => !e.premium.status))
  ) {
    errorPositions.value.content = true
  } else {
    delete errorPositions.value.content
  }

  if (servicePackage.contentOrientatedEnable) {
    if (premiumContentError.value) {
      errorPositions.value.price = true
    }
  } else if (!servicePackage.price) {
    errorPositions.value.price = true
  } else {
    delete errorPositions.value.price
  }

  if (!servicePackage.freq) {
    errorPositions.value.freq = true
  }
  if (!servicePackage.attachments.find((video) => video.videoType === 'AcademicValue')) {
    errorPositions.value.attachments = true
  }
  if (servicePackage.contentOrientatedEnable && !servicePackage.attachments.find((video) => video.videoType === 'Features')) {
    errorPositions.value.attachments = true
  }
  if (!servicePackage.attachments.find((video) => video.videoType === 'QA')) {
    errorPositions.value.attachments = true
  }

  if (servicePackage.bundledCarer && !servicePackage.carerPack?._id) {
    errorPositions.value.carerPack = true
  } else {
    delete errorPositions.value.carerPack
  }

  if (servicePackage.bundledInterview && !servicePackage.interviewPack?._id) {
    errorPositions.value.interviewPack = true
  } else {
    delete errorPositions.value.interviewPack
  }

  if (servicePackage.serviceRoles === 'substitute') {
    console.log('subsPriceError', subsPriceError.value)
    if (subsPriceError.value) {
      errorPositions.value.price = true
    } else {
      console.log('delete errorPositions.value.price')
      delete errorPositions.value.price
    }
  }

  if (Object.keys(errorPositions.value)?.length) {
    servicePackage.filled = false
    loading.value = false
  } else {
    servicePackage.filled = true
  }

  if (scroll && isPublishAction.value) {
    if (!servicePackage.filled) {
      scrollIntoViewWithOffset(`error-${Object.keys(errorPositions.value)[0]}`, 100)
    } else {
      scrollIntoViewWithOffset('bottom-publish', 100)
    }
  }
}

function cancelClick() {
  router.back()
}

function onCurriculumUpdate() {
  currentSubjectList.value?.map((s) => {
    s.selected = false
  })
  servicePackage.gradeGroup.grades = []
  selectAllServiceAreasAndGrades()
}

async function selectAllServiceAreasAndGrades() {
  if (servicePackage.serviceRoles == 'consultant') {
    await sleep(200)
    servicePackage.gradeGroup = {label: '', grades: []}
    if (Array.isArray(gradeList.value)) {
      gradeList.value.forEach((e) => gradeClick(e, e))
    } else {
      for (const key in gradeList.value) {
        if (gradeList.value[key]) {
          gradeClick(gradeList.value[key], key)
        }
      }
    }

    let subjectList = currentSubjectList.value
    if (servicePackage.topicWithChilds) {
      subjectsList = essayOrTeacherTrainingSubjectLists.value
    }
    for (let i = 0; i < subjectList.length; i++) {
      subjectClick(subjectList[i], i)
    }
  }
}

async function getBundledDetail() {
  const premiumIds = servicePackage.contentOrientated.map((e) => e.premium)
  const packageIds = servicePackage.contentOrientated.map((e) => e.servicePack)

  if (servicePackage.interviewPack?._id) {
    packageIds.push(servicePackage.interviewPack._id)
  }

  if (servicePackage.carerPack?._id) {
    packageIds.push(servicePackage.carerPack._id)
  }

  if (premiumIds?.length) {
    const serviceAuths = await App.service('service-auth').get('unit', {query: {_id: {$in: premiumIds}}})
    servicePackage.contentOrientated.map((e) => {
      e.premium = serviceAuths.data?.find((f) => f._id == e.premium)
    })
  }

  if (packageIds?.length) {
    const servicePacks = await App.service('service-pack').find({query: {_id: {$in: packageIds}}})
    servicePackage.contentOrientated.map((e) => {
      e.servicePack = servicePacks.data?.find((f) => f.status && f._id == e.servicePack)
      if (!e.servicePack?.status) {
        e.servicePack = null
      }
    })

    if (servicePackage.interviewPack) {
      servicePackage.interviewPack = {...servicePackage.interviewPack, ...servicePacks.data?.find((f) => f.status && f._id == servicePackage.interviewPack._id)}
      if (!servicePackage.interviewPack.status) {
        servicePackage.interviewPack = {}
        servicePackage.bundledInterview = false
      }
    }

    if (servicePackage.carerPack?._id) {
      servicePackage.carerPack = {...servicePackage.carerPack, ...servicePacks.data?.find((f) => f.status && f._id == servicePackage.carerPack._id)}
      if (!servicePackage.carerPack.status) {
        servicePackage.carerPack = {}
        servicePackage.bundledCarer = false
      }
    }
  }
}

async function main() {
  $q.loading.show()
  if (route.query?.id) {
    const packageData = await servicePackage.getOnePackage(route.query?.id)
    if (packageData) {
      const packCurriculum = sysCurriculumMap.value[packageData.curriculum]
      let gradeGroup = {
        label: '',
        grades: packageData.gradeGroup,
      }
      servicePackage.loadData(packageData, packCurriculum, gradeGroup)
      if (servicePackage.contentOrientatedEnable) {
        await getBundledDetail()
      }
    }
  }

  if (servicePackage.mentoringType == 'academic') {
    currentSubjectList.value.map((s) => {
      if (servicePackage.subject?.includes(s._id)) {
        s.selected = true
      } else {
        delete s.selected
      }
    })
  } else if (servicePackage.topicWithChilds) {
    currentSubjectList.value.map((s) => {
      s.value.map((v) => {
        if (servicePackage.topic?.includes(v?._id)) {
          v.selected = true
          essayOrTeacherTrainingSubjectLists.value = s
        } else {
          delete s.selected
        }
      })
    })
  } else {
    currentSubjectList.value?.map((s) => {
      if (servicePackage.topic?.includes(s._id)) {
        s.selected = true
      } else {
        delete s.selected
      }
    })
  }

  if (servicePackage.gradeGroup.label && !servicePackage.gradeGroup.grades.length) {
    const gradeKey = Object.keys(GradeGroupMap).find((key) => GradeGroupMap[key].label === servicePackage.gradeGroup.label)
    servicePackage.gradeGroup.grades = GradeGroupMap[gradeKey].grades[servicePackage.curriculum]
    if (!servicePackage.gradeGroup?.length) {
      servicePackage.gradeGroup.grades = GradeGroupMap[gradeKey].grades.classcipe
    }
  }

  if (servicePackage.duration && servicePackage.break) {
    tempDuration.value = servicePackage.duration
    tempBreak.value = servicePackage.break
  }

  if (!route.query?.id && servicePackage.serviceRoles == 'consultant') {
    selectAllServiceAreasAndGrades()
  }

  if (servicePackage.contentOrientatedEnable) {
    curriculumList.value.push({label: 'Others', value: 'others'})
  }
  $q.loading.hide()
}

watch(
  () => servicePackage.contentOrientated,
  (val) => {
    if (val.length <= 1) {
      servicePackage.splitSale = false
    }
  },
  {deep: true}
)

watch(
  () => servicePackage.salesTarget,
  (val) => {
    if (!val?.includes('school')) {
      servicePackage.splitSale = false
    }
  },
  {deep: true}
)

onMounted(async () => {
  $q.loading.show()
  await servicePackage.init()
  if (servicePackage.serviceRoles || route.query?.id) {
    await main()
    await sleep(500)
    checkDataFinished(true)
  } else {
    $q.loading.hide()
    $q.dialog({
      component: PackageDialog,
      componentProps: {},
    }).onOk(async () => {
      await main()
    })
  }
})
</script>
