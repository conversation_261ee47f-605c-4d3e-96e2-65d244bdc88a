export const agencyOptions = [
  {
    value: 'unit',
    label: 'Teaching resources',
  },
  {
    value: 'self_study',
    label: 'Self-study content',
  },
  {
    value: 'session',
    label: 'Premium Workshops',
  },
  {
    value: 'service_premium',
    label: 'Lecture service',
  },
  {
    value: 'service',
    label: 'Mentor/carer service',
  },
  {
    value: 'service_substitute',
    label: 'Substitute service',
  },
  {
    value: 'service_correct',
    label: 'Correct service',
  },
  {
    value: 'task',
    label: 'Associate task',
  },
  {
    value: 'saas_tool_paid',
    label: 'SAAS tools-paid',
  },
  {
    value: 'verify',
    label: 'Introduce user to verify-commission',
  },
]
export const organizationOptions = [
  {
    value: 'session',
    label: 'Premium Workshops',
  },
  {
    value: 'service_premium',
    label: 'Lecture service',
  },
  {
    value: 'service',
    label: 'Mentor/carer service',
  },
  {
    value: 'service_substitute',
    label: 'Substitute service',
  },
  {
    value: 'service_correct',
    label: 'Correct service',
  },
  {
    value: 'task',
    label: 'Associate task',
  },
]
export const educationConsultantOptions = [
  {
    value: 'service_premium',
    label: 'Lecture service',
  },
  {
    value: 'service',
    label: 'Mentor/carer service',
  },
  {
    value: 'service_substitute',
    label: 'Substitute service',
  },
  {
    value: 'service_correct',
    label: 'Correct service',
  },
  {
    value: 'task',
    label: 'Associate task',
  },
]
export const classcipeStaffOptions = [
  {
    value: 'service_premium',
    label: 'Lecture service',
  },
  {
    value: 'service',
    label: 'Mentor/carer service',
  },
  {
    value: 'service_substitute',
    label: 'Substitute service',
  },
  {
    value: 'service_correct',
    label: 'Correct service',
  },
  {
    value: 'task',
    label: 'Associate task',
  },
]

export const ambassadorOptions = [
  {
    value: 'session',
    label: 'Premium Workshops',
  },
  {
    value: 'service',
    label: 'Mentor/carer service',
  },
  {
    value: 'service_premium',
    label: 'Lecture service',
  },
  {
    value: 'task',
    label: 'Associate task',
  },
]
