<template>
  <q-layout view="hHh LpR fFf" class="bg-white">
    <Header />
    <q-page-container class="pc-sm page-box ">
      <q-page class="q-pa-md ">
        <!-- <q-breadcrumbs>
          <q-breadcrumbs-el label="Account" to="/account/info" />
          <q-breadcrumbs-el label="Invitation history" class="text-grey-6" />
        </q-breadcrumbs>
        <div class="text-h6 q-mt-sm q-mb-md">Invitation history</div> -->
        <!-- <PointTab /> -->
        <div class="text-h6 q-my-md">Users</div>
        <div class="q-px-md q-pb-xl">
          <div class="row justify-between items-center q-mb-xs q-mt-lg">
            <div class="text-h5 q-my-md text-right col-sm-5 col-xs-12">
              <q-input class="full-width" color="primary" square outlined label="Search" v-model="search" clearable @clear="doSearch" @keyup.enter="doSearch">
                <template v-slot:append>
                  <q-btn round flat icon="search" @click="doSearch"></q-btn>
                </template>
              </q-input>
            </div>
          </div>
          <DetailView ref="child" :load="handleLoad" class="q-mt-lg ">
            <div class="rounded-borders-sm q-mt-md q-border-1 q-pa-md bg-white shadow-1">
              <div class="row text-grey-6">
                <div class="col-6">Name</div>
                <div class="col-6">Registered time</div>
              </div>
              <div class="row q-py-sm" v-for="item in child.data" :key="item._id">
                <div class="col-6">
                  <PubAvatar class="q-mr-sm" size="1.5rem" :src="item?.snapshot?.avatar" />
                  {{ item?.snapshot?.nickname }}
                </div>
                <div class="col-6">{{ date.formatDate(item.createdAt, 'MM/DD/YYYY HH:mm:ss') }}</div>
                <div class="col-12">
                  <q-separator class="q-mt-sm" />
                </div>
              </div>
            </div>
            <template v-slot:noData>
              <NoData message="You have not invited anyone" messageColor="text-grey" />
              <div class="row full-width q-mt-md">
                <div class="col">
                  <q-btn @click="toPoster" icon="control_point" rounded class="full-width" color="teal" no-caps label="Create poster to invite now"></q-btn>
                </div>
              </div>
            </template>
          </DetailView>
        </div>
      </q-page>
    </q-page-container>
  </q-layout>
</template>
<script setup>
/*
  imports
*/
import {onMounted, ref} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {pubStore} from 'stores/pub'
import DetailView from 'components/DetailView.vue'
import {date} from 'quasar'
import Header from './Header.vue'
import PointTab from './PointTab.vue'

const pub = pubStore()

const isStudent = ref(pub.user?.roles?.includes('student'))

const route = useRoute()
const router = useRouter()
const search = ref('')
const child = ref(null)

const doSearch = () => {
  onLoad()
}

const onLoad = async () => {
  child.value.onLoad()
}

const toPoster = () => {
  const url = isStudent.value ? '/study/posters' : '/home/<USER>'
  router.push({
    path: url,
    query: {back: route.fullPath},
  })
}

const handleLoad = async () => {
  const query = {
    $limit: 1000,
    $sort: {createdAt: -1},
    tab: 'earn',
    category: 'invite',
  }

  if (search.value) {
    query['snapshot.name'] = {
      $regex: search.value.toLowerCase(),
      $options: 'i',
    }
  }
  return App.service('point-log').find({query})
}

onMounted(() => {
  onLoad()
})
</script>
<style lang="sass" scope>

.page-box
  padding-left: 0 !important
</style>
