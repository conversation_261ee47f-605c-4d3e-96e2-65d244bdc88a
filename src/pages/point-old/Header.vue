<template>
  <q-header elevated class="bg-teal-1 text-black" height-hint="98">
    <q-toolbar>
      <q-btn flat round dense icon="navigate_before" @click="onClick"></q-btn>
      <q-btn v-if="$q.screen.lt.md" flat @click="toggleDrawer" round dense icon="menu" />
      <q-toolbar-title class=" text-h6">
        Invitation record
      </q-toolbar-title>
    </q-toolbar>
  </q-header>
  <q-resize-observer @resize="onResize" />
  <q-drawer v-model="drawer" :breakpoint="500" bordered >
    <q-scroll-area class="fit">
      <q-list>
        <template v-for="(item, index) in tabs" :key="index">
          <q-item :active="tab === item.value" :to="`/point/${item.value}`" class="cursor-pointer"
            active-class="item-active text-white">
            <q-item-section>
              <div class="q-pl-md rounded-md q-pa-sm"
                :class="[tab === item.value ? 'bg-teal-1 text-bold text-grey-9 ' : 'bg-white text-grey-10']">
                {{ item.label }}
              </div>
            </q-item-section>
          </q-item>
        </template>
      </q-list>
    </q-scroll-area>
  </q-drawer>
</template>

<script setup>
import {ref, onMounted} from 'vue'
import {useRoute, useRouter} from 'vue-router'

import CartPage from 'components/CartPage.vue'
import AccountMenu from 'components/AccountMenu.vue'
import NoticePage from 'components/NoticePage.vue'
import { pubStore } from 'stores/pub'

const pub = pubStore()
const route = useRoute()
const router = useRouter()
const isStudent = ref(pub.user?.roles?.includes('student'))
const drawer = ref($q.screen.gt.sm)

const onClick = () => {
  const homepage = isStudent.value ? '/student/account/info' : '/account/info'

  router.push(homepage)
}

const onResize = (size) => {
  if (size.width >= 1220) {
    drawer.value = true
  } else {
    drawer.value = false
  }
}

const toggleDrawer = () => {
  drawer.value = !drawer.value
}


const tabs = [
  {value: 'invite', label: 'Users'},
  {value: 'record', label: 'Points'},
]
const tab = ref(route.path.split('point/')[1])

function resetFn() {
  router.push(`/point/${tab.value}`)
}
</script>
