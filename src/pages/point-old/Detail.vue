<template>
  <q-layout view="hHh LpR fFf" class="bg-white">
    <q-header elevated class="bg-teal-1 text-black" height-hint="98">
      <q-toolbar>
        <q-btn flat round dense icon="navigate_before" @click="router.back()"></q-btn>
        <q-toolbar-title class=" text-h6">
          {{ tabType[detail?.tab] }} points details
        </q-toolbar-title>
      </q-toolbar>
    </q-header>
    <q-page-container class="pc-sm page-box ">
      <q-page class="q-pa-md q-mt-md">
        <!-- <q-breadcrumbs>
          <q-breadcrumbs-el label="Account" to="/account/info" />
          <q-breadcrumbs-el label="Invitation history" to="/point/invite" />
          <q-breadcrumbs-el label="Points" class="text-grey-6" />
        </q-breadcrumbs>
        <div class="text-h6 q-mt-sm q-mb-md">
          <q-btn flat round dense size="lg" icon="arrow_back" @click="router.back()"></q-btn>
          {{ tabType[detail?.tab] }} points details
        </div> -->
        <div class="q-px-md q-pb-xl">
          <DetailView ref="child" :load="handleLoad" class="q-mt-lg ">
            <div class="rounded-borders-sm q-mt-md q-border-1 q-pa-md bg-white shadow-1">
              <div class="underline q-py-md">{{ productLabel(detail) }}</div>
              <div class="row underline q-py-md">
                <div class="col-2 text-grey-6">Updates</div>
                <div class="col-10" :class="detail?.value > 0 ? 'text-positive' : 'text-negative'">
                  {{ detail?.value > 0 ? '+' : '' }}
                  {{ detail?.value }}
                </div>
              </div>
              <div class="row underline q-py-md">
                <div class="col-2 text-grey-6">Remaining</div>
                <div class="col-10">{{ detail?.total }}</div>
              </div>
              <template v-if="detail?.tab === 'earn'">
                <div class="row underline q-py-md"
                  v-if="detail?.category === 'verify' || detail?.category === 'invite'">
                  <div class="col-2 text-grey-6">User invited</div>
                  <div class="col-10">
                    <PubAvatar class="q-mr-sm" size="1.5rem" :src="detail?.snapshot?.avatar" />
                    {{ detail?.snapshot?.nickname }}
                  </div>
                </div>
                <div class="row underline q-py-md" v-else>
                  <div class="col-2 text-grey-6">Recommended Content</div>
                  <div class="col-10">{{ detail?.snapshot?.links?.[0]?.name }}</div>
                </div>
              </template>
              <template v-if="detail?.tab === 'claim'">
                <div class="row underline q-py-md">
                  <div class="col-2 text-grey-6">Claimed Content</div>
                  <div class="col-10">{{ detail?.snapshot?.links?.[0]?.name }}</div>
                </div>
              </template>
              <div class="row underline q-py-md">
                <div class="col-2 text-grey-6">Action on</div>
                <div class="col-10">{{ date.formatDate(detail?.createdAt, 'MM/DD/YYYY HH:mm:ss') }}</div>
              </div>
            </div>
          </DetailView>
        </div>
      </q-page>
    </q-page-container>
  </q-layout>
</template>
<script setup>
/*
  imports
*/
import { computed, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { pubStore } from 'stores/pub'
import DetailView from 'components/DetailView.vue'
import { date } from 'quasar'
import Header from './Header.vue'
import { productLabel } from './consts'

const pub = pubStore()

const isStudent = ref(pub.user?.roles?.includes('student'))

const route = useRoute()
const router = useRouter()
const search = ref('')
const child = ref(null)

const doSearch = () => {
  onLoad()
}

const tabType = {
  'earn': 'Earning',
  'claim': 'Claiming'
}


const detail = computed(() => child?.value?.data)

const onLoad = async () => {
  child.value.onLoad()
}

const handleLoad = async () => {
  return App.service('point-log').get(route.params.id)
}

onMounted(() => {
  onLoad()
})
</script>
<style lang="scss" scope>
.page-box {
  padding-left: 0 !important
}

.underline {
  border-bottom: 1px solid #ccc;
}
</style>
