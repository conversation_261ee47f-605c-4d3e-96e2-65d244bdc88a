<template>
  <IconPoint :class="props.class" :num="price" />
</template>

<script setup>
import {computed, onMounted, ref} from 'vue'
import {useRouter, useRoute} from 'vue-router'

import {pointStore} from 'stores/point'
import IconPoint from './IconPoint.vue'
import { CalPoint } from '../consts'

const store = pointStore()

const props = defineProps({
  price: {
    type: Number,
    default: 0,
  },
  type: {
    type: String,
    default: null,
  },
  class: {
    type: String,
    default: 'text-primary text-bold q-ml-xs',
  },
})

const price = computed(() => CalPoint(props.price, props.type, store.claimSetting))

</script>
<style lang="scss" scope></style>
