<template>
  <q-avatar :size="`${size}rem`"><img src="~assets/img/point/ic_point.png" /></q-avatar>
  <span style="vertical-align: -2px;" :class="props.class">{{ num }}</span>
</template>

<script setup>
const props = defineProps({
  num: {
    type: Number,
    default: 0,
  },
  size: {
    type: Number,
    default: 1.5,
  },
  class: {
    type: String,
    default: 'text-primary text-bold q-ml-xs',
  },
})
</script>
<style lang="scss" scope></style>
