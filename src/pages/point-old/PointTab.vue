<template>
  <q-tabs
    v-model="tab"
    stretch
    @update:model-value="resetFn()"
    align="left"
    indicator-color="primary"
    active-color="primary"
    inline-label
    mobile-arrows
    shrink>
    <q-tab v-for="o in tabs" :key="o.value" :name="o.value" :label="o.label.toFirstUpperCase()" no-caps />
  </q-tabs>
</template>

<script setup>
import {ref, onMounted} from 'vue'
import {useRoute, useRouter} from 'vue-router'

const route = useRoute()
const router = useRouter()
const tabs = [
  {value: 'invite', label: 'Users'},
  {value: 'record', label: 'Points'},
]
const tab = ref(route.path.split('point/')[1])

function resetFn() {
  router.push(`/point/${tab.value}`)
}
</script>
