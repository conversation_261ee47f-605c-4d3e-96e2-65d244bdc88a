<template>
  <q-layout view="hHh LpR fFf" class="bg-white">
    <Header />
    <q-page-container class="pc-sm page-box">
      <q-page class="q-pa-md">
        <!-- <q-breadcrumbs>
          <q-breadcrumbs-el label="Account" to="/account/info" />
          <q-breadcrumbs-el label="Invitation history" class="text-grey-6" />
        </q-breadcrumbs>
        <div class="text-h6 q-mt-sm q-mb-md">Invitation history</div> -->
        <!-- <PointTab /> -->
        <div class="text-h6 q-my-md">Points</div>
        <div class="rounded-borders-sm q-mt-md q-border-1 q-pa-md bg-white shadow-1 row items-center">
          <q-avatar size="3rem"><img src="~assets/img/point/ic_point.png" /></q-avatar>
          <div class="q-ml-md">
            <div class="text-h6">My points</div>
            <div class="text-primary text-h6">{{pStore?.myPoint}}</div>
          </div>
          <div class="q-ml-lg">
            <q-btn class="text-primary q-mt-lg" outline no-caps rounded label="Claim my points" :to="'/point/mall'" />
          </div>
        </div>
        <div class="rounded-borders-sm q-mt-md q-border-1 q-pa-md bg-white shadow-1">
          <DetailView ref="child" :load="handleLoad" showMore class="q-mt-lg">
            <div>
              <div class="row text-grey-6">
                <div class="col-5">Products/Action</div>
                <div class="col-2">Point</div>
                <div class="col-3">Time</div>
                <div class="col-2">Balance</div>
              </div>
              <div class="row q-py-sm" v-for="item in child.data" :key="item._id">
                <div class="col-5 text-primary text-capitalize cursor-pointer" @click="toDetail(item) ">{{ productLabel(item) }}</div>
                <div class="col-2" :class="item?.value > 0 ? 'text-positive' : 'text-negative'">
                  {{ item?.value > 0 ? '+' : '' }}
                  {{ item?.value }}
                </div>
                <div class="col-3">{{ date.formatDate(item.createdAt, 'MM/DD/YYYY HH:mm:ss') }}</div>
                <div class="col-2">{{ item?.total }}</div>
                <div class="col-12">
                  <q-separator class="q-mt-sm" />
                </div>
              </div>
            </div>
            <template v-slot:noData>
              <NoData message="No point yet!" messageColor="text-grey" />
              <div class="text-center">
                <div class="text-grey-6">
                  There are no point. <br />
                  Learn how to earn point?
                </div>
                <q-btn class="q-mt-md" @click="earnDialog = true" rounded color="teal" no-caps label="How to earn"></q-btn>
              </div>
            </template>
          </DetailView>
        </div>
        <q-dialog v-model="earnDialog">
          <q-card class="q-pa-md" style="width: 342px">
            <q-card-section class="text-bold">
              You might earn points via methods below: <br />
              Invite new user,<br />
              Invite user to verify, <br />
              Recommend user to buy teaching resources,<br />
              Recommend user to purchase service packages,<br />
              Recommend user to subscribe SAAS tools,<br />
              Recommend user to enrol premium workshops,<br />
              The users that you invited continue purchasing products with points on Classcipe
            </q-card-section>
            <q-card-actions>
              <q-btn icon="done" class="q-mt-md full-width" rounded color="teal" no-caps label="I got it" v-close-popup></q-btn>
            </q-card-actions>
          </q-card>
        </q-dialog>
      </q-page>
    </q-page-container>
  </q-layout>
</template>
<script setup>
/*
  imports
*/
import {onMounted, ref} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {pointStore} from 'stores/point'
import DetailView from 'components/DetailView.vue'
import {date} from 'quasar'
import Header from './Header.vue'
import PointTab from './PointTab.vue'
import { productLabel } from './consts'

const pStore = pointStore()


const route = useRoute()
const router = useRouter()
const search = ref('')
const child = ref(null)
const userInfo = ref(null)
const earnDialog = ref(false)
const skip = ref(0)


const onLoad = async () => {
  child.value.onLoad()
}

const handleLoad = async () => {
  const $limit = 10;
  const query = {
    $limit,
    $skip: skip.value,
    $sort: {createdAt: -1},
  }
  skip.value += $limit
  console.log('query', query)
  return App.service('point-log').find({query})
}

const toDetail = (item) => {
  router.push(`/point/record/${item._id}`)
}


const getPoint = async () => {
  pStore.getPoint()
}

const main = () => {
  getPoint()
  onLoad()
}
onMounted(main)
</script>
<style lang="sass" scope>

.page-box
  padding-left: 0 !important
</style>
