const earnOrderType = {
  unit: 'Teaching resources purchased',
  session_public: 'Premium workshops purchased',
  session_service_pack: 'Premium workshops purchased',
  session_self_study: 'Student self-study content',
  service_pack: 'Mentor service packages purchased',
}
export const productLabel = (item) => {
  let text = ''
  if (item?.tab === 'earn') {
    switch (item?.source) {
      case 'refund':
        text = `Refund: ${item?.snapshot?.links?.[0]?.name || ''}`
        break
      case 'reward':
        switch (item?.category) {
          case 'points_purchase':
            text = 'Purchased with points by other users'
            break
          case 'verify':
            text = `Invite user to verify`
            break
          case 'invite':
            text = `Invite new user`
            break
          case 'unit':
            text = `Unit`
            break
          case 'service':
            text = `Service`
            break
          case 'session':
            text = `Session`
            break
          case 'self_study':
            text = `Self-study`
            break
          case 'service_premium':
            text = `Lecture`
            break
          case 'cloud_20g':
            text = `Cloud 20G`
            break
          case 'cloud_40g':
            text = `Cloud 40G`
            break
          case 'task':
            text = `Task`
            break
          case 'saas_tool_paid':
            text = `SAAS tools-paid`
            break
          case 'saas_tool_trail':
            text = `SAAS tools-trail`
            break
          case 'service_substitute':
            text = `Service substitute`
            break
          case 'service_correct':
            text = `Service correct`
            break
          default:
            text = ''
            break
        }
        break
      default:
        text = ``
        break
    }
  } else if (item?.tab === 'claim') {
    if (item?.source === 'order') {
      text = `Claimed:  ${item?.snapshot?.links?.[0]?.name || ''}`
    }
  }

  return text
}

export const CalPoint = (price, pointType, claimSetting) => {
  let num = 0
  const item = claimSetting[pointType]
  if (item?.mode === 'percentage') {
    num = (price * item.value) / 10000
    if (num > 0 && num <= 1) {
      num = 1
    } else if (num > 1) {
      num = Math.floor(num)
    }
  }
  return num
}
