<template>
  <q-layout view="hHh LpR fFf" class="bg-white">
    <q-header elevated class="bg-teal-1 text-black" height-hint="98">
      <q-toolbar>
        <q-btn flat round dense icon="navigate_before" @click="router.back()"></q-btn>
        <q-toolbar-title class=" text-h6">
          Claim my point
        </q-toolbar-title>
      </q-toolbar>
    </q-header>
    <q-page-container class="pc-body page-box">
      <q-page class="q-pa-md">
        <!-- <div class="text-h5 text-bold">Claim my point</div> -->
        <SelfStudy v-if="isStudent" />
        <TeachingResource v-else />
        <PremiumWorkshop />
        <MentorServicePackage />
      </q-page>
    </q-page-container>
  </q-layout>
</template>
<script setup>
/*
  imports
*/
import { computed, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { pubStore } from 'stores/pub'
import Header from '../Header.vue'
import TeachingResource from './TeachingResource.vue'
import SelfStudy from './SelfStudy.vue'
import PremiumWorkshop from './PremiumWorkshop/Index.vue'
import MentorServicePackage from './MentorServicePackage/Index.vue'

const pub = pubStore()

import { pointStore } from 'stores/point'

const pStore = pointStore()

const isStudent = ref(pub.user?.roles?.includes('student'))

const route = useRoute()
const router = useRouter()

onMounted(async () => {
  await pStore.getClaimSetting()
})
</script>
<style lang="scss" scope>
.page-box {
  padding-left: 0 !important;
}
</style>
