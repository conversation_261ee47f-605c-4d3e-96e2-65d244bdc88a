<template>
  <section>
    <div class="text-h6 q-mt-md">Self-study content</div>
    <DetailView ref="child" :load="handleLoad" class="q-mt-sm">
      <div class="q-mt-sm" v-for="item in child.data" :key="item._id">
        <SessionBoard isPointMode  isStudentCenter isSelfStudy :session="item" />
      </div>
    </DetailView>
    <q-btn class="q-mt-sm" flat color="primary" no-caps label="More" icon-right="arrow_forward" :to="'/study/center/self?back=/point/mall&isPointMode=1'" />
  </section>
</template>
<script setup>
/*
  imports
*/
import {computed, onMounted, ref} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {pubStore} from 'stores/pub'
import DetailView from 'components/DetailView.vue'
import SessionBoard from 'components/SessionBoard.vue'

const pub = pubStore()

const child = ref(null)

const isStudent = ref(pub.user?.roles?.includes('student'))

const route = useRoute()
const router = useRouter()

const toShopDetail = (item) => {
  router.push({
    path: `/detail/session/${item?._id}`,
    query: {
      back: route.fullPath,
      isPointMode: 1,
    },
  })
}

const onLoad = async () => {
  child.value.onLoad()
}

const handleLoad = async () => {
  const query = {
    $limit: 3,
    $sort: {updatedAt: -1},
    del: false,
  }

  return App.service('session').get('indexStudy', {query})
}

onMounted(() => {
  onLoad()
})
</script>
