<template>
  <section>
    <div class="text-h6 q-mt-md">Service packages</div>
    <DetailView ref="child" :load="handleLoad" class="q-mt-sm">
      <div @click="toShopDetail(item)" class="q-mt-sm" v-for="item in child.data" :key="item._id">
        <PackageCard isPointMode :pack="item" category="featured" />
      </div>
    </DetailView>
    <q-btn
      class="q-mt-sm"
      flat
      color="primary"
      no-caps
      label="More"
      icon-right="arrow_forward"
      :to="'/home/<USER>/point/mall&isPointMode=1'" />
  </section>
</template>
<script setup>
/*
  imports
*/
import {computed, onMounted, ref} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import DetailView from 'components/DetailView.vue'
import PackageCard from 'components/PackageCard.vue'
import {pubStore} from 'stores/pub'

const pub = pubStore()

const child = ref(null)

const route = useRoute()
const router = useRouter()

const toShopDetail = (item) => {
  router.push({
    path: `/detail/booking/${item?._id}`,
    query: {
      back: route.fullPath,
      isPointMode: 1,
    },
  })
}

const onLoad = async () => {
  child.value.onLoad()
}

const handleLoad = async () => {
  const query = {
    $sort: {_id: -1},
    $limit: 3,
    $skip: 0,
    status: true,
    serviceRoles: 'mentoring',
    mentoringType: 'teacherTraining',
  }
  console.log('query', query)
  return App.service('service-pack').find({query})
}



onMounted(async () => {
  onLoad()
})
</script>
