<template>
  <section>
    <div class="text-h6 q-mt-md">Premium Workshops</div>
    <DetailView ref="child" :load="handleLoad" class="q-mt-sm">
      <div class="q-mt-sm" v-for="item in child.data" :key="item._id">
        <SessionBoard
          isPointMode
          isMyPurchased
          :isMyWorkshop="false"
          :isMyClass="false"
          :subtab="'featured'"
          :session="item"
          :isLectureRoom="false"
          :isSelfStudy="false"
          :categories="categoryOptions"
          isListing />
      </div>
    </DetailView>
    <q-btn
      class="q-mt-sm"
      flat
      color="primary"
      no-caps
      label="More"
      icon-right="arrow_forward"
      :to="'/study/purchased?tab=workshop&subtab=featured&type=?back=/point/mall&isPointMode=1'" />
  </section>
</template>
<script setup>
/*
  imports
*/
import {computed, onMounted, ref} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import DetailView from 'components/DetailView.vue'
import SessionBoard from 'components/SessionBoard.vue'
import {pubStore} from 'stores/pub'

const pub = pubStore()

const child = ref(null)

const route = useRoute()
const router = useRouter()

const categoryOptions = ref([])

const toShopDetail = (item) => {
  router.push({
    path: `/detail/session/${item?._id}`,
    query: {
      back: route.fullPath,
      isPointMode: 1,
    },
  })
}

const onLoad = async () => {
  child.value.onLoad()
}

const handleLoad = async () => {
  const query = {
    del: false,
    $sort: {_id: -1},
    $limit: 3,
    $skip: 0,
    type: {$in: ['taskWorkshop', 'unitCourses']},
    regDate: {$gte: new Date()},
    isLib: true,
    premium: true,
    status: 'scheduled',
  }

  console.log('query', query)
  return App.service('session').find({query})
}

import {subjectsStore} from 'stores/subjects'
const subjects = subjectsStore()
const getSubjects = async () => {
  const pdOptions = await subjects.getPubPdOptions()
  categoryOptions.value.push(...pdOptions.filter((v) => v.participants === 'students'))
  categoryOptions.value.unshift({label: 'Academic', value: 'academic'})
}

onMounted(async () => {
  await getSubjects()
  onLoad()
})
</script>
