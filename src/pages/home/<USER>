<template>
  <div class="rounded-borders-md shadow-3 bg-white overflow-hidden">
    <div class="q-pa-md text-h5 text-weight-medium">{{ title }}</div>
    <div class="row items-center q-px-md justify-between">
      <div class="col-xs-12 col-sm-7 col-md-7 q-pb-md">
        <q-tabs
          dense
          align="left"
          :breakpoint="0"
          indicator-color="primary"
          active-color="primary"
          v-model="subtab"
          inline-label
          mobile-arrows
          shrink
          @update:model-value="find(0)">
          <q-tab v-for="(item, index) in subtabs" :name="item.name" :label="item.label" :key="index" no-caps></q-tab>
        </q-tabs>
      </div>
    </div>
    <div class="q-px-md">
      <GeneralFilters v-model="filters" :options="[]" @update:modelValue="onFilterUpdate"></GeneralFilters>
    </div>
    <div class="column" style="height: 100vh">
      <template v-if="isEmpty(listData)">
        <NoData v-if="!loading" />
        <div v-else class="text-center q-pa-xl text-grey">
          <q-spinner-ball color="primary" size="2em" class="full-width" />
        </div>
      </template>
      <q-virtual-scroll v-else separator class="col q-px-sm full-width overflow-auto" :items="listData" @virtual-scroll="scrollFn">
        <template v-slot:after>
          <div v-if="list.data.length === list.limit" class="row justify-center q-my-md">
            <q-spinner-ball color="primary" size="2em" />
          </div>
          <div v-else class="q-pa-md text-grey text-center hidden">It's over</div>
        </template>
        <template v-slot="{item: o}">
          <div :key="o?._id" class="q-ma-sm q-mb-md" @click="onLinkItemClick(o)">
            <q-item clickable class="rounded-borders-md q-border-1">
              <div class="row full-width">
                <div class="col-xs-12 col-sm-4 q-pr-md">
                  <q-img
                    class="full-width rounded-borders-md"
                    :ratio="$q.screen.gt.xs ? 16 / 12 : 16 / 9"
                    fit="cover"
                    :src="hashToUrl(o?.image) || '/v2/img/no-img.png'" />
                </div>
                <div class="col-xs-12 col-sm-8">
                  <div class="row items-center">
                    <div class="col text-h6">{{ o?.name }}</div>
                    <q-badge
                      :color="getItemStatus(o) == 'Scheduled' ? 'yellow-2' : getItemStatus(o) == 'Ongoing' ? 'primary' : 'grey-2'"
                      :text-color="getItemStatus(o) == 'Scheduled' ? 'yellow-9' : getItemStatus(o) == 'Ongoing' ? 'white' : 'grey'"
                      class="text-weight-medium">
                      {{ getItemStatus(o) }}</q-badge
                    >
                  </div>
                  <div class="q-mt-sm row">
                    <div class="text-subtitle2 q-mr-sm q-mt-xs text-negative" v-if="o?.substituteServicePackSnapshot">
                      {{ o?.substituteServicePackSnapshot?.isOnCampus ? 'OnCampus' : 'Online' }}
                    </div>
                    <div class="col">
                      <SessionChips
                        :session="o"
                        :isListing="true"
                        :isStudentCenter="false"
                        :isMyPurchasedFeaturedTab="false"
                        :sessionStatus="getItemStatus(o)" />
                    </div>
                  </div>
                  <div class="q-mt-md text-grey-7">
                    <SessionTime :session="o" />
                  </div>
                  <div class="q-mt-md row items-center">
                    <div class="col">
                      <PubAvatar :src="o?.owner?.avatar" :title="o?.schoolInfo?.name" />
                      <span class="text-grey-7 q-ml-sm">
                        {{ nameFormatter(o?.owner) }}
                        <q-tooltip max-width="200px">
                          {{ nameFormatter(o?.owner) }}
                        </q-tooltip>
                      </span>
                    </div>
                    <div class="text-yellow-9 q-mr-md text-bold vertical-middle">
                      <q-icon name="o_monetization_on" size="xs" />
                      My income
                      <q-tooltip class="text-subtitle2"> Teaching hour income——USD {{ o?.substituteHourlyRate || 0 }} </q-tooltip>
                    </div>
                    <q-btn label="Enter class" color="primary" class="q-mr-sm" size=".8rem" rounded no-caps></q-btn>
                  </div>
                </div>
              </div>
            </q-item>
            <!-- <SessionBoard
              :price="getPrice(o)"
              :isMyPurchased="isMyPurchased"
              :isMyWorkshop="isMyWorkshop"
              :isMyClass="isMyClass"
              :subtab="subtab"
              :tab="tab"
              :categories="categoryOptions"
              :session="o"
              :isPointMode="isPointMode"
              isListing
              @enrolled="onEnrolled"
              @change="resetFn()"
              :onLinkItemClick="onLinkItemClick"></SessionBoard> -->
          </div>
        </template>
      </q-virtual-scroll>
    </div>
  </div>
</template>
<script setup>
/*
imports
*/
import {ref, onMounted, computed} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {pubStore} from 'stores/pub'
import {date} from 'quasar'

import GeneralFilters from 'components/GeneralFilters.vue'
import {getHourRate} from 'src/pages/sys/package/const'
import useTeacherVerificationConfig from 'src/composables/account/teacher-verification/useTeacherVerificationConfig'
import useSubject from 'src/composables/account/academic/useSubject'
import SessionChips from 'components/SessionChips.vue'
import SessionTime from 'components/SessionTime.vue'
import nameFormatter from 'src/utils/formatters/nameFormatter.js'
/*
consts
*/

const title = ref('Substitute')
const route = useRoute()
const router = useRouter()
const pub = pubStore()
const filters = ref({})
const subtab = ref(route.query.subtab || 'scheduled')
const loading = ref(false)

const list = ref({})
const listData = ref([])

const virtualListIndex = ref(1)

const campusLocation = ref([])

const {sysEssayData, sysTeacherTrainingSubjectData} = useSubject()
const {allTeacherVerificationConfig} = useTeacherVerificationConfig()

/*
computed
*/

const subtabs = computed(() => {
  const defaultTabs = [
    {name: 'scheduled', _label: 'Scheduled', label: 'Scheduled'},
    {name: 'ongoing', _label: 'Ongoing', label: 'Ongoing'},
    {name: 'ended', _label: 'Ended', label: 'Ended'},
  ]

  //let _tabs = pub.user?.school ? defaultTabs : [{name: 'booked', _label: 'Booked', label: 'Booked'}, ...defaultTabs]

  const _tabs = defaultTabs

  const total = list.value?.total ?? 0
  return _tabs.map((item) => {
    if (item.name === subtab.value) {
      return {...item, label: item.label + `(${total})`}
    }
    return item
  })
})

/*
methods
*/

const getItemStatus = (item) => {
  const now = new Date()
  let _status = ''
  if (item.status == 'close') {
    _status = 'Ended'
  } else if (item.start && new Date(item.start) >= now) {
    _status = 'Scheduled'
  } else if (item.start && new Date(item.start) < now) {
    _status = 'Ongoing'
  }
  return _status
}

const scrollFn = async ({index, to}) => {
  virtualListIndex.value = index
  if (index !== to) return // console.warn(index, to, direction)
  const {limit = 10, skip = 0, data} = list.value
  if (Acan.isEmpty(data)) return // has last page
  console.warn('need load more')
  find(skip + limit)
}

const getAllCampusLocation = async () => {
  const query = {
    $limit: 1000,
    $skip: 0,
    $sort: {updatedAt: -1},
    archive: false,
  }
  const res = await App.service('campus-location').find({query})
  if (res) {
    campusLocation.value = res?.data || []
  }
}

const duration = (item) => {
  let minutes = date.getDateDiff(new Date(item.end), new Date(item.start), 'minutes')
  return minutes
}

const campusPrice = (servicePack, userPack, minutes) => {
  let hourlyRate = 0
  const {gradeGroup = []} = servicePack
  const currentCampus = campusLocation.value.find((e) => e.city == userPack?.city)
  const totalHour = (minutes + currentCampus?.compensationHour * 60) / 60
  const currentCampusHourRate = servicePack?.qualification === 'experiencedTeacher' ? currentCampus?.experienceRate : currentCampus?.tutorRate
  hourlyRate = currentCampusHourRate[gradeGroup?.[0]] || 0
  const totalPrice = (totalHour * hourlyRate).toFixed(2)

  return parseInt(totalPrice)
}

const getPrice = (item) => {
  const {substituteServicePackSnapshot = {}, substituteServicePackUserSnapshot = {}} = item

  if (!substituteServicePackSnapshot) {
    return 0
  }

  try {
    let price = 0
    const minutes = duration(item)
    if (substituteServicePackSnapshot?.isOnCampus) {
      price = campusPrice(substituteServicePackSnapshot, substituteServicePackUserSnapshot, duration(item))
    } else {
      let hourRateList = []
      const commonConfig = {
        allTeacherVerificationConfig: allTeacherVerificationConfig.value,
        sysEssayData: sysEssayData.value,
        sysTeacherTrainingSubjectData: sysTeacherTrainingSubjectData.value,
        mentoringType: substituteServicePackSnapshot?.mentoringType,
        qualification: substituteServicePackSnapshot?.qualification,
      }
      if (substituteServicePackSnapshot?.mentoringType === 'academic') {
        substituteServicePackSnapshot?.subject?.forEach((subject) => {
          const rate = getHourRate({
            ...commonConfig,
            curriculum: substituteServicePackSnapshot?.curriculum,
            subject: subject,
          })
          hourRateList.push(rate)
        })
      } else if (substituteServicePackSnapshot?.mentoringType === 'steam') {
        substituteServicePackSnapshot?.topic?.forEach((topic) => {
          const rate = getHourRate({
            ...commonConfig,
            topicId: topic?._id,
          })
          hourRateList.push(rate)
        })
      }

      if (hourRateList.length) {
        const hourRate = Math.max(...hourRateList) || 0
        price = (hourRate * minutes) / 60
      }
    }
    return price
  } catch (e) {
    console.error(e)
    return 0
  }
}

const find = async ($skip) => {
  if ($skip === 0) {
    resetData()
  }

  const query = {
    del: false,
    $sort: {updatedAt: -1},
    $limit: 10,
    $skip,
    $substitute: 1,
  }
  loading.value = true
  // if (subtab.value == 'orders') {
  //   query.status = 'scheduled'
  //   query.substituteTeacherStatus = 0
  //   query.substitutePush = {
  //     $in: [pub.user._id],
  //   }
  //   query.substituteExclude = {
  //     $nin: [pub.user._id],
  //   }
  // } else {
  //   query.status = subtab.value
  //   query.substituteTeacher = pub.user._id
  //   query.substituteTeacherStatus = 1
  // }

  if (filters.value?.search) {
    query.$or = [
      {
        name: {
          $search: filters.value?.search,
        },
      },
    ]
  }

  query.status = subtab.value
  query.substituteTeacher = pub.user._id
  query.substituteTeacherStatus = 1

  list.value = await App.service('session').find({query})
  console.log('list', list.value)
  if (list.value?.data?.length) {
    listData.value.push(...list.value.data)
  }

  loading.value = false
  router.replace({
    query: {
      tab: 'substitute',
      subtab: subtab.value,
    },
  })
}

const resetData = () => {
  listData.value = []
  list.value = {}
}

const onLinkItemClick = (item) => {
  // if (subtab.value == 'orders') {
  //   const in3Hours = date.getDateDiff(new Date(item.start), new Date(), 'hour') < 3
  //   if (in3Hours) {
  //     $q.dialog({
  //       component: AlertDialog,
  //       componentProps: {
  //         title: 'It is currently less than or equal to 3 hours before the start of the class and you are unable to order.',
  //       },
  //     }).onOk(async () => {
  //       find(0)
  //     })
  //     return
  //   }
  // }

  console.log(item)
  router.push({
    path: `/detail/session/${item?._id}`,
    query: {
      back: route.fullPath,
      subType: subtab.value == 'orders' ? 'order' : undefined,
      subform: 'subTeacher',
    },
  })
}

const onFilterUpdate = () => {
  find(0)
}

/*life Cycle*/
onMounted(async () => {
  getAllCampusLocation()
})
</script>
<style lang="sass" scope></style>
