<template>
  <q-layout view="hHh LpR fFf"
    ><div class="shadow-1" style="background-color: #fffbfe; position: sticky; top: 0; z-index: 1000000">
      <q-toolbar>
        <q-btn flat round icon="chevron_left" @click="goBack" />
        <q-toolbar-title class="text-bold">Task details</q-toolbar-title>
      </q-toolbar>
    </div>
    <div class="q-py-xs" style="background-color: #f5f5f5">
      <q-page-container class="pc-md">
        <q-page>
          <ProductDetail :isBuying="true" :taskData="taskData" />

          <div v-if="taskData.associatedTaskStatus !== 'refunded' && route.query.tab !== 'taskManagement'">
            <q-btn
              v-if="route.query.subtab === 'unassigned' && isCreditDeficit"
              class="q-mx-sm q-mb-md full-width bg-white text-primary"
              rounded
              no-caps
              label="Purchase More"
              @click="handlePurchaseMore" />
            <q-btn
              v-else-if="route.query.subtab === 'unassigned'"
              class="q-mx-sm q-mb-md full-width bg-teal-4 text-white"
              rounded
              no-caps
              label="Assign"
              @click="handleAssign"
              :disable="taskData.asssociatedTaskStatus === 'refunded'" />
            <q-btn
              v-else-if="isBuyRemaining"
              class="q-mx-sm q-mb-md full-width bg-teal-4 text-white"
              rounded
              no-caps
              label="Buy Remaining Sections"
              @click="handleBuyRemaining"
              :disable="taskData.asssociatedTaskStatus === 'refunded'" />
          </div>

          <Sections />
          <div v-if="route.query.subtab === 'ongoing'" class="row justify-center q-mt-lg">
            <q-btn
              class="q-mx-sm q-mb-md text-primary full-width"
              style="background-color: #ffffff"
              no-caps
              text-color="primary"
              rounded
              label="Cancel"
              @click="handleCancelTask" />
          </div>
        </q-page>
      </q-page-container>
    </div>
  </q-layout>
</template>

<script setup>
//imports
import {ref, onMounted, computed} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {servicePackageStore} from 'stores/service-package'
import ProductDetail from 'src/components/ServiceTask/ProductDetail.vue'
import Sections from 'src/components/ServiceTask/Sections.vue'
import AssignTeacher from './study/components/popups/AssignTeacher.vue'
import CancelTaskStudent from './study/components/popups/CancelTaskStudent.vue'
import CancelTaskTeacher from './study/components/popups/CancelTaskTeacher.vue'
import {serviceTaskStore} from 'stores/serviceTask'

//state
const route = useRoute()
const router = useRouter()
const servicePackage = servicePackageStore()
const serviceTask = serviceTaskStore()
const isCreditDeficit = ref(null)
const isSys = computed(() => route.path.includes('sys'))
const taskData = ref({})
const isBooking = computed(() => route.path.includes('/booking/'))
const parent = ref({})
const assignDetails = ref(null)
const bookingId = ref(null)

function goBack() {
  if (route.query.back) {
    router.replace(route.query.back)
  } else {
    router.back()
  }
}

const isBuyRemaining = computed(() => {
  return serviceTask.sections?.some((sec) => sec.status === 'pending' && sec.availableCredits === 0)
})

async function main() {
  if (route.params?.id) {
    if (isSys.value) {
      const packageData = await servicePackage.getOnePackage(route.query?.id)
      if (packageData) {
        const packCurriculum = {}
        let gradeGroup = {
          label: '',
          grades: packageData.gradeGroup,
        }
        servicePackage.loadData(packageData, packCurriculum, gradeGroup)
      }
    } else if (isBooking.value && route.query?.id) {
      const rs = await App.service('service-pack').get(route.query?.id)
      console.log('rs value qwerty', rs)
      taskData.value = rs
    } else {
      const [rs, rs1] = await Promise.all([App.service('service-pack-user').get(route.params?.id), serviceTask.getSections(route.params?.id)])
      console.log('rs1', rs1)
      if (route.query.subtab === 'unassigned') {
        // parent.value = await App.service('service-pack-user').get(rs.pid)
        parent.value = rs.pid
        if (rs.sectionCreditDeficit?.points) {
          isCreditDeficit.value = rs.sectionCreditDeficit
        }
        console.log('fkkfkf', rs.sectionCreditDeficit?.points, isCreditDeficit.value)
      } else if (route.query.subtab === 'ongoing') {
        assignDetails.value = rs.associatedServicer
        bookingId.value = rs.associatedBooking
      }

      taskData.value = {...rs.snapshot, associatedTaskStatus: rs.associatedTaskStatus}
    }
  }
}

async function handleCancelTask() {
  if (route.query.tab === 'myAssociatedTask') {
    let isAbove12hours = false
    if (assignDetails.value?.assignedAt) {
      const assignedAt = new Date(assignDetails.value?.assignedAt)
      const now = new Date()

      const diffInMs = now - assignedAt
      const diffInHours = diffInMs / (1000 * 60 * 60)
      console.log('flfl', diffInHours)
      if (diffInHours >= 12) {
        isAbove12hours = true
      }
    }
    $q.dialog({
      component: CancelTaskStudent,
      componentProps: {isAbove12hours, bookingId: bookingId.value, persistent: true},
    })
      .onOk(() => {})
      .onCancel(() => {})
  } else {
    const completedSections = serviceTask.sections?.filter((sec) => sec.status === 'completed')
    const isDeduction = completedSections?.length
    $q.dialog({
      component: CancelTaskTeacher,
      componentProps: {isDeduction, bookingId: bookingId.value, persistent: true},
    })
      .onOk(() => {})
      .onCancel(() => {})
    console.log('dkkfk', completedSections)
  }
}

function handlePurchaseMore() {
  router.replace({
    path: `/order/confirm/section_top_up/${isCreditDeficit.value.sectionId}`,
    query: {
      back: route.fullPath,
    },
  })
  console.log('fkfkkf')
}

function handleBuyRemaining() {
  router.replace({
    path: `/order/confirm/remaining_sections/${route.params.id}`,
    query: {
      back: route.fullPath,
    },
  })
  console.log('fkfkkf')
}

function handleAssign() {
  $q.dialog({
    component: AssignTeacher,
    componentProps: {id: route.params.id, parent: parent.value},
  })
    .onOk(() => {})
    .onCancel(() => {})
}

onMounted(async () => {
  $q.loading.show()
  if (isSys.value || isBooking.value) {
    try {
      servicePackage.serviceRoles = 'service-tasks'
      await servicePackage.init()
      console.log('fjgfjgjgjgj', servicePackage.serviceRoles, servicePackage.state)

      if (servicePackage.serviceRoles || route.params?.id) {
        await main()
        await sleep(500)
      }
    } catch (err) {
      console.error('Error loading service package', err)
    } finally {
      $q.loading.hide()
    }
  } else {
    await servicePackage.init()
    await main()
  }
  $q.loading.hide()
})
</script>
