<template>
  <q-dialog ref="dialogRef" @hide="onDialogHide" @before-show="beforeDialogShow">
    <q-card class="q-dialog-plugin create-new-dialog">
      <q-card-section v-if="!sessionType && ['task', 'pdTask'].includes(mode)">
        <div class="q-pb-md">Session type</div>
        <q-option-group inline :options="sessionTypeOptions" type="radio" v-model="sessionTypeModel" />
      </q-card-section>
      <q-card-section v-if="['pdTask', 'pdUnit'].includes(mode)">
        <div class="q-pb-md">Choose service type</div>
        <div class="row q-col-gutter-md">
          <div class="col-md-6 col-xs-12 col-sm-12" v-for="(o, i) in serviceList" :key="i">
            <q-btn
              align="between"
              class="full-width"
              style="height: 3rem"
              no-caps
              :outline="!isActive(o)"
              :color="isActive(o) ? 'teal' : 'white'"
              :text-color="isActive(o) ? 'white' : 'grey-7'"
              @click="subjectChoose(o)"
              :icon-right="isActive(o) ? 'done' : ''"
              :label="o.label"></q-btn>
          </div>
        </div>
      </q-card-section>
      <q-separator></q-separator>
      <q-card-actions align="right" class="q-px-md">
        <q-btn rounded outline color="primary" label="Cancel" @click="onDialogCancel" no-caps />

        <q-btn rounded :disabled="!selected && mode !== 'task'" color="primary" label="OK" @click="onOKClick" no-caps />
      </q-card-actions>
      <q-inner-loading :showing="loading">
        <q-spinner-ball size="2em" color="primary" />
      </q-inner-loading>
    </q-card>
  </q-dialog>
</template>

<script setup>
import {ref} from 'vue'
import {useDialogPluginComponent} from 'quasar'
import {subjectsStore} from 'stores/subjects'
const subjects = subjectsStore()

import {TaskSessionTypes} from 'src/boot/const'
const sessionTypeOptions = ref(TaskSessionTypes.map(({label, title}) => ({label: title, value: label})))
const sessionTypeModel = ref('live')

const selected = ref(null)
const loading = ref(false)

const props = defineProps({
  mode: {
    type: String,
    default: '',
  },
  sessionType: {
    type: String,
    default: '',
  },
})

defineEmits([...useDialogPluginComponent.emits])

const {dialogRef, onDialogHide, onDialogOK, onDialogCancel} = useDialogPluginComponent()

const onOKClick = () => {
  const data = {}
  if (['task', 'pdTask'].includes(props.mode)) data.sessionType = props.sessionType || sessionTypeModel.value
  if (['pdTask', 'pdUnit'].includes(props.mode)) {
    data.service = {type: selected.value.value, participants: selected.value.participants}
    data.outlineSubjects = [selected.value.label]
  }
  onDialogOK(data)
}
const serviceList = ref([])
const beforeDialogShow = async () => {
  if (!['pdTask', 'pdUnit'].includes(props.mode)) return
  loading.value = true
  serviceList.value = await subjects.getPubPdOptions()
  loading.value = false
}

const isActive = (subject) => {
  return selected.value?.value == subject.value
}

const subjectChoose = (o) => {
  selected.value = o
}
</script>

<style lang="sass" scoped>
.create-new-dialog
  body.screen--md &
   min-width: 30rem
</style>
