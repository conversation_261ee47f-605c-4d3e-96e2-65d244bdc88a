<template>
  <q-dialog ref="dialogRef">
    <q-card class="dialog-height q-pa-md">
      <template v-if="!one._id && !one.sessionType">
        <div class="text-subtitle1">Session type</div>
        <q-card-section class="q-mt-none">
          <q-option-group inline :options="sessionTypeOptions" type="radio" v-model="sessionTypeModel" />
        </q-card-section>
      </template>
      <div class="text-subtitle1" v-if="one._id">Change template</div>
      <div class="text-subtitle1" v-else>Choose {{ chooseCurrType === 'academic' ? 'template' : 'service type' }}</div>
      <div v-if="loading">
        <div class="q-pa-md flex flex-center">
          <q-spinner size="40px" color="primary" />
        </div>
      </div>
      <div v-else>
        <q-card-section class="scroll">
          <div class="flex items-center" v-if="!one.curriculum">
            <q-option-group inline :options="typeOptions" type="radio" v-model="chooseCurrType" />
          </div>
          <q-select
            v-if="chooseCurrType === 'academic'"
            v-model="selectedOption"
            :options="tplOptions"
            option-label="curricName"
            option-value="_id"
            label="Select Template"
            filled
            emit-value
            map-options
            class="q-my-md">
            <template v-slot:option="{opt, itemProps}">
              <q-item v-bind="itemProps" class="column rounded-borders q-ma-sm q-pa-sm" style="border: 1px solid #ccc">
                <div class="">
                  {{ opt.curricName }}
                  <q-chip color="grey" dense outline class="q-my-sm text-capitalize" text-color="white">Task</q-chip>
                </div>
                <div class="text-caption">
                  {{ opt.name }}
                  <q-chip v-if="opt._id == defaultTplId" color="primary" dense outline class="q-my-none" text-color="white">Default</q-chip>
                </div>
              </q-item>
            </template>
            <template v-slot:selected>
              <q-item v-if="selectedOption" v-bind="academicOption" class="full-width column rounded-borders q-ma-sm q-pa-sm" style="border: 1px solid #ccc">
                <div class="">
                  {{ academicOption.curricName }}
                  <q-chip color="grey" dense outline class="q-my-sm text-capitalize" text-color="white">Task</q-chip>
                </div>
                <div class="text-caption">
                  {{ academicOption.name }}
                  <q-chip v-if="academicOption._id == defaultTplId" color="primary" dense outline class="q-my-none" text-color="white">Default</q-chip>
                </div>
              </q-item>
            </template>
          </q-select>
          <q-select v-else v-model="selectedType" emit-value map-options :options="serviceList" label="Select Service Type" filled class="q-my-md" />
        </q-card-section>
        <q-card-actions align="center">
          <div class="flex justify-between items-center q-mb-md full-width">
            <q-btn
              unelevated
              rounded
              class="text-primary bg-white"
              label="Cancel"
              icon="arrow_back"
              style="border: 1px solid #ccc; width: calc(50% - 1rem)"
              @click="onDialogCancel"
              no-caps />
            <q-btn
              style="width: calc(50% - 1rem)"
              unelevated
              rounded
              color="teal-4"
              :disable="disable"
              label="Confirm"
              icon="done"
              @click="onOKClick"
              no-caps />
          </div>
          <q-btn
            v-if="!pub.currentSchoolId || pub.isSchoolAdmin"
            class="full-width"
            unelevated
            rounded
            color="teal-4"
            label="Go to template setting"
            :to="{path: `/account/academic-setting/subject/templateSetting`, query: {back: route.fullPath}}"
            no-caps />
        </q-card-actions>
      </div>
    </q-card>
  </q-dialog>
</template>

<script setup>
import {useDialogPluginComponent} from 'quasar'
import {onMounted, ref, computed} from 'vue'
import {pubStore} from 'stores/pub'
const pub = pubStore()
import {subjectsStore} from 'stores/subjects'
const subjects = subjectsStore()

import {curriculumStore} from 'stores/curriculum'
const curriculum = curriculumStore()

import {TaskSessionTypes} from 'src/boot/const'
const sessionTypeOptions = ref(TaskSessionTypes.map(({label, title}) => ({label: title, value: label})))
const sessionTypeModel = ref('live')

const {dialogRef, onDialogOK, onDialogCancel} = useDialogPluginComponent()

import {useRoute} from 'vue-router'
const route = useRoute()
const typeOptions = ref([
  {label: 'Academic', value: 'academic', disable: false},
  {label: 'Service', value: 'service', disable: false},
])
const academicOption = computed(() => {
  if (!selectedOption.value) return {}
  return tplOptions.value.find((v) => v._id === selectedOption.value)
})

const props = defineProps({
  one: {
    type: Object,
    default: () => ({}),
  },
})
const chooseCurrType = ref('academic')

const loading = ref(true)
const serviceList = ref([])
const selectedOption = ref(null)
const selectedType = ref(null)
const tplOptions = ref([])
const defaultTplId = ref(null)

onMounted(async () => {
  loading.value = true
  serviceList.value = await subjects.getPubPdOptions()

  tplOptions.value = await curriculum.getTplOptions('task')

  defaultTplId.value = await curriculum.getTaskTplDef()
  // 限定大纲
  if (props.one.curriculum) {
    if (props.one.curriculum !== 'pd') {
      const o = tplOptions.value.find((v) => v.curriculum === props.one.curriculum)
      if (o) selectedOption.value = o._id
    } else {
      chooseCurrType.value = 'service'
      selectedType.value = props.one.service.type
    }
  } else if (tplOptions.value.find((v) => v._id === defaultTplId.value)) {
    selectedOption.value = defaultTplId.value // 创建的时候自动取默认模板
  }
  // 不存在大纲模板
  if (Acan.isEmpty(tplOptions.value)) {
    typeOptions.value[0].disable = true
    if (!props.one.curriculum) chooseCurrType.value = 'service'
  }

  const currMap = await curriculum.getOptionsMap()
  // 不存在Service大纲
  if (!currMap.pd) {
    typeOptions.value[1].disable = true
  }
  loading.value = false
})

const disable = computed(() => {
  if (chooseCurrType.value === 'service') return !selectedType.value
  else return !selectedOption.value
})

const onOKClick = async () => {
  const post = {}
  const sessionType = props.one.sessionType || sessionTypeModel.value
  if (chooseCurrType.value === 'service') {
    const {participants, label} = serviceList.value.find((v) => v.value === selectedType.value)
    post.$set = {
      sessionType,
      curriculum: 'pd',
      service: {type: selectedType.value, participants},
      outlineSubjects: [label],
    }
    post.$unset = {tpl: ''}
  } else {
    const curriculum = tplOptions.value.find((v) => v._id === selectedOption.value).curriculum
    post.$set = {tpl: selectedOption.value, curriculum, sessionType}
  }
  if (props.one._id) await App.service('unit').patch(props.one._id, post)
  onDialogOK({data: post.$set})
}
</script>

<style scoped>
.dialog-height {
  width: clamp(32rem, 50%, 60vh);
  overflow-y: auto;
}
</style>
