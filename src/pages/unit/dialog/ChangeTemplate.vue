<template>
  <q-dialog :maximized="$q.screen.lt.sm" ref="dialogRef" @hide="onDialogHide" @before-show="beforeDialogShow">
    <q-card class="q-dialog-plugin criteria-dialog" :class="{'my-card': $q.screen.gt.xs}">
      <q-inner-loading :showing="loading" />
      <template v-if="!loading">
        <q-toolbar>
          <q-btn class="lt-sm" flat round dense icon="arrow_back_ios" v-close-popup />
          <q-toolbar-title>
            <template v-if="defaultSettingMode"> Choose your default template </template>
            <template v-else>
              {{ listMode ? 'Choose ' : 'Change ' }}Template
              <span v-if="!listMode" class="text-caption"> Please select a template for further content editing. </span>
            </template>
          </q-toolbar-title>
          <q-btn class="gt-xs" flat round dense icon="close" v-close-popup />
        </q-toolbar>
        <div class="row q-mb-md">
          <div class="col q-pl-lg text-bold q-py-sm">
            <PubAvatar :src="pub.currentSchoolId ? pub.user.schoolInfo.logo : pub.user.avatar" size="sm" />
            <span class="q-pl-sm">
              {{ pub.currentSchoolId ? nameFormatter(pub.user.schoolInfo) : nameFormatter(pub.user) }}
            </span>
          </div>
          <q-btn
            rounded
            no-caps
            outline
            :disable="defaultSettingMode"
            class="q-mr-md"
            label="Default setting"
            color="primary"
            @click="defaultSettingMode = true" />
          <q-btn
            rounded
            no-caps
            class="q-mr-md"
            label="Manage Templates"
            v-if="!pub.currentSchoolId || pub.isSchoolAdmin"
            color="primary"
            :to="{path: '/account/academic-setting/subject/templateSetting', query: {back: $route.fullPath}}" />
        </div>
        <q-separator />
        <q-card-section style="max-height: 50vh; min-height: 50vh" class="scroll" v-if="!loading">
          <NoData
            v-if="Acan.isEmpty(list)"
            messageColor="grey"
            :message="
              !pub.currentSchoolId
                ? 'No templates set under current account, please set a template first.'
                : pub.isSchoolAdmin
                  ? 'No templates set under current school account, please go to template setting to set a template.'
                  : 'No templates set under current school account, please contact your school admin to set a template.'
            "></NoData>
          <template v-for="(tpl, i) in list" :key="i">
            <q-card class="full-width q-mb-md q-pa-md unelevated" no-ripple no-caps :id="tpl._id">
              <!-- <div class="text-body1">{{ tpl.name }}</div> -->
              <div class="q-pl-md">
                {{ tpl.curricName }}
                <q-chip color="grey" dense outline class="q-my-sm text-capitalize" text-color="white">{{ tpl.mode }}</q-chip>
                <q-chip v-if="tpl._id == defaultTplId" color="primary" dense outline class="q-my-sm" text-color="white">Default</q-chip>
                <q-btn v-else-if="defaultSettingMode" dense outline rounded no-caps color="primary" class="q-py-none q-my-none" @click="setAsDefault(tpl)">
                  <span class="q-px-xs"> Set as default </span>
                </q-btn>
              </div>
              <div class="row">
                <div class="">
                  <div class="q-pl-lg" v-if="defaultSettingMode">{{ tpl.name || '' }}</div>
                  <q-radio v-else v-model="selectedTpl" :val="tpl._id" :label="tpl.name" />
                </div>
              </div>
            </q-card>
          </template>
        </q-card-section>
        <q-separator />

        <q-card-actions align="right" v-if="defaultSettingMode">
          <q-btn rounded class="q-mx-sm" icon="arrow_back" no-caps color="grey" @click="defaultSettingMode = false">Cancel</q-btn>
          <q-btn rounded class="q-mx-sm" :disable="!defaultTplIsSet" icon="check" no-caps color="primary" @click="defaultSettingMode = false">Confirm</q-btn>
        </q-card-actions>
        <q-card-actions align="right" v-else-if="listMode">
          <q-btn rounded class="q-mx-sm" :disable="!selectedTpl" icon="check" no-caps color="primary" @click="onConfirmClick">Confirm</q-btn>
        </q-card-actions>
        <div class="q-pa-md" v-else>
          <div class="row q-col-gutter-md">
            <div class="col-sm-6 col-12">
              <q-btn class="full-width" :disable="!selectedTpl || one.tpl == selectedTpl" rounded no-caps color="primary" @click="onSaveFn(false)">
                Edit on original content
              </q-btn>
            </div>
            <div class="col-sm-6 col-12">
              <q-btn class="full-width" :disable="!selectedTpl || one.tpl == selectedTpl" rounded no-caps color="primary" @click="onSaveFn(true)">
                Duplicate and edit on the copied content
              </q-btn>
            </div>
          </div>
        </div>
      </template>
    </q-card>
  </q-dialog>
</template>

<style scoped>
.my-card {
  min-width: 750px;
}
</style>
<script setup>
import {ref, computed} from 'vue'
import nameFormatter from 'src/utils/formatters/nameFormatter.js'
import {useDialogPluginComponent} from 'quasar'
import {pubStore} from 'stores/pub'
const pub = pubStore()
import {useRouter} from 'vue-router'
const router = useRouter()
import {curriculumStore} from 'stores/curriculum'
const curriculum = curriculumStore()
defineEmits([...useDialogPluginComponent.emits])

const {dialogRef, onDialogHide, onDialogOK} = useDialogPluginComponent()

const props = defineProps(['one', 'listMode'])
const list = ref([])
const defaultSettingMode = ref(false)
const defaultTplId = ref(null)
const selectedTpl = ref(null)
const loading = ref(true)

const setAsDefault = async (item) => {
  $q.loading.show()
  await curriculum.setTplDef(item.group)
  defaultTplId.value = item.mode === 'unit' ? await curriculum.getUnitTplDef() : await curriculum.getTaskTplDef()
  $q.loading.hide()
}
const defaultTplIsSet = computed(() => {
  return !!list.value.find((v) => v._id === defaultTplId.value)
})

const beforeDialogShow = async () => {
  const {_id, mode, tpl} = props.one
  list.value = await curriculum.getTplOptions(mode)
  defaultTplId.value = mode === 'unit' ? await curriculum.getUnitTplDef() : await curriculum.getTaskTplDef()
  loading.value = false
  if (!defaultTplIsSet.value)
    defaultSettingMode.value = true // 未设置，默认打开设置
  else if (!_id) {
    // 已经设置了default模板，跳过模板选择步骤，直接创建
    selectedTpl.value = defaultTplId.value
    return onConfirmClick()
  }
  selectedTpl.value = tpl
}
const onConfirmClick = () => {
  const o = list.value.find((v) => v._id === selectedTpl.value)
  const curriculum = o.curriculum === 'others' ? o.curricId : o.curriculum
  const $set = {
    tpl: selectedTpl.value,
    curriculum,
  }
  onDialogOK($set)
}

const onSaveFn = async (isCopy = false) => {
  if (!selectedTpl.value) return
  const o = list.value.find((v) => v._id === selectedTpl.value)
  const curriculum = o.curriculum === 'others' ? o.curricId : o.curriculum
  if (!isCopy) {
    const $set = {
      tpl: selectedTpl.value,
      curriculum,
    }
    await App.service('unit').patch(props.one._id, $set)
    onDialogOK(selectedTpl.value)
    return setTimeout(() => location.reload(), 500)
  }
  const {_id, name} = props.one
  $q.dialog({
    title: 'Confirm',
    message: `Duplicate content "${name}"?`,
    cancel: true,
  }).onOk(async () => {
    $q.loading.show()
    let name = name + ' - Copy 1'
    if (!isNaN(parseInt(name?.match(/ Copy (\d)$/)?.[1]))) {
      let copyText = ' Copy ' + (parseInt(name?.match(/ \d+$/)?.[0]) + 1)
      name = name.replace(/ Copy \d+$/, copyText)
    }
    await App.service('unit').patch('copy', {_id, name, tpl: selectedTpl.value, curriculum})
    $q.notify({type: 'info', message: 'Successful'})
    onDialogOK(selectedTpl.value)
    router.replace('/home/<USER>')
  })
}
</script>
