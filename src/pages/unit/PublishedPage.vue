<template>
  <q-page class="column full-height" style="padding: 1rem 1rem 0.5rem 1rem" :class="route.query.header === '0' ? '' : 'pc-max'">
    <q-card class="column col full-height full-width">
      <div class="q-pa-md text-h5 text-bold">My published</div>
      <div class="row full-width q-px-md q-pb-md">
        <q-tabs
          :outside-arrows="$q.screen.lt.sm"
          inline-label
          mobile-arrows
          shrink
          v-model="tab"
          stretch
          active-class="text-teal"
          class="text-bold"
          @update:model-value="tabFn">
          <q-tab v-for="(o, i) in tabs" :key="i" :name="o.value" no-caps>
            {{ o.label }}
            <template v-if="o.value === tab && !loading">({{ list.total }})</template>
          </q-tab>
        </q-tabs>
        <q-space />
        <div class="row col-xs-12 col-sm-3 col-md-3 justify-end">
          <q-btn color="primary" rounded dense no-caps padding="0 1rem" icon="add" label="Add" @click="onAddClick" />
        </div>
      </div>
      <q-toolbar class="q-px-md q-pb-sm">
        <SearchFilter :items="['serviceType']" :sortOptions="tab === 'prompt' ? sortOptions : null" @change="filterFn" @search="searchFn" />
        <q-space></q-space>
        <div v-if="pagination?.total" class="text-grey-7 text-right" style="min-width: 4.6rem">
          {{ pagination.start }} - {{ pagination.end }}, of {{ pagination.total }}
        </div>
      </q-toolbar>
      <NoData v-if="!loading && isEmpty(listData)" />
      <q-virtual-scroll v-else separator class="col full-width overflow-auto" :items="listData" @virtual-scroll="scrollFn">
        <template v-slot:after>
          <div v-if="list.data?.length === list.limit" class="row justify-center q-my-md">
            <q-spinner-dots color="primary" size="40px" />
          </div>
          <div v-else class="q-pa-md text-grey text-center">It's over</div>
        </template>
        <template v-slot="{item: o}">
          <q-item>
            <ListItemCard v-if="tab === 'published'" :doc="o" dense />
            <PromptItemCard v-if="tab === 'prompt'" :doc="o" dense />
          </q-item>
        </template>
      </q-virtual-scroll>
    </q-card>
    <ImportDialog :show="showImportDialog" published type="list" @hide="onImportDialogHide"></ImportDialog>
  </q-page>
</template>
<script setup>
import {ref, onMounted, computed, watch} from 'vue'
import PromptItemCard from './PromptItemCard.vue'
import ListItemCard from './ui/ListItemCard.vue'
import SearchFilter from './SearchFilter.vue'
import ImportDialog from 'components/ImportDialog.vue'
import {unitStore} from 'stores/unit'
const unit = unitStore()
unit.isLib = true
import {pubStore} from 'stores/pub'
const pub = pubStore()
import {useRoute, useRouter} from 'vue-router'
const route = useRoute()
const router = useRouter()
const loading = ref(false),
  listData = ref([]),
  list = ref({}),
  query = ref({$skip: 0}),
  tab = ref(route.query.tab || 'published'),
  tabs = ref([
    {label: 'Prompts', value: 'prompt'},
    {label: 'Teaching resource', value: 'published'},
  ])

const sortOptions = [
  {label: 'Latest', value: 'latest'},
  {label: 'Most used', value: 'used'},
]

const showImportDialog = ref(false)
const onImportDialogHide = (obj) => {
  showImportDialog.value = false
  if (obj) router.push(obj)
}
const onAddClick = () => {
  if (tab.value == 'published') {
    showImportDialog.value = true
  } else {
    router.push({path: '/com/published/import/1', hash: '#prompts'})
  }
}

let key = ''
function searchFn(data) {
  key = data
  resetFn()
}
function filterFn(data) {
  Object.assign(query.value, data)
  resetFn()
}

const virtualListIndex = ref(1)
const pagination = computed(() => {
  return {start: virtualListIndex.value + 1, end: listData.value.length, total: list.value.total}
})
async function scrollFn({index, to}) {
  virtualListIndex.value = index
  if (index !== to) return // console.warn(index, to, direction)
  const {limit = 10, skip = 0, data} = list.value
  if (Acan.isEmpty(data) || data.length < limit) return // has last page
  console.warn('need load more')
  query.value.$skip = skip + limit
  await find()
}
function tabFn() {
  if (tab.value === 'published') {
    query.value.$sort = {updatedAt: -1}
  } else {
    query.value.$sort = {updatedAt: -1}
  }
  resetFn()
}
function resetFn() {
  query.value.$skip = 0
  listData.value.length = 0
  list.value = {}
  find()
}
async function findPrompt() {
  router.replace({query: {...route.query, tab: tab.value}})
  const $text = key ? {$search: key} : undefined
  loading.value = true
  const rs = (list.value = await App.service('prompts').find({query: {...query.value, publish: true, $text}}))
  Acan.objClean(rs.data)
  loading.value = false
  listData.value.push(...rs.data)
  return rs.data.length
}
async function find() {
  if (tab.value === 'prompt') return findPrompt()
  router.replace({query: {...route.query, tab: tab.value}})
  const name = key ? {$regex: key, $options: 'i'} : undefined
  loading.value = true
  const rs = (list.value = await unit.find({...query.value, name, tab: tab.value}))
  loading.value = false
  listData.value.push(...rs.data)
  return rs.data.length
}

watch(
  () => pub.user.schoolInfo,
  (newVal) => {
    if (!newVal) return
    router.replace('/home/<USER>')
  }
)
onMounted(find)
</script>
