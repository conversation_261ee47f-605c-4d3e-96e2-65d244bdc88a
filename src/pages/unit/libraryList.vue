<template>
  <q-page class="column full-height" style="padding: 1rem 1rem 0.5rem 1rem" :class="route.query.header === '0' ? '' : 'pc-max'">
    <q-inner-loading :showing="loading" />
    <BreadCrumbs :title="title" v-if="route.query.back"></BreadCrumbs>

    <q-toolbar class="row items-center q-pa-none">
      <q-btn flat round dense size="lg" icon="arrow_back" @click="goBack" v-if="route.query.back"></q-btn>
      <div class="col q-pa-sm text-h5 text-bold">
        {{ title }}
      </div>
    </q-toolbar>

    <q-card class="column col full-height full-width">
      <q-toolbar class="q-px-md q-pt-md q-pb-sm">
        <SearchFilter :items="[$route.params.type === 'task' ? 'sessionType' : 'unitType', 'grades', 'subjects']" @change="filterFn" @search="searchFn" />
        <q-space></q-space>
        <div v-if="pagination?.total" class="text-grey-7 text-right" style="min-width: 4.6rem">
          {{ pagination.start }} - {{ pagination.end }}, of {{ pagination.total }}
        </div>
      </q-toolbar>
      <NoData v-if="!loading && isEmpty(listData)" />
      <q-virtual-scroll v-else separator class="col full-width overflow-auto" :items="listData" @virtual-scroll="scrollFn">
        <template v-slot:after>
          <div v-if="list.data?.length === list.limit" class="row justify-center q-my-md">
            <q-spinner-dots color="primary" size="40px" />
          </div>
          <div v-else class="q-pa-md text-grey text-center">It's over</div>
        </template>
        <template v-slot="{item: o}">
          <q-item>
            <ListItemCard :doc="o" dense>
              <template v-slot:button>
                <ItemPrice :doc="o"></ItemPrice>
                <q-btn
                  v-if="o.order?.links?.find((v) => v.id === o._id)?.newId || o.uid === pub.user?._id"
                  label="Edit"
                  no-caps
                  rounded
                  color="primary"></q-btn>
                <q-btn v-else label="Buy now" no-caps rounded color="primary"></q-btn>
              </template>
            </ListItemCard>
          </q-item>
        </template>
      </q-virtual-scroll>
    </q-card>
  </q-page>
</template>
<script setup>
import {ref, onMounted, computed, watch} from 'vue'
import BreadCrumbs from 'components/BreadCrumbs.vue'
import ListItemCard from './ui/ListItemCard.vue'
import ItemPrice from './ui/ItemPrice.vue'
import SearchFilter from './SearchFilter.vue'
import {unitStore} from 'stores/unit'
const unit = unitStore()
unit.isLib = true
import {pubStore} from 'stores/pub'
const pub = pubStore()
import {useRoute, useRouter} from 'vue-router'
const route = useRoute()
const router = useRouter()
const title = {task: 'Tasks', unit: 'Units'}[route.params.type]
const loading = ref(false),
  listData = ref([]),
  list = ref({}),
  query = ref({$skip: 0, $sort: {updatedAt: -1}})

// const sortOptions = [
//   {label: 'Latest', value: 'latest'},
//   {label: 'Most used', value: 'used'},
// ]

const goBack = (hash) => {
  let path = route.query.back || '/home/<USER>'
  const query = route.query
  if (query?.back) delete query.back
  if (hash && query.back) {
    path = query.back.replace(/(#\w+)$/, hash)
  }
  router.replace(path)
}

let key = ''
function searchFn(data) {
  console.log('searchFn', data)
  key = data
  resetFn()
}
const queryFilter = ref({})
function filterFn(data) {
  queryFilter.value = {}
  Object.assign(queryFilter.value, data)
  resetFn()
}

const virtualListIndex = ref(1)
const pagination = computed(() => {
  return {start: virtualListIndex.value + 1, end: listData.value.length, total: list.value.total}
})
async function scrollFn({index, to}) {
  virtualListIndex.value = index
  if (index !== to) return // console.warn(index, to, direction)
  const {limit = 10, skip = 0, data} = list.value
  if (Acan.isEmpty(data) || data.length < limit) return // has last page
  console.warn('need load more')
  query.value.$skip = skip + limit
  await find()
}
function resetFn() {
  query.value.$skip = 0
  listData.value.length = 0
  list.value = {}
  find()
}
async function find() {
  router.replace({query: {...route.query}})
  const name = key ? {$regex: key, $options: 'i'} : undefined
  loading.value = true
  const rs = (list.value = await App.service('unit').get(route.params.type === 'task' ? 'taskList' : 'unitList', {
    query: {...query.value, ...queryFilter.value, name},
  }))
  loading.value = false
  listData.value.push(...rs.data)
  return rs.data.length
}

watch(
  () => pub.user.schoolInfo,
  (newVal) => {
    if (!newVal) return
    router.replace('/home/<USER>')
  }
)
onMounted(find)
</script>
