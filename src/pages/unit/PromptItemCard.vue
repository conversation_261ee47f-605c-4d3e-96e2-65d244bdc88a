<template>
  <q-card class="full-width">
    <q-item clickable class="q-pa-sm overflow-auto" :to="{path: `/detail/prompts/${doc._id}`, query: {back: $route.fullPath}}">
      <div :class="$q.screen.gt.xs ? 'row' : 'column'" class="full-width">
        <div class="item-cover">
          <q-img :src="hashToUrl(doc.pages[0].pic) || '/v2/img/no-img.png'" class="rounded-borders" :ratio="16 / 9" />
        </div>
        <div class="col column full-width q-pa-none relative-position" :class="$q.screen.gt.xs ? 'q-pl-md' : ''">
          <q-item-label class="q-py-xs row items-center">
            <q-space></q-space>
            <q-btn class="q-mr-sm" round :color="question.color" :icon="question.icon" size="0.6rem"></q-btn>{{ question.title }}
          </q-item-label>
          <MaterialLists :allMaterials="material" :materialList="material" :preview="true" />
          <q-space></q-space>
          <div class="row items-center">
            <OwnerBox class="col" :owner="{nickname: 'Classcipe', avatar: '/img/logo2.png'}" :updatedAt="doc.createdAt" />
            <q-space></q-space>
            <div class="q-gutter-sm">
              <q-btn rounded label="Preview" color="primary" no-caps></q-btn>
            </div>
          </div>
        </div>
      </div>
    </q-item>
  </q-card>
</template>
<script setup>
import {ref, inject, onMounted} from 'vue'
import OwnerBox from 'components/detail/OwnerBox.vue'
import MaterialLists from 'pages/com/AddOnComponents/MaterialLists2.vue'
const props = defineProps(['doc'])
const QuestionTypes = inject('QuestionTypes')
const question = ref({})
const material = ref([])
onMounted(() => {
  const {type} = props.doc.questions[0]
  question.value = {...props.doc.questions[0], ...QuestionTypes[type]}
  material.value = props.doc.materials?.[0]?.list || []
})
</script>
<style scoped>
.item-cover {
  width: 22rem;
}
body.screen--xs .item-cover {
  width: 100%;
  padding-bottom: 1rem;
}
</style>
