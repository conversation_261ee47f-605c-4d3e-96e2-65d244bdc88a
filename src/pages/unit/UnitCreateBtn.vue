<template>
  <q-btn color="primary" rounded dense no-caps :loading="loading">
    <span v-if="!isLink" class="q-px-sm"> Create new </span>
    <q-icon v-else name="add" />
    <q-menu>
      <q-list style="min-width: 200px">
        <q-item clickable v-close-popup v-for="(o, i) in createList" :key="i" @click="createFn(o, i)">
          <q-item-section>{{ o.label }}</q-item-section>
        </q-item>
        <template v-if="!isLink">
          <q-item v-if="!pub.currentSchoolId || pub.isSchoolAdmin">
            <q-btn no-caps flat dense color="primary" class="full-width" :to="{path: `/account/academic-setting/subject/templateSetting`, query: route.query}">
              Manage templates
            </q-btn>
          </q-item>
        </template>
      </q-list>
    </q-menu>
  </q-btn>
  <!-- dialog -->
  <q-dialog v-model="isToolTypeDialogShow" persistent>
    <div class="bg-teal-1 q-pa-lg rounded-lg">
      <div class="q-pb-lg">
        <div>Choose a type of assessment tool</div>
        <q-option-group :options="toolTypeOptions" type="radio" v-model="toolType" option-disable="disable" />
      </div>
      <div class="flex justify-end q-gutter-md">
        <q-btn rounded flat no-caps label="Cancel" icon="arrow_back" class="text-grey-7" style="border: 1px solid" @click="closeToolTypeDialog" />
        <q-btn rounded flat no-caps label="Confirm" icon="done" class="bg-teal-4 text-white" @click="createAssessmentTool" />
      </div>
    </div>
  </q-dialog>
</template>
<script setup>
import {ref, onMounted, inject, watch} from 'vue'
import {curriculumStore} from 'stores/curriculum'
const curriculum = curriculumStore()
const emit = defineEmits(['change'])
const props = defineProps({
  isLink: {type: Boolean, default: false},
  one: {type: Object, default: () => ({})},
})
import {pubStore} from 'stores/pub'
const pub = pubStore()
import {useCriteriasStore} from 'stores/criterias'
const criterias = useCriteriasStore()

import {useRoute, useRouter} from 'vue-router'
const route = useRoute()
const router = useRouter()

const UnitModeMap = inject('UnitModeMap')
const TaskSessionTypes = inject('TaskSessionTypes')

const createList = ref([])
const loading = ref(true)
watch(() => pub.currentSchoolId, initFn) // 身份切换重新判断
async function initFn() {
  loading.value = true
  const lists = Acan.clone(UnitModeMap)
  const curriculumList = await curriculum.getOptions()
  if (!curriculumList.find((v) => v.value == 'pd')) {
    delete lists.pdUnit
    delete lists.pdTask
  }
  const tplOptions = await curriculum.getTplOptions('task')
  if (Acan.isEmpty(tplOptions)) {
    delete lists.unit
    delete lists.task
    if (!lists.pdTask) delete lists.video
  }
  if (props.isLink) {
    if (['unit', 'task'].includes(props.one.mode)) {
      delete lists.unit
      delete lists.pdUnit
      delete lists.pdTask
    } else if (['pdUnit', 'pdTask'].includes(props.one.mode)) {
      delete lists.unit
      delete lists.task
      delete lists.pdUnit
    }
  }
  createList.value = lists
  loading.value = false
}
onMounted(initFn)

import {sectionTypeOptions as toolTypeOptions} from 'src/pages/account/assessment-tool/utils'
const isToolTypeDialogShow = ref(false)
const toolType = ref(toolTypeOptions[0].value)

function closeToolTypeDialog() {
  isToolTypeDialogShow.value = false
  toolType.value = toolTypeOptions[0].value
}

const createAssessmentTool = async () => {
  try {
    await criterias.getUpdatedCriteria()
    const criteriaList = criterias.updatedList?.data || []
    const rubricsCriteria = criteriaList.map((criteria) => ({
      ...criteria,
      default: criteria._id === criterias.default?._id,
    }))
    const dto = {
      name: '',
      guest: true,
      sessionType: 'student',
      mode: 'tool',
      curriculum: 'tool',
      toolType: toolType.value,
      rubricsCriteria,
    }
    // link content下创建的tool 要指定数据源_id，用于继承数据
    if (props.isLink && props.one._id) dto.toolSource = props.one._id
    const rs = await App.service('unit').create(dto)
    closeToolTypeDialog()
    if (props.isLink) return emit('change', rs)
    router.push({path: `/account/assessment-tool/${rs._id}`, query: {back: route.fullPath}})
  } catch (error) {
    console.error(error)
  }
}

import ChangeTemplate from 'pages/unit/dialog/ChangeTemplate.vue'
function changeTemplateFn(mode, ext) {
  $q.dialog({
    component: ChangeTemplate,
    componentProps: {listMode: true, one: {mode}},
  }).onOk((data) => {
    onTemplateChoosed({...ext, ...data, mode})
  })
}
import VideoChange from 'pages/unit/dialog/VideoChange.vue'
import CreateChoose from 'pages/unit/dialog/CreateChoose.vue'

const createFn = (item, mode) => {
  TaskSessionTypes.map(({title, label}) => ({label: title, value: label}))
  if (mode == 'tool') {
    isToolTypeDialogShow.value = true
  } else {
    const isService = ['pdTask', 'pdUnit'].includes(mode)
    if (mode === 'video') {
      $q.dialog({
        component: VideoChange,
        componentProps: {one: {curriculum: props.one.curriculum, service: props.one.service}},
      }).onOk(async ({data}) => {
        await onTemplateChoosed({...data, mode}) // sessionType: 'student'
      })
    } else if (['task', 'pdTask', 'pdUnit'].includes(mode)) {
      if (props.isLink && ['task'].includes(mode)) {
        return changeTemplateFn(mode, {sessionType: 'student'})
      }
      $q.dialog({
        component: CreateChoose,
        componentProps: {
          mode,
          sessionType: ['task', 'pdTask'].includes(mode) && props.isLink ? 'student' : null,
        },
      }).onOk((data) => {
        console.log(data)
        if (isService) return onTemplateChoosed({mode, curriculum: 'pd', ...data})
        changeTemplateFn(mode, data)
      })
    } else {
      changeTemplateFn(mode)
    }
  }
}

const onTemplateChoosed = async (template) => {
  const obj = {name: '', ...template}

  if (['task', 'pdTask'].includes(obj.mode)) obj.guest = true

  await criterias.getUpdatedCriteria()
  const criteriaList = criterias.updatedList?.data || []
  obj.rubricsCriteria = criteriaList.map((criteria) => ({
    ...criteria,
    default: criteria._id === criterias.default?._id,
  }))
  const rs = await App.service('unit').create(obj)
  if (template.curriculum === 'pd') {
    const post = {}
    const prepend = 'pd'
    const label = template.outlineSubjects[0]
    const subjectCode = template.service.type
    const objectives = ['outline']
    post.task = rs._id
    if (subjectCode === 'essay') {
      //objectives.push(...['assess', 'skills'])
      //https://github.com/zran-nz/bug/issues/3969
    }

    objectives.forEach((item) => {
      post[`${item}.curr`] = prepend
      post[`${item}.data`] = {}
      post[`${item}.data`][`${prepend}:${subjectCode}`] = {
        child: [],
        curr: 'Service',
        code: `${prepend}:${subjectCode}`,
        name: label,
      }
    })
    await App.service('task-outline').create(post)
  }
  const path = obj.mode.indexOf('nit') != -1 ? 'unit' : 'task'
  if (props.isLink) return emit('change', rs)
  if (obj.mode === 'video') {
    router.push({path: `/video/edit/${rs._id}`, query: {back: route.fullPath}})
  } else {
    router.push({path: `/com/${path}/edit/${rs._id}`, query: {back: route.fullPath}})
  }
}
</script>
