<template>
  <q-page style="padding: 1rem 1rem 0.5rem 1rem">
    <BreadCrumbs title="Preview" v-if="true"></BreadCrumbs>
    <q-inner-loading :showing="loading" />
    <template v-if="!loading">
      <q-toolbar class="row items-center q-pa-none">
        <q-btn flat round dense size="lg" icon="arrow_back" @click="goBack" v-if="route.query.back"></q-btn>
        <q-icon v-if="unit.one.premiumAuth" size="24px" name="svguse:/v2/icons.svg#premium">
          <q-tooltip>premium workshop</q-tooltip>
        </q-icon>
        <div class="col q-pa-sm text-h4">
          {{ unit.one.name || 'Untitled' }}
        </div>
      </q-toolbar>
      <OwnerBox :updatedAt="unit.one.createdAt" :owner="unit.one.owner" />

      <div>
        <p v-if="!Acan.isEmpty(authDoc.countryCode)">countryCode: {{ authDoc.countryCode }}</p>
        <p>curriculum: {{ authDoc.curriculum }}</p>
        <p v-if="authDoc.subject">subject: {{ authDoc.subject }}</p>
        <p v-if="!Acan.isEmpty(authDoc.gradeGroup)">gradeGroup: {{ authDoc.gradeGroup }}</p>
        <p v-if="!Acan.isEmpty(authDoc.tags)">tags: {{ authDoc.tags }}</p>
        <p v-if="!Acan.isEmpty(authDoc.topic)">topic: {{ authDoc.topic }}</p>
        <p>serviceRoles: {{ authDoc.serviceRoles }}</p>
        <p>serviceRolesUser: {{ authDoc.serviceRolesUser }}</p>
      </div>
      <div class="q-my-md">
        <q-img spinner-color="white" fit="cover" class="fit rounded-borders-md" :ratio="16 / 7" :src="hashToUrl(unit.one.cover) || '/v2/img/avatar.png'">
          <div class="absolute-bottom-left bg-transparent">
            <q-avatar size="1.8rem" :color="UnitModeMap[unit.one.mode].color" text-color="white" :icon="UnitModeMap[unit.one.mode].icon" />
          </div>
        </q-img>
      </div>

      <div class="q-pt-md text-weight-medium">
        <ListItemCardChips :doc="unit.one" />
        <q-item-label>
          Contents
          <q-chip :label="1" color="teal-1" text-color="primary"></q-chip>
        </q-item-label>
      </div>
      <q-list separator>
        <q-item clickable v-ripple v-for="(o, i) in items" :key="i" :to="o.to" :disable="o.disable">
          <q-item-section avatar>
            <q-icon :name="o.icon"></q-icon>
          </q-item-section>
          <q-item-section>
            <q-item-label>{{ o.label }}</q-item-label>
            <q-item-label caption>{{ o.caption }}</q-item-label>
          </q-item-section>
          <q-item-section side>
            <q-icon name="navigate_next" />
          </q-item-section>
          <q-tooltip v-if="o.tips && o.disable">{{ o.tips }}</q-tooltip>
        </q-item>
      </q-list>
      <q-separator spaced />

      <q-expansion-item class="shadow-2 rounded-borders-md overflow-hidden q-my-md" v-for="group in unit.one.linkGroup" :key="group._id">
        <template v-slot:header>
          <q-item-section>
            {{ group.name }}
          </q-item-section>
          <q-item-section side>
            <div class="row items-center">
              <q-icon name="dynamic_feed"></q-icon>
              <span class="q-pl-sm">{{ unit.one.link.filter((v) => v.group === group._id).length }} Lesson</span>
            </div>
          </q-item-section>
        </template>
        <template v-if="unit.one.mode.toLowerCase().includes('unit')">
          <template v-for="link in unit.one.link.filter((v) => v.group === group._id)" :key="link._id">
            <q-separator />
            <ListItemCard :doc="authDoc.linkSnapshot[link.id]" :showBtn="false" dense />
          </template>
        </template>
        <template v-else>
          <q-expansion-item v-for="link in unit.one.link.filter((v) => v.group === group._id)" :key="link._id">
            <template v-slot:header>
              <q-item-section>
                <div class="row items-center">
                  <q-avatar size="1.8rem" :color="UnitModeMap[link.mode].color" text-color="white" :icon="UnitModeMap[link.mode].icon" />
                  <span class="q-pl-sm">{{ link.name }}</span>
                </div>
              </q-item-section>
            </template>
            <div class="q-pa-md">
              <q-card bordered class="shadow-0 rounded-borders-md q-pa-md row">
                <q-btn
                  class="col-xs-4 col-sm-3 col-md-3 q-pa-md"
                  label="Overview"
                  stack
                  flat
                  icon="o_visibility"
                  no-caps
                  :to="unit.viewRoute({link, _id: link.id}, isLib, 'overview')"></q-btn>
                <q-btn
                  class="col-xs-4 col-sm-3 col-md-3"
                  label="Slides"
                  stack
                  flat
                  icon="slideshow"
                  no-caps
                  :to="unit.viewRoute({link, _id: link.id}, isLib, 'slides')"></q-btn>
              </q-card>
            </div>
          </q-expansion-item>
        </template>
      </q-expansion-item>
    </template>
    <div class="row full-width q-pa-md"></div>
  </q-page>
</template>
<script setup>
import {ref, onMounted} from 'vue'
import BreadCrumbs from 'components/BreadCrumbs.vue'
import OwnerBox from 'components/detail/OwnerBox.vue'
import ListItemCard from './ListItemCard.vue'
import ListItemCardChips from './ListItemCardChips.vue'
import {unitStore} from 'stores/unit'
const unit = unitStore()
import {useRoute, useRouter} from 'vue-router'
const route = useRoute()
const router = useRouter()
const items = ref([])

import PromptPage from 'components/PromptPage.vue'
const isLib = ref(true)
const loading = ref(true)
const authDoc = ref(null)
onMounted(async () => {
  const {authId, id} = route.params
  const query = {back: route.fullPath}
  authDoc.value = await App.service('service-auth').get(authId)
  if (!authDoc.value.unit) return console.log('is not service-auth content')
  unit.setAuthUnit(id ? authDoc.value.linkSnapshot[id] : authDoc.value.unitSnapshot)
  const unitId = unit.one._id

  const arr = [{label: 'Overview', icon: 'o_visibility', to: {path: route.path.replace(authId, 'overview/' + authId), query}}]
  if (unit.one.sid) arr.push({label: 'Slides', icon: 'o_slideshow', to: {path: route.path.replace(authId, 'slides/' + authId), query}})

  if (!unit.one) {
    return $q.dialog({
      component: PromptPage,
      componentProps: {
        fullscreen: true,
        persistent: true,
        cancel: false,
        logo: true,
        title: 'The products are no longer available',
        back: true,
      },
    })
  }
  loading.value = false
  arr.push({
    label: 'Teaching notes',
    icon: 'o_note_alt',
    caption: 'Teaching experience shared by others',
    to: {path: '/reflect/lib/' + unitId, query},
  })
  items.value = arr
})

const goBack = (hash) => {
  let path = route.query.back || '/home/<USER>'
  const query = route.query
  if (query?.back) delete query.back
  if (hash && query.back) {
    path = query.back.replace(/(#\w+)$/, hash)
  }
  router.replace(path)
}
</script>
