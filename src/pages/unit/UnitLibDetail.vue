<template>
  <q-page style="padding: 1rem 1rem 0.5rem 1rem">
    <q-inner-loading :showing="loading" />
    <BreadCrumbs title="Preview" v-if="route.query.back"></BreadCrumbs>
    <UnitDetail :items="items" v-if="!loading" :isLib="true">
      <template v-slot:btns>
        <q-btn
          v-for="(o, i) in btns"
          :key="i"
          class="full-width"
          outline
          rounded
          color="primary"
          :label="o.label"
          :icon="o.icon"
          no-caps
          :to="o.to"
          @click="o.click"
          :loading="loadings[o.label]" />
      </template>
    </UnitDetail>
    <div class="row full-width q-pa-md"></div>
  </q-page>
</template>
<script setup>
import {ref, onMounted} from 'vue'
import BreadCrumbs from 'components/BreadCrumbs.vue'
import UnitDetail from './ui/UnitDetail.vue'
import {unitStore} from 'stores/unit'
const unit = unitStore()
import {useRoute, useRouter} from 'vue-router'
const route = useRoute()
const router = useRouter()
const items = ref([])

const loadings = ref({})
const unpublishFn = () => {
  const {_id, name} = unit.one
  $q.dialog({
    title: `Confirm unpublish`,
    message: `Please confirm that you want to unpublish the content "${name ? name : 'Untitled'}".`,
    cancel: true,
    persistent: true,
  }).onOk(async () => {
    loadings.value.Unpublish = true
    await App.service('unit').patch('unPublish', {_id})
    $q.notify({type: 'info', message: 'Successful'})
    loadings.value.Unpublish = false
    location.reload()
  })
}

const copyFn = async () => {
  const {_id, name, order} = unit.one
  $q.dialog({
    title: `Confirm copy`,
    message: `Please confirm that you want to copy the session "${name}"?`,
    cancel: true,
    persistent: true,
  }).onOk(async () => {
    loadings.value.Copy = true
    const rs = await unit.patch('copy', {_id, orderId: order._id})
    if (!rs) $q.notify({type: 'negative', message: 'Copied unsuccessfully'})
    else $q.notify({type: 'positive', message: 'Copied successfully'})
    loadings.value.Copy = false
    router.push({path: `/unit/my`, query: {back: route.fullPath}})
  })
}
const buyFn = async () => {
  let {ordered} = await App.service('order').get('checkLinks', {query: {links: [{id: unit.one._id, style: 'unit'}]}})
  if (!Acan.isEmpty(ordered)) {
    router.push({path: `/order/detail/${ordered[0]}`, query: {back: route.query.back}})
    return
  }
  router.push({
    path: `/order/confirm/unit/${unit.one._id}`,
    query: route.query,
  })
}
import AddCartDialog from 'src/pages/order/AddCartDialog.vue'
const cartFn = async () => {
  if (unit.one.cart) return router.push('/order/cart')
  const doc = await App.service('cart')
    .create({
      goodsId: unit.one._id,
      style: 'unit',
      inviter: route.query?.inviteCode,
    })
    .catch((err) => {
      $q.notify({type: 'negative', message: err.message})
      return
    })
  if (!doc) return
  $q.dialog({
    component: AddCartDialog,
    componentProps: {
      goBack: goBack,
      content: unit.one,
    },
  })
}
const collectFn = async () => {
  const {collected, _id, mode} = unit.one
  loadings.value[collected ? 'Remove bookmark' : 'Bookmark'] = true
  if (collected) {
    await App.service('collect').remove(collected)
    unit.one.collected = null
  } else {
    const doc = await App.service('collect').create({rid: _id, type: mode})
    unit.one.collected = doc._id
  }
  loadings.value[collected ? 'Remove bookmark' : 'Bookmark'] = false
  initBts()
}
const btns = ref([])
function initBts() {
  const {_id, collected, order, cart} = unit.one
  let arr = []
  if (unit.isOwner) {
    arr = [{label: 'Unpublish', icon: 'o_cancel', click: unpublishFn}]
  } else {
    if (order?._id) {
      const link = order.links.find((v) => v.id == _id)
      if (link.archived) {
        arr.push({label: 'Copy', icon: 'o_file_copy', click: copyFn})
      } else {
        arr.push({label: 'Edit', icon: 'edit', to: unit.editRoute({_id: link.newId, mode: unit.one.mode}, route)})
      }
    } else {
      arr.push({label: 'Buy now', icon: 'o_inventory_2', click: buyFn})
      if (cart) arr.push({label: 'In the cart', icon: 'o_shopping_cart', click: cartFn})
      else arr.push({label: 'Add To Cart', icon: 'o_shopping_cart', click: cartFn})
    }
    if (collected) arr.push({label: 'Remove bookmark', icon: 'o_bookmark_remove', click: collectFn})
    else arr.push({label: 'Bookmark', icon: 'bookmark_border', click: collectFn})
  }
  btns.value = arr
}
import PromptPage from 'components/PromptPage.vue'
const isLib = ref(true)
const loading = ref(true)
onMounted(async () => {
  const {id, linkId} = route.params
  const query = {back: route.fullPath}
  const arr = [{label: 'Overview', icon: 'o_visibility', to: {path: route.path.replace(id, 'overview/' + id), query}}]

  await unit.getLib(id, linkId)
  if (unit.isTask) arr.push({label: 'Slides', icon: 'o_slideshow', to: {path: route.path.replace(id, 'slides/' + id), query}})

  if (!unit.one || (!isLib.value && !unit.isOwner && !unit.member)) {
    return $q.dialog({
      component: PromptPage,
      componentProps: {
        fullscreen: true,
        persistent: true,
        cancel: false,
        logo: true,
        title: 'The products are no longer available',
        back: true,
      },
    })
  }
  loading.value = false
  if (!linkId) initBts()
  const teachingNotesItem = {
    label: 'Teaching notes',
    icon: 'o_note_alt',
    caption: 'Teaching experience shared by others',
    to: {path: '/reflect/lib/' + unit.one._id, query},
  }
  arr.push(teachingNotesItem)
  if (unit.isOwner) {
    arr.push({label: 'Edit', icon: 'edit', to: unit.editRoute(unit.one, route)})
    if (unit.one.sid)
      arr.push({
        label: 'Edit prompts',
        icon: 'o_post_add',
        to: {path: '/detail/prompts/edit/' + unit.one._id, query},
      })
  }
  items.value = arr
})

const goBack = (hash) => {
  let path = route.query.back || '/home/<USER>'
  const query = route.query
  if (query?.back) delete query.back
  if (hash && query.back) {
    path = query.back.replace(/(#\w+)$/, hash)
  }
  router.replace(path)
}
</script>
