<template>
  <div class="column">
    <div class="row">
      <q-input
        rounded
        outlined
        label="Search by keyword"
        v-model.trim="key"
        @change="emit('search', key)"
        dense
        clearable
        class="col q-pr-sm q-pb-sm"
        style="max-width: 15rem"
        @clear="emit('search', '')">
        <template v-slot:prepend>
          <q-icon name="search"></q-icon>
        </template>
        <template v-slot:append>
          <q-icon :name="expanded ? 'arrow_drop_up' : 'arrow_drop_down'"> </q-icon>
          <q-menu v-if="!loading" v-model="expanded" :offset="[200, 10]" class="q-pa-md" style="width: 50rem; max-width: 85%" @show="showFn()" @hide="hideFn()">
            <q-expansion-item v-if="items.includes('mode')" label="Type" dense expand-icon-toggle :default-opened="true">
              <q-checkbox v-model="mode" v-for="(o, i) in UnitModeMap" :key="i" :val="i" :label="o.label"></q-checkbox>
            </q-expansion-item>
            <q-expansion-item v-if="items.includes('unitType')" label="Unit type" dense expand-icon-toggle :default-opened="true">
              <q-checkbox v-model="unitType" v-for="(o, i) in UnitTypeTitles" :key="i" :val="o" :label="o"></q-checkbox>
            </q-expansion-item>
            <q-expansion-item v-if="items.includes('sessionType')" label="Task type" dense expand-icon-toggle :default-opened="true">
              <q-checkbox v-model="sessionType" v-for="(o, i) in SessionTypes" :key="i" :val="i" :label="o.label"></q-checkbox>
            </q-expansion-item>
            <q-expansion-item v-if="items.includes('serviceType')" label="Service Type" dense expand-icon-toggle :default-opened="true">
              <q-checkbox v-model="academic" label="Academic"></q-checkbox>
              <q-checkbox v-model="serviceType" v-for="(o, i) in subjects.pubPdOptions" :key="i" :val="o.value" :label="o.label"></q-checkbox>
            </q-expansion-item>
            <q-expansion-item v-if="items.includes('grades')" label="Grades" dense expand-icon-toggle :default-opened="true">
              <q-checkbox v-model="grades" v-for="(o, i) in PlatformGrades" :key="i" :val="o" :label="o"></q-checkbox>
            </q-expansion-item>
            <q-expansion-item v-if="items.includes('subjects')" label="Subjects" dense expand-icon-toggle :default-opened="true">
              <q-checkbox v-model="unitSubjects" v-for="(o, i) in SubjectOptions" :key="i" :val="o" :label="o"></q-checkbox>
            </q-expansion-item>
          </q-menu>
        </template>
      </q-input>

      <q-btn v-if="sortOptions" flat icon="o_sort_by_alpha" unelevated color="primary" label="Sort by" no-caps rounded>
        <q-menu>
          <q-item tag="label" v-for="(o, i) in sortOptions" :key="i" clickable v-close-popup>
            <q-item-section avatar>
              <q-radio v-model="sort" @update:modelValue="sortFn" :val="o.value" />
            </q-item-section>
            <q-item-section>{{ o.label }}</q-item-section>
          </q-item>
        </q-menu>
      </q-btn>
    </div>
    <div class="row items-center" v-if="!Acan.isEmpty(selects) || academic">
      <q-chip
        v-for="o in mode"
        :key="o"
        :label="UnitModeMap[o].label"
        removable
        outline
        color="grey"
        @remove="(mode.splice(mode.indexOf(o), 1), hideFn(1))"></q-chip>
      <q-chip v-for="o in unitType" :key="o" :label="o" removable outline color="grey" @remove="(unitType.splice(unitType.indexOf(o), 1), hideFn(1))"></q-chip>
      <q-chip
        v-for="o in sessionType"
        :key="o"
        :label="SessionTypes[o].label"
        removable
        outline
        color="grey"
        @remove="(sessionType.splice(sessionType.indexOf(o), 1), hideFn(1))"></q-chip>
      <q-chip v-for="o in grades" :key="o" :label="o" removable outline color="grey" @remove="(grades.splice(grades.indexOf(o), 1), hideFn(1))"></q-chip>
      <q-chip
        v-for="o in unitSubjects"
        :key="o"
        :label="o"
        removable
        outline
        color="grey"
        @remove="(unitSubjects.splice(unitSubjects.indexOf(o), 1), hideFn(1))"></q-chip>
      <q-chip v-if="academic" label="Academic" removable outline color="grey" @remove=";(academic = false), hideFn(1)"></q-chip>
      <q-chip
        v-for="o in serviceType"
        :key="o"
        :label="subjects.pubPdOptionsMap[o]"
        removable
        outline
        color="grey"
        @remove="(serviceType.splice(serviceType.indexOf(o), 1), hideFn(1))"></q-chip>
      <q-btn @click="clearFn" flat color="primary" rounded dense class="q-pa-xs" size="sm" icon-right="close" label="Clear filters" no-caps />
    </div>
  </div>
</template>
<script setup>
import {ref, onMounted, computed} from 'vue'
import {subjectsStore} from 'stores/subjects'
const subjects = subjectsStore()

const props = defineProps(['items', 'sortOptions'])
const emit = defineEmits(['change', 'search'])
const mode = ref([]),
  key = ref(''),
  serviceType = ref([]),
  sessionType = ref([]),
  grades = ref([]),
  unitSubjects = ref([]),
  unitType = ref([]),
  academic = ref(false),
  expanded = ref(false),
  loading = ref(true),
  sort = ref(null)
function sortFn() {
  const $sort = {}
  $sort[sort.value] = -1
  emit('change', sort.value ? {$sort} : {})
}
function clearFn() {
  mode.value.length = 0
  serviceType.value.length = 0
  academic.value = false
  grades.value.length = 0
  sessionType.value.length = 0
  unitSubjects.value.length = 0
  unitType.value.length = 0
  hideFn(1)
}
const lastValue = []
const selects = computed(() => {
  return [...mode.value, ...unitType.value, ...serviceType.value, ...sessionType.value, ...grades.value, ...unitSubjects.value]
})
function showFn() {
  lastValue.length = 0
  lastValue.push(academic.value, ...selects.value)
}
function hideFn(f = false) {
  if (!f && JSON.stringify(lastValue) === JSON.stringify([academic.value, ...selects.value])) return console.log('no modity') // 无变化
  const query = {}
  if (!Acan.isEmpty(mode.value)) query.mode = mode.value
  if (!Acan.isEmpty(sessionType.value)) query.sessionType = sessionType.value
  if (!Acan.isEmpty(unitType.value)) query.type = unitType.value
  // for unit model
  if (!Acan.isEmpty(grades.value)) query['grades.value'] = grades.value
  if (!Acan.isEmpty(unitSubjects.value)) query['subjects.value'] = unitSubjects.value

  const srr = []
  if (academic.value) srr.push({curriculum: {$ne: 'pd'}})
  if (!Acan.isEmpty(serviceType.value)) srr.push({curriculum: 'pd', 'service.type': {$in: serviceType.value}})
  if (srr.length > 1) {
    query.$or = srr
  } else if (srr[0]) {
    Object.assign(query, srr[0])
  }
  Acan.objClean(query)
  emit('change', query)
  if (f) showFn()
}

onMounted(async () => {
  if (props.items.includes('serviceType')) await subjects.getPubPdOptions()
  loading.value = false
})
</script>
