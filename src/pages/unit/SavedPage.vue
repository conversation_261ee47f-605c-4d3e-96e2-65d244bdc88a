<template>
  <q-page class="column full-height" style="padding: 1rem 1rem 0.5rem 1rem" :class="route.query.header === '0' ? '' : 'pc-max'">
    <q-card class="column col full-height full-width">
      <div class="q-pa-md text-h5 text-bold">My saved</div>
      <q-toolbar class="q-px-md q-pb-sm">
        <SearchFilter :items="['mode']" @change="filterFn" @search="searchFn" />
        <q-space></q-space>
        <div v-if="pagination?.total" class="text-grey-7 text-right" style="min-width: 4.6rem">
          {{ pagination.start }} - {{ pagination.end }}, of {{ pagination.total }}
        </div>
      </q-toolbar>
      <NoData v-if="!loading && isEmpty(listData)" />
      <q-virtual-scroll v-else separator class="col full-width overflow-auto" :items="listData" @virtual-scroll="scrollFn">
        <template v-slot:after>
          <div v-if="list.data?.length === list.limit" class="row justify-center q-my-md">
            <q-spinner-dots color="primary" size="40px" />
          </div>
          <div v-else class="q-pa-md text-grey text-center">It's over</div>
        </template>
        <template v-slot="{item: o}">
          <q-item>
            <ListItemCard :doc="o.data" dense />
          </q-item>
        </template>
      </q-virtual-scroll>
    </q-card>
  </q-page>
</template>
<script setup>
import {ref, onMounted, computed} from 'vue'
import ListItemCard from './ListItemCard.vue'
import SearchFilter from './SearchFilter.vue'
import {useRoute} from 'vue-router'
const route = useRoute()
const loading = ref(false),
  listData = ref([]),
  list = ref({}),
  query = ref({$skip: 0, $sort: {createdAt: -1}})

let key = ''
function searchFn(data) {
  key = data
  resetFn()
}
function filterFn(data) {
  if (data.mode) {
    data.type = data.mode
    delete data.mode
  }
  Object.assign(query.value, data)
  resetFn()
}

const virtualListIndex = ref(1)
const pagination = computed(() => {
  return {start: virtualListIndex.value + 1, end: listData.value.length, total: list.value.total}
})
async function scrollFn({index, to}) {
  virtualListIndex.value = index
  if (index !== to) return // console.warn(index, to, direction)
  const {limit = 10, skip = 0, data} = list.value
  if (Acan.isEmpty(data) || data.length < limit) return // has last page
  console.warn('need load more')
  query.value.$skip = skip + limit
  await find()
}
function resetFn() {
  query.value.$skip = 0
  listData.value.length = 0
  list.value = {}
  find()
}
async function find() {
  const name = key ? {$regex: key, $options: 'i'} : undefined
  loading.value = true
  const rs = (list.value = await App.service('collect').find({query: {...query.value, name}}))
  loading.value = false
  listData.value.push(...rs.data)
  return rs.data.length
}

onMounted(find)
</script>
