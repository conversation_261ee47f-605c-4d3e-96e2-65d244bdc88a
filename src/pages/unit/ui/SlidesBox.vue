<template>
  <div class="library-slide">
    <q-carousel
      v-if="imgList.length"
      ref="dom"
      class="bg-grey-1"
      style="max-height: 675px"
      :style="`height: ${((dom?.$el?.offsetWidth || $q.screen.width) / 16) * 9}px`"
      v-model="carouselModel"
      @update:model-value="onSlideChanged($event, o)"
      :navigation="$q.screen.lt.sm"
      :thumbnails="$q.screen.gt.xs"
      arrows
      animated
      swipeable>
      <template v-for="(o, i) in imgList">
        <q-carousel-slide v-if="o.video" :key="o.src" :name="i">
          <q-video class="absolute-full" :src="o.src" />
        </q-carousel-slide>
        <q-carousel-slide v-else :key="o.src" :name="i" :img-src="o.src">
          <a v-if="o.media?.url && !o.locked" :href="o.media.url" target="_blank" rel="noopener" class="absolute-full" style="z-index: 10"></a>
          <div class="fit flex items-center justify-center column lock-background" v-if="o.locked">
            <q-icon name="o_lock" size="xl" color="white"></q-icon>
            <div class="text-white text-subtitle1 q-pt-sm">This slide is locked</div>
            <div class="text-grey-5">purchase to view the details</div>
          </div>
        </q-carousel-slide>
      </template>
    </q-carousel>
    <NoData v-else></NoData>
  </div>
</template>
<script setup>
import {ref, computed, onMounted} from 'vue'
const props = defineProps(['content', 'poster', 'lockAll'])
const emit = defineEmits(['change'])
const carouselModel = ref(0)
const dom = ref(null)

const onSlideChanged = (val) => {
  emit('change', val, imgList.value)
}

const imgList = computed(() => {
  const arr = []
  const rs = props.content
  if (props.poster) {
    if (rs.cover_video) arr.push({src: rs.cover_video, video: true})
    if (rs.cover) arr.push({src: Fn.hashToUrl(rs.cover)})
    else if (rs.image) arr.push({src: Fn.hashToUrl(rs.image)})
  }
  if (rs.pages) {
    rs.pages.map((v) => {
      arr.push({src: props.lockAll ? '/v2/img/slide-locked.png' : Fn.hashToUrl(v.pic || v.url), pageId: v._id, locked: props.lockAll})
    })
  }
  let lockedPageNum = rs.pageNum - arr.length
  if (props.poster && lockedPageNum) {
    lockedPageNum += 1
  }

  if (lockedPageNum) {
    for (let i = 0; i < lockedPageNum; i++) {
      arr.push({src: '/v2/img/slide-locked.png', locked: true})
    }
  }
  if (Acan.isEmpty(arr)) {
    arr.push({src: '/v2/img/no-img.png'})
  }
  return arr
})

onMounted(() => {
  onSlideChanged(0)
})

// watch(
//   () => props.content,
//   () => {
//     updateSlide()
//   }
// )
</script>

<style lang="sass" scoped>
.library-slide
  .q-carousel__slide
    padding:0
  .lock-background
    border-radius: 5px
    background-image: url('assets/img/slide-bg.png')
    background-size: 100%
    background-repeat: no-repeat
</style>
