<template>
  <q-card class="full-width">
    <q-item clickable class="q-pa-sm overflow-auto">
      <div :class="$q.screen.gt.xs ? 'row' : 'column'" class="full-width">
        <div class="item-cover col-5" style="max-width: 41%">
          <q-img :src="unitDoc.cover || '/v2/img/no-img.png'" class="rounded-borders" :ratio="16 / 9">
            <div class="absolute-bottom-left bg-transparent" style="padding: 8px">
              <q-avatar size="1.6rem" :color="UnitModeMap[unitDoc.mode].color" text-color="white" :icon="UnitModeMap[unitDoc.mode].icon" />
            </div>
          </q-img>
        </div>
        <div class="col-7 column q-pa-none relative-position" :class="$q.screen.gt.xs ? 'q-pl-md' : ''">
          <q-item-label class="q-py-xs text-h6 row">
            <AuthIcon :doc="unitDoc" />
            <span class="col ellipsis">
              {{ unitDoc.name }}
              <span v-if="!unitDoc.name" class="text-grey-7">Untitled</span>
            </span>
            <q-space></q-space>
          </q-item-label>
          <AuthItemCardChips :authDoc="authDoc" :subjectMaps="subjectMaps" />
          <q-space></q-space>
          <div class="row items-center">
            <OwnerBox v-if="authDoc.owner" class="col" :owner="authDoc.owner" :updatedAt="authDoc.createdAt" />
            <q-space></q-space>
            <!-- 发布的课件显示价格（不存在按钮的时候） -->
            <ItemPrice v-if="!$slots.button" :doc="unitDoc"></ItemPrice>
            <slot name="button"></slot>
          </div>
        </div>
      </div>
    </q-item>
  </q-card>
</template>
<script setup>
import AuthIcon from './AuthIcon.vue'
import OwnerBox from 'components/detail/OwnerBox.vue'
import ItemPrice from './ItemPrice.vue'
import AuthItemCardChips from './AuthItemCardChips.vue'
import {unitStore} from 'stores/unit'
const unit = unitStore()
import {ref} from 'vue'

import {serviceAuthStore} from 'stores/service-auth'
// library 课件不需要查询认证项
const serviceAuth = unit.isLib ? null : serviceAuthStore()

const props = defineProps(['authDoc', 'subjectMaps', 'pid'])
const unitDoc = ref(props.authDoc.unitSnapshot)
</script>
<style scoped>
.item-cover {
  width: 22rem;
}
body.screen--xs .item-cover {
  width: 100%;
  padding-bottom: 1rem;
}
</style>
