<template>
  <div class="full-width col">
    <q-toolbar class="row items-center q-pa-none">
      <q-btn flat round dense size="lg" icon="arrow_back" @click="goBack" v-if="route.query.back"></q-btn>
      <q-icon v-if="serviceAuth?.isWorkshopAuth(unit.one)" size="24px" name="svguse:/v2/icons.svg#premium">
        <q-tooltip>premium workshop</q-tooltip>
      </q-icon>
      <div class="col q-pa-sm text-h5 text-bold">
        {{ unit.one.name || 'Untitled' }}
      </div>
      <ItemPrice v-if="!pid" :doc="unit.one" />
    </q-toolbar>
    <OwnerBox :updatedAt="unit.one.createdAt" :owner="unit.one.owner" />

    <div class="q-my-md">
      <q-img spinner-color="white" fit="cover" class="fit rounded-borders-md" :ratio="16 / 7" :src="hashToUrl(unit.one.cover) || '/v2/img/avatar.png'">
        <div class="absolute-bottom-left bg-transparent">
          <q-avatar size="1.8rem" :color="UnitModeMap[unit.one.mode].color" text-color="white" :icon="UnitModeMap[unit.one.mode].icon" />
        </div>
      </q-img>
    </div>
    <div class="q-pt-md text-weight-medium">
      <ListItemCardChips :doc="unit.one" />
      <q-item-label>
        Contents
        <q-chip :label="1" color="teal-1" text-color="primary"></q-chip>
      </q-item-label>
    </div>
    <q-list separator>
      <q-item clickable v-ripple v-for="(o, i) in items" :key="i" :to="o.to" :disable="o.disable">
        <q-item-section avatar>
          <q-icon :name="o.icon"></q-icon>
        </q-item-section>
        <q-item-section>
          <q-item-label>{{ o.label }}</q-item-label>
          <q-item-label caption>{{ o.caption }}</q-item-label>
        </q-item-section>
        <q-item-section side>
          <q-icon name="navigate_next" />
        </q-item-section>
        <q-tooltip v-if="o.tips && o.disable">{{ o.tips }}</q-tooltip>
      </q-item>
    </q-list>
    <div class="q-gutter-md q-ml-none q-pa-md">
      <slot name="btns"></slot>
    </div>
    <q-separator spaced />
    <UnitLinkContent />
  </div>
</template>
<script setup>
import OwnerBox from 'components/detail/OwnerBox.vue'
import UnitLinkContent from './UnitLinkContent.vue'
import ItemPrice from './ItemPrice.vue'
import ListItemCard from './ListItemCard.vue'
import ListItemCardChips from './ListItemCardChips.vue'
import {unitStore} from 'stores/unit'
const unit = unitStore()
import {useRoute, useRouter} from 'vue-router'
const route = useRoute()
const router = useRouter()
import {ref} from 'vue'
const pid = ref(route.params.id !== unit.one._id ? route.params.id : null)

import {serviceAuthStore} from 'stores/service-auth'
// library 课件不需要查询认证项
const serviceAuth = unit.isLib ? null : serviceAuthStore()

defineProps({items: {type: Array}})

const goBack = (hash) => {
  let path = route.query.back || '/home/<USER>'
  const query = route.query
  if (query?.back) delete query.back
  if (hash && query.back) {
    path = query.back.replace(/(#\w+)$/, hash)
  }
  router.replace(path)
}
</script>
