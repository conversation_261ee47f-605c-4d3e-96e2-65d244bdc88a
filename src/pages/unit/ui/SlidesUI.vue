<template>
  <div v-if="unit.one?.pages">
    <SlidesBox @change="onSlideChanged" :content="unit.one" />
    <div class="row flex items-center">
      <q-space></q-space>
      <div class="text-h6 q-pa-sm">
        <span> {{ index + 1 }}/{{ unit.one.pageNum }} </span>
      </div>
    </div>
    <div class="text-center rounded-borders border-1 q-pa-md q-mb-md" v-if="locked && !unit.isMember">
      <div class="text-h5 q-pb-md">Please purchase to view the details</div>
      <q-btn rounded class="full-width" color="primary" label="Buy now" no-caps @click="buyFn"></q-btn>
    </div>
    <div class="rounded-borders border-1 q-my-md" v-else-if="unit.pageId">
      <SlidesQuestion :question="unit.question" :material="unit.material" :numbersOfOutline="unit.numbersOfOutline" />
    </div>
  </div>
</template>
<script setup>
import {ref} from 'vue'
import {unitStore} from 'stores/unit'
const unit = unitStore()

import SlidesBox from './SlidesBox.vue'
import SlidesQuestion from './SlidesQuestion.vue'

const index = ref(0)
const locked = ref(false)
const onSlideChanged = (i, slides) => {
  index.value = i
  locked.value = slides[i].locked
  unit.pageId = slides[i].pageId
}

const buyFn = async () => {}
</script>
