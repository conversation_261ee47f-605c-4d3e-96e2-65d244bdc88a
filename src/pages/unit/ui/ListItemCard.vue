<template>
  <q-card class="full-width">
    <q-item clickable class="q-pa-sm overflow-auto" :to="Acan.isDefined(nolink) ? null : unit.viewRoute(doc, pid)">
      <div :class="$q.screen.gt.xs ? 'row' : 'column'" class="full-width">
        <div class="item-cover" style="max-width: 50%">
          <q-img :src="doc.cover || '/v2/img/no-img.png'" class="rounded-borders" :ratio="16 / 9">
            <div class="absolute-bottom-left bg-transparent" style="padding: 8px">
              <q-avatar size="1.6rem" :color="UnitModeMap[doc.mode].color" text-color="white" :icon="UnitModeMap[doc.mode].icon" />
            </div>
          </q-img>
        </div>
        <div class="col column full-width q-pa-none relative-position" :class="$q.screen.gt.xs ? 'q-pl-md' : ''">
          <q-item-label class="q-py-xs text-h6 row">
            <AuthIcon v-if="!unit.isLib" :doc="doc" />
            <span class="col ellipsis">
              {{ doc.name }}
              <span v-if="!doc.name" class="text-grey-7">Untitled</span>
            </span>
            <q-space></q-space>
            <q-btn v-if="doc.orderId" class="q-mx-xs" round size="sm" text-color="grey-8" icon="o_shopping_bag"></q-btn>
          </q-item-label>
          <ListItemCardChips :doc="doc" :dense="dense" />
          <q-space></q-space>
          <div class="row items-center">
            <OwnerBox v-if="doc.owner" class="col" :owner="doc.owner" :updatedAt="doc.createdAt" />
            <q-space></q-space>
            <!-- 发布的课件显示价格（不存在按钮的时候） -->
            <ItemPrice v-if="!$slots.button" :doc="doc"></ItemPrice>
            <slot name="button"></slot>
          </div>
        </div>
      </div>
    </q-item>
  </q-card>
</template>
<script setup>
import OwnerBox from 'components/detail/OwnerBox.vue'
import ItemPrice from './ItemPrice.vue'
import ListItemCardChips from './ListItemCardChips.vue'
import AuthIcon from './AuthIcon.vue'
import {unitStore} from 'stores/unit'
const unit = unitStore()

defineProps(['doc', 'dense', 'pid', 'nolink'])
</script>
<style scoped>
.item-cover {
  width: 22rem;
}
body.screen--xs .item-cover {
  width: 100%;
  padding-bottom: 1rem;
}
</style>
