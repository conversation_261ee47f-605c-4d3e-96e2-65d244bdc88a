<template>
  <div v-if="one">
    <OnePage v-if="one.mode == 'tool'" :isPreview="true" :assessmentToolOneId="one._id"></OnePage>
    <template v-else>
      <OverviewItems :content="one" />
      <OutlinePreview :isStudent="pub.isStudent" v-if="one.outline" :outline="one.outline" />
    </template>
  </div>
</template>
<script setup>
import OnePage from 'pages/account/assessment-tool/OnePage.vue'
import OverviewItems from './OverviewItems.vue'
import OutlinePreview from 'components/OutlinePreview.vue'
defineProps(['one'])
import {pubStore} from 'stores/pub'
const pub = pubStore()
</script>
