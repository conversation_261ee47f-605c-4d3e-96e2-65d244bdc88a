<template>
  <div v-if="score">
    <q-toolbar v-if="outlineNum && (score.enable || score.val)">
      <div :class="$q.screen.lt.sm ? 'col' : 'row'">
        <!-- <div class="q-mr-sm">
          <q-toggle v-model="form.enable"></q-toggle>
          <span> Using score </span>
        </div>
        <div class="q-mr-sm">
          <q-toggle v-model="form.rubric" @update:model-value="openChooseCriteria"></q-toggle>
          <span> Rubric </span>
        </div> -->
        <div v-if="score.criteria && score.rubric">
          <q-badge class="q-mt-sm q-ml-sm" v-for="(standard, i) in score.criteria.standard" :key="i" :color="session.tagColorList[i]">
            {{ standard.code }}
          </q-badge>
        </div>
        <!-- <div v-if="score.criteria && score.rubric">
          <q-btn round flat icon="sync" size="0.7rem" class="text-teal" @click="ChooseCriteriaDialogOpen()"></q-btn>
        </div> -->
      </div>
      <q-space />
      <div v-if="score.enable">
        <span class="text-subtitle2">Total score: </span>
        <span class="text-h6">{{ score.val ? score.val : 0 }}</span>
      </div>
    </q-toolbar>
    <div v-if="score.criteria && outlineNum" class="q-mx-lg text-grey text-caption">
      The points were originally set in
      <span class="text-black text-subtitle2">
        {{ sortOrder[tagType] }}
        <!-- <q-icon :name="sortOrder[outlineType] === 'Descending' ? 'keyboard_arrow_down' : 'keyboard_arrow_up'"></q-icon> -->
        Order
      </span>
    </div>
    <div v-for="(outlineSubject, index) in outline" :key="index">
      <q-item class="row">
        <q-item-section class="text-subtitle2">
          {{ tagType === 'goal' ? `${outlineSubject.name}` : `${outlineSubject.name}(${outlineSubject.curr}) ` }}
        </q-item-section>
      </q-item>
      <QuestionScoreRubric :child="outlineSubject.child" :score="score" />
    </div>
  </div>
</template>
<script setup>
import {ref, onMounted} from 'vue'
// import MaterialLists from 'pages/com/AddOnComponents/MaterialLists2.vue'
import QuestionScoreRubric from 'pages/com/AddOnComponents/QuestionScoreRubric.vue'
// import QuestionAnswerDialog from 'pages/com/AddOnComponents/QuestionAnswerDialog.vue'

import {sessionStore} from 'stores/session'
const session = sessionStore()

const props = defineProps(['score', 'outlineNum', 'tagType', 'outline'])

const sortOrder = ref({assess: 'Descending', outline: 'Descending', skills: 'Descending', goal: 'Descending'})

const form = ref({})
onMounted(() => {
  form.value = props.score ?? {}
})
</script>
