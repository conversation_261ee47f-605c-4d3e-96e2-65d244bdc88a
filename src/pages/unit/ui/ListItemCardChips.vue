<template>
  <q-item-label class="q-gutter-sm">
    <!-- <span class="text-yellow-9">{{ unit._getTitle(doc) }}</span> -->
    <!-- pdtask/pdUnit -->
    <span class="text-primary" v-if="!Acan.isEmpty(doc.service?.type)"> {{ mentoringTypeMap[doc.service?.type] }} </span>
    <!-- unit/task -->
    <span class="text-primary" v-else>
      {{ curriculum.pubList[doc.curriculum]?.replace('curriculum', '') ?? 'Others' }}
    </span>
  </q-item-label>
  <q-item-label v-if="!Acan.isEmpty(doc.subjects)">
    Subject
    <q-chip v-for="(o, i) in doc.subjects" :key="i" :label="o.value" color="teal-1" text-color="primary"></q-chip>
  </q-item-label>
  <q-item-label v-if="UnitTypeTitles[doc.type]">
    Unit type
    <q-chip class="text-weight-medium" :ripple="false" color="teal-1" text-color="primary" :label="UnitTypeTitles[doc.type]"></q-chip>
  </q-item-label>
  <q-item-label v-if="doc.sessionType">
    Session type
    <q-chip class="text-weight-medium" :ripple="false" color="teal-1" text-color="primary" :label="doc.sessionType === 'live' ? 'Live' : 'Self study'"></q-chip>
  </q-item-label>
  <q-item-label v-if="doc.type && !Acan.isDefined(dense)">
    Assessment type
    <q-chip class="text-weight-medium" :ripple="false" :label="doc.type" color="teal-1" text-color="primary"></q-chip>
  </q-item-label>
  <q-item-label v-if="!Acan.isEmpty(doc.grades)">
    Grade
    <template v-for="(o, i) in doc.grades" :key="i">
      <q-chip :label="o.value" color="teal-1" text-color="primary"></q-chip>
      <q-chip v-if="o.label" :label="o.label" color="teal-1" text-color="primary"></q-chip>
    </template>
  </q-item-label>
</template>
<script setup>
import {curriculumStore} from 'stores/curriculum'
const curriculum = curriculumStore()
import {unitStore} from 'stores/unit'
const unit = unitStore()
defineProps(['doc', 'dense'])
</script>
