<template>
  <div class="full-height col">
    <div v-if="question">
      <q-toolbar>
        <q-avatar :class="`bg-${question.color} text-white`" round :icon="question.icon"></q-avatar>
        <q-toolbar-title>
          <div class="text-h5">{{ question.title }}</div>
        </q-toolbar-title>
      </q-toolbar>
      <!-- <MaterialLists :allMaterials="material.list" :materialList="material.list" :preview="true" /> -->
      <div>
        <q-toolbar>
          <q-item-label class="text-h6">Details</q-item-label>
        </q-toolbar>
        <div v-if="question.type === 'choice'" class="q-px-md q-py-sm text-teal" @click="viewAnswer()">
          <div>
            View answers
            <q-icon name="chevron_right"></q-icon>
          </div>
        </div>
      </div>

      <q-separator inset></q-separator>
      <q-card-section v-if="question.outlines">
        <div v-for="(tagType, i) in ['assess', 'outline', 'skills', 'goal']" :key="i">
          <q-expansion-item switch-toggle-side expand-separator :label="`${OutlineTypesTitle[tagType]} (${numbersOfOutline[tagType]})`">
            <SlidesQuestionScore
              :tagType="tagType"
              :score="question.score[tagType]"
              :outline="question.outlines[tagType]"
              :outlineNum="numbersOfOutline[tagType]" />
          </q-expansion-item>
        </div>
      </q-card-section>
    </div>
  </div>
</template>
<script setup>
import MaterialLists from './MaterialLists.vue'
import QuestionAnswerDialog from 'pages/com/AddOnComponents/QuestionAnswerDialog.vue'
import SlidesQuestionScore from './SlidesQuestionScore.vue'
const props = defineProps(['question', 'material', 'numbersOfOutline'])
function viewAnswer() {
  $q.dialog({
    component: QuestionAnswerDialog,
    componentProps: {
      question: props.question,
      preview: true,
    },
  })
}
</script>
