<template>
  <q-item-label class="q-gutter-sm">
    <span class="text-yellow-9">{{ unit._getTitle(doc) }}</span>
    <!-- pdtask/pdUnit -->
    <span class="text-primary" v-if="authDoc.curriculum === 'pd'"> {{ mentoringTypeMap[authDoc.subject] }} </span>
    <!-- unit/task -->
    <span class="text-primary" v-else>
      {{ curriculum.pubList[authDoc.curriculum]?.replace('curriculum', '') ?? 'Others' }}
    </span>
  </q-item-label>
  <q-item-label v-if="authDoc.curriculum !== 'pd'">
    Subject
    <q-chip :label="subjectMaps[authDoc.subject]" color="teal-1" text-color="primary"></q-chip>
  </q-item-label>
  <q-item-label v-if="authDoc.topic">
    Topic
    <template v-for="(topic, ti) in authDoc.topic" :key="ti">
      <q-chip v-for="(o, i) in topic.label" :key="i" class="text-weight-medium" :ripple="false" :label="o" color="teal-1" text-color="primary" />
    </template>
  </q-item-label>
  <q-item-label class="row items-center" v-if="!Acan.isEmpty(authDoc.gradeGroup)">
    Grades
    <template v-for="(o, i) in authDoc.gradeGroup" :key="i">
      <q-chip v-if="GradeGroupMap[o]" :label="GradeGroupMap[o].label" color="teal-1" text-color="primary"></q-chip>
    </template>
    <span v-for="grade in authDoc.grades" :key="grade" class="text-primary q-pa-xs">{{ grade }}</span>
  </q-item-label>
</template>
<script setup>
import {curriculumStore} from 'stores/curriculum'
const curriculum = curriculumStore()
import {unitStore} from 'stores/unit'
import {onMounted} from 'vue'
const unit = unitStore()
const props = defineProps(['authDoc', 'subjectMaps'])
const doc = props.authDoc.unitSnapshot
</script>
