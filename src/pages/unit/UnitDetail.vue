<template>
  <q-page style="padding: 1rem 1rem 0.5rem 1rem">
    <q-inner-loading :showing="loading" />
    <BreadCrumbs title="Preview" v-if="route.query.back"></BreadCrumbs>
    <UnitDetail :items="items" v-if="!loading">
      <template v-slot:btns>
        <q-btn
          v-for="(o, i) in btns"
          :key="i"
          class="full-width"
          outline
          rounded
          color="primary"
          :label="o.label"
          :icon="o.icon"
          no-caps
          @click="o.click"
          :loading="loadings[o.label]" />
      </template>
    </UnitDetail>
    <div class="row full-width q-pa-md"></div>
  </q-page>
</template>
<script setup>
import {ref, onMounted} from 'vue'
import BreadCrumbs from 'components/BreadCrumbs.vue'
import UnitDetail from './ui/UnitDetail.vue'
import {unitStore} from 'stores/unit'
const unit = unitStore()
import {useRoute, useRouter} from 'vue-router'
const route = useRoute()
const router = useRouter()
const items = ref([])

const loadings = ref({})
const deleteFn = () => {
  const {_id, name} = unit.one
  $q.dialog({
    title: `Confirm delete`,
    message: `Please confirm that you want to delete : "${name || 'Untitled'}"?`,
    cancel: true,
  }).onOk(async () => {
    loadings.value.Delete = true
    await unit.remove(_id)
    $q.notify({type: 'info', message: 'Successful'})
    loadings.value.Delete = false
    goBack()
  })
}
const archiveFn = () => {
  const {_id, name, del} = unit.one
  $q.dialog({
    title: `Confirm ${!del ? 'Archive' : 'Restoration'}`,
    message: `Please confirm that you want to ${!del ? 'archive' : 'restore'} the content "${name || 'Untitled'}".`,
    cancel: true,
  }).onOk(async () => {
    loadings.value[!del ? 'Archive' : 'Restore'] = true
    await unit.patch(_id, {del: !del})
    $q.notify({type: 'info', message: 'Successful'})
    loadings.value[!del ? 'Archive' : 'Restore'] = false
    goBack()
  })
}
const duplicateFn = () => {
  const {_id, name} = unit.one
  $q.dialog({
    title: 'Confirm',
    message: `Duplicate content "${name || 'Untitled'}"?`,
    cancel: true,
  }).onOk(async () => {
    loadings.value.Duplicate = true
    const post = {_id}
    post.name = name + ' - Copy 1'
    if (!isNaN(parseInt(name?.match(/ Copy (\d)$/)?.[1]))) {
      let copyText = ' Copy ' + (parseInt(name?.match(/ \d+$/)?.[0]) + 1)
      post.name = name.replace(/ Copy \d+$/, copyText)
    }
    await unit.patch('copy', post)
    $q.notify({type: 'info', message: 'Successful'})
    loadings.value.Duplicate = false
    router.replace('/unit/my')
  })
}

const leaveFn = () => {
  const {name} = unit.one
  if (!unit.member?._id) return
  $q.dialog({
    title: 'Confirm',
    message: `Leave content collaboration: "${name}"?`,
    cancel: true,
  }).onOk(async () => {
    loadings.value.Leave = true
    await App.service('collab').patch(unit.collab._id, {$pull: {members: {_id: unit.member._id}}})
    loadings.value.Leave = false
    $q.notify({type: 'info', message: 'Successful'})
  })
}
const btns = ref([])
function initBts() {
  let arr = []
  const dupBtn = {label: 'Duplicate', icon: 'o_file_copy', click: duplicateFn}
  if (unit.isOwner) {
    if (unit.one.del) {
      arr = [
        {label: 'Restore', icon: 'o_restore', click: archiveFn},
        {label: 'Delete', icon: 'o_delete', click: deleteFn},
      ]
    } else {
      arr = [{label: 'Archive', icon: 'o_inventory_2', click: archiveFn}, dupBtn]
    }
  } else {
    if (unit.member) arr.push({label: 'Leave', icon: 'logout', click: leaveFn})
    arr.push(dupBtn)
  }
  btns.value = arr
}
import PromptPage from 'components/PromptPage.vue'
const isLib = ref(false)
const loading = ref(true)
onMounted(async () => {
  const {id} = route.params
  isLib.value = !route.path.includes('/my/')
  const query = {back: route.fullPath}
  const arr = [{label: 'Overview', icon: 'o_visibility', to: {path: route.path.replace(id, 'overview/' + id), query}}]

  await unit.get(id, true)
  await unit.getCollab()
  if (unit.isTask) arr.push({label: 'Slides', icon: 'o_slideshow', to: {path: route.path.replace(id, 'slides/' + id), query}})

  if (!unit.one || (!unit.isOwner && !unit.member)) {
    return $q.dialog({
      component: PromptPage,
      componentProps: {
        fullscreen: true,
        persistent: true,
        cancel: false,
        logo: true,
        title: 'The products are no longer available',
        back: true,
      },
    })
  }
  loading.value = false
  initBts()
  const teachingNotesItem = {
    label: 'Teaching notes',
    icon: 'o_note_alt',
    caption: 'Teaching experience shared by others',
    to: {path: '/reflect/lib/' + id, query},
  }
  if (!unit.isTool) {
    arr.push(
      {label: 'Schedule', icon: 'o_event_note', to: unit.editRoute(unit.one, route, {action: 'schedule'})},
      {label: route.query.back?.includes('sys') ? 'Evaluation' : 'Reflection', icon: 'o_event_note', to: {path: '/reflect/' + id, query}}
    )
  }
  if (unit.isOwner && ((unit.isTask && unit.one.sid !== 'disable' && !unit.one.sid?.includes('hash:')) || unit.isUnit))
    arr.push({
      label: unit.one.publish?.lib ? 'Republish' : 'Publish',
      icon: 'o_drive_folder_upload',
      to: {path: '/com/task/edit/' + id, query: {action: 'publish', ...query}},
    })
  else if (!unit.isTool) arr.push(teachingNotesItem)
  arr.push({label: 'Edit', icon: 'edit', to: unit.editRoute(unit.one, route)})
  if (unit.isOwner && unit.one.sid)
    arr.push({
      label: 'Edit prompts',
      icon: 'o_post_add',
      disable: unit.one.sid.includes('hash:'), // 未初始化的禁用
      to: {path: '/detail/prompts/edit/' + id, query},
      tips: 'Please click the “Edit” button above, then choose "Edit Slides", click the “Initiate slides” button to save the copied slides into your drive before editing prompts.',
    })
  items.value = arr
})

const goBack = (hash) => {
  let path = route.query.back || '/home/<USER>'
  const query = route.query
  if (query?.back) delete query.back
  if (hash && query.back) {
    path = query.back.replace(/(#\w+)$/, hash)
  }
  router.replace(path)
}
</script>
