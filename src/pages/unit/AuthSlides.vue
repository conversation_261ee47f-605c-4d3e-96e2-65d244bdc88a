<template>
  <q-page class="q-pa-md">
    <div class="text-h5 text-weight-medium q-mb-md">
      <q-btn flat round dense size="lg" icon="arrow_back" @click="goBack"></q-btn>
      Slides
    </div>
    <div v-if="loading" class="text-center q-pa-xl text-grey">
      <q-spinner-ball color="primary" size="2em" class="full-width" />
    </div>
    <template v-else-if="unit.one.pages">
      <SessionSlide @change="onSlideChanged" :content="unit.one" />
      <div class="row flex items-center">
        <q-space></q-space>
        <div class="text-h6 q-pa-sm">
          <span> {{ index + 1 }}/{{ unit.one.pageNum }} </span>
        </div>
      </div>
      <div class="text-center rounded-borders border-1 q-pa-md q-mb-md" v-if="locked && !unit.isMember">
        <div class="text-h5 q-pb-md">Please purchase to view the details</div>
        <q-btn rounded class="full-width" color="primary" label="Buy now" no-caps @click="buyFn"></q-btn>
      </div>
      <div class="rounded-borders border-1 q-my-md" v-else-if="unit.pageId">
        <SlidesPageQuestion :question="unit.question" :material="unit.material" :numbersOfOutline="unit.numbersOfOutline" />
      </div>
    </template>
  </q-page>
</template>
<script setup>
import {ref, onMounted} from 'vue'
import {useRoute, useRouter} from 'vue-router'
const route = useRoute()
const router = useRouter()
import {unitStore} from 'stores/unit'
const unit = unitStore()

import SessionSlide from '../session/SessionSlide.vue'
import SlidesPageQuestion from '../session/SlidesPageQuestion.vue'

const loading = ref(true)
const index = ref(0)
const locked = ref(false)
const onSlideChanged = (i, slides) => {
  index.value = i
  locked.value = slides[i].locked
  unit.pageId = slides[i].pageId
}

const goBack = () => {
  router.replace(route.query.back || '/home/<USER>')
}

const buyFn = async () => {}
onMounted(async () => {
  const {authId, id} = route.params
  const authDoc = await App.service('service-auth').get(authId)
  if (!authDoc.unit) return console.log('is not service-auth content')
  unit.setAuthUnit(id ? authDoc.linkSnapshot[id] : authDoc.unitSnapshot)
  loading.value = false
})
</script>
