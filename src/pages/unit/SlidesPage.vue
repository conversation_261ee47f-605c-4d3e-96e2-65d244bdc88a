<template>
  <q-page class="q-pa-md">
    <div class="text-h5 text-weight-medium q-mb-md">
      <q-btn flat round dense size="lg" icon="arrow_back" @click="goBack"></q-btn>
      Slides
    </div>
    <div v-if="loading" class="text-center q-pa-xl text-grey">
      <q-spinner-ball color="primary" size="2em" class="full-width" />
    </div>
    <SlidesUI v-else-if="unit.one.pages"></SlidesUI>
  </q-page>
</template>
<script setup>
import {ref, onMounted} from 'vue'
import {useRoute, useRouter} from 'vue-router'
const route = useRoute()
const router = useRouter()
import {unitStore} from 'stores/unit'
const unit = unitStore()

import SlidesUI from './ui/SlidesUI.vue'

const loading = ref(true)

const goBack = () => {
  router.replace(route.query.back || '/home/<USER>')
}

onMounted(async () => {
  const {id} = route.params
  if (!id) return
  await unit.get(id, true)
  loading.value = false
})
</script>
