<template>
  <q-page :class="pub.isStudent ? 'student-theme pc-body' : 'q-pa-md pc-body'">
    <div class="text-h5 text-weight-medium" :class="pub.isStudent ? ' q-pa-md student-bg rounded-borders-bottom-left-right-lg' : ''">
      <q-btn flat round dense size="lg" icon="arrow_back" @click="goBack"></q-btn>
      <q-icon v-if="pub.isStudent" size="lg" :name="`svguse:/v2/icons.svg#kid-overview`"> </q-icon>
      Overview
    </div>
    <q-spinner-ball v-if="loading" color="primary" size="2em" class="full-width" />
    <div v-else-if="unit.one">
      <OnePage v-if="unit.one.mode == 'tool'" :isPreview="true" :assessmentToolOneId="unit.one._id"></OnePage>
      <SessionOverviewItems v-else :content="{...unit.one, outline: unit.outline}" :gradeOptions="gradeOptions" />
    </div>
  </q-page>
</template>

<script setup>
import {ref, onMounted} from 'vue'
import {useRoute, useRouter} from 'vue-router'
const route = useRoute()
const router = useRouter()

import {curriculumStore} from 'stores/curriculum'
const curriculum = curriculumStore()
import {unitStore} from 'stores/unit'
const unit = unitStore()

import SessionOverviewItems from '../session/SessionOverviewItems.vue'
// import OnePage from 'pages/account/assessment-tool/OnePage.vue'

import {pubStore} from 'stores/pub'
const pub = pubStore()

const loading = ref(true)
const gradeOptions = ref(null)

const goBack = () => {
  router.replace(route.query.back || '/home/<USER>')
}
onMounted(async () => {
  const {authId, id} = route.params
  const authDoc = await App.service('service-auth').get(authId)
  if (!authDoc.unit) return console.log('is not service-auth content')
  unit.setAuthUnit(id ? authDoc.linkSnapshot[id] : authDoc.unitSnapshot)
  const {uid} = unit.one
  gradeOptions.value = await curriculum.gradeOptions(uid)
  loading.value = false
})
</script>
