<template>
  <q-page class="column full-height" style="padding: 1rem 1rem 0.5rem 1rem" :class="route.query.header === '0' ? '' : 'pc-max'">
    <q-card class="column col full-height full-width">
      <div class="q-pa-md text-h5 text-bold">My contents</div>
      <div class="row full-width q-px-md q-pb-md">
        <q-tabs
          :outside-arrows="$q.screen.lt.sm"
          inline-label
          mobile-arrows
          shrink
          v-model="tab"
          stretch
          active-class="text-teal"
          class="text-bold"
          @update:model-value="resetFn()">
          <q-tab v-for="(o, i) in tabs" :key="i" :name="o.value" no-caps>
            {{ o.label }}
            <template v-if="o.value === tab && !loading">({{ list.total }})</template>
          </q-tab>
        </q-tabs>
        <q-space />
        <div class="row col-xs-12 col-sm-3 col-md-3 justify-end">
          <UnitCreateBtn />
        </div>
      </div>
      <q-toolbar class="q-px-md q-pb-sm">
        <SearchFilter :items="['mode']" @change="filterFn" @search="searchFn" />
        <q-space></q-space>
        <div v-if="pagination?.total" class="text-grey-7 text-right" style="min-width: 4.6rem">
          {{ pagination.start }} - {{ pagination.end }}, of {{ pagination.total }}
        </div>
      </q-toolbar>
      <NoData v-if="!loading && isEmpty(listData)" />
      <q-virtual-scroll v-else separator class="col full-width overflow-auto" :items="listData" @virtual-scroll="scrollFn">
        <template v-slot:after>
          <div v-if="list.data?.length === list.limit" class="row justify-center q-my-md">
            <q-spinner-dots color="primary" size="40px" />
          </div>
          <div v-else class="q-pa-md text-grey text-center">It's over</div>
        </template>
        <template v-slot="{item: o}">
          <q-item>
            <ListItemCard :doc="o" dense>
              <template v-slot:button>
                <template v-if="tab !== 'arch'">
                  <div class="q-gutter-sm">
                    <q-btn rounded outline label="Edit" color="grey-7" :to="unit.editRoute(o)" no-caps></q-btn>
                    <q-btn v-if="!['tool'].includes(o.mode)" rounded outline label="Schedule" color="primary" :to="unit.scheduleRoute(o)" no-caps></q-btn>
                  </div>
                </template>
              </template>
            </ListItemCard>
          </q-item>
        </template>
      </q-virtual-scroll>
    </q-card>
  </q-page>
</template>
<script setup>
import {ref, onMounted, computed} from 'vue'
import ListItemCard from './ui/ListItemCard.vue'
import UnitCreateBtn from './UnitCreateBtn.vue'
import SearchFilter from './SearchFilter.vue'
import {unitStore} from 'stores/unit'
const unit = unitStore()
unit.isLib = false
import {useRoute, useRouter} from 'vue-router'
const route = useRoute()
const router = useRouter()
const loading = ref(false),
  search = ref(''),
  listData = ref([]),
  list = ref({}),
  query = ref({$sort: {createdAt: -1}}),
  tab = ref('me'),
  tabs = ref([
    {label: 'Created by me', value: 'me'},
    {label: 'Shared by me', value: 'share'},
    {label: 'Shared by others', value: 'other'},
    {label: 'Archived', value: 'arch'},
  ])
function searchFn(key) {
  if (Acan.isEmpty(key)) delete query.value.name
  else query.value.name = {$regex: key, $options: 'i'}
  resetFn()
}
function filterFn(data) {
  query.value = {$sort: {createdAt: -1}, ...data}
  resetFn()
}

const virtualListIndex = ref(1)
const pagination = computed(() => {
  return {start: virtualListIndex.value + 1, end: listData.value.length, total: list.value.total}
})
async function scrollFn({index, to}) {
  virtualListIndex.value = index
  if (index !== to) return // console.warn(index, to, direction)
  const {limit = 10, skip = 0, data} = list.value
  if (Acan.isEmpty(data) || data.length < limit) return // has last page
  console.warn('need load more')
  query.value.$skip = skip + limit
  await find()
}

function resetFn() {
  query.value.$skip = 0
  listData.value.length = 0
  list.value = {}
  find()
}
async function find() {
  router.replace({query: {...route.query, tab: tab.value}})
  query.value.tab = tab.value
  if (search.value) query.value.$search = search.value
  else delete query.value.$search
  loading.value = true
  const rs = (list.value = await unit.find(query.value))
  loading.value = false
  listData.value.push(...rs.data)
  return rs.data.length
}
onMounted(find)
</script>
