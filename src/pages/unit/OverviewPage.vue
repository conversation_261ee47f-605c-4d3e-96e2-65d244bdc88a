<template>
  <q-page :class="pub.isStudent ? 'student-theme pc-body' : 'q-pa-md pc-body'">
    <div class="text-h5 text-weight-medium" :class="pub.isStudent ? ' q-pa-md student-bg rounded-borders-bottom-left-right-lg' : ''">
      <q-btn flat round dense size="lg" icon="arrow_back" @click="goBack"></q-btn>
      <q-icon v-if="pub.isStudent" size="lg" :name="`svguse:/v2/icons.svg#kid-overview`"> </q-icon>
      Overview
    </div>
    <q-spinner-ball v-if="loading" color="primary" size="2em" class="full-width" />
    <div v-else-if="unit.one">
      <OnePage v-if="unit.isTool" :isPreview="true" :assessmentToolOneId="unit.one._id"></OnePage>
      <template v-else>
        <OverviewItems :content="unit.one" />
        <OutlinePreview :isStudent="pub.isStudent" v-if="unit.outline" :outline="unit.outline" :gradeOptions="gradeOptions" />
      </template>
    </div>
  </q-page>
</template>

<script setup>
import {ref, onMounted} from 'vue'
import {useRoute, useRouter} from 'vue-router'
const route = useRoute()
const router = useRouter()

import {curriculumStore} from 'stores/curriculum'
const curriculum = curriculumStore()
import {unitStore} from 'stores/unit'
const unit = unitStore()

import OverviewItems from './ui/OverviewItems.vue'
import OutlinePreview from 'components/OutlinePreview.vue'
import OnePage from 'pages/account/assessment-tool/OnePage.vue'

import {pubStore} from 'stores/pub'
const pub = pubStore()

const loading = ref(true)
const gradeOptions = ref(null)

const goBack = () => {
  router.replace(route.query.back || '/home/<USER>')
}
const isLib = ref(false)
onMounted(async () => {
  isLib.value = !route.path.includes('/my/')
  await unit.get(route.params.id, isLib.value)
  const {uid} = unit.one
  gradeOptions.value = await curriculum.gradeOptions(uid)
  loading.value = false
})
</script>
