<template>
  <q-page :class="pub.isStudent ? 'student-theme pc-body' : 'q-pa-md pc-body'">
    <div class="text-h5 text-weight-medium" :class="pub.isStudent ? ' q-pa-md student-bg rounded-borders-bottom-left-right-lg' : ''">
      <q-btn flat round dense size="lg" icon="arrow_back" @click="goBack"></q-btn>
      <q-icon v-if="pub.isStudent" size="lg" :name="`svguse:/v2/icons.svg#kid-overview`"> </q-icon>
      Overview
    </div>
    <q-spinner-ball v-if="loading" color="primary" size="2em" class="full-width" />
    <OverView v-else-if="unit.one" :one="unit.one" />
  </q-page>
</template>

<script setup>
import {ref, onMounted} from 'vue'
import {useRoute, useRouter} from 'vue-router'
const route = useRoute()
const router = useRouter()

import {unitStore} from 'stores/unit'
const unit = unitStore()

import OverView from './ui/OverView.vue'

import {pubStore} from 'stores/pub'
const pub = pubStore()

const loading = ref(true)
// const gradeOptions = ref(null)

const goBack = () => {
  router.replace(route.query.back || '/home/<USER>')
}
onMounted(async () => {
  const {id, linkId} = route.params
  if (!id) return
  await unit.getLib(id, linkId)
  // const {uid} = unit.one
  // gradeOptions.value = await curriculum.gradeOptions(uid)
  loading.value = false
})
</script>
