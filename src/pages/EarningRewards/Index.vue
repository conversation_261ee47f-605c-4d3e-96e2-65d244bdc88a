<template>
  <div class="full-height">
    <q-layout view="hHh Lpr lff" class="detail-layout">
      <LeftDrawer :menu-list="menuItems" :active="activeTab" />
      <q-page-container>
        <router-view />
      </q-page-container>
    </q-layout>
  </div>
</template>

<script setup>
import {computed, onMounted, ref, watch} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import useSchool from 'src/composables/common/useSchool'
import LeftDrawer from '../order/components/LeftDrawer.vue'

const route = useRoute()
const router = useRouter()
const {schoolId, isSchool, isTeacher, isStudent} = useSchool()

const pageTitle = ref('')
const menuList = ref([])
const activeTab = ref('Income')

const menuItems = ref(
  [
    {label: 'Income', to: '/account/earning-rewards/income'},
    {label: 'Commission', to: '/account/earning-rewards/commission'},
    {label: 'Point', to: '/account/earning-rewards/point'},
  ].filter((item) => {
    if (item.label === 'Income' && (isSchool.value || isStudent.value)) {
      return false
    } else if (item.label === 'Point' && isSchool.value) {
      return false
    }
    return true
  })
)

watch(
  () => route.path,
  (newPath) => {
    if (newPath.includes('/income')) {
      activeTab.value = 'Income'
    } else if (newPath.includes('/commission')) {
      activeTab.value = 'Commission'
    } else if (newPath.includes('/point')) {
      activeTab.value = 'Point'
    } else {
      console.log('Invalfjbcshdgbvush')
      if (menuItems.value.length > 0) {
        activeTab.value = menuItems.value[0].label
      }
    }
  },
  {immediate: true}
)

onMounted(() => {
  if (menuItems.value.length > 0 && (route.path.endsWith('/earning-rewards') || route.path.endsWith('/earning-rewards/'))) {
    router.push(menuItems.value[0].to)
  }
})
</script>

<style scoped>
.detail-layout {
  height: 100vh;
}

.full-height {
  height: 100%;
}
</style>
