<template>
  <main>
    <canvas class="record-canvas" v-if="isRecording" ref="canvasRef" width="400" height="100"></canvas>
    <div v-if="src && src.length" ref="waveformContainer" class="waveform"></div>
    <div class="controls" v-if="src && src.length">
      <q-btn :size="'sm'" flat dense round unelevated color="primary" @click="togglePlayPause">
        <q-icon :name="isPlaying ? 'pause' : 'play_arrow'" size="24px" />
      </q-btn>
      <div class="text-caption" style="min-width: 80px; color: #232323">{{ formatTime(currentTime) }} / {{ formatTime(duration) }}</div>
      <q-slider thumb-size="0px" v-model="progress" :min="0" :max="100" :step="0.1" @update:model-value="seekAudio" class="progress-bar" color="primary" />
      <div class="column items-center">
        <q-btn @mouseenter="handleMouseEnter" @mouseleave="handleMouseLeave" size="sm" flat dense round unelevated color="primary" @click="toggleMute">
          <q-icon :name="isMuted ? 'volume_off' : 'volume_up'" size="24px" />
        </q-btn>
        <div v-if="showVolumeSlider" ref="volumeSlider" @mouseenter="isHovering = true" @mouseleave="isHovering = false">
          <q-slider
            v-model="volume"
            vertical
            reverse
            :min="0"
            :max="100"
            :step="1"
            thumb-size="15px"
            color="primary"
            track-color="grey"
            @update:model-value="changeVolume"
            style="
              height: 100px;
              position: absolute;
              bottom: 40px;
              margin-top: 10px;
              background: #fff;
              padding: 10px 0 15px 0;
              border-radius: 8px;
              box-shadow: 1px 2px 4px 1px #c4c2c2;
              z-index: 2;
            " />
        </div>
      </div>
    </div>
  </main>
</template>

<script setup>
import {ref, onMounted, onUnmounted, watch, nextTick, watchEffect} from 'vue'
import WaveSurfer from 'wavesurfer.js'

const props = defineProps({
  src: String,
  isRecording: Boolean,
})

const recognition = new (window.SpeechRecognition || window.webkitSpeechRecognition)()
recognition.continuous = false
recognition.lang = 'en-US'

const isListening = ref(false)
const pitchValue = ref(0)
const canvasRef = ref(null)

const waveformContainer = ref(null)
const wavesurfer = ref(null)
const isPlaying = ref(false)
const volume = ref(50)
const isMuted = ref(false)
const volumeSlider = ref(null)
const previousVolume = ref(0.5)
const progress = ref(0)
const currentTime = ref(0)
const duration = ref(0)

const isDragging = ref(false)
const showVolumeSlider = ref(false)
const isHovering = ref(false)

const getAudioFormat = (src) => {
  if (!src) return null
  const extension = src.split('.').pop().toLowerCase()
  return extension
}

const handleMouseEnter = () => {
  showVolumeSlider.value = true
}

const handleMouseLeave = () => {
  // if (!isDragging.value) {
  //   showVolumeSlider.value = false
  // }
}

const handlePointerDown = (event) => {
  if (volumeSlider.value && volumeSlider.value.contains(event.target)) {
    isDragging.value = true
  }
}

const handlePointerUp = () => {
  setTimeout(() => {
    isDragging.value = false
  }, 100)
}

const handleClickOutside = (event) => {
  if (isDragging.value || isHovering.value) return

  if (volumeSlider.value && !volumeSlider.value.contains(event.target)) {
    showVolumeSlider.value = false
  }
}

const seekAudio = (value) => {
  if (wavesurfer.value) {
    if (duration.value > 0) {
      const newTime = (value / 100) * duration.value
      wavesurfer.value.seekTo(newTime / duration.value)
    }
  }
}

watchEffect(() => {
  if (wavesurfer.value) {
    wavesurfer.value.on('audioprocess', () => {
      if (wavesurfer.value.getDuration() > 0) {
        progress.value = (wavesurfer.value.getCurrentTime() / wavesurfer.value.getDuration()) * 100
        currentTime.value = wavesurfer.value.getCurrentTime()
      }
    })
  }
})

let audioContext = null
let analyser = null
let microphone = null
let dataArray

const togglePlayPause = () => {
  if (wavesurfer.value) {
    wavesurfer.value.playPause()
    isPlaying.value = wavesurfer.value.isPlaying()
  }
}

const toggleMute = () => {
  if (wavesurfer.value) {
    isMuted.value = !isMuted.value
    wavesurfer.value.setVolume(isMuted.value ? 0 : volume.value / 100)
  }
}

const changeVolume = (newVolume) => {
  if (wavesurfer.value) {
    wavesurfer.value.setVolume(newVolume / 100)
  }
}
watch(volume, changeVolume)
const initAudioProcessing = async () => {
  audioContext = new AudioContext()
  analyser = audioContext.createAnalyser()
  analyser.fftSize = 2048
  dataArray = new Uint8Array(analyser.frequencyBinCount)

  const stream = await navigator.mediaDevices.getUserMedia({audio: true})
  microphone = audioContext.createMediaStreamSource(stream)
  microphone.connect(analyser)

  drawWaveform()
  detectPitch()
}

const detectPitch = () => {
  if (!analyser) return

  requestAnimationFrame(detectPitch)
  analyser.getByteFrequencyData(dataArray)

  let maxVal = -Infinity
  let index = -1
  for (let i = 0; i < dataArray.length; i++) {
    if (dataArray[i] > maxVal) {
      maxVal = dataArray[i]
      index = i
    }
  }

  const frequency = (index * audioContext.sampleRate) / analyser.fftSize
  if (frequency > 50 && frequency < 5000) {
    pitchValue.value = Math.round(frequency)
  }
}

// Function to draw waveform on canvas
const drawWaveform = () => {
  if (!canvasRef.value || !analyser) return

  const canvas = canvasRef.value
  const ctx = canvas.getContext('2d')
  if (!ctx) return

  const width = canvas.width
  const height = canvas.height

  const draw = () => {
    if (!analyser) return
    requestAnimationFrame(draw)

    analyser.getByteTimeDomainData(dataArray)

    ctx.fillStyle = '#A1D5BB'
    ctx.fillRect(0, 0, width, height)

    ctx.lineWidth = 4
    ctx.strokeStyle = 'black'
    ctx.beginPath()

    const sliceWidth = width / dataArray.length
    let x = 0

    for (let i = 0; i < dataArray.length; i++) {
      const v = dataArray[i] / 128.0
      const y = (v * height) / 2

      if (i === 0) {
        ctx.moveTo(x, y)
      } else {
        ctx.lineTo(x, y)
      }

      x += sliceWidth
    }

    ctx.lineTo(width, height / 2)
    ctx.stroke()
  }

  draw()
}

const startListening = () => {
  isListening.value = true
  recognition.start()
  initAudioProcessing()
}

const stopListening = () => {
  isListening.value = false
  recognition.stop()
  audioContext?.close()
}

const initWaveSurfer = async () => {
  await nextTick()

  if (wavesurfer.value && typeof wavesurfer.value.destroy === 'function') {
    wavesurfer.value.destroy()
  }

  if (!props.src || !props.src.length) return

  const audio = new Audio()
audio.crossOrigin = 'anonymous'
audio.src = props.src

  wavesurfer.value = WaveSurfer.create({
    container: waveformContainer.value,
    waveColor: '#808080',
    currentTimeColor: '#232323',
    cursorColor: 'transparent',
    barWidth: null,
    responsive: true,
    hideScrollbar: true,
    interact: false,
    backend: 'MediaElement',
    media: audio
  })

  wavesurfer.value.load(audio)

  wavesurfer.value.on('ready', () => {
    // wavesurfer.value.play()
    duration.value = wavesurfer.value.getDuration()
    wavesurfer.value.setVolume(volume.value / 100)
  })

  wavesurfer.value.on('play', () => {
    isPlaying.value = true
  })

  wavesurfer.value.on('pause', () => {
    isPlaying.value = false
  })

  // Set background color after rendering
  wavesurfer.value.on('waveform-ready', () => {
    waveformContainer.value.style.backgroundColor = '#A1D5BB'
  })
}

watch(
  () => props.src,
  () => {
    initWaveSurfer()
  },
  {immediate: true}
)

watch(
  () => props.isRecording,
  async (newValue) => {
    if (newValue) {
      startListening()
    } else {
      stopListening()
      await initWaveSurfer()
    }
  },
  {immediate: true}
)

onMounted(() => {
  document.addEventListener('pointerdown', handlePointerDown, true)
  document.addEventListener('pointerup', handlePointerUp, true)
  document.addEventListener('pointerdown', handleClickOutside, true)
})

onUnmounted(() => {
  document.removeEventListener('pointerdown', handlePointerDown, true)
  document.removeEventListener('pointerup', handlePointerUp, true)
  document.removeEventListener('pointerdown', handleClickOutside, true)
  audioContext?.close()
  if (wavesurfer.value) {
    wavesurfer.value.destroy()
  }
})
const formatTime = (time) => {
  const minutes = Math.floor(time / 60)
  const seconds = Math.floor(time % 60)
  return `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`
}
</script>

<style scoped>
.waveform {
  width: 100%;
  height: fit-content;
  background-color: #a1d5bb;
  border-radius: 10px
}
.controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 10px;
}
.record-canvas {
  width: 100%;
}

.volume-control {
  position: relative;
  display: inline-block;
}

.volume-slider-container {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  background: white;
  padding: 6px;
  border-radius: 8px;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.2);
  z-index: 10;
  width: 40px;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
  bottom: 20px;
}

.volume-slider-container.visible {
  opacity: 1;
  visibility: visible;
}

.fade {
  z-index: 9999;
}
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s;
}
.fade-enter,
.fade-leave-to {
  opacity: 0;
}
.currentTime-bar {
  flex-grow: 1;
  margin: 0 12px;
}
</style>
