<template>
  <main>
    <video ref="videoRef" class="video-player" width="100%" @timeupdate="onTimeUpdate" @loadedmetadata="onLoadedMetadata" :src="url"></video>
    <div v-if="url" class="q-gutter-x-sm row items-center no-wrap controls">
      <!-- Play / Pause Button -->
      <q-btn size="sm" flat dense round unelevated color="primary" @click="togglePlayPause">
        <q-icon :name="isPlaying ? 'pause' : 'play_arrow'" size="24px" />
      </q-btn>
      <!-- Time Display -->
      <div class="text-caption" style="min-width: 80px; color: #232323">{{ formatTime(progress) }} / {{ formatTime(duration) }}</div>
      <!-- Progress Slider -->
      <q-slider class="col" thumb-size="0px" v-model="progress" :min="0" :max="duration" :step="0.1" @update:model-value="seekVideo" color="primary" />
      <!-- Volume Button & Slider -->
      <div class="relative-position" @mouseleave="isHovering = false">
        <q-btn size="sm" flat dense round unelevated color="primary" @click="toggleMute" @mouseenter="handleMouseEnter">
          <q-icon :name="isMuted ? 'volume_off' : 'volume_up'" size="24px" />
        </q-btn>
        <!-- Vertical Volume Slider -->
        <div
          v-if="showVolumeSlider"
          ref="volumeSlider"
          class="absolute-bottom"
          style="bottom: 35px; left: 50%; transform: translateX(-50%)"
          @mouseenter="isHovering = true"
          @mouseleave="isHovering = false">
          <q-slider
            v-model="volume"
            vertical
            reverse
            :min="0"
            :max="100"
            :step="1"
            thumb-size="15px"
            color="primary"
            track-color="grey"
            @update:model-value="changeVolume"
            class="volume-slider"
            style="height: 80px" />
        </div>
      </div>
    </div>
  </main>
</template>

<script setup>
import {ref, watch, onMounted, onUnmounted} from 'vue'

const props = defineProps({
  url: String,
})

const videoRef = ref(null)
const isPlaying = ref(false)
const isMuted = ref(false)
const volume = ref(50)
const progress = ref(0)
const duration = ref(0)

const showVolumeSlider = ref(false)
const isHovering = ref(false)
const isDragging = ref(false)
const volumeSlider = ref(null)

const togglePlayPause = () => {
  if (!videoRef.value) return

  if (videoRef.value.paused) {
    videoRef.value.play()
    isPlaying.value = true
  } else {
    videoRef.value.pause()
    isPlaying.value = false
  }
}

const toggleMute = () => {
  if (videoRef.value) {
    isMuted.value = !isMuted.value
    videoRef.value.muted = isMuted.value
  }
}

const changeVolume = (newVolume) => {
  if (videoRef.value) {
    videoRef.value.volume = newVolume / 100
    isMuted.value = newVolume === 0
  }
}

const seekVideo = (value) => {
  if (videoRef.value) {
    videoRef.value.currentTime = value
  }
}

const onTimeUpdate = () => {
  if (videoRef.value) {
    progress.value = videoRef.value.currentTime
  }
}

const onLoadedMetadata = () => {
  if (videoRef.value) {
    duration.value = videoRef.value.duration
  }
}

const handleMouseEnter = () => {
  showVolumeSlider.value = true
}

const handleMouseLeave = () => {
  if (!isHovering.value && !isDragging.value) {
    showVolumeSlider.value = false
  }
}

const handlePointerDown = (event) => {
  if (volumeSlider.value && volumeSlider.value.contains(event.target)) {
    isDragging.value = true
  }
}

const handlePointerUp = () => {
  setTimeout(() => {
    isDragging.value = false
  }, 100)
}

const handleClickOutside = (event) => {
  if (isDragging.value || isHovering.value) return

  if (volumeSlider.value && !volumeSlider.value.contains(event.target)) {
    showVolumeSlider.value = false
  }
}

onMounted(() => {
  document.addEventListener('pointerdown', handlePointerDown, true)
  document.addEventListener('pointerup', handlePointerUp, true)
  document.addEventListener('pointerdown', handleClickOutside, true)
})

onUnmounted(() => {
  document.removeEventListener('pointerdown', handlePointerDown, true)
  document.removeEventListener('pointerup', handlePointerUp, true)
  document.removeEventListener('pointerdown', handleClickOutside, true)
})
const formatTime = (time) => {
  const minutes = Math.floor(time / 60)
  const seconds = Math.floor(time % 60)
  return `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`
}
</script>

<style scoped>
.video-player {
  width: 100%;
  max-height: 400px;
  border-radius: 8px;
}

.controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 8px;
}

.progress-bar {
  flex-grow: 1;
  margin: 0 12px;
}

.volume-slider {
  height: 100px;
  position: absolute;
  bottom: 0px;
  background: #fff;
  padding: 10px 0 15px 0;
  border-radius: 8px;
  box-shadow: 1px 2px 4px 1px #c4c2c2;
  z-index: 2;
}
</style>
