<template>
  <q-dialog v-model="modelValue" persistent @hide="resetFilters">
    <q-card style="min-width: 400px; max-width: 600px">
      <q-card-section class="q-pt-sm q-pb-sm">
        <div class="text-h6">Select to view takeaway</div>
      </q-card-section>

      <q-separator />

      <!-- Add search bar -->
      <q-card-section class="q-pa-none q-px-md q-mt-sm">
        <div class="row q-col-gutter-sm items-center">
          <div class="col">
            <q-input v-model="searchQuery" dense outlined placeholder="Search by session name" clearable>
              <template v-slot:prepend>
                <q-icon name="search" />
              </template>
              <template v-slot:append v-if="isSearching">
                <q-spinner-dots color="primary" size="sm" />
              </template>
            </q-input>
          </div>
          <div class="col-auto">
            <q-btn flat dense round :icon="dateRange?.from ? 'event' : 'o_event'" :color="dateRange?.from ? 'primary' : 'grey'" @click="showDatePicker = true">
              <q-tooltip>Filter by date range</q-tooltip>
            </q-btn>
          </div>
        </div>

        <!-- Separate dialog component -->
        <q-dialog v-model="showDatePicker">
          <q-card style="min-width: 350px">
            <q-card-section class="row items-center">
              <div class="text-h6">Select Date Range</div>
              <q-space />
              <q-btn icon="close" flat round dense v-close-popup />
            </q-card-section>

            <q-card-section>
              <q-date v-model="dateRange" range minimal :min="minDate" :max="maxDate" />
            </q-card-section>

            <q-card-actions align="right">
              <q-btn flat label="Clear" color="grey" @click="clearDateRange" />
              <q-btn flat label="Apply" color="primary" @click="applyDateRange" v-close-popup />
            </q-card-actions>
          </q-card>
        </q-dialog>
      </q-card-section>

      <q-card-section ref="scrollContainerRef" class="scroll" style="min-height: 320px; max-height: 70vh; overflow-y: auto" @scroll="handleScroll">
        <q-inner-loading :showing="loading" />
        <div v-if="sessions.length === 0 && !loading" class="text-center q-mt-md">
          <img class="q-pa-md" :style="`width: 8rem;`" src="~assets/icons/nodata.svg" alt="" />
          <div v-if="fromWorkshop" class="text-center" style="color: #808080; max-width: 290px; margin: auto">
            <div v-if="searchQuery || isSearching">Oops! Looks like {{ studentName }} hasn't attended any of your workshops with the search term</div>
            <div v-else-if="typeof dateRange === 'string' || dateRange?.from">
              Oops! Looks like {{ studentName }} hasn't attended any of your workshops for the filtered dates
            </div>
            <div v-else>
              Oops! Looks like {{ studentName }} hasn't attended any of your workshops yet
              <div class="q-mt-md">Once they join a session we will generate thier personalized takeaway!</div>
            </div>
          </div>
          <div v-else>No sessions found</div>
        </div>

        <q-card v-for="session in sessions" :key="session._id" class="q-mb-md q-pa-md cursor-pointer" flat bordered @click="goToTakeaway(session)">
          <div class="row items-center">
            <q-img :src="session.image" style="width: 60px; height: 60px" class="rounded-borders q-mr-md" spinner-color="primary" />
            <div>
              <div class="text-subtitle1">{{ session.name }}</div>
              <div class="row items-center text-grey text-caption">
                <q-icon name="event" class="q-mr-xs" />
                {{ formatDate(session.ended) }}
              </div>
            </div>
          </div>
        </q-card>

        <div v-if="hasMoreSessions && sessions.length > 0" class="text-center q-py-sm">
          <q-spinner-dots color="primary" size="40px" />
        </div>
        <div v-else-if="sessions.length > 0" class="text-center q-py-sm text-grey">No more sessions</div>
      </q-card-section>

      <q-separator />

      <q-card-actions style="position: sticky; bottom: 0; background-color: #fff" align="right">
        <q-btn flat label="Close" color="primary" @click="modelValue = false" />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import {ref, watch, defineProps, defineEmits, computed} from 'vue'
import {useRouter} from 'vue-router'
import {pubStore} from 'stores/pub'
import useSchool from 'src/composables/common/useSchool'
import {date} from 'quasar'
import {useDebounceFn} from '@vueuse/core'
import '@quasar/quasar-ui-qcalendar/dist/index.css'

const props = defineProps({
  fromWorkshop: Boolean,
  studentId: String,
  studentName: String,
  classId: String,
})

defineEmits(['update:modelValue'])

const modelValue = defineModel()

const loading = ref(false)
const sessions = ref([])
const currentSkip = ref(0)
const hasMoreSessions = ref(true)
const scrollContainerRef = ref(null)
const router = useRouter()
const pub = pubStore()
const {schoolId} = useSchool()
const showDatePicker = ref(false)
const dateRange = ref({
  from: null,
  to: null,
})

const minDate = computed(() => date.formatDate(date.subtractFromDate(new Date(), {months: 6}), 'YYYY/MM/DD'))
const maxDate = computed(() => date.formatDate(new Date(), 'YYYY/MM/DD'))

const clearDateRange = () => {
  dateRange.value = {from: null, to: null}
  showDatePicker.value = false
  fetchEndedSessions()
}

const applyDateRange = () => {
  if (dateRange.value) {
    fetchEndedSessions()
  }
}

const resetFilters = () => {
  searchQuery.value = ''
  dateRange.value = {from: null, to: null}
  showDatePicker.value = false
  isSearching.value = false
}

const searchQuery = ref('')
const isSearching = ref(false)

const debouncedSearch = useDebounceFn(async () => {
  await fetchEndedSessions()
  isSearching.value = false
}, 3000)

watch(searchQuery, () => {
  isSearching.value = true
  debouncedSearch()
})

const formatDate = (val) => date.formatDate(val, 'YYYY/M/D HH:mm')

const goToTakeaway = async (session) => {
  const findSessionTakeAway = await App.service('session-takeaway').find({query: {session: session._id}})
  if (findSessionTakeAway?.data?.length) {
    const query = {}
    if (props.fromWorkshop) query.journal_class = props.classId
    router.push({
      path: `/account/takeaway/${session._id}/view/${props.studentId}`,
      query,
    })
  } else {
    $q.notify({message: 'Please generate takeaway report first', type: 'warning'})
  }
}

const fetchEndedSessions = async (loadMore = false) => {
  loading.value = true
  try {
    if (!loadMore) {
      sessions.value = []
      currentSkip.value = 0
      hasMoreSessions.value = true
    }

    const query = {
      status: 'close',
      $limit: 20,
      $skip: currentSkip.value,
      $sort: {ended: -1},
      $select: ['start', 'name', 'ended', '_id', 'image', 'students'],
      ended: {$ne: null},
      uid: pub.user._id,
      school: schoolId.value,
      isLib: true,
    }

    if (props.fromWorkshop) {
      query['reg._id'] = props.studentId
      query.type = {$in: ['pdSchoolStudentWorkshop', 'pdSchoolTeacherWorkshop', 'taskSchoolWorkshop']}
    } else {
      query.students = props.studentId
      query.classId = props.classId
    }

    if (searchQuery.value) {
      query.name = {
        $regex: searchQuery.value,
        $options: 'i',
      }
    }

    if (dateRange.value) {
      // Handle single date (string) selection
      if (typeof dateRange.value === 'string') {
        const singleDate = date.startOfDate(dateRange.value, 'day')
        const endOfDay = date.endOfDate(dateRange.value, 'day')
        query.ended = {
          $gte: singleDate,
          $lte: endOfDay,
        }
      }
      // Handle date range (object) selection
      else if (dateRange.value.from) {
        const fromDate = date.startOfDate(dateRange.value.from, 'day')
        const toDate = date.endOfDate(dateRange.value.to || dateRange.value.from, 'day')
        query.ended = {
          $gte: fromDate,
          $lte: toDate,
        }
      }
    }

    const res = await App.service('session').find({query})

    if (loadMore) {
      sessions.value = [...sessions.value, ...res.data]
    } else {
      sessions.value = res.data
    }

    hasMoreSessions.value = res.data.length === query.$limit
    currentSkip.value += res.data.length
  } catch (err) {
    console.error('Failed to fetch sessions', err)
  }
  loading.value = false
}

watch(
  () => modelValue.value,
  (val) => {
    if (val) fetchEndedSessions()
  }
)

const handleScroll = () => {
  const container = scrollContainerRef.value?.$el || scrollContainerRef.value
  if (!container) return

  const scrollPosition = container.scrollTop + container.clientHeight
  const scrollHeight = container.scrollHeight
  if (scrollPosition >= scrollHeight - 100 && !loading.value && hasMoreSessions.value) {
    fetchEndedSessions(true)
  }
}
</script>
