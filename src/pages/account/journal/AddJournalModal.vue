<template>
  <q-dialog :model-value="modelValue" persistent>
    <q-card class="q-pa-md journal-modal" style="width: 500px; max-width: 90vw">
      <!-- Header -->
      <q-card-section>
        <div class="text-h6">Add to journal</div>
      </q-card-section>
      <q-separator />

      <!-- Student's Answer -->
      <q-card-section v-if="!isStudent" class="student-answers">
        <div>
          <div v-if="response?.type === 'text'" style="max-height: 146px; overflow-y: auto">
            <QuestionText :key="response._id" :response="response" />
          </div>
          <div v-else-if="response?.type === 'choice'">
            <QuestionChoice
              :key="response._id"
              :isMultiple="questionData?.multi"
              :response="response"
              :allStudentsResponse="allStudentsResponse"
              :stats="statsMapByPage?.[response.page]"
              :studentNumber="sessionData?.students?.length || 0"
              :options="questionData.options" />
          </div>
          <div v-else-if="response?.type === 'media'" style="width: 100%">
            <QuestionMedia :key="response._id" :response="response" @mediaPreview="(e) => onMediaPreview(e, response, response.page)" />
          </div>
          <div v-else-if="response?.type === 'draw'">
            <QuestionDraw :isStudentAns="true" :key="response._id" :backgroundHash="question?.pic" :response="response" />
          </div>
          <div v-else-if="response?.type === 'website'">
            <QuestionWebsite :key="response._id" :response="response" />
          </div>
          <div v-else-if="response?.type === 'comment'">
            <QuestionComment
              :isStudentAns="true"
              :key="response._id"
              :backgroundHash="question?.pic"
              :response="response"
              @commentPreview="(response, backgroundHash) => onCommentPreview(response, backgroundHash)" />
          </div>
          <div v-else></div>
        </div>
      </q-card-section>

      <!-- Reason Input -->
      <q-card-section>
        <q-item-label :class="{'text-subtitle1': true, 'q-mb-md': isStudent, 'q-mb-sm': !isStudent}">{{
          isStudent ? 'Please provide a reason to add to journal' : 'Please provide the reason and select tags to add to journal'
        }}</q-item-label>
        <q-input
          v-model="reason"
          label="Enter a new comment"
          type="textarea"
          outlined
          autogrow
          counter
          :maxlength="maxReasonLength"
          :input-style="{minHeight: '52px'}"
          :error="reason.length >= maxReasonLength"
          :error-message="`Maximum ${maxReasonLength} characters allowed`">
          <template v-slot:append>
            <q-btn flat round dense color="primary" icon="mic" @click="startVoiceInput" />
          </template>
        </q-input>
      </q-card-section>

      <!-- Tag Selection -->
      <q-card-section v-if="!isStudent">
        <q-item-label class="text-subtitle1">Please choose at least one tag below</q-item-label>
        <q-list>
          <RecursiveList :data="questionData?.outlines" :selected="selectedItems" @update:selected="updateSelected" />
        </q-list>
      </q-card-section>

      <!-- Fixed Bottom Section -->
      <q-card-section class="fixed-bottom q-pa-md bg-white row justify-between items-center shadow-2">
        <q-btn-dropdown v-if="!isStudent" size="md" flat color="primary" :label="selectedVisibilityLabel" class="q-mr-md" no-caps>
          <q-list>
            <q-item clickable v-for="option in visibilityOptions" :key="option.value" @click="updateShareVisibility(option.value)">
              <q-item-section side style="width: 40px">
                <q-icon v-if="selectedVisibility === option.value" name="check" color="primary" />
              </q-item-section>
              <q-item-section>
                <q-item-label class="text-bold">{{ option.label }}</q-item-label>
                <q-item-label caption>{{ option.description }}</q-item-label>
              </q-item-section>
            </q-item>
          </q-list>
        </q-btn-dropdown>
        <q-btn class="q-mr-md" size="md" rounded outline label="Cancel" color="negative" @click="onCancel" />
        <q-btn
          size="md"
          rounded
          unelevated
          :label="isStudent ? 'Send' : 'Confirm'"
          color="primary"
          @click="onConfirm"
          :disable="disableSubmit"
          :loading="saveLoading" />
      </q-card-section>
    </q-card>
  </q-dialog>
  <MediaPreviewDialog
    :model-value="isMediaDialogShow"
    @update:model-value="isMediaDialogShow = $event"
    :media="currentPreviewMedia"
    :media-response="response"
    :session-takeaway-snapshot-id="sessionData?._id"
    :comments="feedbackData"
    :is-editing="false" />
  <CommentPreviewDialog
    :model-value="isCommentDialogShow"
    @update:model-value="isCommentDialogShow = $event"
    :response="currentCommentResponse"
    :background-hash="currentPreviewBackground"
    :session-takeaway-snapshot-id="sessionData?._id"
    :comments="feedbackData"
    :is-editing="false" />
</template>

<script setup>
import {ref, watch, computed, defineProps, defineEmits} from 'vue'
import {pubStore} from 'stores/pub'
import QuestionText from 'src/pages/account/takeaway/components/questions/Text.vue'
import QuestionChoice from 'src/pages/account/takeaway/components/questions/Choice.vue'
import QuestionMedia from 'src/pages/account/takeaway/components/questions/Media.vue'
import QuestionDraw from 'src/pages/account/takeaway/components/questions/Draw.vue'
import QuestionWebsite from 'src/pages/account/takeaway/components/questions/Website.vue'
import QuestionComment from 'src/pages/account/takeaway/components/questions/Comment.vue'
import RecursiveList from './RecursiveList.vue'
import MediaPreviewDialog from 'src/components/MediaPreviewDialog.vue'
import CommentPreviewDialog from 'src/components/CommentPreviewDialog.vue'

const pub = pubStore()
const emit = defineEmits(['update:modelValue'])

const props = defineProps({
  modelValue: Boolean,
  question: Object,
  response: Object,
  allStudentsResponse: Array,
  questionData: Object,
  statsMapByPage: Object,
  sessionData: Object,
  classId: String,
  feedbackData: Array,
  isStudent: Boolean,
  updateHasJournal: Function,
})

const reason = ref('')
const maxReasonLength = 1000
const selectedVisibility = ref('students-parents')
const selectedItems = ref({})
const saveLoading = ref(false)
const disableSubmit = computed(
  () =>
    !reason.value?.trim() ||
    reason.value?.trim().length > maxReasonLength ||
    (!props.isStudent && !Object.keys(selectedItems.value).filter((k) => selectedItems.value[k]).length)
)

const visibilityOptions = [
  {label: 'Students & Parents', value: 'students-parents', description: 'Visible to the students of this class as well as parents of those students'},
  {label: 'Parents only', value: 'parents', description: 'Visible only to the parents of the students of this class'},
  {label: 'Students only', value: 'students', description: 'Visible only to the students of this class'},
  {label: 'Private only', value: 'private', description: 'Visible only to teachers of this class'},
]

const selectedVisibilityLabel = computed(() => {
  const selectedOption = visibilityOptions.find((option) => option.value === selectedVisibility.value)
  return selectedOption ? `Share to ${selectedOption.label.toLowerCase()}` : 'Share to student & family'
})

const updateShareVisibility = (value) => {
  selectedVisibility.value = value
}

const onCancel = () => {
  emit('update:modelValue', false)
}

const onConfirm = async () => {
  try {
    saveLoading.value = true
    const tagIds = Object.keys(selectedItems.value).reduce((acc, k) => {
      if (selectedItems.value[k]) acc.push(k)
      return acc
    }, [])

    const dataToPost = {
      sessionId: props.sessionData._id,
      classId: props.classId || props.sessionData.classId,
      teacherId: props.isStudent ? props.sessionData.uid : pub.user._id,
      studentId: props.response.uid,
      subjects: props.sessionData.subjects,
      tagIds,
      question: {
        _id: props.question._id,
        pic: props.question.pic,
        data: props.questionData?.data,
        type: props.questionData?.type,
        multi: props.questionData?.multi,
        options: props.questionData?.options,
        studentsCount: props.sessionData?.students?.length,
        stats: props.statsMapByPage?.[props.response.page],
      },
      responseId: props.response._id,
      teacherReason: !props.isStudent ? reason.value.trim() : '',
      studentReason: props.isStudent ? reason.value.trim() : '',
      visibility: selectedVisibility.value,
    }
    console.log('dataToPost', dataToPost)
    const response = await App.service('journals').create(dataToPost)
    console.log('response', response)
    emit('update:modelValue', false)
    $q.notify({message: props.isStudent ? 'Jounral request sent successfully' : 'Journal added successfully', type: 'positive'})
    props.updateHasJournal(props.response._id)
  } catch (e) {
    console.error('response error', e)
    $q.notify({message: e?.message || 'Error saving journal', type: 'negative'})
  } finally {
    saveLoading.value = false
  }
}

const updateSelected = (selected) => {
  console.log('TopLevel', selected)
  selectedItems.value = selected
}

// Speech-to-text functionality
const startVoiceInput = () => {
  if (!('webkitSpeechRecognition' in window)) {
    alert('Your browser does not support speech recognition. Please use Chrome.')
    return
  }

  const recognition = new window.webkitSpeechRecognition()
  recognition.lang = 'en-US'
  recognition.continuous = false
  recognition.interimResults = false

  recognition.onstart = () => {
    console.log('Voice recognition started...')
  }

  recognition.onresult = (event) => {
    const text = event.results[0][0].transcript
    const remainingLength = maxReasonLength - reason.value.length
    if (remainingLength <= 0) return
    const truncatedText = text.substring(0, remainingLength)
    reason.value += truncatedText
  }

  recognition.onerror = (event) => {
    console.error('Speech recognition error:', event.error)
  }

  recognition.start()
}

function getSelectedIds(data) {
  if (!data) return
  const result = {}
  function traverse(node) {
    if (Array.isArray(node.child) && node.child.length > 0) {
      node.child.forEach((child) => traverse(child))
    } else {
      if (node._id) {
        result[node._id] = true
      }
    }
  }
  for (const key in data) {
    if (Object.hasOwnProperty.call(data, key)) {
      const item = data[key]
      if (Array.isArray(item)) {
        item.forEach((subItem) => traverse(subItem))
      }
    }
  }
  selectedItems.value = result
}

// reset on close
const reset = () => {
  reason.value = ''
  selectedVisibility.value = 'students-parents'
  selectedItems.value = {}
}
// Watch for modal close
watch(
  () => props.modelValue,
  (newValue) => {
    if (!newValue) {
      reset()
    }

    if (newValue) {
      console.log('propsresponse', props)
      getSelectedIds(props.questionData?.outlines)
      reason.value = props.isStudent
        ? ''
        : props.feedbackData
            ?.filter((item) => item.from === pub.user._id)
            .map((item) => item.review)
            .join('\n\n')
    }
  }
)

const isMediaDialogShow = ref(false)
const currentPreviewMedia = ref(null)

function onMediaPreview(media) {
  currentPreviewMedia.value = media
  isMediaDialogShow.value = true
}

const isCommentDialogShow = ref(false)
const currentCommentResponse = ref(null)
const currentPreviewBackground = ref('')

function onCommentPreview(response, backgroundHash = '') {
  if (response?.type === 'comment') {
    currentCommentResponse.value = response
    currentPreviewBackground.value = backgroundHash
    isCommentDialogShow.value = true
  }
}
</script>

<style scoped>
.journal-modal {
  padding: 0;
}

.journal-modal .student-answers {
  border: 16px solid #f1f9f9 !important;
  margin-bottom: 16px;
}

/* Fix bottom section */
.fixed-bottom {
  position: sticky;
  bottom: 0;
  width: 100%;
  z-index: 10;
  padding: 16px 16px 22px 16px;
  display: flex;
  justify-content: flex-end;
}

/* Make dropdown options match the image */
.q-list {
  width: 418px;
  padding: 10px 0;
}

.q-list .q-item {
  min-height: 48px;
  padding: 8px 20px;
}

.q-item-label.text-bold {
  font-weight: bold;
}
</style>
