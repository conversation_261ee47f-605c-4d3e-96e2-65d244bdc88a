<template>
  <div>
    <div v-if="journal?.response?.type === 'text'" style="max-height: 146px; overflow-y: auto">
        <QuestionText :key="journal?.response?._id" :response="journal?.response" />
      </div>

    <div v-else-if="journal?.response?.type === 'choice'">
      <QuestionChoice
        :key="journal?.response?._id"
        :isMultiple="journal?.question?.multi"
        :response="journal?.response"
        :allStudentsResponse="journal?.response?.allResponse"
        :stats="journal?.question?.stats"
        :studentNumber="journal?.question?.studentsCount || 0"
        :options="journal?.question?.options" />
    </div>

    <div v-else-if="journal?.response?.type === 'media'" style="width: 100%">
      <QuestionMedia :key="journal?.response?._id" :response="journal?.response" @mediaPreview="onMediaPreview" />
    </div>

    <div v-else-if="journal?.response?.type === 'draw'">
      <QuestionDraw
        :isStudentAns="true"
        :key="journal?.response?._id"
        :backgroundHash="journal?.question?.pic"
        :response="journal?.response" />
    </div>

    <div v-else-if="journal?.response?.type === 'website'">
      <QuestionWebsite :key="journal?.response?._id" :response="journal?.response" />
    </div>

    <div v-else-if="journal?.response?.type === 'comment'">
      <QuestionComment
        :isStudentAns="true"
        :key="journal?.response?._id"
        :backgroundHash="journal?.question?.pic"
        :response="journal?.response"
        @commentPreview="(response, backgroundHash) => onCommentPreview(response, backgroundHash)" />
    </div>

    <div v-else></div>
  </div>

  <MediaPreviewDialog
    :model-value="isMediaDialogShow"
    @update:model-value="isMediaDialogShow = $event"
    :media="currentPreviewMedia"
    :media-response="journal?.response"
    :session-takeaway-snapshot-id="journal?.sessionId"
    :comments="journal?.feedbacks"
    :is-editing="false" />

  <CommentPreviewDialog
    :model-value="isCommentDialogShow"
    @update:model-value="isCommentDialogShow = $event"
    :response="currentCommentResponse"
    :background-hash="currentPreviewBackground"
    :session-takeaway-snapshot-id="journal?.sessionId"
    :comments="journal?.feedbacks"
    :is-editing="false" />
</template>

<script setup>
import QuestionText from 'src/pages/account/takeaway/components/questions/Text.vue'
import QuestionChoice from 'src/pages/account/takeaway/components/questions/Choice.vue'
import QuestionMedia from 'src/pages/account/takeaway/components/questions/Media.vue'
import QuestionDraw from 'src/pages/account/takeaway/components/questions/Draw.vue'
import QuestionWebsite from 'src/pages/account/takeaway/components/questions/Website.vue'
import QuestionComment from 'src/pages/account/takeaway/components/questions/Comment.vue'
import MediaPreviewDialog from 'src/components/MediaPreviewDialog.vue'
import CommentPreviewDialog from 'src/components/CommentPreviewDialog.vue'
import { ref } from 'vue'

defineProps({
  journal: Object
})

// Media preview
const isMediaDialogShow = ref(false)
const currentPreviewMedia = ref(null)

function onMediaPreview(media) {
  currentPreviewMedia.value = media
  isMediaDialogShow.value = true
}

// Comment preview
const isCommentDialogShow = ref(false)
const currentPreviewBackground = ref('')
const currentCommentResponse = ref(null)

function onCommentPreview(response, backgroundHash = '') {
  if (response?.type === 'comment') {
    currentCommentResponse.value = response
    currentPreviewBackground.value = backgroundHash
    isCommentDialogShow.value = true
  }
}
</script>


