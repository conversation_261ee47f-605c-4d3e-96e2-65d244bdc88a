<script setup>
import {ref, reactive, toRefs, watch, computed} from 'vue'
import {getTimeAgo} from './utils'
import {pubStore} from 'stores/pub'
import QuestionRenderer from './QuestionRenderer.vue'
import ResponseRenderer from './ResponseRenderer.vue'

const props = defineProps({
  isTeacher: <PERSON>ole<PERSON>,
  journal: Object,
  openJournalDetails: Function,
  openCommentDetails: Function,
})

// Create a **local copy** of the comments to avoid mutating props
const localJournal = reactive({
  likes: props.journal.likes,
  liked: props.journal.liked,
  commentsCount: props.journal.comments.count || 0,
  comments: Array.isArray(props.journal.comments.latest_comments)
    ? [...(props.journal.comments.latest_comments && props.journal.comments.latest_comments)]
    : [],
})

watch(
  () => props.journal.comments?.count,
  () => {
    localJournal.commentsCount = props.journal.comments?.count || 0
    localJournal.comments = Array.isArray(props.journal.comments?.latest_comments)
      ? [...(props.journal.comments.latest_comments && props.journal.comments.latest_comments)]
      : []
  },
  {deep: true}
)

watch(
  () => props.journal.likes,
  (newLikes) => {
    localJournal.likes = newLikes
  }
)

console.log('localJournal', localJournal.comments)
const newComment = ref('')
const showLikeAnimation = ref(false)
const pub = pubStore()

const formatName = (name) => (Array.isArray(name) ? name.join(' ') : name)
const addComment = async () => {
  const content = newComment.value.trim()
  if (!content) return

  // Prepare optimistic comment
  const tempComment = {
    name: pub?.user?.name,
    avatar: pub?.user?.avatar,
    content,
  }

  console.log('tempComment', tempComment)

  // Optimistically update local state
  localJournal.comments.unshift(tempComment)
  localJournal.commentsCount++
  const removedComment = localJournal.comments.length > 2 ? localJournal.comments[2] : null
  localJournal.comments = localJournal.comments.slice(0, 2)
  newComment.value = ''

  try {
    await App.service('journal-comments').create({
      journalId: props.journal._id,
      content,
    })
  } catch (error) {
    // Rollback changes if API fails
    localJournal.comments.shift()
    if (removedComment) {
      localJournal.comments.push(removedComment)
    }
    localJournal.commentsCount--
    $q.notify({
      type: 'negative',
      message: 'Failed to post comment. Please try again.',
    })
  }
}

const {comments} = toRefs(localJournal)

const handleLike = async () => {
  showLikeAnimation.value = true
  setTimeout(() => (showLikeAnimation.value = false), 300) // animation duration

  await toggleLike()
}
const toggleLike = async () => {
  console.log('pubUser', pub.user)
  const likedBefore = localJournal.liked
  const journalId = props.journal._id

  // Optimistically update
  localJournal.liked = !likedBefore
  localJournal.likes += likedBefore ? -1 : 1

  try {
    if (likedBefore) {
      await App.service('journal-likes').remove(null, {
        query: {journalId},
      })
    } else {
      await App.service('journal-likes').create({journalId})
    }
  } catch (err) {
    // Revert on error
    localJournal.liked = likedBefore
    localJournal.likes += likedBefore ? 1 : -1

    $q.notify({
      type: 'negative',
      message: likedBefore ? 'Failed to remove like' : 'Failed to like',
    })
  }
}

const isReasonExpanded = ref(false)
const teacherReasonRef = ref(null)
const showReasonExpand = computed(() => {
  if (!teacherReasonRef.value) return false
  const lineHeight = parseInt(getComputedStyle(teacherReasonRef.value).lineHeight)
  const elementHeight = teacherReasonRef.value.scrollHeight
  return elementHeight > lineHeight * 3
})
</script>

<template>
  <q-card class="content-card q-mx-md">
    <q-card-section class="answer-section q-pa-md">
      <div v-if="!isTeacher && journal.studentInfo" class="row items-center q-mb-sm q-pl-sm">
        <q-avatar size="24px">
          <PubAvatar :src="journal.studentInfo.avatar" />
        </q-avatar>
        <div class="text-bold q-ml-sm">{{ journal.studentInfo.name.join(' ') }}</div>
      </div>
      <q-chip
        v-for="tag in journal.subjects"
        :key="tag._id"
        style="background-color: #26a69a36; font-size: 10px; max-width: 160px"
        text-color="black"
        class="q-mr-sm">
        <div class="ellipsis">
          {{ tag.value }}
          <q-tooltip>{{ tag.value }}</q-tooltip>
        </div>
      </q-chip>

      <q-card-section class="row q-mt-sm q-pa-none q-pl-sm q-pr-sm items-center" style="flex-wrap: nowrap; gap: 10px">
        <!-- Answer Box -->
        <div class="answer-box bg-white q-pa-md" style="flex: 1 1 auto; min-width: 0">
          <QuestionRenderer :question="journal?.question" />
          <q-expansion-item label="View answer" dense-toggle switch-toggle-side class="q-mt-sm" header-class="text-teal">
            <ResponseRenderer class="q-ma-none q-mt-sm" :journal="journal" />
          </q-expansion-item>
        </div>

        <!-- Arrow Button -->
        <q-btn flat rounded icon="arrow_forward" color="primary" @click="openJournalDetails(journal)" />
      </q-card-section>

      <q-card-section>
        <div class="row items-center">
          <q-avatar size="24px">
            <PubAvatar :src="journal.teacherInfo.avatar" />
          </q-avatar>
          <div class="text-bold q-ml-sm">{{ journal.teacherInfo.name.join(' ') }}</div>
        </div>
        <div class="q-ml-sm q-mt-sm">
          <div class="text-caption">
            <div ref="teacherReasonRef" :class="{'line-clamp-3': !isReasonExpanded}" class="reason-text">
              {{ journal.teacherReason }}
            </div>
            <div
              v-if="showReasonExpand"
              @click="isReasonExpanded = !isReasonExpanded"
              :style="{'margin-top': isReasonExpanded ? '4px' : '0', lineHeight: isReasonExpanded ? '20px' : '10px'}"
              class="expand-button text-primary cursor-pointer">
              {{ isReasonExpanded ? 'Show less' : '...' }}
            </div>
          </div>
        </div>
      </q-card-section>
    </q-card-section>

    <q-card-section class="row items-center justify-between q-pb-sm">
      <div class="row items-center">
        <q-avatar size="24px">
          <PubAvatar :src="journal.teacherInfo.avatar" />
        </q-avatar>
        <div class="q-ml-sm text-bold text-grey-5">{{ journal.teacherInfo.name.join(' ') }}</div>
        <div class="text-caption q-ml-sm text-grey-5">{{ getTimeAgo(journal.publishedAt || journal.createdAt) }}</div>
      </div>
      <div class="row items-center">
        <q-icon size="22px" name="o_insert_comment" class="q-mr-xs" color="grey-7" />
        <span>{{ Math.max(localJournal.commentsCount, 0) }}</span>

        <q-icon
          size="22px"
          :name="localJournal.liked ? 'favorite' : 'favorite_border'"
          class="q-ml-md q-mr-xs cursor-pointer animated-like"
          :class="{'liked-animation': showLikeAnimation}"
          :color="localJournal.liked ? 'red' : 'grey-7'"
          @click="handleLike" />
        <span>{{ Math.max(localJournal.likes, 0) }}</span>
      </div>
    </q-card-section>

    <!-- Comments Section -->
    <q-card-section v-if="comments.length" class="row q-gutter-md q-pl-xl q-pr-sm" style="flex-wrap: nowrap">
      <div style="flex: 1 1 auto; min-width: 0">
        <q-card-section v-for="(comment, index) in comments" :key="index">
          <div class="row items-center">
            <q-avatar size="24px">
              <PubAvatar :src="comment.avatar" />
            </q-avatar>
            <div class="text-bold q-ml-sm">{{ formatName(comment.name) }}</div>
          </div>
          <div class="q-mt-sm q-ml-sm">
            <div class="text-caption line-clamp-3">
              {{ comment.content }}
              <q-tooltip v-if="comment.content.split('\n').length > 3 || comment.content.length > 150" max-width="400px" class="text-body2">
                {{ comment.content }}
              </q-tooltip>
            </div>
          </div>
        </q-card-section>
      </div>
      <div v-if="comments.length" class="row items-center">
        <q-btn flat rounded icon="arrow_forward" color="primary" @click="openCommentDetails(journal)" />
      </div>
    </q-card-section>
    <div v-else class="col-grow">
      <div class="text-grey column flex-center">
        <img class="q-pa-sm" :style="`width: 8rem;`" src="~assets/icons/nodata.svg" alt="" />
        <div>No comments yet!</div>
        <slot />
      </div>
    </div>

    <!-- Add Comment Section -->
    <q-card-section class="row items-center">
      <q-input v-model="newComment" outlined dense placeholder="Write a comment..." @keyup.enter="addComment" class="col-grow" />
      <q-btn label="Send" color="primary" rounded class="q-ml-md" @click="addComment" />
    </q-card-section>
  </q-card>
</template>

<style scoped>
.content-card {
  background-color: white;
  border-radius: 8px;
  padding: 16px;
}

.answer-section {
  border-radius: 15px !important;
  background-color: #f1f9f9;
}

.answer-box {
  border-radius: 6px !important;
}

.animated-like {
  transition: transform 0.2s;
}

.liked-animation {
  animation: bounce 0.3s ease;
}

@keyframes bounce {
  0% {
    transform: scale(1);
  }

  30% {
    transform: scale(1.3);
  }

  100% {
    transform: scale(1);
  }
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.expand-button {
  font-weight: 500;
}

.reason-text {
  white-space: pre-wrap;
}
</style>
