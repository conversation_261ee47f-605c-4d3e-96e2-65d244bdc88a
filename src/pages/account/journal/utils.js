import moment from "moment";

export function getTimeAgo(dateString) {
  const now = moment();
  const date = moment(dateString);
  const diffInMinutes = now.diff(date, "minutes");
  const diffInHours = now.diff(date, "hours");
  const diffInDays = now.diff(date, "days");
  const diffInYears = now.diff(date, "years");

  if (diffInMinutes < 1) return "Just now"; // Within 1 min
  if (diffInMinutes < 60) return `${diffInMinutes} mins ago`; // Within 1 hour
  if (diffInHours < 24) return `Today ${date.format("H:mm")}`; // Within a day
  if (diffInDays === 1) return `Yesterday ${date.format("H:mm")}`; // Within 2 days
  if (diffInDays >= 2 && diffInYears === 0) return date.format("DD/MM H:mm"); // More than 2 days (Same Year)
  return date.format("DD/MM/YYYY H:mm"); // More than a year
}
