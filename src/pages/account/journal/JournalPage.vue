<template>
  <!-- Journal Details Page -->
  <CustomDrawer v-model="showJournalDetails" side="right" width="100%">
    <JournalDetails
      :journal="selectedJournal"
      :isTeacher="isTeacher"
      :visible="showJournalDetails"
      @goBack="closeJournalDetails"
      @updated="fetchAllData"
      @hideLoading="contentLoading = false"
      @showLoading="contentLoading = true" />
  </CustomDrawer>

  <!-- Comment Details Page -->
  <CustomDrawer v-model="showCommentDetails" side="right" width="100%">
    <CommentDetails :journal="selectedJournal" :visible="showCommentDetails" @goBack="showCommentDetails = false" />
  </CustomDrawer>

  <!-- Approve Journal Modal -->
  <ApproveJournalModal v-model="showApproveModal" :journal="selectedJournal" :callback="journalApproveCallback" />

  <EndedSessions
    v-model="sessionDialog"
    :fromWorkshop="fromWorkshop"
    :studentId="selectedStudent"
    :studentName="selectedStudentName"
    :classId="journalClassId" />

  <!-- Main Journal List Page -->
  <q-header class="journal-hd bg-white shadow-1">
    <q-toolbar>
      <!-- Back Button -->
      <q-btn flat round dense icon="arrow_back" @click="router.back()" />

      <!-- Journal Title -->
      <span class="text-h5 q-mr-sm"> Journal </span>

      <!-- Updated Text -->
      <span v-if="lastUpdatedAt" class="text-caption text-grey-6">Updated {{ getTimeAgo(lastUpdatedAt) }}</span>
    </q-toolbar>
  </q-header>

  <!-- Sidebar Student List -->
  <q-drawer v-if="isTeacher" v-model="drawerOpen" class="bg-white student-sidebar" bordered width="280" :breakpoint="1024" show-if-above>
    <q-list class="q-pa-md" @scroll.passive="handleStudentScroll" ref="studentListRef" style="overflow-y: auto; max-height: calc(100vh - 50px)">
      <q-input class="bg-teal-1 overflow-hidden rounded-borders-xl" outlined rounded dense placeholder="Search student’s name" v-model="search">
        <template v-slot:prepend>
          <q-icon name="search" />
        </template>
      </q-input>

      <q-separator class="q-my-md" />

      <!-- Skeleton Loader -->
      <template v-if="studentsLoading && studentsSkip === 0">
        <q-item v-for="n in 6" :key="'skeleton-' + n">
          <q-item-section avatar>
            <q-skeleton type="QAvatar" />
          </q-item-section>
          <q-item-section>
            <q-skeleton type="text" width="70%" />
          </q-item-section>
        </q-item>
      </template>

      <!-- Student List -->
      <template v-else>
        <q-item
          v-for="(student, index) in filteredStudents"
          :key="index"
          clickable
          v-ripple
          :class="{'selected-student': selectedStudent === student.uid, student: true}"
          @click="onStudentClick(student)">
          <q-item-section avatar>
            <q-avatar size="24px">
              <!-- <img :src="student.avatar" alt="avatar" /> -->
              <PubAvatar :src="student.avatar" />
            </q-avatar>
          </q-item-section>
          <q-item-section>
            <q-item-label :class="{ellipsis: true, 'text-bold': selectedStudent === student.uid}">
              {{ student.name.join(' ') }}
              <q-tooltip v-if="student.name.join(' ').length > 18">{{ student.name.join(' ') }}</q-tooltip>
            </q-item-label>
          </q-item-section>
        </q-item>
        <q-inner-loading :showing="studentsLoading" />
      </template>
    </q-list>
  </q-drawer>

  <div v-if="contentLoading && skip === 0" class="main-body full-height q-pb-md">
    <div width="100%" class="q-pa-md">
      <q-card v-for="i in 2" :key="i" class="q-pa-md q-mb-md">
        <!-- Subject Header -->
        <q-skeleton type="text" width="25%" class="q-mb-sm" />
        <q-skeleton type="text" width="50%" class="q-mb-md" />

        <!-- Main Post Section -->
        <div class="row items-center q-mb-sm">
          <q-skeleton type="QAvatar" size="32px" />
          <q-skeleton type="text" width="100px" class="q-ml-sm" />
        </div>
        <q-skeleton type="text" class="q-mb-xs" width="95%" />
        <q-skeleton type="text" class="q-mb-xs" width="98%" />
        <q-skeleton type="text" class="q-mb-md" width="90%" />

        <!-- Replies -->
        <div class="q-mb-md">
          <div class="row items-center q-mb-sm">
            <q-skeleton type="QAvatar" size="28px" />
            <q-skeleton type="text" width="80px" class="q-ml-sm" />
          </div>
          <q-skeleton type="text" class="q-mb-xs" width="95%" />
          <q-skeleton type="text" class="q-mb-xs" width="85%" />
        </div>
      </q-card>
    </div>
  </div>

  <div v-else ref="mainBodyRef" @scroll.passive="handleScroll" class="main-body full-height q-pb-md">
    <!-- Main Body Top Section -->
    <div class="main-body-top row items-center justify-between q-pa-md q-gutter-md">
      <!-- Tabs -->
      <div class="row items-center">
        <!-- Hamburger Menu for Mobile (Only visible when isTeacher) -->
        <q-btn v-if="isTeacher" flat round dense icon="menu" class="lt-md q-mr-sm" @click="drawerOpen = !drawerOpen" />

        <q-tabs v-model="activeTab" indicator-color="primary" no-caps>
          <q-tab name="published" :class="{'text-primary': activeTab === 'published'}" :label="'Published' + ` (${journalCounts.published})`" />
          <q-tab
            v-if="isTeacher"
            name="approval"
            :class="{'text-primary': activeTab === 'approval'}"
            :label="'In approval' + ` (${journalCounts.inApproval})`" />
          <q-tab
            v-if="!isTeacher"
            name="my_selected"
            :class="{'text-primary': activeTab === 'my_selected'}"
            :label="'My selected' + ` (${journalCounts.mySelected})`" />
        </q-tabs>
      </div>

      <!-- Search and Add Button -->
      <div v-if="activeTab === 'published'" class="row items-center" style="flex-wrap: wrap; gap: 8px">
        <q-input
          class="bg-teal-1 overflow-hidden rounded-borders-xl search-input"
          outlined
          rounded
          dense
          placeholder="Search Subjects/Learning area by keywords"
          v-model="searchKeyword">
          <template v-slot:prepend>
            <q-icon name="search" />
          </template>
          <template v-slot:append>
            <q-spinner-dots v-if="isSearching" color="primary" size="sm" />
            <q-menu>
              <div class="q-pa-md" style="min-width: 280px">
                <div class="text-weight-bold q-pb-sm">{{ isTeacher ? 'Published by' : 'Belongs to' }}</div>
                <div class="row">
                  <q-checkbox v-model="filterBy" val="me" class="col-6">
                    <template v-slot:default>
                      <span>Me</span>
                      <span class="text-grey-6 q-ml-sm"> ({{ journalCounts.userTotal > 100 ? '100+' : journalCounts.userTotal }}) </span>
                    </template>
                  </q-checkbox>
                  <q-checkbox v-model="filterBy" val="others" class="col-6">
                    <template v-slot:default>
                      <span>Others</span>
                      <span class="text-grey-6 q-ml-sm"> ({{ journalCounts.others > 100 ? '100+' : journalCounts.others }}) </span>
                    </template>
                  </q-checkbox>
                </div>
              </div>
            </q-menu>
            <q-btn round flat dense icon="arrow_drop_down" />
          </template>
        </q-input>
        <q-btn v-if="isTeacher" @click="sessionDialog = true" label="Add" color="primary" rounded class="q-ml-md" />
      </div>
    </div>

    <!-- Main Body Content Section -->
    <div v-if="activeTab === 'published'">
      <template v-if="publishedJournals.length">
        <div v-for="(journal, index) in publishedJournals" :key="index" class="q-mb-md">
          <JournalPublished :is-teacher="isTeacher" :journal="journal" :openJournalDetails="openJournalDetails" :openCommentDetails="openCommentDetails" />
        </div>
      </template>
      <div v-else class="q-pa-md">
        <NoData messageColor="grey-8" message="No journals published yet!" />
      </div>
    </div>

    <div v-if="activeTab === 'approval'">
      <template v-if="inApprovalJournals.length">
        <div v-for="(journal, index) in inApprovalJournals" :key="index" class="q-mb-md">
          <JournalPending
            :is-teacher="isTeacher"
            :journal="journal"
            :openJournalDetails="openJournalDetails"
            :openApproveModal="openApproveModal"
            @updated="fetchAllData"
            @hideLoading="contentLoading = false"
            @showLoading="contentLoading = true" />
        </div>
      </template>
      <div v-else class="q-pa-md">
        <NoData messageColor="grey-8" message="No journals to approve!" />
      </div>
    </div>

    <div v-if="activeTab === 'my_selected'">
      <template v-if="inApprovalJournals.length">
        <div v-for="(journal, index) in inApprovalJournals" :key="index" class="q-mb-md">
          <JournalPending
            :journal="journal"
            :openJournalDetails="openJournalDetails"
            @updated="fetchAllData"
            @hideLoading="contentLoading = false"
            @showLoading="contentLoading = true" />
        </div>
      </template>
      <div v-else class="q-pa-md">
        <NoData messageColor="grey-8" message="No journals selected!" />
      </div>
    </div>
  </div>
</template>

<script setup>
/* Libraries */
import {ref, computed, onMounted, watch, onUnmounted} from 'vue'
import {useRoute, useRouter} from 'vue-router'
/* Hooks */
import {pubStore} from 'stores/pub'
import useSchool from 'src/composables/common/useSchool'
import useJournalRealtime from './useJournalRealtime'
/* Utils */
import {getTimeAgo} from './utils'
/* Components */
import JournalPublished from './JournalPublished.vue'
import JournalPending from './JournalPending.vue'
import CustomDrawer from './CustomDrawer.vue'
import JournalDetails from './JournalDetails.vue'
import CommentDetails from './CommentDetails.vue'
import ApproveJournalModal from './ApproveJournalModal.vue'
import EndedSessions from './EndedSessions.vue'
import NoStudents from './NoStudents.vue'

const route = useRoute()
const router = useRouter()
const pub = pubStore()
const {schoolId, isSchool} = useSchool()
const searchKeyword = ref('')
const skip = ref(0)
const limit = 5
const hasMore = ref(false)
const showJournalDetails = ref(false)
const selectedJournal = ref({})
const showCommentDetails = ref(false)
const showApproveModal = ref([])
const contentLoading = ref(true)
const students = ref([])
const studentsLoading = ref(false)
const studentsSkip = ref(0)
const search = ref('')
const hasMoreStudents = ref(true)
const selectedStudent = ref(null)
const selectedStudentName = ref('')
const publishedJournals = ref([])
const inApprovalJournals = ref([])
const journalCounts = ref({
  published: 0,
  inApproval: 0,
  mySelected: 0,
  userTotal: 0, // Add a new property for user total
  others: 0,
})
const lastUpdatedAt = ref(null)
const isTeacher = ref(pub.user.roles?.includes('teacher'))
const sessionDialog = ref(false)
const studentListRef = ref(null)
const mainBodyRef = ref(null)
const filterBy = ref([])
const isSearching = ref(false)
const drawerOpen = ref(true) // Start with drawer open
const fromWorkshop = ref(route.query.workshop === 'true')
const journalClassId = ref(route.query.class || null)

const getInitialTabValue = () => {
  const tabFromUrl = route.query.tab
  return ['published', 'approval', 'my_selected'].includes(tabFromUrl) ? tabFromUrl : 'published'
}
const activeTab = ref(getInitialTabValue())

const handleStudentScroll = () => {
  const el = studentListRef.value?.$el || studentListRef.value
  if (!el) return
  if (el.scrollTop + el.clientHeight >= el.scrollHeight - 50) {
    getStudentsList()
  }
}

const handleScroll = () => {
  const el = mainBodyRef.value?.$el || mainBodyRef.value
  if (!el) return
  if (!contentLoading.value && el.scrollTop + el.clientHeight >= el.scrollHeight - 50) {
    if (activeTab.value === 'published') {
      getJournals()
    } else if (activeTab.value === 'approval' || activeTab.value === 'my_selected') {
      getInApprovalJournals()
    }
  }
}

const onStudentClick = (student) => {
  selectedStudent.value = student.uid
  selectedStudentName.value = student.name?.join(' ').trim()
  if (window.innerWidth < 1024) {
    drawerOpen.value = false
  }
}

const fetchStudentById = async (studentId) => {
  try {
    const existingStudent = students.value.find((s) => s.uid === studentId)
    if (existingStudent) {
      selectedStudent.value = existingStudent.uid
      selectedStudentName.value = existingStudent.name?.join(' ').trim()
      return existingStudent
    }

    const result = await App.service('students').find({
      query: {
        uid: studentId,
        school: schoolId.value,
        status: 2,
        del: {$ne: true},
        $limit: 1,
        $select: ['uid', 'name', 'avatar'],
      },
    })

    if (result.data && result.data.length > 0) {
      // Add to our list if not already there
      const student = result.data[0]
      if (!students.value.some((s) => s.uid === student.uid)) {
        students.value.push(student)
      }
      selectedStudent.value = student.uid
      selectedStudentName.value = student.name?.join(' ').trim()
      return student
    }
    return null
  } catch (err) {
    console.error('Failed to fetch student by ID:', err)
    return null
  }
}

// Update URL with current parameters
const updateUrlWithParams = () => {
  const query = {
    ...route.query,
    tab: activeTab.value,
  }

  if (isTeacher.value && selectedStudent.value) {
    query.student = selectedStudent.value
  }

  if (journalClassId.value) {
    query.class = journalClassId.value
  }

  if (fromWorkshop.value) {
    query.workshop = 'true'
  }

  router.replace(
    {
      path: route.path,
      query,
    },
    () => {}
  )
}

watch(
  [activeTab, selectedStudent],
  () => {
    updateUrlWithParams()
  },
  {deep: true}
)

onMounted(async () => {
  await getStudentsList(true)
  setupJournalSocketListeners()

  if (route.params.id) {
    try {
      if (window.innerWidth < 1024) drawerOpen.value = false
      contentLoading.value = true
      const journal = await App.service('journals').get(route.params.id)
      selectedJournal.value = journal
      showJournalDetails.value = true
    } catch {
      $q.notify({
        type: 'negative',
        message: 'Failed to load journal details',
      })

      router.push(
        {
          name: 'journal',
          query: route.query,
        },
        () => {}
      )
    } finally {
      contentLoading.value = false
    }
  }
})

onUnmounted(() => {
  cleanupJournalSocketListeners()
})

const getStudentsList = async (initial = false, reset = false) => {
  if (!isTeacher.value) {
    selectedStudent.value = pub.user._id
    return
  }

  if (studentsLoading.value || (!reset && !hasMoreStudents.value)) return

  studentsLoading.value = true

  if (reset) {
    students.value = []
    studentsSkip.value = 0
    hasMoreStudents.value = true
  }

  try {
    // Add class validation when isSchool and isTeacher are true
    if (initial && isSchool.value && isTeacher.value && journalClassId.value) {
      const schoolClassesList = await App.service('school-user').get('classList', {
        query: {school: schoolId.value},
      })

      const classExists = schoolClassesList.some((cls) => cls._id === journalClassId.value)
      if (!classExists) {
        router.replace('/')
        return
      }
    }

    const res = await App.service('students').find({
      query: {
        $sort: {_id: -1},
        $skip: studentsSkip.value,
        $limit: 50,
        class: {$in: [journalClassId.value]},
        school: schoolId.value,
        status: 2,
        del: {$ne: true},
        $select: ['uid', 'name', 'avatar'],
      },
    })

    const newStudents = res.data
    if (newStudents.length < 50) hasMoreStudents.value = false

    if (initial && newStudents.length === 0) {
      drawerOpen.value = false
      $q.dialog({
        component: NoStudents,
        persistent: true,
      }).onDismiss(() => {
        router.go(-1)
      })
    } else {
      students.value.push(...newStudents)
      studentsSkip.value += newStudents.length

      if (initial && students.value.length) {
        if (route.query.student) {
          await fetchStudentById(route.query.student)
        } else {
          selectedStudent.value = students.value[0].uid
          selectedStudentName.value = students.value[0].name?.join(' ').trim()
        }
      }
    }
  } catch (err) {
    console.error('Failed to fetch students:', err)
  } finally {
    studentsLoading.value = false
  }
}

const getUserJournalCount = async (all) => {
  if (!all) return

  const userJournalsQuery = {
    $limit: 0,
    ...(isTeacher.value && {classId: journalClassId.value}),
    schoolId: schoolId.value,
    ...(isTeacher.value && {studentId: {$in: [selectedStudent.value]}}),
  }

  if (isTeacher.value) {
    userJournalsQuery.teacherId = pub.user._id
  } else {
    userJournalsQuery.studentId = {$in: [pub.user._id]}
  }

  const result = await App.service('journals').find({query: userJournalsQuery})
  console.log('Journal Results', result)
  journalCounts.value.userTotal = result.total || 0
  journalCounts.value.others = journalCounts.value.published - journalCounts.value.userTotal
}

const getJournals = async (all = false) => {
  if (!hasMore.value && !all) return

  try {
    if (all) skip.value = 0
    contentLoading.value = true

    const query = {
      $sort: {publishedAt: -1},
      $skip: skip.value,
      $limit: limit,
      ...(isTeacher.value && {classId: journalClassId.value}),
      schoolId: schoolId.value,
      ...(isTeacher.value && {studentId: {$in: [selectedStudent.value]}}),
    }

    const isFilterByUser = filterBy.value.length === 1
    if (isFilterByUser) {
      const isFilteringByMe = filterBy.value.includes('me')
      const userId = pub.user._id

      if (isTeacher.value) {
        query.teacherId = isFilteringByMe ? userId : {$ne: userId}
      } else {
        query.studentId = isFilteringByMe ? {$in: [userId]} : {$ne: userId}
      }
    }

    if (searchKeyword.value.trim()) {
      query['subjects.value'] = {$regex: searchKeyword.value, $options: 'i'}
    }

    const journals = await App.service('journals').find({query})

    if (all) {
      publishedJournals.value = journals.data
    } else {
      publishedJournals.value = [...publishedJournals.value, ...journals.data]
    }

    if (activeTab.value === 'published') {
      skip.value += limit
      hasMore.value = journals.total > publishedJournals.value.length
    }

    journalCounts.value.published = journals.total
    getUserJournalCount(all && !isFilterByUser)
  } catch (e) {
    if (all) {
      publishedJournals.value = []
      journalCounts.value.published = 0
      journalCounts.value.userTotal = 0
    }
    $q.notify({message: e?.message || 'Failed to fetch journals', type: 'negative'})
  } finally {
    if (!all) {
      contentLoading.value = false
    }
  }
}

const getInApprovalJournals = async (all = false) => {
  if (!hasMore.value && !all) return
  try {
    if (all) skip.value = 0
    contentLoading.value = true

    const journals = await App.service('journals').find({
      query: {
        $sort: {createdAt: -1},
        $skip: skip.value,
        $limit: limit,
        ...(isTeacher.value && {classId: journalClassId.value}),
        schoolId: schoolId.value,
        studentId: {$in: [selectedStudent.value]},
        ...(isTeacher.value && {
          $or: [{teacherId: pub.user._id}, {teachers: pub.user._id}],
        }),
        status: isTeacher.value ? 0 : {$in: [0, 2]},
      },
    })

    if (all) {
      inApprovalJournals.value = journals.data
    } else {
      inApprovalJournals.value = [...inApprovalJournals.value, ...journals.data]
    }

    if (activeTab.value === 'approval' || activeTab.value === 'my_selected') {
      skip.value += limit
      hasMore.value = journals.total > inApprovalJournals.value.length
    }

    lastUpdatedAt.value = journals.lastUpdatedAt
    journalCounts.value.inApproval = journals.total
    journalCounts.value.mySelected = journals.total
  } catch (e) {
    if (all) {
      inApprovalJournals.value = []
      journalCounts.value.inApproval = 0
      journalCounts.value.mySelected = 0
    }
    $q.notify({message: e?.message || 'Failed to fetch journals', type: 'negative'})
  } finally {
    if (!all) {
      contentLoading.value = false
    }
  }
}

function debounce(fn, delay) {
  let timer
  return function () {
    isSearching.value = true // Set to true immediately when typing starts
    const context = this
    const args = arguments
    clearTimeout(timer)
    timer = setTimeout(() => {
      fn.apply(context, args).finally(() => {
        isSearching.value = false // Set to false after function completes
      })
    }, delay)
  }
}

watch(
  searchKeyword,
  debounce(async () => {
    if (activeTab.value !== 'published') return
    try {
      await getJournals(true)
    } finally {
      contentLoading.value = false
    }
  }, 3000)
)

watch(selectedStudent, async (newVal, oldVal) => {
  if (newVal !== oldVal) {
    fetchAllData()
  }
})

watch(activeTab, async (newVal) => {
  searchKeyword.value = ''
  if (newVal === 'published') {
    await getJournals(true)
  } else if (newVal === 'approval' || newVal === 'my_selected') {
    await getInApprovalJournals(true)
  }
  contentLoading.value = false
})

watch(filterBy, async () => {
  if (activeTab.value !== 'published') return
  try {
    await getJournals(true)
  } finally {
    contentLoading.value = false
  }
})

const filteredStudents = computed(() => {
  return !search.value ? students.value : students.value.filter((student) => student.name.join(' ').toLowerCase().includes(search.value.toLowerCase()))
})

const openJournalDetails = (journal) => {
  selectedJournal.value = journal
  showJournalDetails.value = true
  router.push(
    {
      name: 'journal-details',
      params: {id: journal._id},
      query: route.query,
    },
    () => {}
  )
}

const openCommentDetails = (journal) => {
  selectedJournal.value = journal
  showCommentDetails.value = true
}

const openApproveModal = (journal) => {
  selectedJournal.value = journal
  showApproveModal.value = true
}

const journalApproveCallback = () => {
  fetchAllData()
}

const fetchAllData = async () => {
  await Promise.all([getJournals(true), getInApprovalJournals(true)])
  contentLoading.value = false
}

/* Realtime listeners */
const journals = {
  publishedJournals,
  inApprovalJournals,
}
useJournalRealtime(journals, activeTab)

function setupJournalSocketListeners() {
  App.service('journals').on('patched', (data) => {
    console.log('Socket Data Updated', data)
    const updateList = (list) => {
      const index = list.findIndex((j) => j._id === data._id)
      if (index === -1) return
      list[index] = {...list[index], ...data}
      lastUpdatedAt.value = data.updatedAt
    }
    if (data.status === 1 && activeTab.value === 'published') {
      updateList(publishedJournals.value)
    } else if (activeTab.value === 'approval' || activeTab.value === 'my_selected') {
      updateList(inApprovalJournals.value)
    }
  })
}

function cleanupJournalSocketListeners() {
  App.service('journals').removeAllListeners('patched')
}

watch(
  () => isSchool.value,
  (newVal) => {
    if (!newVal) {
      router.replace('/')
    }
  },
  {immediate: true}
)

const closeJournalDetails = () => {
  showJournalDetails.value = false
  router.push(
    {
      name: 'journal',
      query: route.query,
    },
    () => {}
  )
}
</script>

<style scoped>
.journal-hd {
  color: #232323;
  height: auto !important;
}

.student {
  border-radius: 8px;
  margin: 8px 0;
  min-height: unset;
  height: 42px;
  color: #939094;
}

.selected-student {
  background: #b2dfdb;
  color: #787579;
}

.student .q-item__section--avatar {
  min-width: unset;
}

.main-body {
  overflow: auto;
  background-color: #fafafa;
}

.main-body-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.comment-drawer {
  width: 100% !important;
  max-width: 100% !important;
}

.search-input {
  width: 370px;
}

@media (max-width: 768px) {
  .search-input {
    flex: 1;
    width: auto;
  }
}
</style>
