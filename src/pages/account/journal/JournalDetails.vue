<template>
  <ApproveJournalModal v-model="showApproveModal" :journal="journal" :callback="journalApproveCallback" />

  <q-header class="details-hd bg-white shadow-1">
    <q-toolbar>
      <!-- Back Button -->
      <q-btn flat round dense icon="arrow_back" @click="$emit('goBack')" />

      <span class="text-h6 q-ml-sm"> Journal Details </span>
    </q-toolbar>
  </q-header>

  <div class="details-container">
    <q-card-section v-if="loading" class="flex flex-center" style="height: 100%">
      <q-spinner color="primary" size="2em" />
    </q-card-section>
    <q-card v-else class="details-box">
      <div v-if="!isTeacher && journal.status !== 1" class="flex justify-end q-pr-sm">
        <q-badge v-if="!isTeacher" class="status-badge" :color="journal.status === 0 ? 'primary' : 'negative'" text-color="white">{{
          journal.status === 0 ? 'Pending' : 'Rejected'
        }}</q-badge>
      </div>
      <q-card-section class="row q-mt-sm q-pa-none q-pl-sm q-pr-sm items-center" style="flex-wrap: nowrap">
        <!-- Answer Box -->
        <div class="answer-box q-pa-md" style="flex: 1 1 auto; min-width: 0">
          <QuestionRenderer :question="journal?.question" />
        </div>
      </q-card-section>
      <q-expansion-item class="q-mt-sm" :header-style="{backgroundColor: '#F1F9F9', borderRadius: '8px'}">
        <template v-slot:header>
          <q-avatar :class="`bg-${getQuestionIconColor(journal.response)} text-white q-mr-sm`" round size="md" :icon="getQuestionIcon(journal.response)">
          </q-avatar>
          <q-item-section>
            <q-item-label class="text-semi-bold">{{ getQuestionTitle(journal.response).replace(' Question', '') || Answer }}</q-item-label>
          </q-item-section>
        </template>

        <ResponseRenderer class="q-ma-none q-mt-sm" :journal="journal" />
      </q-expansion-item>
      <q-card-section v-if="journal.status === 1">
        <div class="row items-center">
          <q-avatar size="24px">
            <PubAvatar :src="journal.teacherInfo?.avatar" />
          </q-avatar>
          <div class="text-bold q-ml-sm">{{ journal.teacherInfo?.name.join(' ') }}</div>
        </div>
        <div class="q-ml-sm q-mt-sm">
          <div class="text-caption" style="white-space: pre-wrap">{{ journal.teacherReason }}</div>
        </div>
      </q-card-section>

      <div v-if="journal.status !== 1" class="q-pt-md q-pb-md">
        <!-- Teacher Feedbacks (when isTeacher) -->
        <q-card-section v-if="isTeacher && journal.feedbacks?.length > 0" class="q-pb-none">
          <div class="text-bold q-mb-sm">Feedbacks provided by teacher</div>
          <div
            v-for="(feedback, index) in journal.feedbacks"
            :key="feedback._id"
            :class="['feedback-item', {'q-mb-md': index !== journal.feedbacks.length - 1}]">
            <div class="row items-center">
              <q-avatar size="24px">
                <PubAvatar :src="feedback.from === journal.teacherId ? journal.teacherInfo?.avatar : ''" />
              </q-avatar>
              <div class="text-bold q-ml-sm">{{ feedback.sender }}</div>
              <div class="text-caption q-ml-sm">{{ getTimeAgo(feedback.createdAt) }}</div>
            </div>
            <div class="q-ml-lg q-mt-sm">
              <div class="text-body2">{{ feedback.review }}</div>
            </div>
          </div>
        </q-card-section>

        <!-- Student Reason Section -->
        <q-card-section class="q-pb-none">
          <div class="text-bold q-mb-sm">Reason provided by student</div>
          <div class="row items-center">
            <q-avatar size="24px">
              <PubAvatar :src="journal.studentInfo?.avatar" />
            </q-avatar>
            <div class="text-bold q-ml-sm">{{ journal.studentInfo?.name.join(' ') }}</div>
          </div>
          <div class="q-ml-sm q-mt-sm">
            <div class="text-caption">{{ journal.studentReason }}</div>
          </div>
        </q-card-section>

        <!-- Teacher Feedbacks (when not isTeacher) -->
        <q-card-section v-if="!isTeacher && journal.feedbacks?.length > 0" class="q-pb-none">
          <div class="text-bold q-mb-sm">Feedbacks provided by teacher</div>
          <div
            v-for="(feedback, index) in journal.feedbacks"
            :key="feedback._id"
            :class="['feedback-item', {'q-mb-md': index !== journal.feedbacks.length - 1}]">
            <div class="row items-center">
              <q-avatar size="24px">
                <PubAvatar :src="feedback.from === journal.teacherId ? journal.teacherInfo?.avatar : ''" />
              </q-avatar>
              <div class="text-bold q-ml-sm">{{ feedback.sender }}</div>
              <div class="text-caption q-ml-sm">{{ getTimeAgo(feedback.createdAt) }}</div>
            </div>
            <div class="q-ml-lg q-mt-sm">
              <div class="text-body2">{{ feedback.review }}</div>
            </div>
          </div>
        </q-card-section>
      </div>
      <q-card-section>
        <q-list>
          <q-item-label class="text-h6 q-mb-md">Tags</q-item-label>
          <RecursiveListView :data="outlines" :selected="{}" :isReadOnly="true" />
        </q-list>
      </q-card-section>

      <!-- Teacher Actions -->
      <q-card-section v-if="isTeacher && journal.status === 0" class="row justify-center">
        <q-btn size="sm" color="primary" label="Approve" rounded unelevated class="q-mr-sm" @click="openApproveModal" />
        <q-btn size="sm" color="negative" label="Reject" rounded unelevated @click="confirmReject" />
      </q-card-section>

      <!-- Delete Button -->
      <div
        v-if="(journal.status === 1 && pub.user?._id === journal.teacherId) || (journal.status !== 1 && pub.user?._id === journal.studentId)"
        class="row justify-center q-mt-sm q-mb-md">
        <q-btn size="md" color="negative" :label="!isTeacher ? 'Delete' : 'Remove from journal'" no-caps flat @click="confirmDelete" />
      </div>
    </q-card>
  </div>
</template>

<script setup>
import {ref, watch, defineEmits} from 'vue'
import {getTimeAgo} from './utils'
import {pubStore} from 'stores/pub'
import useTakeaway from 'src/composables/account/takeaway/useTakeaway'
import QuestionRenderer from './QuestionRenderer.vue'
import ResponseRenderer from './ResponseRenderer.vue'
import RecursiveListView from './RecursiveListView.vue'
import ApproveJournalModal from './ApproveJournalModal.vue'
import {getQuestionTitle, getQuestionIcon, getQuestionIconColor} from 'src/pages/account/takeaway/utils'
const {getSessionSnapShotBySessionId} = useTakeaway()

const props = defineProps({
  journal: Object,
  isTeacher: Boolean,
  visible: Boolean,
})

const pub = pubStore()
const loading = ref(true)
const outlines = ref([])
const emit = defineEmits(['updated', 'hideLoading', 'showLoading', 'goBack'])

const confirmReject = () => {
  $q.dialog({
    title: 'Confirm Rejection',
    message: 'Are you sure you want to reject this requested takeaway question?',
    cancel: true,
    persistent: true,
    ok: {
      label: 'Reject',
      color: 'negative',
      flat: true,
    },
  }).onOk(async () => {
    try {
      await App.service('journals').patch(props.journal._id, {
        status: 2, // Rejected
      })

      $q.notify({
        type: 'positive',
        message: 'Requested takeaway rejected successfully',
      })
      emit('showLoading')
      emit('updated')
      emit('goBack')
    } catch (err) {
      $q.notify({
        type: 'negative',
        message: err?.code === 404 ? 'This item has already been deleted' : err?.message || 'Failed to reject journal',
      })
    }
  })
}

const openApproveModal = () => {
  showApproveModal.value = true
}

const fetchData = async () => {
  try {
    const res = await getSessionSnapShotBySessionId(props.journal.sessionId)

    console.log('res', res)
    const resOutlines = res?.questions?.find((q) => q.page === props.journal.question._id || q._id === props.journal.question._id)?.outlines ?? []
    outlines.value = filterSelectedTree(resOutlines, props.journal.tagIds)
  } catch (e) {
    console.error('Error fetching session snapshot:', e)
  }
  loading.value = false
}

function filterSelectedTree(data, selected) {
  const selectedValues = selected.reduce((acc, cur) => {
    acc[cur] = true
    return acc
  }, {})
  const processNode = (node) => {
    let children = node.child || []

    const filteredChildren = children.map(processNode).filter((child) => child !== null)

    const isSelected = !!selectedValues[node._id]
    if (isSelected || filteredChildren.length > 0) {
      return {
        ...node,
        child: filteredChildren,
      }
    }

    return null
  }

  const filtered = {}

  for (const key of Object.keys(data)) {
    filtered[key] = data[key].map(processNode).filter((node) => node !== null)
  }
  console.log('filtered', selectedValues, filtered)
  return filtered
}

const confirmDelete = async () => {
  console.log('Delete clicked')
  $q.dialog({
    title: 'Confirm Deletion',
    message: props.isTeacher
      ? 'Remove Journal and all associated comments?'
      : `Are you sure you want to delete this ${props.journal.status === 0 ? 'requested' : 'rejected'} takeaway question?`,
    persistent: true,
    ok: {
      label: 'Delete',
      color: 'negative',
      flat: true,
    },
    cancel: true,
  }).onOk(async () => {
    try {
      // emit('showLoading')
      await App.service('journals').remove(props.journal._id)
      $q.notify({
        type: 'positive',
        message: 'Deleted successfully',
        icon: 'delete_forever',
      })
      emit('goBack')
      emit('showLoading')
      emit('updated') // optional: tell parent to refresh or remove from list
    } catch (err) {
      $q.notify({type: 'negative', message: props.isTeacher ? 'Failed to remove journal' : 'Failed to delete journal'})
    } finally {
      // emit('hideLoading')
    }
  })
}

watch(
  () => props.visible,
  (val) => {
    if (val) fetchData()
  }
)

const showApproveModal = ref(false)

const journalApproveCallback = () => {
  emit('updated')
  emit('goBack')
}
</script>

<style scoped>
.details-hd {
  color: #232323;
  height: auto !important;
}

.details-container {
  padding: 16px;
  margin-top: 50px;
  background-color: #f5f7fa;
  height: calc(100vh - 50px);
  overflow: auto;
}

.details-box {
  display: flex;
  flex-direction: column;
  background: #26a69a14;
  border-radius: 12px;
  padding: 16px 32px;
  margin: auto;
  min-width: 70%;
  max-width: 1024px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.answer-box {
  border-radius: 6px !important;
}

.status-badge {
  font-size: 12px;
  padding: 6px 12px;
  border-radius: 12px;
}

.text-caption {
  color: #666;
}

.text-body2 {
  font-size: 14px;
  line-height: 1.4;
}
</style>
