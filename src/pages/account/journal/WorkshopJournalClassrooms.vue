<template>
  <q-dialog v-model="isOpen" persistent>
    <q-card style="min-width: 350px">
      <q-card-section class="row items-center q-pb-none">
        <div style="font-weight: 500">Choose the classroom from your associated list</div>
      </q-card-section>

      <q-card-section v-if="loading">
        <div class="flex justify-center">
          <q-spinner color="primary" size="3em" />
        </div>
      </q-card-section>

      <q-card-section v-else-if="classList.length === 0">
        <div class="text-center text-grey-8">No classrooms found</div>
      </q-card-section>

      <q-card-section v-else style="max-height: 50vh; padding-left: 32px" class="scroll">
        <div style="font-weight: 500" class="q-mb-sm">Standard Classroom List</div>
        <q-list>
          <div class="q-pa-none" v-for="classItem in classList" :key="classItem._id">
            <q-radio size="sm" style="font-size: 13px" v-model="selectedClass" :val="classItem._id" :label="classItem.name" />
          </div>
        </q-list>
      </q-card-section>

      <div class="flex justify-end q-pa-md" style="gap: 10px">
        <q-btn rounded outline label="Cancel" color="negative" v-close-popup />
        <q-btn size="md" rounded unelevated color="primary" label="Confirm" :disable="!selectedClass" @click="onConfirm" />
      </div>
    </q-card>
  </q-dialog>
</template>

<script setup>
import {ref, defineProps, defineEmits, onMounted, watch} from 'vue'
import {useRouter} from 'vue-router'
import useSchool from 'src/composables/common/useSchool'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:modelValue'])

const router = useRouter()
const {schoolId} = useSchool()

const isOpen = ref(false)
const loading = ref(true)
const classList = ref([])
const selectedClass = ref(null)

// Watch for changes in the modelValue prop
watch(
  () => props.modelValue,
  (newVal) => {
    isOpen.value = newVal
    if (newVal) {
      fetchClassList()
    }
  }
)

// Watch for changes in the isOpen value
watch(isOpen, (newVal) => {
  emit('update:modelValue', newVal)
  if (!newVal) {
    selectedClass.value = null
  }
})

const fetchClassList = async () => {
  loading.value = true
  try {
    const schoolClassesList = await App.service('school-user').get('classList', {
      query: {school: schoolId.value},
    })

    classList.value = schoolClassesList || []
  } catch (error) {
    console.error('Failed to fetch class list:', error)
    $q.notify({
      type: 'negative',
      message: 'Failed to load classrooms',
    })
  } finally {
    loading.value = false
  }
}

const onConfirm = () => {
  if (selectedClass.value) {
    router.push({
      path: '/journal',
      query: {
        class: selectedClass.value,
        workshop: true,
      },
    })
    isOpen.value = false
  }
}

onMounted(() => {
  if (props.modelValue) {
    fetchClassList()
  }
})
</script>
