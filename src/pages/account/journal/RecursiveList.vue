<template>
  <q-list>
    <template v-if="isTopLevel">
      <template v-for="(items, key) in filteredData" :key="key">
        <template v-if="getBottomChildCount(items) > 0">
          <q-item-label header>{{ tagTitle[key] }} ({{ getBottomChildCount(items) }})</q-item-label>
          <RecursiveList
            :data="{ [key]: items }"
            :isTopLevel="false"
            :selected="selected"
            @update:selected="updateSelected"
          />
        </template>
      </template>
    </template>

    <template v-else>
      <template v-for="(items, key) in data" :key="key">
        <template v-for="item in items" :key="item._id">
          <template v-if="item.child && item.child.length">
            <q-item>
              <q-item-section side>
                <q-btn
                  flat
                  dense
                  round
                  :icon="!collapsed[item._id] ? 'expand_more' : 'chevron_right'"
                  @click="toggleCollapse(item._id)"
                />
              </q-item-section>
              <q-item-section>
                <div class="row no-wrap items-start">
                  <span class="q-ml-sm">{{ item.name }}</span>
                </div>
              </q-item-section>
            </q-item>

            <div v-if="!collapsed[item._id]" class="q-ml-md">
              <RecursiveList
                :data="{ [key]: item.child }"
                :isTopLevel="false"
                :selected="selected"
                @update:selected="updateSelected"
              />
            </div>
          </template>
          <template v-else-if="!item.child || item.child.length === 0">
            <q-item>
              <q-item-section>
                <div class="row no-wrap items-start">
                  <q-checkbox
                    :model-value="selected[item._id] ?? false"
                    @update:model-value="(val) => toggleSelection(item, key, val)"
                    dense
                    style="margin-top: 4px"
                  />
                  <span class="q-ml-sm">{{ item.name }}</span>
                </div>
              </q-item-section>
            </q-item>
          </template>
        </template>
      </template>
    </template>
  </q-list>
</template>

<script>
import { ref, watch, computed } from "vue";

export default {
  name: "RecursiveList",
  props: {
    data: Object,
    isTopLevel: { type: Boolean, default: true },
    selected: Object, // Parent-managed state
  },
  emits: ["update:selected"],
  setup(props, { emit }) {
    const collapsed = ref({});
    const tagTitle = {
      assess: "Standard",
      outline: "Topics",
      skills: "21st Century Skills",
      goal: "Learning Goals",
    };

    const filteredData = computed(() => {
      const result = {};
      for (const [key, items] of Object.entries(props.data)) {
        if (getBottomChildCount(items) > 0) {
          result[key] = items;
        }
      }
      return result;
    });

    const toggleSelection = (item, key, val) => {
      const newSelected = { ...props.selected };
      newSelected[item._id] = val;
      emit("update:selected", newSelected);
    };

    const toggleCollapse = (id) => {
      collapsed.value[id] = !collapsed.value[id];
    };

    const updateSelected = (selected_new) => {
      emit("update:selected", selected_new);
    };

    const getBottomChildCount = (items) => {
      let count = 0;
      const countLeafNodes = (arr) => {
        for (const item of arr) {
          if (!item.child || item.child.length === 0) {
            count++;
          } else {
            countLeafNodes(item.child);
          }
        }
      };
      countLeafNodes(items);
      return count;
    };

    watch(props.selected, () => emit("update:selected", props.selected), { deep: true });

    return {
      collapsed,
      toggleSelection,
      toggleCollapse,
      tagTitle,
      updateSelected,
      getBottomChildCount,
      filteredData,
    };
  },
};
</script>



