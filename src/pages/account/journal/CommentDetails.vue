<template>
  <q-header class="comment-hd bg-white shadow-1">
    <q-toolbar>
      <!-- Back Button -->
      <q-btn flat round dense icon="arrow_back" @click="$emit('goBack')" />

      <span class="text-h6 q-ml-sm"> Comments </span>
    </q-toolbar>
  </q-header>

  <div class="comment-container">
    <div class="comment-box">
      <!-- Comments List -->
      <!-- Comments List -->
      <div class="comment-list-box">
        <q-inner-loading v-if="isInitialLoading" showing color="primary" />
        <template v-else>
          <q-list separator>
            <q-item v-for="(comment, index) in journalComments" :key="index">
              <q-item-section>
                <div class="row items-center justify-between">
                  <div class="row items-center">
                    <q-avatar size="24px">
                      <PubAvatar :src="comment.avatar" />
                    </q-avatar>
                    <q-item-label class="q-ml-sm text-bold">{{ formatName(comment.name) }}</q-item-label>
                  </div>

                  <div v-if="comment.uid === pub.user?._id && comment._id">
                    <q-btn flat dense icon="delete" color="red" @click="deleteComment(index)" />
                  </div>
                </div>

                <q-item-label class="q-mt-sm comment-text">{{ comment.content }}</q-item-label>
              </q-item-section>
            </q-item>
          </q-list>

          <!-- Load More Button -->
          <q-btn v-if="hasMore" flat color="primary" @click="loadMore" class="more-btn" :loading="isLoadingMore" label="More" />
        </template>
      </div>

      <!-- Add Comment -->
      <div class="comment-input-container">
        <q-input v-model="newComment" outlined dense placeholder="Write a comment..." @keyup.enter="addComment" class="comment-input" />
        <q-btn label="Send" color="primary" rounded class="send-btn" @click="addComment" />
      </div>
    </div>
  </div>
</template>

<script setup>
import {ref, computed, watch} from 'vue'
import {pubStore} from 'stores/pub'

const props = defineProps({
  journal: Object,
  visible: Boolean,
})

const pub = pubStore()
const isInitialLoading = ref(true)
const isLoadingMore = ref(false)
const journalComments = ref([])
const totalComments = ref(0)
const limit = 10
const skip = ref(0)

const newComment = ref('')

// Check if more comments are available
const hasMore = computed(() => journalComments.value.length < totalComments.value)
const formatName = (name) => (Array.isArray(name) ? name.join(' ') : name)

const loadComments = async (initial = false) => {
  if (initial) isInitialLoading.value = true
  else isLoadingMore.value = true

  try {
    const result = await App.service('journal-comments').find({
      query: {
        journalId: props.journal._id,
        $limit: limit,
        $skip: skip.value,
        $sort: {createdAt: -1},
      },
    })

    if (Array.isArray(result.data)) {
      journalComments.value.push(...result.data)
      totalComments.value = result.total
      skip.value += result.data.length
    }
  } catch (err) {
    $q.notify({type: 'negative', message: 'Failed to load comments'})
  } finally {
    isInitialLoading.value = false
    isLoadingMore.value = false
  }
}

watch(
  () => props.visible,
  (val) => {
    if (val) loadComments(true)
  }
)

const loadMore = () => {
  loadComments()
}

const addComment = async () => {
  if (!newComment.value.trim()) return

  const optimisticId = Date.now()
  const temp = {
    _optimisticId: optimisticId,
    uid: pub.user?._id,
    name: pub.user?.name,
    avatar: pub.user?.avatar,
    content: newComment.value.trim(),
    journalId: props.journal._id,
  }

  journalComments.value.unshift(temp)
  totalComments.value++
  newComment.value = ''

  try {
    const res = await App.service('journal-comments').create({
      journalId: props.journal._id,
      content: temp.content,
    })

    const index = journalComments.value.findIndex((c) => c._optimisticId === optimisticId)
    if (index !== -1) {
      journalComments.value.splice(index, 1, {
        ...temp,
        _id: res._id,
        createdAt: res.createdAt,
      })
    }
  } catch (err) {
    console.log(err)
    const index = journalComments.value.findIndex((c) => c._optimisticId === optimisticId)
    if (index !== -1) {
      journalComments.value.splice(index, 1)
      totalComments.value--
    }
    $q.notify({type: 'negative', message: 'Failed to add comment'})
  }
}

const deleteComment = async (index) => {
  const comment = journalComments.value[index]
  journalComments.value.splice(index, 1)
  totalComments.value--

  try {
    console.log('comment Id', comment._id)
    await App.service('journal-comments').remove(comment._id)
  } catch (err) {
    $q.notify({type: 'negative', message: 'Failed to delete comment'})
    journalComments.value.splice(index, 0, comment)
    totalComments.value++
  }
}
</script>

<style scoped>
.comment-hd {
  color: #232323;
  height: auto !important;
}

.comment-container {
  display: flex;
  justify-content: center;
  /* align-items: center; */
  padding: 16px;
  margin-top: 50px;
  background-color: #f5f7fa;
  height: calc(100vh - 50px);
  /* Adjust this value as needed */
}

.comment-box {
  display: flex;
  flex-direction: column;
  background: #26a69a14;
  border-radius: 12px;
  padding: 16px 32px;
  min-width: 60%;
  max-width: 800px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.comment-list-box {
  flex: 1;
  overflow: auto;
  /* background: white; */
  border-radius: 15px;
  padding: 16px;
  width: 100%;
}

/* Comment Text */
.comment-text {
  white-space: pre-wrap;
  font-size: 14px;
  color: #333;
  word-wrap: break-word;
}

/* More Button */
.more-btn {
  margin: 12px auto;
  display: block;
  font-size: 14px;
}

/* Comment Input */
.comment-input-container {
  display: flex;
  align-items: center;
  padding: 20px 8px 8px;
}

.comment-input {
  flex-grow: 1;
}

.send-btn {
  margin-left: 8px;
}
</style>
