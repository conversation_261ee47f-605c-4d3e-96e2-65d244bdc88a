<script setup>
import {ref, defineProps, defineEmits, computed} from 'vue'
import {getTimeAgo} from './utils'
import QuestionRenderer from './QuestionRenderer.vue'
import ResponseRenderer from './ResponseRenderer.vue'

const props = defineProps({
  isTeacher: Boolean,
  journal: Object,
  openJournalDetails: Function,
  openApproveModal: Function,
})
const emit = defineEmits(['updated', 'hideLoading', 'showLoading'])

const confirmReject = () => {
  $q.dialog({
    title: 'Confirm Rejection',
    message: 'Are you sure you want to reject this requested takeaway question?',
    cancel: true,
    persistent: true,
    ok: {
      label: 'Reject',
      color: 'negative',
      flat: true,
    },
  }).onOk(async () => {
    try {
      // emit('showLoading')
      await App.service('journals').patch(props.journal._id, {
        status: 2, // Rejected
      })

      $q.notify({
        type: 'positive',
        message: 'Requested takeaway rejected successfully',
      })
      emit('showLoading')
      emit('updated') // Emit to parent
    } catch (err) {
      // emit('hideLoading')
      $q.notify({
        type: 'negative',
        message: err?.code === 404 ? 'This item has already been deleted' : err?.message || 'Failed to reject journal',
      })
    }
  })
}

const confirmDelete = async () => {
  console.log('Delete clicked')
  $q.dialog({
    title: 'Confirm Deletion',
    message: `Are you sure you want to delete this ${props.journal.status === 0 ? 'requested' : 'rejected'} takeaway question?`,
    persistent: true,
    ok: {
      label: 'Delete',
      color: 'negative',
      flat: true,
    },
    cancel: true,
  }).onOk(async () => {
    try {
      // emit('showLoading')
      await App.service('journals').remove(props.journal._id)
      $q.notify({
        type: 'positive',
        message: 'Deleted successfully',
        icon: 'delete_forever',
      })
      emit('showLoading')
      emit('updated') // optional: tell parent to refresh or remove from list
    } catch (err) {
      $q.notify({type: 'negative', message: 'Failed to delete journal'})
    } finally {
      // emit('hideLoading')
    }
  })
}

const isExpanded = ref(false)
const reasonRef = ref(null)
const shouldShowExpand = computed(() => {
  if (!reasonRef.value) return false
  const lineHeight = parseInt(getComputedStyle(reasonRef.value).lineHeight)
  const elementHeight = reasonRef.value.scrollHeight
  return elementHeight > lineHeight * 3
})
</script>

<template>
  <q-card class="content-card q-mx-md">
    <!-- Status Indicator (Only for Non-Teachers) -->
    <div class="publish-status">
      <q-badge v-if="!isTeacher" class="status-badge" :color="journal.status === 0 ? 'primary' : 'negative'" text-color="white">{{
        journal.status === 0 ? 'Pending' : 'Rejected'
      }}</q-badge>
    </div>

    <q-card-section class="answer-section q-pa-md">
      <!-- Answers -->
      <q-card-section class="row q-mt-sm q-pa-none q-pl-sm q-pr-sm items-center" style="flex-wrap: nowrap; gap: 10px">
        <!-- Answer Box -->
        <div class="answer-box bg-white q-pa-md" style="flex: 1 1 auto; min-width: 0">
          <QuestionRenderer :question="journal?.question" />
          <q-expansion-item label="View answer" dense-toggle switch-toggle-side class="q-mt-sm" header-class="text-teal">
            <ResponseRenderer class="q-ma-none q-mt-sm" :journal="journal" />
          </q-expansion-item>
        </div>

        <!-- Arrow Button -->
        <q-btn flat rounded icon="arrow_forward" color="primary" @click="openJournalDetails(journal)" />
      </q-card-section>

      <!-- Author Details -->
      <q-card-section>
        <div v-if="journal.studentInfo" class="row items-center">
          <q-avatar size="24px">
            <PubAvatar :src="journal.studentInfo.avatar" />
          </q-avatar>
          <div class="text-bold q-ml-sm">{{ journal.studentInfo.name.join(' ') }}</div>
        </div>
        <div class="q-ml-sm q-mt-sm">
          <div class="text-caption">
            <div ref="reasonRef" :class="{'line-clamp-3': !isExpanded}" class="reason-text">
              {{ journal.studentReason }}
            </div>
            <div
              v-if="shouldShowExpand"
              @click="isExpanded = !isExpanded"
              :style="{'margin-top': isExpanded ? '4px' : '0', lineHeight: isExpanded ? '20px' : '10px'}"
              class="expand-button text-primary cursor-pointer">
              {{ isExpanded ? 'Show less' : '...' }}
            </div>
          </div>
        </div>
      </q-card-section>
    </q-card-section>

    <!-- Submission Time -->
    <div class="text-caption text-grey-6 q-ma-sm">Submitted: {{ getTimeAgo(journal.createdAt) }}</div>

    <!-- Approve/Reject Buttons (Only for Teachers) -->
    <q-card-section v-if="isTeacher" class="row">
      <q-btn size="sm" color="primary" label="Approve" rounded unelevated class="q-mr-sm" @click="openApproveModal(journal)" />
      <q-btn size="sm" color="negative" label="Reject" rounded unelevated @click="confirmReject" />
    </q-card-section>

    <!-- Delete Button (Only for Non-Teachers) -->
    <div v-else class="row justify-center q-mt-sm q-mb-md">
      <q-btn size="sm" color="negative" label="Delete" flat @click="confirmDelete" />
    </div>
  </q-card>
</template>

<style scoped>
.content-card {
  background-color: white;
  border-radius: 8px;
  padding: 16px;
  position: relative;
}

.publish-status {
  height: 32px;
  padding-left: 6px;
}

.answer-section {
  border-radius: 15px !important;
  background-color: #f1f9f9;
}

.answer-box {
  border-radius: 6px !important;
}

/* Status Badge */
.status-badge {
  position: absolute;
  top: 12px;
  right: 20px;
  font-size: 12px;
  padding: 6px 12px;
  border-radius: 12px;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.expand-button {
  font-weight: 500;
}

.reason-text {
  white-space: pre-wrap;
}
</style>
