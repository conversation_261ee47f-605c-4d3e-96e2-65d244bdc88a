<template>
  <Teleport to="body">
    <!-- Overlay (click to close) -->
    <div
      v-if="modelValue"
      class="custom-drawer-overlay"
      @click="closeDrawer"
    ></div>

    <!-- Drawer -->
    <div
      class="custom-drawer"
      :class="[side, { open: modelValue }]"
      :style="{ width: computedWidth }"
    >
      <slot></slot> <!-- Allows any child content -->
    </div>
  </Teleport>
</template>

<script setup>
import { computed, watch } from "vue";

const props = defineProps({
  modelValue: Boolean, // Controls drawer visibility
  side: {
    type: String,
    default: "right", // Can be "left" or "right"
    validator: (val) => ["left", "right"].includes(val),
  },
  width: {
    type: [String, Number], // Supports "300px", "50%", etc.
    default: "300px",
  },
});

const emit = defineEmits(["update:modelValue"]); // Correct event definition

const computedWidth = computed(() => {
  return typeof props.width === "number" ? `${props.width}px` : props.width;
});

// Close the drawer properly
const closeDrawer = () => {
  emit("update:modelValue", false);
};

watch(() => props.modelValue, (newVal) => console.log(newVal));
</script>

<style scoped>
/* Overlay (dimmed background) */
.custom-drawer-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1998;
}

/* Drawer */
.custom-drawer {
  position: fixed;
  top: 0;
  height: 100vh;
  background: white;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.2);
  z-index: 2000;
  transition: transform 0.3s ease-in-out;
}

/* Right-side drawer */
.custom-drawer.right {
  right: 0;
  transform: translateX(100%); /* Hide by default */
}

/* Left-side drawer */
.custom-drawer.left {
  left: 0;
  transform: translateX(-100%); /* Hide by default */
}

/* Show drawer when open */
.custom-drawer.open {
  transform: translateX(0);
}
</style>
