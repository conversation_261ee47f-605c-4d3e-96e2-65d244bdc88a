import {ref, watch, onUnmounted, computed} from 'vue'

export default function useJournalRealtime(journals, activeTab) {
  const subscribedJournalIds = ref([])

  const getCurrentJournalIds = computed(() => {
    if (activeTab.value === 'published') {
      return journals.publishedJournals.value.map((j) => j._id)
    } else if (activeTab.value === 'approval' || activeTab.value === 'my_selected') {
      return journals.inApprovalJournals.value.map((j) => j._id)
    }
    return []
  })

  const updateJournalSubscriptions = async () => {
    try {
      const currentIds = getCurrentJournalIds.value

      if (JSON.stringify(currentIds.sort()) === JSON.stringify(subscribedJournalIds.value.sort())) {
        return
      }

      const idsToUnsubscribe = subscribedJournalIds.value.filter((id) => !currentIds.includes(id))

      const idsToSubscribe = currentIds.filter((id) => !subscribedJournalIds.value.includes(id))

      if (idsToUnsubscribe.length > 0) {
        await App.service('journals').create({journalIds: idsToUnsubscribe, type: 'unsubscribe'})
        console.log('Unsubscribed from journals:', idsToUnsubscribe)
      }

      if (idsToSubscribe.length > 0) {
        await App.service('journals').create({journalIds: idsToSubscribe, type: 'subscribe'})
        console.log('Subscribed to journals:', idsToSubscribe)
      }

      // Update our tracking array
      subscribedJournalIds.value = [...currentIds]
    } catch (e) {
      console.error('Error updating journal subscriptions', e)
    }
  }

  const unsubscribeFromJournals = async () => {
    try {
      if (subscribedJournalIds.value.length > 0) {
        await App.service('journals').create({journalIds: subscribedJournalIds.value, type: 'unsubscribe'})
        console.log('Unsubscribed from journals:', subscribedJournalIds.value)
      }
    } catch (e) {
      console.error('Error unsubscribing from journals', e)
    }
  }

  watch(
    [getCurrentJournalIds, activeTab],
    () => {
      updateJournalSubscriptions()
    },
    {deep: true}
  )

  onUnmounted(() => {
    unsubscribeFromJournals()
  })
}
