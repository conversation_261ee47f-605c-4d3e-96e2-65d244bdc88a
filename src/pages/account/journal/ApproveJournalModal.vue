<template>
  <q-dialog :model-value="modelValue" persistent>
    <q-card class="q-pa-md journal-modal" style="width: 500px; max-width: 90vw">
      <!-- Header -->
      <q-card-section>
        <div class="text-h6">Approve with comment</div>
      </q-card-section>
      <q-separator />

      <!-- loading -->
      <q-card-section v-if="loading" class="flex flex-center" style="min-height: calc(100vh - 48px)">
        <q-spinner color="primary" size="2em" />
      </q-card-section>

      <template v-else>
        <!-- Student's Answer -->
        <q-card-section class="student-answers">
          <ResponseRenderer class="q-ma-none q-mt-sm" :journal="journal" />
        </q-card-section>

        <!-- Reason Input -->
        <q-card-section>
          <q-item-label :class="{'text-subtitle1': true, 'q-mb-sm': true}">Please provide the reason and select tags to add to journal</q-item-label>
          <q-input
            v-model="reason"
            label="Enter a new comment"
            type="textarea"
            outlined
            autogrow
            counter
            :maxlength="maxReasonLength"
            :input-style="{minHeight: '52px'}"
            :error="reason.length >= maxReasonLength"
            :error-message="`Maximum ${maxReasonLength} characters allowed`">
            <template v-slot:append>
              <q-btn flat round dense color="primary" icon="mic" @click="startVoiceInput" />
            </template>
          </q-input>
        </q-card-section>

        <!-- Tag Selection -->
        <q-card-section>
          <q-item-label class="text-subtitle1">Please choose at least one tag below</q-item-label>
          <q-list>
            <RecursiveList :data="outlines" :selected="selectedItems" @update:selected="updateSelected" />
          </q-list>
        </q-card-section>

        <!-- Fixed Bottom Section -->
        <q-card-section class="fixed-bottom q-pa-md bg-white row justify-between items-center shadow-2">
          <q-btn-dropdown size="md" flat color="primary" :label="selectedVisibilityLabel" class="q-mr-md" no-caps>
            <q-list>
              <q-item clickable v-for="option in visibilityOptions" :key="option.value" @click="updateShareVisibility(option.value)">
                <q-item-section side style="width: 40px">
                  <q-icon v-if="selectedVisibility === option.value" name="check" color="primary" />
                </q-item-section>
                <q-item-section>
                  <q-item-label class="text-bold">{{ option.label }}</q-item-label>
                  <q-item-label caption>{{ option.description }}</q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
          </q-btn-dropdown>
          <q-btn class="q-mr-md" size="md" rounded outline label="Cancel" color="negative" @click="onCancel" />
          <q-btn size="md" rounded unelevated label="Confirm" color="primary" @click="onConfirm" :disable="disableSubmit" :loading="saveLoading" />
        </q-card-section>
      </template>
    </q-card>
  </q-dialog>
</template>

<script setup>
import {ref, watch, computed, defineProps, defineEmits} from 'vue'
import ResponseRenderer from './ResponseRenderer.vue'
import useTakeaway from 'src/composables/account/takeaway/useTakeaway'
import RecursiveList from './RecursiveList.vue'

const emit = defineEmits(['update:modelValue'])

const props = defineProps({
  modelValue: Boolean,
  journal: Object,
  callback: Function,
})

const reason = ref('')
const maxReasonLength = 1000
const selectedVisibility = ref('students-parents')
const selectedItems = ref({})
const saveLoading = ref(false)
const loading = ref(true)
const outlines = ref([])
const {getSessionSnapShotBySessionId} = useTakeaway()
const disableSubmit = computed(
  () => !reason.value?.trim() || reason.value?.trim().length > maxReasonLength || !Object.keys(selectedItems.value).filter((k) => selectedItems.value[k]).length
)

const visibilityOptions = [
  {label: 'Students & Parents', value: 'students-parents', description: 'Visible to the students of this class as well as parents of those students'},
  {label: 'Parents only', value: 'parents', description: 'Visible only to the parents of the students of this class'},
  {label: 'Students only', value: 'students', description: 'Visible only to the students of this class'},
  {label: 'Private only', value: 'private', description: 'Visible only to teachers of this class'},
]

const selectedVisibilityLabel = computed(() => {
  const selectedOption = visibilityOptions.find((option) => option.value === selectedVisibility.value)
  return selectedOption ? `Share to ${selectedOption.label.toLowerCase()}` : 'Share to student & family'
})

const updateShareVisibility = (value) => {
  selectedVisibility.value = value
}

const onCancel = () => {
  emit('update:modelValue', false)
}

const onConfirm = async () => {
  try {
    saveLoading.value = true
    const tagIds = Object.keys(selectedItems.value).reduce((acc, k) => {
      if (selectedItems.value[k]) acc.push(k)
      return acc
    }, [])

    const dataToPost = {
      tagIds,
      teacherReason: reason.value.trim(),
      visibility: selectedVisibility.value,
      status: 1,
    }
    console.log('dataToPost', dataToPost)
    const response = await App.service('journals').patch(props.journal._id, dataToPost)
    console.log('response', response)
    emit('update:modelValue', false)
    props.callback()
    $q.notify({message: 'Journal published successfully', type: 'positive'})
  } catch (e) {
    console.error('response error', e)
    $q.notify({
      message: e?.code === 404 ? 'This item has already been deleted' : e?.message || 'Error approving journal',
      type: 'negative',
    })
  } finally {
    saveLoading.value = false
  }
}

const updateSelected = (selected) => {
  console.log('TopLevel', selected)
  selectedItems.value = selected
}

// Speech-to-text functionality
const startVoiceInput = () => {
  if (!('webkitSpeechRecognition' in window)) {
    alert('Your browser does not support speech recognition. Please use Chrome.')
    return
  }

  const recognition = new window.webkitSpeechRecognition()
  recognition.lang = 'en-US'
  recognition.continuous = false
  recognition.interimResults = false

  recognition.onstart = () => {
    console.log('Voice recognition started...')
  }

  recognition.onresult = (event) => {
    const text = event.results[0][0].transcript
    const remainingLength = maxReasonLength - reason.value.length
    if (remainingLength <= 0) return
    const truncatedText = text.substring(0, remainingLength)
    reason.value += truncatedText
  }

  recognition.onerror = (event) => {
    console.error('Speech recognition error:', event.error)
  }

  recognition.start()
}

const fetchData = async () => {
  try {
    const res = await getSessionSnapShotBySessionId(props.journal.sessionId)

    console.log('res', res)
    outlines.value = res?.questions?.find((q) => q.page === props.journal.question._id || q._id === props.journal.question._id)?.outlines ?? []
  } catch (e) {
    console.error('Error fetching session snapshot:', e)
  }

  reason.value = props.journal.feedbacks?.map((item) => item.review).join('\n\n') ?? ''
  selectedVisibility.value = props.journal.visibility
  selectedItems.value = props.journal.tagIds.reduce((acc, id) => {
    acc[id] = true
    return acc
  }, {})
  loading.value = false
}

// reset on close
const reset = () => {
  reason.value = ''
  selectedVisibility.value = 'students-parents'
  selectedItems.value = {}
}
// Watch for modal close
watch(
  () => props.modelValue,
  (newValue) => {
    if (!newValue) {
      reset()
    }

    if (newValue) {
      fetchData()
    }
  }
)
</script>

<style scoped>
.journal-modal {
  padding: 0;
}

.journal-modal .student-answers {
  border: 16px solid #f1f9f9 !important;
  margin-bottom: 16px;
}

/* Fix bottom section */
.fixed-bottom {
  position: sticky;
  bottom: 0;
  width: 100%;
  z-index: 10;
  padding: 16px 16px 22px 16px;
  display: flex;
  justify-content: flex-end;
}

/* Make dropdown options match the image */
.q-list {
  width: 418px;
  padding: 10px 0;
}

.q-list .q-item {
  min-height: 48px;
  padding: 8px 20px;
}

.q-item-label.text-bold {
  font-weight: bold;
}
</style>
