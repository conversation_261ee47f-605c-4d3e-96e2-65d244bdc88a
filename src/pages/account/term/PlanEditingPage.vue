<template>
  <div>
    <PubTopBanner v-if="!isSys" title="Set time" :isShowMenu="false" />

    <q-page class="">
      <div class="pc-max q-px-md q-mt-xs">
        <!-- <pre>Plan:{{ plans }}</pre> -->
        <div class="flex justify-between items-center">
          <div v-if="plans?.length" class="flex items-center q-ma-md">
            <div v-for="(plan, index) in plans" :key="index" class="flex items-center rounded-md q-px-sm q-mx-sm" style="border: 1px solid teal">
              <span class="q-mr-sm">Plan {{ index + 1 }} </span>
              <q-chip v-for="week in plan.week" :key="week" :label="weekMap?.[week]" class="q-mx-xs bg-teal-4 text-white" square dense />
              <q-btn class="q-ml-sm" flat rounded icon="o_edit" dense color="teal" @click="() => onSetPlanClick(plan)" />
            </div>
          </div>
          <div v-else class="text-grey q-ma-md">No plan</div>
          <q-btn flat rounded no-caps label="Set plan" class="bg-teal text-white" @click="() => onSetPlanClick()" />
        </div>
        <CalendarWeekHoursNoTool v-model="weekData" :key="weekData" :editable="false" :clickable="false" />
      </div>

      <!-- dialogs -->
      <q-dialog v-model="isDialogShowMap.setPlan" class="" persistent>
        <div class="bg-white q-pa-lg" style="width: clamp(360px, 60%, 600px)">
          <CalendarDayHours v-if="showCaendarDayHours" v-model="timeBlocks" style="width: 100%" class="is-hide-day-header" />
          <div class="flex q-my-md">
            <div v-for="week in weekList" :key="week" class="relative-position">
              <q-btn
                :label="weekMap[week]"
                no-caps
                flat
                style="border: 1px solid"
                class="q-mx-xs rounded-md"
                :class="[selectedWeeks.includes(+week) ? 'bg-teal-4 text-white' : 'bg-white text-teal-4']"
                @click="() => onWeekClick(+week)" />
              <q-icon v-if="currentWeeks.includes(+week)" class="btn-check-icon" name="done" color="green-8" size="sm" />
            </div>
          </div>
          <div class="flex justify-end q-gutter-md full-width no-wrap">
            <q-btn flat rounded color="grey-9" no-caps label="Back" icon="arrow_back" style="border: 1px solid" @click="(e) => closeSetPlanDialog()" />
            <q-btn
              flat
              rounded
              color="white"
              no-caps
              label="Finish"
              icon="check"
              class="border-grey bg-teal-4"
              @click="(e) => onFinishClick()"
              :disable="!selectedWeeks?.length || !timeBlocks?.length" />
          </div>
        </div>
      </q-dialog>
    </q-page>
  </div>
</template>

<script setup>
import {ref, computed, onMounted} from 'vue'
import {useQuasar} from 'quasar'
import {useRoute} from 'vue-router'

import useSchool from 'src/composables/common/useSchool'
import useSchoolTerm from 'src/composables/account/school/useSchoolTerm'

import CalendarDayHours from 'components/CalendarDayHours.vue'
import CalendarWeekHoursNoTool from 'components/CalendarWeekHoursNoTool.vue'

const {isSys} = useSchool()
const {getOneById, createPlan, getPlanList, patchPlanById} = useSchoolTerm()

const $q = useQuasar()
const $route = useRoute()

const termId = computed(() => $route.params.termId)

const isDialogShowMap = ref({setPlan: false})
const data = ref(null)
const plans = ref([])

const currentWeeks = computed(() => {
  if (!plans.value.length) return []
  const weeks = []
  plans.value.forEach((e) => {
    const week = e.week
    week.forEach((d) => {
      if (!weeks.includes(d)) weeks.push(d)
    })
  })
  return weeks
})

const weekMap = {
  0: 'Sun',
  1: 'Mon',
  2: 'Tue',
  3: 'Wed',
  4: 'Thu',
  5: 'Fri',
  6: 'Sat',
}
const weekList = Object.keys(weekMap)
function onWeekClick(week) {
  if (selectedWeeks.value.includes(week)) {
    selectedWeeks.value = selectedWeeks.value.filter((e) => e != week)
  } else {
    selectedWeeks.value.push(week)
  }
}

const weekData = computed(() => {
  if (!plans.value?.length) return []
  const list = []
  plans.value.forEach((e) => {
    e.week.forEach((week) => {
      e.block.forEach((block) => {
        const dto = {week: +week, start: block.start, end: block.end}
        list.push(dto)
      })
    })
  })
  return list
})

const editingPlanId = ref(false)
const selectedWeeks = ref([])
const timeBlocks = ref([])

const showCaendarDayHours = ref(false)
const isHideDayHeader = ref(false)
let timer = null
function onSetPlanClick(editingPlan = null) {
  if (editingPlan) {
    editingPlanId.value = editingPlan._id
    selectedWeeks.value = Acan.clone(editingPlan.week)
    timeBlocks.value = editingPlan.block.map((e) => [e.start, e.end])
  } else {
    editingPlanId.value = ''
    selectedWeeks.value = []
    timeBlocks.value = []
  }
  isDialogShowMap.value.setPlan = true
  setTimeout(() => {
    showCaendarDayHours.value = true
    isHideDayHeader.value = false

    if (timer) clearInterval(timer)
    timer = setInterval(() => {
      const element = document.querySelector('.is-hide-day-header .fc-scrollgrid-section-header')
      console.warn(element)
      if (element) {
        element.remove()
        isHideDayHeader.value = true
        clearInterval(timer)
      }
    }, 50)
  }, 300)
}

function closeSetPlanDialog() {
  isDialogShowMap.value.setPlan = false
  showCaendarDayHours.value = false
}

async function onFinishClick() {
  const dto = {
    schoolTerm: termId.value,
    week: selectedWeeks.value,
    block: timeBlocks.value.map((e) => ({start: e[0], end: e[1]})),
  }
  if (editingPlanId.value) {
    await onPatchPlan(editingPlanId.value, dto)
  } else {
    await onCreatePlan(dto)
  }
  await init()
}

async function onCreatePlan(dto) {
  try {
    await createPlan(dto)
    $q.notify({type: 'positive', message: 'Plan created successfully'})
    closeSetPlanDialog()
  } catch (error) {
    console.error(error)
    $q.notify({type: 'negative', message: 'Plan created unsuccessfully'})
  }
}

async function onPatchPlan(id, dto) {
  try {
    await patchPlanById(id, dto)
    $q.notify({type: 'positive', message: 'Plan updated successfully'})
    closeSetPlanDialog()
  } catch (error) {
    console.error(error)
    $q.notify({type: 'negative', message: 'Plan updated unsuccessfully'})
  }
}

async function init() {
  data.value = await getOneById(termId.value)
  const planRes = await getPlanList({schoolTerm: termId.value})
  plans.value = planRes?.data || []
}

onMounted(async () => {
  await init()
})
</script>

<style lang="scss" scoped>
.btn-border {
  border: 1px solid;
}
.btn-check-icon {
  position: absolute;
  bottom: -0px;
  right: 5px;
}
.is-hide-day-header {
  .fc-scrollgrid-section-header {
    display: none;
  }
}
</style>
