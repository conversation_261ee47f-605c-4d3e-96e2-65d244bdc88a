<template>
  <div>
    <PubTopBanner v-if="!isSys" title="Set time" :isShowMenu="false" />

    <q-page class="">
      <q-card v-if="isBannerShow" class="q-pa-md bg-amber-2 flex justify-between items-center full-width" flat>
        <div class="flex items-center q-gutter-sm">
          <q-icon name="error" color="amber-7" size="sm" />
          <div class="text-grey-8">No data set on the timeable, please set data.</div>
        </div>
        <q-btn dense rounded flat size="sm" icon="close" @click.stop="isBannerShow = false" />
      </q-card>
      <div class="pc-max q-px-md q-mt-xs">
        <div class="flex justify-between items-center q-my-md">
          <div></div>
          <q-btn flat rounded no-caps label="Add daily plan" class="bg-teal text-white" @click="() => goToPlanEdit()" />
        </div>
        <CalendarWeekHoursNoTool v-model="weekData" :key="weekData" :editable="false" :clickable="false" />
      </div>
    </q-page>
  </div>
</template>

<script setup>
import {ref, watch, computed, watchEffect, onMounted} from 'vue'
import {useQuasar, date} from 'quasar'
import {useRoute, useRouter} from 'vue-router'

import useSchool from 'src/composables/common/useSchool'
import useSchoolTerm from 'src/composables/account/school/useSchoolTerm'

import CalendarWeekHoursNoTool from 'components/CalendarWeekHoursNoTool.vue'

const {isSys, schoolId, isAdmin, isSchool} = useSchool()
const {getOneById, patchOneById, createPlan, getPlanList, patchPlanById} = useSchoolTerm()

const $q = useQuasar()
const $route = useRoute()
const $router = useRouter()

const termId = computed(() => $route.params.termId)
const yearId = computed(() => $route.params.yearId)

const isBannerShow = ref(false)
const data = ref(null)
const plans = ref([])

const currentWeeks = computed(() => {
  if (!plans.value.length) return []
  const weeks = []
  plans.value.forEach((e) => {
    const week = e.week
    week.forEach((d) => {
      if (!weeks.includes(d)) weeks.push(d)
    })
  })
  return weeks
})

const weekData = computed(() => {
  if (!plans.value?.length) return []
  const list = []
  plans.value.forEach((e) => {
    e.week.forEach((week) => {
      e.block.forEach((block) => {
        const dto = {week: +week, start: block.start, end: block.end}
        list.push(dto)
      })
    })
  })
  return list
})

function goToPlanEdit() {
  $router.replace({
    path: `/account/term/${yearId.value}/${termId.value}/plan/edit`,
    // query: {back: $route.fullPath},
  })
}

async function init() {
  data.value = await getOneById(termId.value)
  const planRes = await getPlanList({schoolTerm: termId.value})
  plans.value = planRes?.data || []
}

onMounted(async () => {
  await init()
  if (!plans.value?.length) isBannerShow.value = true
})
</script>

<style lang="scss" scoped>
.btn-border {
  border: 1px solid;
}
.btn-check-icon {
  position: absolute;
  bottom: -3px;
  right: -5px;
}
</style>
