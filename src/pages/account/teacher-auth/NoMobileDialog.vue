<template>
  <q-dialog :modelValue="modelValue" @update:modelValue="(bool = false) => emit('update:modelValue', bool)" persistent class="offset-top-dialog">
    <div class="bg-teal-1 q-pa-md rounded-lg" style="width: clamp(300px, 50%, 512px)">
      <!-- <div class="flex justify-start full-width"> -->
      <!--   <q-btn flat rounded dense icon="close" @click="emit('update:modelValue', false)" /> -->
      <!-- </div> -->
      <div class="q-pa-lg">
        <div class="text-subtitle1 q-pb-lg">If you're a verified service provider, connect your phone number to your Classcipe account to receive instant system notifications about your sessions. This ensures timely updates and improved service delivery.</div>
        <q-btn rounded flat no-caps label="I got it" class="text-teal full-width q-mt-md" style="border: 1px solid #888" @click="goToSetMobile" />
      </div>
    </div>
  </q-dialog>
</template>

<script setup>
import {useRoute, useRouter} from 'vue-router'

const route = useRoute()
const router = useRouter()

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:modelValue'])

function goToSetMobile() {
  emit('update:modelValue', false)
  router.push({
    path: '/account/security',
    query: {dialogId: 'setMobile', back: route.path},
  })
}
</script>

<style lang="scss">
.offset-top-dialog {
  .q-dialog__backdrop {
    top: 3rem;
  }
}
</style>
