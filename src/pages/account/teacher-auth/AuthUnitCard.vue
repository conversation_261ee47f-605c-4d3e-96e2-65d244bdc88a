<template>
  <q-item @click="onClick" class="q-pa-sm full-width full-height rounded-borders-md bg-white overflow-hidden q-border-1">
    <div class="full-width overflow-hidden">
      <div class="row q-col-gutter-md">
        <div class="col-xs-12" :class="{'col-sm-5 col-md-4': !isLessThanSmall}">
          <div class="row">
            <div class="col-xs-3" :class="{'col-sm-12': !isLessThanSmall}">
              <q-img
                class="full-width rounded-borders-md"
                :ratio="$q.screen.gt.xs ? 16 / 12 : 16 / 9"
                fit="cover"
                :src="hashToUrl(item?.unitSnapshot?.cover) || '/v2/img/no-img.png'" />
            </div>
            <div v-if="isLessThanSmall" class="col q-pl-sm">
              <div class="ellipsis-2-lines text-h6" :title="item?.unitSnapshot?.name">
                {{ item?.unitSnapshot?.name }}
              </div>
            </div>
          </div>
        </div>

        <div class="col-xs-12" :class="{'col-sm-7 col-md-8': !isLessThanSmall}">
          <div v-if="!isLessThanSmall" class="col">
            <div class="ellipsis-1-line text-h6" :title="item?.unitSnapshot?.name">
              {{ item?.unitSnapshot?.name }}
            </div>
          </div>

          <div class="flex items-center">
            <div class="text-yellow-9 text-weight-medium q-pr-sm">{{ tag }}</div>
            <div v-if="item?.curriculum !== 'pd'" class="text-weight-medium text-primary">
              {{ sysCurriculumMap[item?.curriculum]?.label?.replace('curriculum', '') }}
            </div>
            <div v-else class="text-weight-medium text-primary">
              {{ subjectTitle(item.subject) }}
            </div>
          </div>

          <div v-if="item?.curriculum !== 'pd' && subjectTitle(item?.subject)" class="flex items-center">
            <div class="text-weight-medium q-pr-sm">Subject</div>
            <q-chip
              v-if="subjectTitle(item.subject)"
              class="text-weight-medium"
              :ripple="false"
              size="12px"
              color="teal-1"
              text-color="primary"
              :label="subjectTitle(item.subject)" />
          </div>

          <div v-if="item?.topic.some((e) => e?.label.filter((e) => e)?.length)" class="flex items-center">
            <div class="text-weight-medium q-pr-sm">Topics</div>
            <template v-for="(topic, i) in item.topic" :key="i">
              <template v-for="label in topic.label" :key="label">
                <q-chip v-if="label" class="text-weight-medium" :ripple="false" size="12px" color="teal-1" text-color="primary" :label="label" />
              </template>
            </template>
          </div>

          <div v-if="item?.gradeGroup?.length" class="flex items-center">
            <div class="text-weight-medium q-pr-sm">Grades</div>
            <template v-for="(gradeGroup, i) in item.gradeGroup" :key="i">
              <q-chip
                class="text-weight-medium"
                :ripple="false"
                size="12px"
                color="teal-1"
                text-color="primary"
                :label="GradeGroupMap?.[gradeGroup]?.['label']" />
            </template>
          </div>
          <slot></slot>
        </div>
      </div>
    </div>
  </q-item>
</template>
<script setup>
import {computed} from 'vue'
import {GradeGroupMap} from 'src/boot/const'

import useAcademicSetting from 'src/composables/account/academic/useAcademicSetting'
import useSubject from 'src/composables/account/academic/useSubject'

import {servicePackageStore} from 'stores/service-package'
import {serviceAuthTagName} from 'src/pages/teacher-verification/utils'

const props = defineProps({
  item: Object,
  onItemClick: Function,
})

const servicePackage = servicePackageStore()

const {sysMap, sysCodeMap} = useSubject()
const {sysCurriculumMap} = useAcademicSetting()

const tag = computed(() => {
  return serviceAuthTagName(props.item?.status, props.item?.unitSnapshot?.mode) || 'Premium course material'
})

const onClick = () => {
  if (props.onItemClick) {
    props.onItemClick(props.item)
  }
}

const isLessThanSmall = computed(() => {
  return $q.screen.lt.sm
})

const subjectTitle = (subject) => {
  let title = ''
  const mentoringType = props.item?.mentoringType || ''
  if (mentoringType && mentoringType !== 'academic') {
    const subjectList = servicePackage.serviceSubjects[mentoringType]?.['topic']
    if (!subjectList) return title
    if (servicePackage.isTopicWithChilds(mentoringType)) {
      return getTopicTitles(subject, subjectList)
    } else {
      subjectList?.map((topic) => {
        if (topic?._id === subject) {
          title = topic.name
        }
      })
      return title
    }
  }
  if (subject) {
    if (!Acan.isObjectId(subject)) {
      title = sysCodeMap.value?.[subject]?.name || ''
    } else if (sysMap.value?.[subject]) {
      title = sysMap.value?.[subject]?.name || ''
    }
  }
  return title
}
const getTopicTitles = (subject, subjectList) => {
  let titles = []
  const topics = Array.isArray(subject) ? subject : [subject]
  topics.forEach((e) => {
    subjectList.map((topic) => {
      topic.child.map((child) => {
        if (child?._id === e) {
          if (titles.length == 0) {
            titles.push(topic.name)
          }
          titles.push(child.name)
        }
      })
    })
  })

  if (titles?.length == 0) {
    topics.forEach((e) => {
      if (Array.isArray(e?.label)) {
        titles = [...e.label, ...titles]
      }
    })
  }
  return titles
}
</script>
<style lang="sass" scoped>
.package-bg
  background: #F1F9F9 !important
</style>
