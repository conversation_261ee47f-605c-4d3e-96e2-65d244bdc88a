<template>
  <q-card
    v-if="isMentoringHintShowMap?.[currentKey] && getMentoringHint(currentKey)"
    class="bg-green-1 flex justify-between items-center q-pa-sm q-ma-sm rounded-lg no-shadow">
    <div class="flex items-center">
      <q-icon name="o_info" color="green" size="sm" />
      <div class="q-ml-sm">{{ getMentoringHint(currentKey) }}</div>
    </div>
    <q-btn icon="close" flat rounded dense @click="isMentoringHintShowMap[currentKey] = false" />
  </q-card>
</template>

<script setup>
import {isMentoringHintShowMap} from 'src/composables/useGlobalStates.js'
import {ServiceTypes} from 'src/pages/teacher-verification/utils.js'

const props = defineProps({
  currentKey: {
    type: String,
    default: '',
  },
  userVerificationMap: {
    type: Object,
    default: () => {},
  },
})

function getMentoringHint(key = '') {
  if (!key) return ''
  const approvalCount = props.userVerificationMap?.[key]?.filter((e) => e?.status === 2)?.length ?? 0
  if (!approvalCount) return ''
  const academicLabel = `You have ${approvalCount} verified subjects and are now eligible to submit content for the same under ‘Premium Course Material’`
  const serviceLabel = `You have ${approvalCount} verified topics and are now eligible to submit content for the same under ‘Premium Course Material’`
  if (key === 'mentoring:academic') return academicLabel
  const arr = ServiceTypes.map((code) => `mentoring:${code}`)
  if (arr.includes(key)) return serviceLabel
  return ''
}
</script>
