<template>
  <div v-if="allTeacherVerificationConfig?.[typeKey]?.topic?.length">
    <div
      v-for="itemGroup in [...new Set(allTeacherVerificationConfig?.[typeKey].topic?.map((e) => e?.label[e?.label?.length - 2]))]"
      :key="itemGroup"
      class="q-px-md q-pt-md q-pb-lg">
      <div class="text-subtitle2 q-mb-md">{{ itemGroup }}</div>
      <div class="flex q-gutter-md">
        <div v-for="item in allTeacherVerificationConfig[typeKey].topic.filter((e) => e?.label[e?.label?.length - 2] === itemGroup)" :key="item._id">
          <div class="flex q-gutter-sm">
            <q-btn
              flat
              no-caps
              dense
              :label="item.label[item.label?.length - 1]"
              class="q-px-sm"
              style="border: 1px solid"
              :class="[getTopicButtonClass(typeKey, item?._id)]"
              :disable="isDisable(item)"
              @click="() => emit('click', {key: typeKey, topic: {_id: item._id, label: item.label}})">
              <q-tooltip v-if="isDisable(item)" class="text-subtitle1" anchor="top middle" self="center middle"> Selected </q-tooltip>
            </q-btn>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div v-else class="q-ml-md text-grey-6">No data</div>
</template>

<script setup>
const props = defineProps({
  allTeacherVerificationConfig: {
    type: Object,
    require: true,
  },
  userVerificationMap: {
    type: Object,
    require: true,
  },
  currentVerificationToCreate: {
    type: Object,
    require: true,
  },
  typeKey: {
    type: String,
    require: true,
  },
})

const emit = defineEmits(['click'])

function isDisable(item) {
  return !!props.userVerificationMap?.[props.typeKey].map((e) => e?.topic?.[0]?._id).includes(item?._id)
}
function getTopicButtonClass(key = '', topicId = '') {
  if (props.userVerificationMap?.[key].map((e) => e?.topic?.[0]?._id).includes(topicId)) return 'bg-green text-white'
  if (props.currentVerificationToCreate?.map((e) => e?.topic?._id).includes(topicId)) return 'bg-green text-white'
  else return 'bg-white text-green'
}
</script>
