<template>
  <q-btn flat dense color="teal" icon="add" label="Add" style="border: 1px solid #aaa" rounded class="q-px-sm" no-caps @click.stop="emits('click')">
    <q-tooltip v-if="toolTip" class="text-subtitle1" anchor="center left" self="center right">{{ toolTip }}</q-tooltip>
  </q-btn>
</template>

<script setup>
const emits = defineEmits(['click'])
defineProps({
  toolTip: {
    type: String,
    default: '',
  },
})
</script>
