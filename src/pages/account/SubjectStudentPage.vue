<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div>
    <PubTopBanner :title="title" :isShowMenu="false" />
    <q-page class="column pc-max">
      <!-- <NavBar :plist="plist" /> -->
      <!-- <AppTab v-if="isUserEditing" :tab="tab" :tabOptions="tabOptions" @change="(str) => (tab = str)" /> -->
      <StudentList :tab="tab" :tabOptions="tabOptions" />
    </q-page>
  </div>
</template>

<script setup>
import {ref, watch, computed, watchEffect} from 'vue'
import {useRoute, useRouter} from 'vue-router'
// import AppTab from 'src/components/pub/AppTab.vue'

import StudentList from 'components/account/students/SubjectList.vue'
import useClasses from 'src/composables/account/school/useClasses'
import useSchool from 'src/composables/common/useSchool'

const $router = useRouter()
const $route = useRoute()
const {list: classList} = useClasses()
const {
  // isUserEditing,
  schoolIdOrUserId,
} = useSchool()

const tab = ref('all')
const tabOptions = [
  {
    name: 'all',
    label: 'All',
    query: {
      // queryType: 0,
      // schoolUserStatus: 1,
      roles: 'student',
      column: 'createTime',
    },
  },
  {
    name: 'active',
    label: 'Active',
    query: {
      schoolUserStatus: 1,
      roles: 'student',
      column: 'createTime',
    },
  },
  {
    name: 'inactive',
    label: 'Inactive',
    query: {
      schoolUserStatus: 0,
      roles: 'student',
      column: 'createTime',
    },
  },
  {
    name: 'archive',
    label: 'Archive',
    query: {
      schoolUserStatus: 4,
      roles: 'student',
      column: 'createTime',
    },
  },
]

// const tabToComponent = {
//   all: 'all',
//   active: 'active',
//   inactive: 'inactive',
//   archive: 'archive',
// }

const classTab = ref('')
const className = ref('')
// const plist = computed(() => {
//   if (!isUserEditing.value) return []
//   if (className.value) {
//     return ['account', 'studentManage', {label: className.value, icon: 'widgets', to: `/account/classes/${classTab.value}`}]
//   }
//   return ['account', 'studentManage']
// })
const title = computed(() => {
  // if (className.value) return `Students Manage / ${className.value}`
  return 'Student list'
})

const setClassById = async (id) => {
  const target = classList.value.find((e) => e._id === id)
  if (target) {
    className.value = target.name
    classTab.value = target.subject ? 'subject' : 'standard'
  }
}

watchEffect(async () => {
  if (!schoolIdOrUserId.value) return
  const options = tabOptions.map((e) => e.name)
  if (options.includes($route.params.tab)) {
    tab.value = $route.params.tab
  } else {
    // tab.value = options[0]
  }
  if ($route.query?.classId) {
    setClassById($route.query?.classId)
  } else {
    className.value = ''
    classTab.value = 'standard'
  }
})

watch(tab, (newValue) => {
  if (!tab.value && !$route.params.tab) return
  console.log('change', tab.value)
  const qs = Object.entries($route.query)
    .map((e) => e.join('='))
    .join('&')
  if (qs) {
    $router.push(`/account/student/${newValue}?${qs}`)
  } else {
    $router.push(`/account/student/${newValue}`)
  }
})
watch($route, async () => {
  tab.value = $route.params.tab
})
</script>

<style lang="scss"></style>
