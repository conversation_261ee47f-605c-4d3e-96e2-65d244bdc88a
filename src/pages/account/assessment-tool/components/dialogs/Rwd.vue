<template>
  <div>
    <!-- TODO rwd -->
    <q-dialog v-model="isRwdSectionDialogOpen" class="relative-position">
      <div class="rwd-dialog bg-teal-1 full-width">
        <div class="q-pa-md">
          <q-list>
            <q-item>
              <q-item-section>
                <div>Teacher review</div>
                <q-toggle dense v-model="section.teacher" />
              </q-item-section>
            </q-item>
            <q-item>
              <q-item-section>
                <div>Self review</div>
                <q-toggle dense v-model="section.self" />
              </q-item-section>
            </q-item>
            <q-item>
              <q-item-section>
                <div>Peer review</div>
                <q-toggle dense v-model="section.peer" />
              </q-item-section>
            </q-item>
            <q-item>
              <q-item-section>
                <div>Anonymous review</div>
                <q-toggle dense v-model="section.anonymous" />
              </q-item-section>
            </q-item>
            <q-item>
              <q-item-section>
                <div class="flex q-my-sm">
                  <div
                    class="border-grey-2 rounded-left q-py-sm q-pr-md q-pl-lg cursor-pointer"
                    :class="[section.visible === 'all' ? 'btn-active' : '']"
                    @click.stop="section.visible = 'all'">
                    All
                  </div>
                  <div
                    class="border-grey-2 rounded-right q-py-sm q-pl-md q-pr-lg cursor-pointer"
                    :class="[section.visible === 'teacher' ? 'btn-active' : '']"
                    @click.stop="section.visible = 'teacher'">
                    Teacher
                  </div>
                </div>
              </q-item-section>
            </q-item>
          </q-list>
        </div>
        <div class="bg-white flex flex-center full-width q-pa-sm q-gutter-sm">
          <q-btn flat icon="settings" color="teal" />
          <q-btn flat rounded icon="add" no-caps label="Add new" color="teal" style="border: 1px solid #26a69a" />
          <q-btn flat rounded icon="done" no-caps label="Finish" class="bg-teal" color="white" />
        </div>
      </div>
    </q-dialog>

    <q-dialog v-model="isRwdSectionDataDialogOpen" class="flex items-end">
      <div class="bg-teal-1 flex items-end">
        <div>rwd</div>
      </div>
    </q-dialog>
  </div>
</template>

<script setup>
import {ref} from 'vue'

const section = ref({
  teacher: false,
  self: false,
  peer: false,
  anonymous: false,
  visible: 'all',
})

// TODO: RWD
const isRwdSectionDialogOpen = ref(false)
const setIsRwdSectionDialogOpen = (bool = false) => (isRwdSectionDialogOpen.value = bool)
const isRwdSectionDataDialogOpen = ref(false)
</script>

<style lang="scss">
// RWD
.rwd-dialog {
  position: absolute;
  bottom: 0;
  border-radius: 1rem 1rem 0 0;
}
</style>
