<template>
  <!-- skills -->
  <div v-if="isShow">
    <div class="q-my-sm">
      <span class="text-subtitle1">Skills</span>
    </div>
    <div style="border: 1px solid #aaa; border-radius: 0.5rem">
      <div v-for="curriculumCode in currentPreSelectData.skills.curriculumCodes" :key="curriculumCode">
        <div v-if="currentPreSelectData?.skills?.data?.[curriculumCode]?.child?.length">
          <div class="q-my-sm q-ml-sm">
            <span class="text-subtitle1">{{
              getNamesByCode(curriculumCode)?.subject || currentPreSelectData?.skills?.data?.[curriculumCode]?.name || ''
            }}</span>
            <span v-if="getNamesByCode(curriculumCode)?.curriculum" class="text-grey-7"> ({{ getNamesByCode(curriculumCode).curriculum }})</span>
          </div>
          <OutlineChild
            v-if="currentPreSelectData?.skills?.data?.[curriculumCode]?.child?.length"
            :data="currentPreSelectData.skills.data[curriculumCode].child"
            :form="currentPreSelectData.skills"
            :_id="currentPreSelectData._id"
            type="skills"
            :code="curriculumCode"
            @remove="emit('rmFn')"
            :disable="false"
            :isAssessmentToolSelect="true"
            :sourceIds="sourceOutlineIdsMap.skills"
            :hintColor="hintColor"
            :isEdit="true" />
        </div>
        <div v-if="currentPreSelectData?.skills?.data?.[curriculumCode]?.custom?.length">
          <div class="q-my-sm q-ml-sm">
            <span class="text-subtitle1 text-grey">Custom</span>
          </div>
          <OutlineChild
            v-if="currentPreSelectData?.skills?.data?.[curriculumCode]?.custom?.length"
            :data="currentPreSelectData.skills.data[curriculumCode].custom"
            :form="currentPreSelectData.skills"
            :_id="currentPreSelectData._id"
            :isCustom="true"
            type="outline"
            :code="curriculumCode"
            @unSelect="deleteToolData"
            @remove="emit('rmFn')"
            :disable="false"
            :isAssessmentToolSelect="true"
            :sourceIds="sourceOutlineIdsMap.outline"
            :hintColor="hintColor"
            :isEdit="true" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import {computed} from 'vue'
import useAssessmentToolEditing from 'src/composables/account/assessment-tool/useAssessmentToolEditing'
import OutlineChild from 'components/OutlineChild.vue'

const {currentPreSelectData, getNamesByCode, sourceOutlineIdsMap} = useAssessmentToolEditing()

const emit = defineEmits(['rmFn', 'deleteToolData'])

const isShow = computed(() => {
  return (
    currentPreSelectData.value?.skills?.curriculumCodes?.length &&
    currentPreSelectData.value?.skills?.data &&
    !Object.values(currentPreSelectData.value.skills.data).every((e) => e?.child?.length === 0)
  )
})

const hintColor = computed(() => {
  // no need hint by now
  return ''
  // const mode = sourceUnit.value?.mode || ''
  // if (['pdTask, task'].includes(mode)) return '#F4B302'
  // else if (['pdUnit', 'unit'].includes(mode)) return '#26A69A'
  // return ''
})
</script>
