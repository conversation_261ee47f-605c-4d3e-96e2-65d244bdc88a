<template>
  <!-- standard -->
  <div v-if="isShow">
    <div class="q-my-sm">
      <span class="text-subtitle1">Topic</span>
    </div>
    <div style="border: 1px solid #aaa; border-radius: 0.5rem">
      <div v-for="curriculumCode in currentPreSelectData.outline.curriculumCodes" :key="curriculumCode">
        <div v-if="currentPreSelectData?.outline?.data?.[curriculumCode]?.child?.length">
          <div class="q-my-sm q-ml-sm">
            <span class="text-subtitle1">{{
              getNamesByCode(curriculumCode)?.subject || currentPreSelectData?.outline?.data?.[curriculumCode]?.name || ''
            }}</span>
            <span v-if="getNamesByCode(curriculumCode)?.curriculum" class="text-grey-7"> ({{ getNamesByCode(curriculumCode).curriculum }})</span>
          </div>
          <OutlineChild
            v-if="currentPreSelectData?.outline?.data?.[curriculumCode]?.child?.length"
            :data="currentPreSelectData.outline.data[curriculumCode].child"
            :form="currentPreSelectData.outline"
            :_id="currentPreSelectData._id"
            type="outline"
            :code="curriculumCode"
            @remove="emit('rmFn')"
            :disable="false"
            :isAssessmentToolSelect="true"
            :sourceIds="sourceOutlineIdsMap.outline"
            :hintColor="hintColor"
            :isEdit="true" />
        </div>
        <div v-if="currentPreSelectData?.outline?.data?.[curriculumCode]?.custom?.length">
          <div class="q-my-sm q-ml-sm">
            <span class="text-subtitle1 text-grey">Custom</span>
          </div>
          <OutlineChild
            v-if="currentPreSelectData?.outline?.data?.[curriculumCode]?.custom?.length"
            :data="currentPreSelectData.outline.data[curriculumCode].custom"
            :form="currentPreSelectData.outline"
            :_id="currentPreSelectData._id"
            :isCustom="true"
            type="outline"
            :code="curriculumCode"
            @unSelect="deleteToolData"
            @remove="emit('rmFn')"
            :disable="false"
            :isAssessmentToolSelect="true"
            :sourceIds="sourceOutlineIdsMap.outline"
            :hintColor="hintColor"
            :isEdit="true" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import {computed} from 'vue'
import useAssessmentToolEditing from 'src/composables/account/assessment-tool/useAssessmentToolEditing'
import OutlineChild from 'components/OutlineChild.vue'

const {currentPreSelectData, getNamesByCode, sourceOutlineIdsMap} = useAssessmentToolEditing()

const emit = defineEmits(['rmFn', 'deleteToolData'])

const isShow = computed(() => {
  return (
    currentPreSelectData.value?.outline?.curriculumCodes?.length &&
    currentPreSelectData.value?.outline?.data &&
    !Object.values(currentPreSelectData.value.outline.data).every((e) => !e?.child?.length && !e?.custom?.length)
  )
})

const hintColor = computed(() => {
  // no need hint by now
  return ''
  // const mode = sourceUnit.value?.mode || ''
  // if (['pdTask, task'].includes(mode)) return '#F4B302'
  // else if (['pdUnit', 'unit'].includes(mode)) return '#26A69A'
  // return ''
})
</script>
