<template>
  <!-- <div v-if="Object.entries(preSelectedTagIdObject).some(([key, value]) => value?.[key]?.length)"> -->
  <div v-if="isShow">
    <div class="q-my-sm">
      <span class="text-subtitle1">Learning goals</span>
    </div>

    <div class="border-grey-2 rounded-lg">
      <div v-for="item in preSelectedTags" :key="item._id">
        <div v-if="!item.layer && item?.child?.length">
          <div class="q-my-sm q-ml-sm">
            <span class="text-subtitle1">{{ item.set }}</span>
          </div>
          <div v-for="childItem in item.child" :key="childItem._id">
            <div class="q-ml-md q-my-sm flex justify-between items-center no-wrap cursor-pointer q-mr-sm" @click.stop="emit('onSelectTag', item, childItem)">
              <div class="flex justify-start items-center no-wrap full-width">
                <div class="q-mr-md">
                  <q-icon v-if="selectedTagIdObject?.[item._id]?.includes(childItem._id)" name="check_box" size="sm" color="teal-5" />
                  <q-icon v-else name="check_box_outline_blank" size="sm" color="grey-8" />
                </div>
                <div class="flex justify-between full-width border-dashed">
                  <div class="q-ml-sm q-ml-sm" :style="[sourceUnitIds?.includes(childItem._id) && hintColor ? `border-left: 5px solid ${hintColor}` : '']">
                    {{ childItem.name }}
                  </div>
                  <q-btn
                    size="sm"
                    :disable="false"
                    icon="delete_outline"
                    rounded
                    dense
                    flat
                    color="red-3"
                    no-caps
                    @click.stop="emit('onRemoveTagClick', item, childItem)"></q-btn>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-if="item.layer && item?.child?.length && item?.child.some((e) => e?.child?.length)">
          <div class="q-my-sm q-ml-sm">
            <span class="text-subtitle1">{{ item.set }}</span>
          </div>
          <div v-for="firstLayer in item.child" :key="firstLayer._id">
            <div class="q-ml-md" v-if="firstLayer.child && firstLayer.child.length">
              <div class="last-second-layer">{{ firstLayer.name }}</div>
              <div v-for="secondLayer in firstLayer.child" :key="secondLayer._id">
                <div
                  class="q-ml-md q-my-sm flex justify-between items-center no-wrap cursor-pointer q-mr-sm"
                  @click.stop="emit('onSelectTag', item, firstLayer, secondLayer)">
                  <div class="flex justify-start items-center no-wrap full-width">
                    <div class="q-mr-md">
                      <q-icon
                        v-if="selectedTagIdObject?.[`${item._id}:${firstLayer._id}`]?.includes(secondLayer._id)"
                        name="check_box"
                        size="sm"
                        color="teal-5" />
                      <q-icon v-else name="check_box_outline_blank" size="sm" color="grey-8" />
                    </div>
                    <div class="flex justify-between no-wrap full-width border-dashed">
                      <div class="q-ml-sm q-ml-sm">{{ secondLayer.name }}</div>
                      <q-btn
                        size="sm"
                        :disable="false"
                        icon="delete_outline"
                        rounded
                        dense
                        flat
                        color="red-3"
                        no-caps
                        @click.stop="emit('onRemoveTagClick', item, firstLayer, secondLayer)"></q-btn>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import {computed} from 'vue'
import useAssessmentToolEditing from 'src/composables/account/assessment-tool/useAssessmentToolEditing'

const {preSelectedTags, selectedTagIdObject, sourceUnitIds} = useAssessmentToolEditing()

const emit = defineEmits(['onSelectTag', 'onRemoveTagClick'])

const isShow = computed(() => {
  let hasValidChild = false
  preSelectedTags.value.forEach((e) => {
    if (!e?.layer) {
      if (e.child?.length) hasValidChild = true
    } else {
      if (e.child?.length) {
        e.child.forEach((_e) => {
          if (_e.child?.length) hasValidChild = true
        })
      }
    }
  })
  return hasValidChild
})

const hintColor = computed(() => {
  // no need hint by now
  return ''
  // const mode = sourceUnit.value?.mode || ''
  // if (['pdTask, task'].includes(mode)) return '#F4B302'
  // else if (['pdUnit', 'unit'].includes(mode)) return '#26A69A'
  // return ''
})
</script>
