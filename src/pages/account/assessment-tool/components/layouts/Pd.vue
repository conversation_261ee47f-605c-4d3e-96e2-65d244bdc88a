<template>
  <div v-if="isShow">
    <div class="q-my-sm">
      <span class="text-subtitle1">Pd</span>
    </div>
    <div style="border: 1px solid #aaa; border-radius: 0.5rem">
      <div v-for="curriculumCode in currentPreSelectData.pd.curriculumCodes" :key="curriculumCode">
        <div v-if="currentPreSelectData?.pd?.data?.[curriculumCode]?.child?.length">
          <div class="q-my-sm q-ml-sm">
            <span class="text-subtitle1">{{ getNamesByCode(curriculumCode)?.subject || currentPreSelectData?.pd?.data?.[curriculumCode]?.name || '' }}</span>
            <span v-if="getNamesByCode(curriculumCode)?.curriculum" class="text-grey-7"> ({{ getNamesByCode(curriculumCode).curriculum }})</span>
          </div>
          <OutlineChild
            v-if="currentPreSelectData?.pd?.data?.[curriculumCode]?.child?.length"
            :data="currentPreSelectData.pd.data[curriculumCode].child"
            :form="currentPreSelectData.pd"
            :_id="currentPreSelectData._id"
            type="pd"
            :code="curriculumCode"
            @remove="rmFn"
            :disable="false"
            :isAssessmentToolSelect="true"
            :sourceIds="sourceOutlineIdsMap.pd"
            :hintColor="hintColor"
            :isEdit="true" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import {computed} from 'vue'
import useAssessmentToolEditing from 'src/composables/account/assessment-tool/useAssessmentToolEditing'
import OutlineChild from 'components/OutlineChild.vue'

const {currentPreSelectData, getNamesByCode, sourceOutlineIdsMap} = useAssessmentToolEditing()

const emit = defineEmits(['rmFn', 'deleteToolData'])

const isShow = computed(() => {
  return (
    currentPreSelectData.value?.pd?.curriculumCodes?.length &&
    currentPreSelectData.value?.pd?.data &&
    !Object.values(currentPreSelectData.pd.data).every((e) => e.child.length === 0)
  )
})

const hintColor = computed(() => {
  // no need hint by now
  return ''
  // const mode = sourceUnit.value?.mode || ''
  // if (['pdTask, task'].includes(mode)) return '#F4B302'
  // else if (['pdUnit', 'unit'].includes(mode)) return '#26A69A'
  // return ''
})
</script>
