<template>
  <div>
    <q-btn-dropdown flat rounded icon="add" dense no-caps color="white" label="Import" class="col-4 border-grey-3 bg-teal-4 q-pr-sm">
      <q-list>
        <q-item v-for="item in items" :key="item.value" clickable v-close-popup @click="onImport(item.value)">
          <q-item-section>
            <q-item-label class="flex justify-start items-center">
              <q-icon :name="item.icon" size="sm" class="q-mr-sm" />
              <span>{{ item.label }}</span>
            </q-item-label>
          </q-item-section>
        </q-item>

        <q-separator />

        <q-item clickable v-close-popup @click="onImport(tagItem.value)">
          <q-item-section>
            <q-item-label class="flex justify-start items-center">
              <q-icon :name="tagItem.icon" size="sm" class="q-mr-sm" />
              <span>{{ tagItem.label }}</span>
            </q-item-label>
          </q-item-section>
        </q-item>
      </q-list>
    </q-btn-dropdown>

    <!-- dialogs -->

    <!-- subjects: standard/topic -->
    <ResourceSelectDialog
      v-model="isSubjectDialogOpen"
      key="subject-standard"
      :curriculumList="curriculumList"
      :editType="editType"
      @batchRemove="batchDeleteToolData"
      @initTaskOutline="emit('initTaskOutline')" />

    <!-- skills -->
    <ResourceSelectDialog
      v-model="isSkillsCurriculumDialogOpen"
      key="skill-standard"
      :curriculumList="curriculumList"
      :editType="editType"
      @batchRemove="batchDeleteToolData"
      @initTaskOutline="emit('initTaskOutline')" />

    <!-- pd -->
    <ResourceSelectDialog
      v-model="isPdCurriculumDialogOpen"
      key="pd"
      :curriculumList="curriculumList"
      :editType="editType"
      @initTaskOutline="emit('initTaskOutline')" />

    <!-- leaning goal -->
    <LearningGoalSelectDialog
      v-model="isLearningGoalDialogOpen"
      @initData="emit('initData')"
      @removeTag="emit('removeTag')"
      @batchRemove="batchDeleteToolData" />
  </div>
</template>

<script setup>
import {ref} from 'vue'
import useAssessmentToolEditing from 'src/composables/account/assessment-tool/useAssessmentToolEditing'
import ResourceSelectDialog from 'src/pages/account/assessment-tool/components/dialogs/ResourceSelectDialog.vue'
import LearningGoalSelectDialog from 'src/pages/account/assessment-tool/components/dialogs/LearningGoalSelectDialog.vue'

const {data, calcToolCount, curriculumList, loadingCount, editType, currentCurriculum, currentImportTypeMap, currentImportType} = useAssessmentToolEditing()
import {curriculumStore} from 'stores/curriculum'

const emit = defineEmits(['initTaskOutline', 'initData', 'removeTag'])

const items = [
  //  editType: assess
  {label: 'Standards', value: 'standard', icon: 'o_play_lesson'},
  // editType: outline
  {label: 'Topic', value: 'topic', icon: 'o_play_lesson'},
  // editType: skills
  {label: 'Skills', value: 'skills', icon: 'o_play_lesson'},
  // {label: 'Pd', value: 'pd'},
]

const tagItem = {label: 'Learning Goals', value: 'tags', icon: 'o_label'}

const curriculum = curriculumStore()

// dialogs
const isSubjectDialogOpen = ref(false)
const isSkillsCurriculumDialogOpen = ref(false)
const isPdCurriculumDialogOpen = ref(false)
const isLearningGoalDialogOpen = ref(false)

function onImport(typeString = '') {
  currentImportType.value = typeString
  if (typeString === currentImportTypeMap.skills) {
    editType.value = 'skills'
    isSkillsCurriculumDialogOpen.value = true
    return
  }
  if (typeString === currentImportTypeMap.topic) editType.value = 'outline'
  if (typeString === currentImportTypeMap.standard) editType.value = 'assess'
  if (typeString === currentImportTypeMap.pd) {
    editType.value = 'pd'
    isPdCurriculumDialogOpen.value = true
    return
  }
  if (typeString === currentImportTypeMap.tags) {
    isLearningGoalDialogOpen.value = true
    return
  }
  currentCurriculum.value.subject = null
  isSubjectDialogOpen.value = true
}

async function deleteToolData(id) {
  try {
    loadingCount.value++
    const {_id, toolData, toolGroup} = data.value
    const target = toolData.find((e) => e._id === id)
    if (!target) return
    const toolDataIndex = toolData.findIndex((e) => e._id === target._id)
    toolData.splice(toolDataIndex, 1)
    await App.service('unit').patch(_id, {$pull: {toolData: {_id: target._id}}})
    let res = null
    const groupLength = toolData.filter((e) => e.group === target.group).length
    if (groupLength > 0) {
      const target1 = toolGroup.find((e) => e._id === target.group)
      const index = target1.index.filter((e) => e !== target._id)
      res = await App.service('unit').patch(_id, {'toolGroup.$.index': index}, {query: {'toolGroup._id': target.group}})
    } else {
      const toolGroupIndex = toolGroup.findIndex((e) => e._id === target.group)
      toolGroup.splice(toolGroupIndex, 1)
      const toolCount = calcToolCount({toolGroup})
      res = await App.service('unit').patch(_id, {toolCount, $pull: {toolGroup: {_id: target.group}}})
    }
    // clear topic and standard ids
    curriculum.assessmentToolSelects = toolData.map((e) => e._id)
    // await initData(res)
    emit('initData', res)
  } catch (error) {
    console.error(error)
  } finally {
    loadingCount.value--
  }
}

async function batchDeleteToolData(ids = []) {
  if (!ids?.length) return
  for (const id of ids) {
    await deleteToolData(id)
  }
}
</script>
