<template>
  <div :class="localDialogClassName" class="justify-between" style="max-width: 50rem">
    <TemplateDownloadInstruction :layers="data" />
    <div class="full-width flex justify-end items-center q-gutter-md">
      <q-btn
        class="border-grey bg-teal-4"
        flat
        rounded
        no-caps
        label="Download"
        color="white"
        icon="done"
        :disable="uploadFile && !errors.every((e) => e?.length === 0)"
        @click="onDownloadTemplate()" />
    </div>
  </div>
</template>
<script setup>
import TemplateDownloadInstruction from 'src/pages/account/academic-setting/components/TemplateDownloadInstruction.vue'
import useAcademicSettingExcel from 'src/composables/account/academic/useAcademicSettingExcel'
const {exportTemplate} = useAcademicSettingExcel()
const props = defineProps(['data'])
async function onDownloadTemplate() {
  await exportTemplate(props.data)
}
</script>
