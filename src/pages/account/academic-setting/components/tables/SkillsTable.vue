<template>
  <div>
    <section class="q-mb-sm q-ml-xs">
      <div class="text-grey-7">{{ filteredList.filter((e) => e[currentMode.level]?.length).length }}/{{ filteredList.length }} configured</div>
    </section>

    <q-table :rows="filteredList" :columns="currentColumns" :rows-per-page-options="[0]" row-key="_id" @row-click.stop="(event, row) => onRowClick(row)">
      <template v-slot:header="props">
        <q-tr :props="props">
          <q-th v-for="col in props.cols" :key="col.name" :props="props" class="bg-teal-3">
            {{ col.label }}
          </q-th>
        </q-tr>
      </template>

      <template v-slot:body-cell-skill="props">
        <q-td :props="props" :class="[props.row.del ? archiveClassName : '']">
          <div class="text-subtitle2">{{ props.row.name }}</div>
          <div v-if="isSys && props.row.subtitle" class="text-caption text-grey-8">{{ props.row.subtitle }}</div>
        </q-td>
      </template>

      <template v-slot:body-cell-curriculum="props">
        <q-td :props="props" :class="[props.row.del ? archiveClassName : '']">
          <TextList
            v-if="props.row?.curriculum?.filter((e) => userCurriculumCodeOrIdList.includes(e))?.length"
            :texts="props.row.curriculum.filter((e) => userCurriculumCodeOrIdList.includes(e)).map((e) => findCurriculumByCode(e)?.name || 'No Curriculum')" />
          <div v-else>No curriculum</div>
        </q-td>
      </template>

      <template v-slot:body-cell-grade="props">
        <q-td :props="props" :class="[props.row.del ? archiveClassName : '']">
          <div v-if="props.row.grade?.length <= 1">
            <span>{{ getGradeById(props.row.grade[0])?.label || props.row.grade[0] || 'No grade' }}</span>
          </div>
          <div v-else>
            <span v-for="gradeId in props.row.grade.slice(0, 1)" :key="gradeId">{{ getGradeById(gradeId)?.label || props.row.grade[0] || 'No grade' }}</span>
            <span>
              <span class="text-teal q-ml-sm">+ {{ props.row.grade.length - 1 }} more</span>
              <q-tooltip class="text-subtitle1">
                <div v-for="gradeId in props.row.grade.slice(1, props.row.grade.length)" :key="gradeId">
                  {{ getGradeById(gradeId)?.label || gradeId }}
                </div>
              </q-tooltip>
            </span>
          </div>
        </q-td>
      </template>

      <template v-slot:body-cell-configurationStats="props">
        <q-td :props="props" :class="[props.row.del ? archiveClassName : '']">
          <div v-if="props.row?.[currentMode.level]?.length" :class="[props.row.del ? archiveClassName : 'text-grey-8']">configured</div>
          <div v-else :class="[props.row.del ? archiveClassName : 'text-teal-7']">Not configured yet</div>
        </q-td>
      </template>

      <template v-slot:body-cell-subjectStandards="props">
        <q-td :props="props" :class="[props.row.del ? archiveClassName : '']">
          <div v-if="!isSys && props.row?.source?.[currentMode.sourceSet]?.[0]" class="flex flex-center">
            <span>{{ props.row.source[currentMode.sourceSet][0] }}</span>
            <CopyrightIcon
              v-if="props.row?.source?.[currentMode.sourceCurriculum]?.[0]"
              :curriculum="props.row.source?.[currentMode.sourceCurriculum]?.[0]"
              :subtitle="props.row.source?.[currentMode.sourceSet][0].includes(' - ') ? props.row.source?.[currentMode.sourceSet][0].split(' - ')[0] : ''"
              size="1.2rem"
              class="q-ml-sm" />
          </div>
          <div v-else-if="!isSys && props.row?.[currentMode.level]?.length">Custom</div>
          <div v-if="props.row?.count?.[currentMode.mode]?.reduce((a, b) => a + b, 0)" class="text-grey-7">
            <span v-for="(key, index) in props.row[currentMode.level]" :key="key">
              <span class="q-mr-xs">{{ props.row.count[currentMode.mode][index] }}</span>
              <span>{{ key }}</span>
              <span v-if="index !== props.row[currentMode.level]?.length - 1" class="q-mr-xs">,</span>
            </span>
          </div>
          <div v-else-if="props.row?.[currentMode.level]?.length" class="text-grey-7">
            <span v-for="(key, index) in props.row[currentMode.level]" :key="key">
              <span class="q-mr-xs">0</span>
              <span>{{ key }}</span>
              <span v-if="index !== props.row[currentMode.level]?.length - 1" class="q-mr-xs">,</span>
            </span>
          </div>
          <div v-else class="text-grey-7">-</div>
        </q-td>
      </template>

      <template v-slot:body-cell-action="props">
        <!-- <q-td :props="props" :class="[props.row.del ? archiveClassName : '']">
          <q-icon name="chevron_right" color="grey-7" />
        </q-td> -->
        <q-td :props="props" :class="[props.row.del ? archiveClassName : '']">
          <div v-if="isSys ? !currentActionAuthMap?.edit_service_skill : false">-</div>
          <q-btn-dropdown v-else flat rounded dense no-caps color="white" class="col-4 border-grey-3 bg-grey-5 q-pr-sm" @click.stop="">
            <q-list>
              <q-item
                v-for="button in currentStatus.value === statusType.archived.value || props.row.del
                  ? archivedDropdownButtons
                  : [...dropdownButtons.slice(0, 2)]"
                :key="button.label"
                clickable
                v-close-popup
                @click.stop="onDropdownButtonClick({type: button.label, data: props.row})">
                <q-item-section>
                  <q-item-label class="flex justify-start items-center">
                    <span>{{ button.label }}</span>
                  </q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
          </q-btn-dropdown>
        </q-td>
      </template>

      <template v-slot:no-data>
        <NoTableData :isNoData="isNoData" />
      </template>
    </q-table>
  </div>
</template>

<script setup>
import {ref, watchEffect, computed, onMounted} from 'vue'
import {useRouter, useRoute} from 'vue-router'
import {skillsStore} from 'src/stores/skills'
import CopyrightIcon from 'src/components/utils/CopyrightIcon.vue'
import useSchool from 'src/composables/common/useSchool'
import useAcademicSetting from 'src/composables/account/academic/useAcademicSetting'
import useGrade from 'src/composables/account/academic/useGrade'
import useRawSubjectEditing from 'src/composables/account/academic/useRawSubjectEditing'
import useSkill from 'src/composables/account/academic/useSkill'

import NoTableData from './NoTableData.vue'
import TextList from 'src/components/utils/TextList.vue'
import {statusType, statusTypeList, archiveClassName, subjectMenuMap} from 'src/pages/account/academic-setting/utils'

const {isSys, currentUid, currentActionAuthMap} = useSchool()
const {currentCurriculum, sysCurriculumList, userCurriculumList, currentMode, currentStatusValue, findCurriculumByCode} = useAcademicSetting()
const {list: gradeList, getOneById: getGradeById} = useGrade()
const {getOneById, patchOneById, deleteOneById} = useSkill()
const {isDialogShowMap, currentEditSubject} = useRawSubjectEditing()

const props = defineProps({
  title: {
    type: String,
  },
  list: {
    type: Array,
    default: () => [],
  },
  currentSubjectTab: {
    type: String,
  },
  currentStatus: {
    type: Object,
    default: statusTypeList[0],
  },
})

const $router = useRouter()
const $route = useRoute()
const skills = skillsStore()

const publishedSkillList = ref([])
onMounted(async () => {
  const res = await skills.find(currentUid.value, true)
  publishedSkillList.value = res?.data || []
})

const columns = computed(() => [
  {
    name: 'skill',
    required: true,
    label: '21st century skills',
    align: 'left',
    field: (row) => row.name,
    format: (val) => `${val}`,
    sortable: false,
  },
  {name: 'curriculum', align: 'center', label: subjectMenuMap.curriculumSetting.label, field: 'curriculum'},
  {name: 'subjectStandards', label: `${props.title} standards`, field: 'subjectStandards', align: 'center'},
  {name: 'grade', label: 'Grade', field: 'grade', align: 'center'},
  {
    name: 'configurationStats',
    label: 'Configuration status',
    align: 'center',
    // field: 'status',
    field: (row) => row?.[currentMode.value.level]?.length || 0,
    sortable: true,
    sort: (a, b) => !!a - !!b,
  },
  {name: 'action', align: 'center', label: 'Action', field: 'action'},
])

const currentColumns = computed(() => {
  if (isSys.value) return [columns.value[0], columns.value[2], columns.value[3], ...columns.value.slice(-1)]
  else return columns.value
})

const dropdownButtonsType = {
  edit: 'Edit',
  archive: 'Archive skill',
  restore: 'Restore skill',
  delete: 'Delete skill',
}

const dropdownButtons = [
  {label: dropdownButtonsType.edit},
  {label: dropdownButtonsType.archive},
  {label: dropdownButtonsType.restore},
  {label: dropdownButtonsType.delete},
]

const archivedDropdownButtons = [{label: dropdownButtonsType.restore}, {label: dropdownButtonsType.delete}]

async function onDropdownButtonClick({type, data}) {
  const subjectName = data?.name || 'skill'
  let res = null
  switch (type) {
    case dropdownButtonsType.edit:
      isDialogShowMap.value.addOrEditSubject = true
      res = await getOneById(data._id, false)
      if (isSys.value) {
        currentEditSubject.value = {
          ...res,
          // grade: res.grade.map((e) => gradeList.value.find((_) => _.value === e)),
          curriculum: res.curriculum.map((e) => sysCurriculumList.value.find((_) => _.value === e)),
        }
      } else {
        currentEditSubject.value = {
          ...res,
          grade: res.grade.map((e) => gradeList.value.find((_) => _.value === e)),
          curriculum: res.curriculum
            .map((codeOrId) => userCurriculumList.value.find((_) => _.code === codeOrId || _._id === codeOrId))
            .filter((e) => !!e)
            .map((e) => ({value: e.code || e._id, label: e.name})),
        }
      }
      break
    case dropdownButtonsType.archive:
      $q.dialog({
        title: `Archive '${subjectName}'`,
        message: `You are archiving this subject, titled '${subjectName}'. You can restore this subject at any time.`,
        cancel: true,
      }).onOk(async () => {
        await patchOneById(data._id, {del: true})
        currentStatusValue.value = 'archived'
      })
      break
    case dropdownButtonsType.restore:
      $q.dialog({
        title: `Restore '${subjectName}'`,
        message: `You are restoring this subject, titled '${subjectName}'. Would you like to continued?`,
        cancel: true,
      }).onOk(async () => {
        await patchOneById(data._id, {del: false})
        currentStatusValue.value = 'active'
      })
      break
    case dropdownButtonsType.delete:
      $q.dialog({
        title: `Delete '${subjectName}'`,
        message: `You are deleting this subject, titled '${subjectName}'. Would you like to continued?`,
        cancel: true,
      }).onOk(async () => {
        await deleteOneById(data._id)
      })
      break
    default:
      throw new TypeError(`no dropdown button type: ${type}`)
  }
}

async function onRowClick(row) {
  if (row?.del) return
  const {level} = currentMode.value
  try {
    const res = await getOneById(row._id)
    const query = {...$route.query, back: `/${isSys.value ? 'sys' : 'account'}/academic-setting/skill/${$route.params.tab}`}
    // if (res?.snapshot?.[mode]?.length) {
    //   const path = `/${isSys.value ? 'sys' : 'account'}/academic-setting/skill/${$route.params.tab}/${res._id}`
    //   await $router.push({path, query})
    // } else if (res?.[level]?.length) {
    if (res?.[level]?.length) {
      const path = `/${isSys.value ? 'sys' : 'account'}/academic-setting/skill/${$route.params.tab}/${res._id}`
      await $router.push({path, query})
    } else {
      if (isSys.value ? !currentActionAuthMap.value?.edit_subject_standard : false) return
      const url = `/${isSys.value ? 'sys' : 'account'}/academic-setting/skill/${$route.params.tab}/${res._id}/select`
      $router.push({path: url, query})
    }
  } catch (error) {
    console.log(error)
  }
}

const gradeListIds = computed(() => gradeList.value?.map((e) => e.value) || [])
const filteredList = ref([])
const isNoData = ref(false)
watchEffect(() => {
  if (!isSys.value) filteredList.value = props.list || []
  else filteredList.value = props.list.filter((e) => e.curriculum.includes(currentCurriculum.value?.value)) || []
  if (!filteredList.value.length) isNoData.value = true
  if (filteredList.value.length) {
    switch (props.currentStatus?.value) {
      case statusType.archived.value:
        filteredList.value = filteredList.value.filter((e) => e.del)
        break
      case statusType.active.value:
        filteredList.value = filteredList.value.filter((e) => !e.del)
        break
      case statusType.all.value:
        // filteredList.value = filteredList.value
        break
      default:
        break
    }
  }
  filteredList.value = filteredList.value.map((e) => {
    let grade = []
    if (isSys.value) {
      grade = e.grade
    } else {
      grade = e.grade.filter((e) => gradeListIds.value.includes(e))
    }
    const target = publishedSkillList.value.find((_) => _._id === e._id)
    if (target) return {...e, grade, count: target?.count || {}}
    return {...e, grade}
  })
  filteredList.value = filteredList.value.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
})

const userCurriculumCodeOrIdList = computed(() => {
  const list = []
  userCurriculumList.value.forEach((e) => {
    if (e?.code) list.push(e.code)
    else list.push(e._id)
  })
  return list
})
</script>

<style scoped lang="scss">
.border-grey {
  border: 1px solid #aaa;
}
.page-dialog {
  width: clamp(60vw, 60%, 70vw);
  max-width: 70vw;
  height: clamp(50vh, auto, 75vh);
  overflow-y: auto;
  max-height: 75vh;
}
.level-1 {
  width: 7rem;
  height: 1px;
  background: #aaa;
  margin-top: 0.5rem;
  position: relative;
}
.level-1-top {
  position: absolute;
  top: -5px;
  left: 50%;
  transform: translate(-50%, -50%);
  height: 9px;
  width: 1px;
  background: #aaa;
}
.level-1-left {
  position: absolute;
  top: 5px;
  left: 0;
  transform: translate(-50%, -50%);
  height: 10px;
  width: 1px;
  background: #aaa;
}
.level-1-right {
  position: absolute;
  top: 5px;
  left: 100%;
  transform: translate(-50%, -50%);
  height: 10px;
  width: 1px;
  background: #aaa;
}

.level-2 {
  width: 7rem;
  height: 1px;
  background: #aaa;
  margin-top: 0.5rem;
  position: relative;
  left: 25%;
}
.level-2-left {
  position: absolute;
  top: 0px;
  left: 0;
  transform: translate(-50%, -50%);
  height: 20px;
  width: 1px;
  background: #aaa;
}
.level-2-right {
  position: absolute;
  top: 5px;
  left: 100%;
  transform: translate(-50%, -50%);
  height: 10px;
  width: 1px;
  background: #aaa;
}
</style>
