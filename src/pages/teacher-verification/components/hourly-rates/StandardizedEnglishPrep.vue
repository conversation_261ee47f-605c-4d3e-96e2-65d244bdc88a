<template>
  <div class="flex justify-between items-center">
    <div>Selected the tags for verification</div>
    <q-btn rounded flat no-caps dense icon="edit" class="text-grey-8" @click="isDialogShowMap.standardizedEnglishPrep = true" />
  </div>
  <div v-if="config?.topic?.length" class="flex q-gutter-sm">
    <div v-for="item in config.topic" :key="item._id">
      <div class="item-wrapper">
        <q-chip flat square no-caps :label="item?.label[item?.label?.length - 1] ?? ''" class="q-px-sm bg-green text-white" style="border: 1px solid" />
        <q-btn
          rounded
          flat
          no-caps
          icon="close"
          size="sm"
          dense
          class="item-delete-button text-grey-1 bg-red-4"
          @click="onDeleteStandardizedEnglishPrep(item)" />
      </div>
    </div>
  </div>
  <q-card v-if="config?.topic?.length" flat :class="hourRateCardClass" class="q-mt-lg">
    <div class="text-h6">Hourly rate income of service provider</div>
    <div v-for="item in computedQualificationOptions" :key="item.value" class="q-my-md">
      <div class="text-subtitle2">{{ item.label }}</div>
      <div class="flex items-center q-gutter-sm q-pl-md">
        <div>
          Standard hourly rate: USD
          {{ config?.hourRate?.find((e) => e?.default === true && e?.qualification === item.value)?.price ?? '0' }}
        </div>
        <q-btn flat rounded dense icon="edit" @click="() => onEdithHourRateClick({default: true, qualification: item.value})" />
        <q-btn flat rounded dense no-caps label="Add special rate" class="bg-teal-4 text-white q-px-md" @click="() => onCreateHourRate(item)" />
      </div>
      <div class="q-pl-md">
        <div
          v-for="(hourRateItem, index) in config?.hourRate?.filter((e) => e?.default === false && e?.qualification === item.value)"
          :key="`${item.qualification}-${index}`"
          class="flex items-center q-gutter-sm q-my-xs">
          <div class="item-wrapper q-pr-md q-mr-sm">
            <div>USD {{ hourRateItem?.price }}</div>
            <q-btn
              rounded
              flat
              no-caps
              icon="close"
              size="sm"
              dense
              class="item-delete-button text-grey-1 bg-red-4"
              @click="() => onDeleteRateHour({default: false, qualification: item.value, price: hourRateItem?.price})" />
          </div>
          <div>
            <div v-if="sysStandardizedEnglishPrepData['topic']?.length" class="flex items-center q-gutter-md">
              <div
                v-for="item in sysStandardizedEnglishPrepData['topic'].filter((e) =>
                  getCurrentHourRateByOption(config?.hourRate, {default: false, qualification: item.value, price: hourRateItem?.price})?.value?.includes(e._id)
                )"
                :key="item._id">
                <q-chip flat square no-caps dense :label="item.name" :class="hourRateChipClass" style="border: 1px solid" />
              </div>
            </div>
          </div>
          <q-btn
            flat
            rounded
            dense
            no-caps
            class="text-grey-7 text-teal q-px-md"
            style="border: 1px solid #aaa"
            label="Edit verification items"
            @click="() => onEditStandardizedEnglishPrepHourRateClick({default: false, qualification: item.value, price: hourRateItem?.price})" />
          <q-btn
            flat
            rounded
            dense
            no-caps
            class="text-grey-7 text-teal q-px-md"
            style="border: 1px solid #aaa"
            label="Edit grade group"
            @click="() => onEditStandardizedEnglishPrepHourRateClick({default: false, qualification: item.value, price: hourRateItem?.price}, 'gradeGroup')" />
        </div>
      </div>
    </div>
  </q-card>

  <q-dialog v-model="isDialogShowMap.standardizedEnglishPrep" persistent>
    <div class="column no-wrap bg-white editing-dialog">
      <div class="flex justify-between shadow-2 q-pa-sm">
        <div class="flex items-center">
          <q-btn flat rounded dense icon="close" @click="onCancelStandardizedEnglishPrep" />
          <div class="text-h6">Editing tags of {{ VerificationMap[currentVerification]?.label.toLowerCase() }} service</div>
        </div>
        <q-btn rounded flat dense no-caps label="Save" class="q-px-md bg-teal-4 text-white" @click="onSaveStandardizedEnglishPrep" />
      </div>
      <div v-if="sysStandardizedEnglishPrepData?.['topic']?.length" class="q-px-md q-pt-md q-pb-lg flex q-gutter-md">
        <div v-for="item in sysStandardizedEnglishPrepData['topic']" :key="item._id">
          <div class="flex q-gutter-sm">
            <q-btn
              flat
              no-caps
              dense
              :label="item.name"
              class="q-px-sm"
              :class="[
                currentConfig?.topic?.map((e) => e._id)?.includes(item._id) ? 'bg-green text-white' : 'bg-white text-green',
                config?.topic?.map((e) => e._id)?.includes(item._id) ? 'bg-grey-6' : '',
              ]"
              style="border: 1px solid"
              :disable="config?.topic?.map((e) => e._id)?.includes(item._id)"
              @click="() => onStandardizedEnglishPrepClick(item)" />
          </div>
        </div>
      </div>
      <div v-else class="q-ml-md text-grey-6">No topic</div>
    </div>
  </q-dialog>

  <q-dialog v-model="isDialogShowMap.standardizedEnglishPrepHourRate" persistent>
    <div class="column no-wrap bg-white editing-dialog">
      <div class="flex justify-between shadow-2 q-pa-sm">
        <div class="flex items-center">
          <q-btn flat rounded dense icon="close" @click="onCancelStandardizedEnglishPrepHourRate" />
          <div class="text-h6">Editing hour rate of {{ VerificationMap[currentVerification]?.label.toLowerCase() }} service</div>
        </div>
        <q-btn rounded flat dense no-caps label="Save" class="q-px-md bg-teal-4 text-white" @click="onSaveStandardizedEnglishPrepHourRate" />
      </div>
      <div v-if="sysStandardizedEnglishPrepData?.['topic']?.length" class="q-px-md q-pt-md q-pb-lg flex q-gutter-md">
        <div v-for="item in sysStandardizedEnglishPrepData['topic']" :key="item._id">
          <div class="flex q-gutter-sm">
            <q-btn
              flat
              no-caps
              dense
              :label="item.name"
              class="q-px-sm"
              :class="[
                otherHourRateStandardizedEnglishPrep.includes(item._id)
                  ? 'bg-grey-6'
                  : currentHourRateItem?.includes(item._id)
                    ? 'bg-green text-white'
                    : 'bg-white text-green',
              ]"
              style="border: 1px solid"
              :disable="otherHourRateStandardizedEnglishPrep.includes(item._id)"
              @click="() => onStandardizedEnglishPrepHourRateClick(item)" />
          </div>
        </div>
      </div>
      <div v-else class="q-ml-md text-grey-6">No data</div>
    </div>
  </q-dialog>

  <q-dialog v-model="isDialogShowMap.standardizedEnglishPrepGradeGroup" persistent>
    <div class="column no-wrap bg-white editing-dialog">
      <div class="flex justify-between shadow-2 q-pa-sm">
        <div class="flex items-center">
          <q-btn flat rounded dense icon="close" @click="onCancelStandardizedEnglishPrepHourRate" />
          <div class="text-h6">Editing grade group of {{ VerificationMap[currentVerification]?.label.toLowerCase() }} service</div>
        </div>
        <q-btn rounded flat dense no-caps label="Save" class="q-px-md bg-teal-4 text-white" @click="onSaveStandardizedEnglishPrepHourRate" />
      </div>
      <div v-if="sysStandardizedEnglishPrepGradeGroup?.length" class="q-px-md q-pt-md q-pb-lg">
        <div v-for="group in sysStandardizedEnglishPrepGradeGroup" :key="group">
          <div v-if="getGradesByGroup({group, curriculum: 'pd'})?.length" class="flex justify-start items-start">
            <q-checkbox dense v-model="selectedGradeGroups" :val="group" />
            <div class="q-ml-sm">
              <div>{{ GradeGroupMap[group]?.label }}</div>
              <div class="flex q-gutter-sm text-grey-8">
                <div v-for="grade in getGradesByGroup({group, curriculum: 'pd'})" :key="grade">{{ grade }}</div>
              </div>
            </div>
          </div>
          <div v-else class="q-ml-md text-grey-6">No subjects</div>
        </div>
      </div>
      <div v-else class="q-ml-md text-grey-6">No data</div>
    </div>
  </q-dialog>

  <q-dialog v-model="isDialogShowMap.hourRate" persistent>
    <div class="column no-wrap bg-white editing-dialog">
      <div class="flex justify-between shadow-2 q-pa-sm">
        <div class="flex items-center">
          <q-btn flat rounded dense icon="close" @click="isDialogShowMap.hourRate = false" />
          <div class="text-h6">{{ isSpecialHourRate ? 'Add special' : 'Set standard' }} hourly rate</div>
        </div>
        <q-btn rounded flat dense no-caps label="Save" class="q-px-md bg-teal-4 text-white" @click="() => onPatchHourRate()" />
      </div>
      <div class="q-pa-lg">
        <q-input type="number" v-model="hourRatePrice">
          <template v-slot:prepend>
            <q-icon name="attach_money" />
          </template>
        </q-input>
      </div>
    </div>
  </q-dialog>
</template>

<script setup>
import {ref, computed, watch} from 'vue'
import {useQuasar} from 'quasar'

import useSubject from 'src/composables/account/academic/useSubject'
import useTeacherVerificationConfig from 'src/composables/account/teacher-verification/useTeacherVerificationConfig'
import useTeacherVerification from 'src/composables/account/teacher-verification/useTeacherVerification'
import useGrade from 'src/composables/account/academic/useGrade'

import {
  hourRateCardClass,
  hourRateChipClass,
  confirmDeleteOption,
  defaultHourRatePrice,
  getCurrentHourRateByOption,
  sayncTopic,
} from 'src/pages/teacher-verification/components/hourly-rates/utils.js'

import {QUALIFICATION_OPTIONS} from 'src/boot/const'

import {VerificationMap} from 'src/pages/teacher-verification/utils'
import ConfirmDialog from 'src/components/utils/dialogs/ConfirmDialog.vue'

const props = defineProps({
  config: {
    type: Object,
    require: true,
  },
})
const emit = defineEmits(['refresh'])

const {patchTeacherVerificationConfig} = useTeacherVerificationConfig()
const {sysStandardizedEnglishPrepData, sysStandardizedEnglishPrepGradeGroup} = useSubject()
const {currentVerification} = useTeacherVerification()
const {getGradesByGroup} = useGrade()

const $q = useQuasar()

const isDialogShowMap = ref({
  standardizedEnglishPrep: false,
  hourRate: false,
  standardizedEnglishPrepHourRate: false,
  standardizedEnglishPrepGradeGroup: false,
})

const currentConfigKey = computed(() => `Service:${currentVerification.value}`)
const computedQualificationOptions = computed(() => {
  if (currentConfigKey.value === 'Service:mentoring:teacherTraining') return QUALIFICATION_OPTIONS.slice(0, 2)
  if (currentConfigKey.value === 'Service:mentoring:teacherTrainingSubject') return QUALIFICATION_OPTIONS.slice(0, 2)
  return QUALIFICATION_OPTIONS
})

const isSync = ref(false)
async function syncSysData() {
  const dto = Acan.clone(props.config)
  if (!dto || !dto?.topic?.length) return
  const topic = dto?.topic || []
  const {isNeedUpdate, newTopic} = sayncTopic({topic, topicData: sysStandardizedEnglishPrepData.value?.['topic'], code: 'standardizedEnglishPrep'})
  if (isNeedUpdate) {
    $q.loading.show()
    dto.topic = newTopic
    console.warn(newTopic)
    await patchTeacherVerificationConfig(currentConfigKey.value, dto)
    emit('refresh')
    $q.loading.hide()
  }
  isSync.value = true
}

const currentConfig = ref(null)
watch(
  () => props.config,
  async () => {
    if (props.config) {
      currentConfig.value = Acan.clone(props.config)
      if (!isSync.value) await syncSysData()
    }
  },
  {deep: true}
)

async function onDeleteStandardizedEnglishPrep(item) {
  const {title, message, okButtonLabel} = confirmDeleteOption
  $q.dialog({
    component: ConfirmDialog,
    componentProps: {title, message, okButtonLabel},
  })
    .onOk(async () => {
      const dto = props.config
      if (!dto) return
      dto.topic = dto.topic.filter((e) => e._id !== item._id)
      $q.loading.show()
      await patchTeacherVerificationConfig(currentConfigKey.value, dto)
      emit('refresh')
      $q.loading.hide()
    })
    .onCancel(() => {})
    .onDismiss(() => {})
}

const currentHourRateOption = ref({})
const hourRatePrice = ref(0)
const isSpecialHourRate = ref(false)
function onEdithHourRateClick(option) {
  currentHourRateOption.value = option
  const target = getCurrentHourRateByOption(props.config?.hourRate, option)
  if (target) {
    hourRatePrice.value = +target?.price ?? 0
  } else {
    hourRatePrice.value = 0
  }
  isSpecialHourRate.value = false
  isDialogShowMap.value.hourRate = true
}

function onCreateHourRate(item) {
  const dto = {default: false, qualification: item.value, gradeGroup: sysStandardizedEnglishPrepGradeGroup}
  currentHourRateOption.value = dto
  hourRatePrice.value = 0
  isSpecialHourRate.value = true
  isDialogShowMap.value.hourRate = true
}

async function onDeleteRateHour(option) {
  const target = getCurrentHourRateByOption(props.config?.hourRate, option)
  if (target) {
    currentConfig.value.hourRate = currentConfig.value.hourRate.filter((e) => {
      const entries = Object.entries(option)
      let isTarget = true
      entries.forEach(([key, value]) => {
        if (e[key] !== value) isTarget = false
      })
      if (isTarget) {
        return false
      } else {
        return true
      }
    })
  }
  await patchCurrentConfig()
  isDialogShowMap.value.hourRate = false
}

async function patchCurrentConfig() {
  $q.loading.show()
  const now = new Date().toISOString()
  const {newTopic} = sayncTopic({
    topic: currentConfig.value?.topic,
    topicData: sysStandardizedEnglishPrepData.value?.['topic'],
    code: 'standardizedEnglishPrep',
    isFilterOldTopic: true,
  })
  const dto = {...currentConfig.value, topic: newTopic, updatedAt: now}
  await patchTeacherVerificationConfig(currentConfigKey.value, dto)
  emit('refresh')
  $q.loading.hide()
}

async function onPatchHourRate() {
  const option = currentHourRateOption.value
  let price = +hourRatePrice.value ?? 0
  if (price <= 0) {
    $q.notify({type: 'negative', message: 'Price can not be zero or negative'})
    return
  }
  if (isSpecialHourRate.value) {
    const target = getCurrentHourRateByOption(props.config?.hourRate, {...option, price})
    if (target) {
      isDialogShowMap.value.hourRate = false
      $q.notify({type: 'negative', message: 'This price already exists'})
      return
    }
    const dto = {...option, price}
    currentConfig.value.hourRate.push(dto)
  } else {
    const target = getCurrentHourRateByOption(props.config?.hourRate, {...option})
    if (target) {
      currentConfig.value.hourRate = currentConfig.value.hourRate.map((e) => {
        const entries = Object.entries(option)
        let isTarget = true
        entries.forEach(([key, value]) => {
          if (e[key] !== value) isTarget = false
        })
        if (isTarget) {
          const target = getCurrentHourRateByOption(props.config?.hourRate, option)
          return {...target, price}
        } else {
          return e
        }
      })
    } else {
      const dto = {...option, price}
      currentConfig.value.hourRate.push(dto)
    }
  }
  currentConfig.value.hourRate.sort((a, b) => +a.price - +b.price)
  await patchCurrentConfig()
  isDialogShowMap.value.hourRate = false
}

// === standardizedEnglishPrep hour rate ===
const currentHourRateItem = ref([])
const otherHourRateStandardizedEnglishPrep = ref([])
const selectedGradeGroups = ref([])
function onEditStandardizedEnglishPrepHourRateClick(option, key = 'topic') {
  const other =
    props.config?.hourRate?.filter((e) => {
      return +e.price !== +option?.price && e.default === option?.default && e.qualification === option?.qualification && e.topic === option?.topic
    }) ?? []
  otherHourRateStandardizedEnglishPrep.value = []
  if (other?.length) {
    other.forEach((e) => {
      e?.value?.forEach((id) => {
        if (!otherHourRateStandardizedEnglishPrep.value.includes(id)) otherHourRateStandardizedEnglishPrep.value.push(id)
      })
    })
  }
  currentHourRateOption.value = option
  const target = getCurrentHourRateByOption(props.config?.hourRate, option)
  if (target) {
    currentHourRateItem.value = target?.value?.slice() ?? []
    selectedGradeGroups.value = target?.gradeGroup?.slice() ?? []
  } else {
    currentHourRateItem.value = []
  }
  if (key === 'topic') {
    isDialogShowMap.value.standardizedEnglishPrepHourRate = true
  } else if (key === 'gradeGroup') {
    isDialogShowMap.value.standardizedEnglishPrepGradeGroup = true
  }
}
function onCancelStandardizedEnglishPrepHourRate() {
  currentHourRateItem.value = []
  isDialogShowMap.value.standardizedEnglishPrepHourRate = false
  isDialogShowMap.value.standardizedEnglishPrepGradeGroup = false
}
function onStandardizedEnglishPrepHourRateClick(item) {
  const id = item._id
  if (currentHourRateItem.value.includes(id)) {
    currentHourRateItem.value = currentHourRateItem.value.filter((e) => e !== id)
  } else {
    currentHourRateItem.value.push(id)
  }
}
async function onSaveStandardizedEnglishPrepHourRate() {
  const option = currentHourRateOption.value
  const target = getCurrentHourRateByOption(props.config?.hourRate, option)
  if (target) {
    currentConfig.value.hourRate = currentConfig.value.hourRate.map((e) => {
      const entries = Object.entries(option)
      let isTarget = true
      entries.forEach(([key, value]) => {
        if (e[key] !== value) isTarget = false
      })
      if (isTarget) {
        const target = getCurrentHourRateByOption(props.config?.hourRate, option)
        return {...target, value: currentHourRateItem.value}
      } else {
        return e
      }
    })
  }
  await patchCurrentConfig()
  isDialogShowMap.value.standardizedEnglishPrepHourRate = false
  isDialogShowMap.value.standardizedEnglishPrepGradeGroup = false
}

function onStandardizedEnglishPrepClick(item) {
  const id = item._id
  const label = [item?.name || '']
  const dto = {_id: id, label}
  if (!currentConfig.value?.topic) currentConfig.value.topic = []
  let target = currentConfig.value?.topic
  if (!target.map((e) => e._id).includes(id)) target.push(dto)
  else target = target.filter((e) => e._id !== id)
  currentConfig.value.topic = target
}
async function onSaveStandardizedEnglishPrep() {
  computedQualificationOptions.value.forEach((option) => {
    const target = currentConfig.value.hourRate.find((e) => e.qualification === option.value && e.default === true)
    if (!target) {
      currentConfig.value.hourRate.push({
        qualification: option.value,
        default: true,
        price: defaultHourRatePrice,
      })
    }
  })
  await patchCurrentConfig()
  isDialogShowMap.value.standardizedEnglishPrep = false
}
function onCancelStandardizedEnglishPrep() {
  isDialogShowMap.value.standardizedEnglishPrep = false
  currentConfig.value = Acan.clone(props.config)
}
</script>

<style lang="scss" scoped>
.editing-dialog {
  max-width: 640px;
  width: 80%;
}
.item-wrapper {
  position: relative;
  &:hover {
    .item-delete-button {
      opacity: 1;
    }
  }
  .item-delete-button {
    position: absolute;
    top: -0.35rem;
    right: -0.65rem;
    opacity: 0;
    transition: opacity 0.2s ease-in-out;
    cursor: pointer;
  }
}
.border-grey-1 {
  border: 1px solid #aaa;
}
</style>
