<template>
  <q-layout view="hHh LpR fFf" class="teacher-auth-page my-intro-bg">
    <PubTopBanner title="School Ambassador Verification" :isShowMenu="false" showShare :shareUrl="'/v2/account/ambassador-verification'">
      <template v-if="$q.screen.lt.md" v-slot:left>
        <q-btn class="q-ml-sm" flat rounded dense icon="menu" @click="drawer = !drawer" />
      </template>
    </PubTopBanner>

    <q-drawer side="left" :width="260" show-if-above v-model="drawer" bordered>
      <Drawer :active="route.params.type"></Drawer>
    </q-drawer>

    <Verification v-if="route.params.type === 'verification'" />

    <!-- dialogs -->
  </q-layout>
</template>

<script setup>
import {ref, onMounted} from 'vue'
import {useRoute} from 'vue-router'
import {useStorage} from '@vueuse/core'

import {pubStore} from 'stores/pub'
import useSchool from 'src/composables/common/useSchool'
import useTeacherVerificationAuth from 'src/composables/account/teacher-verification/useTeacherVerificationAuth'
import Drawer from './components/Drawer.vue'
import Verification from './components/Verification.vue'

const route = useRoute()
const pub = pubStore()

const {userId} = useSchool()
const {getList, getUserServiceConfigById} = useTeacherVerificationAuth()

const drawer = ref(false)
const loading = ref(true)
const isShowAdd = useStorage('is-show-add', true)

const currentEnable = ref({})
const isNoMobileDialogShow = ref(false)
onMounted(async () => {
  loading.value = true
  await getList(true, {$skip: 0, $limit: 2000})
  const res = await getUserServiceConfigById(userId.value)
  currentEnable.value = res?.enable ?? {}
  loading.value = false
  setTimeout(() => {
    if (!pub?.user?.mobile) {
      isNoMobileDialogShow.value = true
      return
    }
    isShowAdd.value = true
  }, 500)
})
</script>

<style lang="sass" scope>
.teacher-auth-page
  .my-height
    height: 30rem
  .my-intro-height
    height: 132px
  .my-intro-bg
    background: #49BBBD17
</style>
