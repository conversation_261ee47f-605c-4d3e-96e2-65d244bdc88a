<template>
  <section>
    <div class="q-mb-lg">
      <div v-if="currentConfig?.updatedAt" class="flex justify-end full-width">
        <div class="text-grey-8 text-italic">last updated at: {{ date.formatDate(currentConfig.updatedAt, 'YYYY-MM-DD HH:mm:ss') }}</div>
      </div>

      <div class="flex justify-between items-center">
        <div>Verification material needed</div>
        <q-btn rounded flat no-caps dense icon="edit" class="text-grey-8" @click="onMaterialNeededClick" />
      </div>

      <!-- <div v-if="form?.[currentConfigKey]?.desc" class="text-subtitle1 text-teal-4" style="white-space: pre-line">{{ form[currentConfigKey].desc }}</div> -->
      <!-- <div v-else class="text-grey-6">No description</div> -->

      <div v-if="form?.[currentConfigKey]?.attachmentType?.length" class="q-pl-md">
        <div v-for="pair in Object.entries(getAttachmentType(form?.[currentConfigKey]?.attachmentType, currentConfigKey))" :key="pair[0]">
          <div class="flex items-center q-mt-md q-gutter-sm">
            <div class="text-bold">{{ pair[0] }}</div>
            <q-icon v-if="pair[1]?.desc" name="help_outline" size="1.25rem" color="primary">
              <q-tooltip>{{ pair[1].desc }}</q-tooltip>
            </q-icon>
          </div>
          <div class="q-pl-md q-gutter-md">
            <div v-for="item in pair[1].child" :key="item.label">
              <div class="flex items-center q-gutter-sm">
                <q-icon name="check" color="teal" />
                <div>{{ item.label }}</div>
                <q-icon v-if="item?.desc" name="help_outline" size="1.25rem" color="primary">
                  <q-tooltip>{{ item.desc }}</q-tooltip>
                </q-icon>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-else class="text-grey-6">No attachment</div>
    </div>

    <!-- dialogs -->
    <q-dialog v-model="isDialogShowMap.materialNeeded" persistent>
      <div class="column no-wrap bg-white editing-dialog">
        <div class="flex justify-between shadow-2 q-pa-sm">
          <div class="flex items-center">
            <q-btn flat rounded dense icon="close" @click="onCancelMaterialNeeded" />
            <div class="text-h6">Editing verification material</div>
          </div>
          <q-btn rounded flat dense no-caps label="Save" class="q-px-md bg-teal-4 text-white" @click="onSaveMaterialNeeded" />
        </div>
        <div class="q-px-md q-pt-md q-pb-lg">
          <!-- <div class="text-subtitle2 q-mb-md">Verification material needed</div> -->
          <!-- <q-input outlined v-model="currentConfig.desc" placeholder="material needed" autogrow class="q-my-md" /> -->
          <div>{{ currentConfigKey }}</div>
          <div v-for="group in currentMaterialOptions" :key="group.category">
            <div>{{ group.category }}</div>
            <div v-for="item in group.child" :key="item.label" class="q-pl-md">
              <q-checkbox v-model="currentConfig.attachmentType" :label="item.label" :val="item.label" @update:modelValue="() => onAttatchmentTypeChange()" />
            </div>
          </div>
        </div>
      </div>
    </q-dialog>
  </section>
</template>

<script setup>
import {ref, computed, onMounted, watch} from 'vue'
import {date} from 'quasar'

// import ConfirmDialog from 'src/components/utils/dialogs/ConfirmDialog.vue'
import useTeacherVerificationConfig from 'src/composables/account/teacher-verification/useTeacherVerificationConfig'
import {AMBASSADOR_VERIFICATION_MATERIAL_OPTIONS, VERIFICATION_MATERIAL_OPTIONS, getAttachmentType} from 'src/boot/const'

const {getTeacherVerificationConfig, patchTeacherVerificationConfig} = useTeacherVerificationConfig()

const form = ref({})
onMounted(async () => {
  form.value['ambassador'] = {
    desc: '',
    // curriculum: [],
    // topic: [],
    // hourRate: [],
    attachmentType: [],
  }
  await getCurrentConfig()
})

const currentConfigKey = ref('ambassador')
const currentConfig = ref(null)
async function getCurrentConfig() {
  const key = currentConfigKey.value
  $q.loading.show()
  const res = await getTeacherVerificationConfig(key)
  Object.assign(form.value[key], res)
  currentConfig.value = Acan.clone(form.value[key])
  $q.loading.hide()
}
watch(currentConfigKey, async () => {
  await getCurrentConfig()
})

const isDialogShowMap = ref({
  materialNeeded: false,
})

async function patchCurrentConfig() {
  $q.loading.show()
  const now = new Date().toISOString()
  const dto = {...currentConfig.value, updatedAt: now}
  await patchTeacherVerificationConfig(currentConfigKey.value, dto)
  await getCurrentConfig()
  $q.loading.hide()
}

const currentMaterialOptions = computed(() => {
  let options = []
  if (currentConfigKey.value === 'ambassador') {
    options = AMBASSADOR_VERIFICATION_MATERIAL_OPTIONS
  } else {
    options = VERIFICATION_MATERIAL_OPTIONS
  }
  return options
})

function onAttatchmentTypeChange() {
  const validList = []
  currentMaterialOptions.value.forEach((group) => {
    if (group?.child?.length) {
      group?.child?.forEach((item) => {
        validList.push(item.label)
      })
    }
  })
  currentConfig.value.attachmentType = currentConfig.value.attachmentType.filter((e) => validList.includes(e))
}
function onMaterialNeededClick() {
  isDialogShowMap.value.materialNeeded = true
}
async function onSaveMaterialNeeded() {
  await patchCurrentConfig()
  isDialogShowMap.value.materialNeeded = false
}
function onCancelMaterialNeeded() {
  isDialogShowMap.value.materialNeeded = false
  currentConfig.value = Acan.clone(form.value[currentConfigKey.value])
}
</script>

<style lang="scss" scoped>
.editing-dialog {
  max-width: 640px;
  width: 80%;
}
.item-wrapper {
  position: relative;
  &:hover {
    .item-delete-button {
      opacity: 1;
    }
  }
  .item-delete-button {
    position: absolute;
    top: -0.35rem;
    right: -0.65rem;
    opacity: 0;
    transition: opacity 0.2s ease-in-out;
    cursor: pointer;
  }
}
.border-grey-1 {
  border: 1px solid #aaa;
}
</style>
