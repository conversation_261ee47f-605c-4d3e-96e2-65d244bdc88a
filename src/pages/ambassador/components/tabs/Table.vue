<template>
  <div class="q-pa-md">
    <!-- Test: random add a verification -->
    <!-- <div>
      <q-btn icon="add" @click="onAddClick" />
    </div> -->

    <q-table
      title=""
      :rows="filteredList"
      :columns="columns"
      row-key="name"
      :pagination="pagination"
      :rows-per-page-options="[5, 10, 20]"
      @update:pagination="updateFilteredList"
      @request="updateFilteredList">
      <template v-slot:body-cell-name="props">
        <q-td :props="props">
          <div class="flex items-center">
            <div v-if="props.row.status === 2" class="q-mr-sm">
              <!-- <q-icon v-if="props.row.enable === true" name="check" color="green" size="1rem" /> -->
              <!-- <q-icon v-else name="cancel" color="red-4" size="1rem" /> -->
            </div>
            <PubAvatar v-if="props.row?.owner?.avatar" :src="props.row.owner.avatar" size="1.75rem" class="q-mr-xs" />
            <div>
              <TextItem v-if="props.row?.owner" :text="nameFormatter(props.row.owner)" :maxTextLength="20" maxToolTipWidth="15rem" />
              <div v-else class="text-grey-9">-</div>
              <TextItem
                v-if="props.row?.qualification && props.row.status === 2"
                :text="QUALIFICATION_TYPE[props.row.qualification]"
                :maxTextLength="20"
                maxToolTipWidth="15rem"
                class="text-teal" />
            </div>
          </div>
        </q-td>
      </template>

      <template v-slot:body-cell-action="props">
        <q-td :props="props">
          <Auth :action="'operate'">
            <div class="flex no-wrap justify-center">
              <!-- v-if="allTeacherVerificationConfig?.[getKeyByItem(props.row)] && !['content'].includes(getKeyByItem(props.row))" -->
              <VerifyServiceButton
                v-if="allTeacherVerificationConfig?.[getKeyByItem(props.row)]"
                :key="props.row._id + '-serivce-button'"
                :isSys="true"
                :item="props.row"
                :isPremiumContent="['content'].includes(getKeyByItem(props.row))"
                :config="allTeacherVerificationConfig[getKeyByItem(props.row)]" />
              <!-- <q-btn flat dense rounded icon="o_mail" color="grey-8" @click="onEmailClick(props.row)" /> -->
              <q-btn flat dense rounded icon="o_visibility" color="grey-8" @click="onViewClick(props.row)" />
              <!-- <q-btn flat dense rounded icon="o_delete" color="grey-8" @click="onDeleteClick(props.row._id)" /> -->
              <MessageButton
                v-if="!['content'].includes(props.row?.type)"
                :key="props.row._id + '-message-button'"
                :item="props.row"
                :isSmallButton="true"
                type="ambassador-auth" />
            </div>
          </Auth>
        </q-td>
      </template>
    </q-table>
  </div>
</template>

<script setup>
import {date} from 'quasar'
import {ref, computed, watch} from 'vue'
import {useRoute, useRouter} from 'vue-router'

import VerifyServiceButton from 'src/pages/account/verify-service/VerifyServiceButton.vue'
import TextItem from 'src/components/utils/TextItem.vue'
import MessageButton from 'src/pages/teacher-verification/components/MessageButton.vue'

import useAmbassador from 'src/composables/account/ambassador/useAmbassador'
import useTeacherVerificationConfig from 'src/composables/account/teacher-verification/useTeacherVerificationConfig'
import {TabStatusMap} from 'src/pages/teacher-verification/utils'
import {TIME_FORMAT, QUALIFICATION_TYPE} from 'src/boot/const.js'
import nameFormatter from 'src/utils/formatters/nameFormatter'

import Auth from 'src/pages/sys/Auth.vue'

const {list, getList, pagination, query, search, tab, role, duration, currentVerification} = useAmbassador()
const {allTeacherVerificationConfig} = useTeacherVerificationConfig()

const $route = useRoute()
const $router = useRouter()
const columns = ref([
  {name: 'name', required: true, label: 'Name', align: 'left'},
  {name: 'updateAt', label: 'Updated at', align: 'center', field: (row) => date.formatDate(row.updatedAt, TIME_FORMAT)},
  {name: 'action', label: 'Operate', align: 'center'},
])

const filteredList = ref([])
const status = computed(() => TabStatusMap?.[tab.value])

function getTypesByKey(key) {
  let type = ''
  let mentoringType = ''
  if (key.includes(':')) {
    ;[type, mentoringType] = key.split(':')
  } else {
    type = key
  }
  return [type, mentoringType]
}
function getKeyByItem(item) {
  let type = item?.type ?? ''
  let mentoringType = item?.mentoringType ?? ''
  if (type && mentoringType) return `${type}:${mentoringType}`
  if (type) return type
  return ''
}

// Test create
// async function onAddClick() {
//   const index = Math.floor(Math.random() * sysSubjectList.value.length)
//   const target = sysSubjectList.value[index]
//   const groupKeys = Object.keys(GradeGroupMap)
//   const gradeGroup = groupKeys
//   const grades = []
//   gradeGroup.forEach((e) => {
//     const gradeArray = getGradesByGroup({group: e, curriculum: target?.curriculum?.[0]}) ?? []
//     gradeArray.forEach((g) => {
//       if (!grades.includes(g)) grades.push(g)
//     })
//   })
//
//   const [type, mentoringType] = getTypesByKey(currentVerification.value)
//   const dto = {
//     uid: userId.value,
//     subject: target._id,
//     curriculum: target.curriculum?.[0] ?? '',
//     gradeGroup,
//     countryCode: 'nz',
//     grades,
//     status: 1,
//   }
//   if (type) dto.type = type
//   if (mentoringType) dto.mentoringType = mentoringType
//   console.log(dto)
//   await createOne(dto)
// }

// async function onDeleteClick(id) {
//   const title = 'Confirm delete'
//   const message = 'Please confirm that you want to delete this teacher?'
//   const okButtonLabel = 'OK'
//   const cancelButtonLabel = 'Cancel'
//   $q.dialog({
//     component: ConfirmDialog,
//     componentProps: {title, message, okButtonLabel, cancelButtonLabel},
//   })
//     .onOk(async () => {
//       $q.loading.show()
//       await deleteOneById(id)
//       await updateFilteredList()
//       $q.loading.hide()
//     })
//     .onCancel(() => {})
//     .onDismiss(() => {})
// }

watch([status, currentVerification, role], updateFilteredList)
async function updateFilteredList(arg) {
  if (arg?.pagination) pagination.value = arg?.pagination
  query.value.status = status.value
  const [type, mentoringType] = getTypesByKey(currentVerification.value)
  if (mentoringType) {
    query.value.type = type
    query.value.mentoringType = mentoringType
  } else {
    query.value.type = type
    delete query.value.mentoringType
  }
  if (role.value) {
    query.value.role = role.value
  }
  const res = await getList(true, {...query.value})
  filteredList.value = list.value
  pagination.value.rowsNumber = res.total
}
watch(list, () => {
  filteredList.value = list.value
})

watch(
  [search, duration],
  async () => {
    const _search = search.value?.toLowerCase() ?? ''
    let userIds = []
    if (_search) {
      const users = await App.service('users').find({query: {$limit: 1000, name: {$regex: _search, $options: 'i'}}})
      userIds = users?.data?.map((e) => e._id) ?? []
    }

    query.value.status = status.value
    const [type, mentoringType] = getTypesByKey(currentVerification.value)
    if (mentoringType) {
      query.value.type = type
      query.value.mentoringType = mentoringType
    } else {
      query.value.type = type
      delete query.value.mentoringType
    }

    if (_search && userIds.length) {
      query.value.uid = {$in: userIds}
    } else {
      delete query.value.uid
    }
    if (duration.value?.from && duration.value?.from) {
      query.value.dateRange = [duration.value.from, duration.value.to]
    } else {
      delete query.value.dateRange
    }
    Acan.objClean(query.value)

    const res = await getList(true, {...query.value})
    filteredList.value = list.value
    pagination.value.rowsNumber = res.total
  },
  {deep: true}
)
function onViewClick(row) {
  $router.push({
    path: `/sys/ambassador/${row?._id}`,
    query: {back: $route.path},
  })
}

const premiumDataMap = ref({})
const premiumImages = ref({})
watch(filteredList, getPremiumImages)
async function getPremiumImages() {
  const unitIds = filteredList.value.map((e) => e?.unit?._id).filter((e) => e)
  for (const id of unitIds) {
    const res = await getPremiumImage(id)
    premiumDataMap.value[id] = res
    premiumImages.value[id] = res?.cover ?? ''
  }
}
async function getPremiumImage(id) {
  if (!id) return
  let res = null
  try {
    res = await App.service('unit').get(id)
  } catch (error) {
    console.log(error)
  }
  return res
}
</script>
