<template>
  <q-page-container>
    <q-page class="pc-sm q-pa-md">
      <div class="row items-center q-mb-md">
        <div class="flex items-center">
          <div class="text-h5 text-bold text-capitalize q-mr-md">{{ route.params.type }}</div>
          <MessageButton v-if="currentDataId" :item="currentData" type="ambassador-auth" />
        </div>
        <q-space></q-space>

        <div v-if="isEditing">
          <q-btn class="q-mr-sm" rounded icon="close" color="grey" label="Cancel" no-caps @click="onCancelEditClick"></q-btn>
          <q-btn
            rounded
            icon="save"
            color="primary"
            label="Resubmit"
            no-caps
            @click="onSubmitClick"
            :disable="!isAttachmentValid || (isEditing && !isUpdated)"></q-btn>
        </div>

        <div v-else class="flex items-center">
          <StatusChip v-if="currentDataId" :item="currentData" />
          <q-btn
            v-if="!currentData || currentData?.status === 0"
            rounded
            icon="save"
            color="primary"
            label="Submit"
            no-caps
            @click="onSubmitClick"
            :disable="!isAttachmentValid"></q-btn>
          <q-btn v-if="currentData?.status === -1" rounded icon="edit" color="primary" label="Edit" no-caps @click="onEditClick"></q-btn>
          <q-btn
            v-if="currentData?.status === 2 || currentData?.status === 1"
            rounded
            icon="download"
            color="red-4"
            label="Withdraw"
            no-caps
            :disable="currentData?.status === 2"
            @click="() => onWithdrawClick(currentData?.status)"></q-btn>
        </div>
      </div>

      <div v-if="currentData?.status === -1 && currentData?.reason" class="text-subtitle2 q-mb-sm text-red-4">{{ currentData.reason }}</div>

      <q-spinner-ball color="primary" size="2em" class="full-width" v-if="loading" />
      <q-card v-else-if="!loading" class="rounded-borders-md">
        <q-card-section>
          <div v-for="pair in Object.entries(getAttachmentType(currentConfig?.attachmentType, currentKey))" :key="pair[0]">
            <div v-for="item in pair[1].child" :key="item.label" class="full-width q-my-md">
              <div class="flex items-center q-gutter-sm">
                <div class="text-subtitle2">{{ item.label }}</div>
                <q-icon v-if="item?.desc" name="help_outline" size="1.25rem" color="primary">
                  <q-tooltip>{{ item.desc }}</q-tooltip>
                </q-icon>
              </div>
            </div>
          </div>

          <div class="text-grey-7 text-body2">Max 5 files each type, only one video can be uploaded and cannot exceed 100 mb.</div>
          <q-separator />

          <div v-for="pair in Object.entries(getAttachmentType(currentConfig?.attachmentType, currentKey))" :key="pair[0]">
            <div class="flex items-center q-mt-md q-gutter-sm">
              <div class="text-bold">{{ pair[0] }}</div>
              <q-icon v-if="pair[1]?.desc" name="help_outline" size="1.25rem" color="primary">
                <q-tooltip>* {{ pair[1].desc }}</q-tooltip>
              </q-icon>
            </div>
            <div class="flex q-ml-md q-gutter-md">
              <div v-for="item in pair[1].child" :key="item.label" class="full-width">
                <div class="flex items-center q-gutter-sm">
                  <div>{{ item.label }}</div>
                  <q-icon v-if="item?.desc" name="help_outline" size="1.25rem" color="primary">
                    <q-tooltip>{{ item.desc }}</q-tooltip>
                  </q-icon>
                </div>
                <DragUploader
                  :isReadonly="isSys ? true : isReadonly || attachments.filter((e) => e?.type === item.label)?.length >= 5"
                  @uploaded="(res) => onUploaded(res, item.label)"
                  :hasVideo="hasVideo(attachments.filter((e) => e?.type === item.label))"
                  :fileType="item.type" />
                <div v-if="attachments.filter((e) => e?.type === item.label)?.length" class="q-my-md">
                  <div
                    v-for="item in attachments.filter((e) => e?.type === item.label).map((e) => ({...e, _id: e.hash, key: e.hash, desc: e.filename}))"
                    :key="item._id"
                    class="item-wrapper relative-position q-pb-sm q-my-sm">
                    <div class="flex justify-between items-center q-px-sm q-mt-sm">
                      <div class="flex no-wrap items-center">
                        <q-icon class="q-mr-sm" :name="getFileIcon(item)" color="teal-4" size="sm" />
                        <TextItem :text="item?.filename" :maxTextLength="30" maxToolTipWidth="20rem" />
                      </div>
                      <div class="flex q-gutter-xs">
                        <q-btn
                          flat
                          rounded
                          dense
                          icon="o_delete"
                          color="red"
                          style="background: rgba(0, 0, 0, 0.1)"
                          @click.stop="() => onDeleteAttachment(item)"
                          :disable="isSys ? true : isReadonly" />
                        <q-btn flat rounded dense icon="crop_free" color="teal" style="background: rgba(0, 0, 0, 0.1)" @click="() => onPreview(item)" />
                      </div>
                    </div>
                    <!-- <MaterialItem :item="item" class="full-width" style="height: 12rem" /> -->
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="flex no-wrap justify-start items-start q-my-md">
            <q-checkbox dense v-model="isAgree" :disable="isReadonly" />
            <div class="q-ml-sm">
              I have read and agreed with the
              <AgreementItem type="agents" mode="ambassador_agreement" />
            </div>
          </div>
        </q-card-section>
      </q-card>
    </q-page>

    <!-- dialogs -->
    <MaterialDialog class="z-max" v-model="isPreviewDialogShow">
      <MaterialItem :item="currentPreviewData" style="width: 90%; height: 90%" :isPreviewBig="true" :isDialog="true" />
    </MaterialDialog>
  </q-page-container>
</template>

<script setup>
import {ref, onMounted, computed} from 'vue'
import {useRoute} from 'vue-router'

import {getAttachmentType, getFileIcon} from 'src/boot/const'
import useSchool from 'src/composables/common/useSchool'
import useAmbassador from 'src/composables/account/ambassador/useAmbassador.js'
import useTeacherVerificationConfig from 'src/composables/account/teacher-verification/useTeacherVerificationConfig'

import StatusChip from 'src/pages/account/verify-service/components/StatusChip.vue'
import TextItem from 'src/components/utils/TextItem.vue'
import DragUploader from 'src/components/utils/inputs/DragUploader.vue'
import MaterialDialog from 'src/pages/class/components/MaterialDialog.vue'
import MaterialItem from 'src/components/material/MaterialItem.vue'
import MessageButton from 'src/pages/teacher-verification/components/MessageButton.vue'
import AgreementItem from 'src/components/utils/AgreementItem.vue'

const route = useRoute()
const {getTeacherVerificationConfig} = useTeacherVerificationConfig()

const {userId, isSys} = useSchool()
const {getList, createOne, patchOneById} = useAmbassador()

const loading = ref(true)
const isAgree = ref(false)

const currentKey = ref('ambassador')
const currentConfig = ref({attachmentType: []})
const attachments = ref([])
async function getCurrentConfig() {
  const key = currentKey.value
  $q.loading.show()
  const res = await getTeacherVerificationConfig(key)
  currentConfig.value = res
  $q.loading.hide()
}

const currentData = ref(null)
const currentDataId = computed(() => currentData.value?._id || '')

onMounted(async () => {
  loading.value = true
  await getCurrentConfig(currentConfig.value)
  await init()
  loading.value = false
})

async function init() {
  loading.value = true
  const res = await getList(true, {$skip: 0, $limit: 10, uid: userId.value})
  currentData.value = res?.data?.[0] || null
  if (currentData.value?.attachments?.length) {
    isAgree.value = true
    attachments.value = currentData.value?.attachments
  }
  loading.value = false
}

const isEditing = ref(false)
function onEditClick() {
  isEditing.value = true
}
const isUpdated = computed(() => {
  if (currentData.value?.attachments?.length && attachments.value?.length) {
    console.warn(attachments.value)
    console.warn(currentData.value?.attachments)
    const newStr = JSON.stringify(attachments.value)
    const oldStr = JSON.stringify(currentData.value?.attachments)
    return newStr !== oldStr
  }
  return false
})
const isReadonly = computed(() => {
  if (isEditing.value) return false
  if (!currentDataId.value) return false
  return true
})

async function onSubmitClick() {
  const dto = {attachments: attachments.value, status: 1}
  if (currentDataId.value) {
    await patchOneById(currentDataId.value, dto)
    isEditing.value = false
  } else {
    await createOne(dto)
  }
  await init()
}
async function onWithdrawClick() {
  if (!currentDataId.value) return
  const dto = {status: 0}
  await patchOneById(currentDataId.value, dto)
  await init()
}

function onCancelEditClick() {
  isEditing.value = false
  if (currentData.value?.attachments?.length) {
    attachments.value = currentData.value?.attachments
  }
}

const isPreviewDialogShow = ref(false)
const currentPreviewData = ref(null)
function onPreview(item) {
  currentPreviewData.value = item
  isPreviewDialogShow.value = true
}

function onUploaded(res, type) {
  const dto = {
    filename: res?.title?.[userId.value] ?? '',
    mime: res.mime,
    hash: res._id,
    size: res.size,
    date: new Date().toISOString(),
    type,
  }
  if (!attachments.value?.map((e) => `${e.hash}:${e.type}`).includes(`${dto.hash}:${dto.type}`)) {
    attachments.value.push(dto)
  } else {
    attachments.value = attachments.value.map((e) => {
      if (`${e.hash}:${e.type}` === `${dto.hash}:${dto.type}`) return dto
      return e
    })
  }
}
function onDeleteAttachment(item) {
  attachments.value = attachments.value.filter((e) => `${e.hash}:${e.type}` !== `${item.hash}:${item.type}`)
}
const isAttachmentValid = computed(() => {
  let isValid = true
  const attachmentTypes = getAttachmentType(currentConfig.value?.attachmentType, currentKey.value)
  for (let pair of Object.entries(attachmentTypes)) {
    for (let item of pair[1].child) {
      if (attachments.value.filter((e) => e?.type === item.label)?.length === 0) isValid = false
    }
  }
  return isValid
})
</script>

<style lang="scss" scope>
.item-wrapper {
  border: 1px solid #ddd;
  border-radius: 0.5rem;
  background: #fafafa;
  overflow: hidden;
}
</style>
