<template>
  <q-list class="q-mx-sm">
    <q-item
      v-for="(item, index) in items"
      :key="index"
      clickable
      dense
      @click="() => onItemClick(item)"
      :active="item.value == active"
      active-class="bg-teal-2 text-black text-weight-medium"
      class="q-my-md q-py-sm rounded-borders"
      :class="{'text-grey-7': item.value !== active}">
      <q-item-section side>
        <q-icon :color="`${item.value == active ? 'black' : 'grey-7'}`" :name="item.icon" />
      </q-item-section>
      <q-item-section>{{ item.label }}</q-item-section>
    </q-item>
    <q-separator />
  </q-list>
</template>
<script setup>
import {useRoute, useRouter} from 'vue-router'
import useParser from 'src/composables/utils/useParser'

const route = useRoute()
const router = useRouter()
const {parsePathToHref} = useParser()

defineProps({
  active: String,
})

const items = [
  {label: 'Verification', value: 'verification', icon: 'o_library_books', to: '/account/ambassador/verification'},
  {label: 'Help center', value: 'help', icon: 'o_help_outline', to: '', _target: true}, // /test/ambassador-verification
]

function onItemClick(item) {
  if (item?._target) {
    if (item?.to) {
      window.open(parsePathToHref(item.to), '_blank')
    }
  } else {
    if (item?.to) router.replace({path: item.to, query: route.query})
  }
}
</script>
