<template>
  <q-page>
    <section class="pc-max q-pa-md q-pb-lg">
      <div class="flex justify-between">
        <div class="flex items-start q-gutter-sm" style="max-width: calc(100% - 320px)">
          <span v-for="option in VerificationList?.filter((item) => item?.value !== 'content' && item?.value !== 'mentoring')" :key="option.value">
            <Auth :action="option.value === 'content' ? 'view_content' : 'view_others'">
              <q-btn
                flat
                no-caps
                dense
                :label="sysServiceCodeMap?.[option?.subejctCode]?.name || option.label"
                class="q-px-sm"
                :class="[getBtnClass(option)]"
                style="border: 1px solid"
                @click="role = option.value">
                <!-- <q-badge v-if="count?.[option.value]" floating color="red">{{ count[option.value] }}</q-badge> -->
              </q-btn>
            </Auth>
          </span>
        </div>
        <div>
          <q-input class="bg-teal-1 overflow-hidden rounded-borders-xl" outlined rounded dense :label="'Search by keyword'" v-model="search">
            <template v-slot:prepend>
              <q-btn v-if="search?.length" round flat dense icon="close" @click="search = ''"></q-btn>
              <q-btn v-else round flat dense icon="search" @click="() => {}"></q-btn>
            </template>
            <template v-slot:append>
              <q-btn
                round
                flat
                dense
                class="relative-position"
                :class="[showMenu ? 'rotate-180' : '']"
                icon="arrow_drop_down"
                @click.prevent="showMenu = !showMenu">
                <q-menu max-width="45rem" @hide="onMenuHide" fit :offset="[0, 20]" class="shadow-3 rounded-borders-md">
                  <q-card style="min-width: 15rem">
                    <q-card-section class="scroll">
                      <div class="flex items-center">
                        <div>Duration:</div>
                        <div class="flex items-center">
                          <q-icon name="event" class="cursor-pointer q-mx-sm" size="1.5rem" color="teal">
                            <q-popup-proxy cover transition-show="scale" transition-hide="scale">
                              <q-date v-model="duration" range>
                                <div class="row items-center justify-end">
                                  <q-btn v-close-popup label="Close" color="primary" flat />
                                </div>
                              </q-date>
                            </q-popup-proxy>
                          </q-icon>
                          <div v-if="duration?.from" class="text-grey-8">{{ duration.from }} ~</div>
                          <div v-if="duration?.to" class="text-grey-8">{{ duration.to }}</div>
                          <q-btn
                            v-if="duration?.from && duration?.to"
                            flat
                            rounded
                            dense
                            no-caps
                            label="clear"
                            class="q-ml-md q-px-md"
                            @click=";(duration.from = ''), (duration.to = '')" />
                        </div>
                      </div>
                    </q-card-section>
                  </q-card>
                </q-menu>
              </q-btn>
            </template>
          </q-input>
        </div>
      </div>

      <q-tabs
        v-model="tab"
        dense
        class="text-grey full-width flex justify-between q-my-md"
        active-color="primary"
        indicator-color="primary"
        align="justify"
        narrow-indicator>
        <div class="flex items-center">
          <q-tab v-for="item in [TabMap.pending, TabMap.approved, TabMap.reject]" :key="item.value" :name="item.value" no-caps :label="item.label" />
        </div>
        <q-space />
        <div>
          <Auth :action="'setting'">
            <q-tab name="setting" no-caps label="Setting" />
          </Auth>
        </div>
      </q-tabs>

      <q-tab-panels v-model="tab" animated>
        <q-tab-panel :name="TabMap.pending.value">
          <Table />
        </q-tab-panel>

        <q-tab-panel :name="TabMap.approved.value">
          <Table />
        </q-tab-panel>

        <q-tab-panel :name="TabMap.reject.value">
          <Table />
        </q-tab-panel>

        <q-tab-panel :name="TabMap.setting.value">
          <Setting v-if="currentVerification !== 'content'" />
          <!-- <ContentSetting v-else /> -->
        </q-tab-panel>
      </q-tab-panels>
    </section>
  </q-page>
</template>

<script setup>
import {ref} from 'vue'

import {VerificationList, TabMap} from './utils'
import useSubject from 'src/composables/account/academic/useSubject.js'
import useAmbassador from 'src/composables/account/ambassador/useAmbassador'
// import useTeacherVerificationAuth from 'src/composables/account/teacher-verification/useTeacherVerificationAuth'
import Setting from './components/tabs/Setting.vue'
// import ContentSetting from './components/tabs/ContentSetting.vue'
import Table from './components/tabs/Table.vue'
import Auth from 'src/pages/sys/Auth.vue'

const {tab, role, search, duration, currentVerification} = useAmbassador()

// const {count} = useTeacherVerificationAuth()
const {sysServiceCodeMap} = useSubject()

const showMenu = ref(false)

function onMenuHide() {}

function getBtnClass(option) {
  if (option?.btnColor === 'green') {
    return role.value === option.value ? 'bg-green text-white' : 'bg-white text-green'
  } else if (option?.btnColor === 'orange') {
    return role.value === option.value ? 'bg-orange text-white' : 'bg-white text-orange'
  }
}
</script>
