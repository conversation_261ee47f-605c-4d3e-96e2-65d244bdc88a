<template>
  <div style="background-color: #ceddff2b">
    <section class="full-width" style="z-index: 2">
      <a id="top"></a>
      <div class="q-pa-sm q-pa-lg-md items-center">
        <h4 class="text-h3 text-center text-bold q-mb-md mobile-sm-padding" style="color: #252b42">
          Become a Classscipe School <br />
          Ambassador!
        </h4>

        <div class="font-16 text-center text-black" style="margin: 50px 0; display: flex; justify-content: center">
          <div style="max-width: 850px">
            <div class="text-weight-medium">Inspire your network, share great learning, and earn rewards.</div>
            <div>
              Join <PERSON>cipe School Ambassador Program—open to passionate students and teachers who want to spread the word about Classcipe's innovative
              learning solutions. Get exclusive access to our latest premium workshops for free, and earn cash rewards when your referrals purchase any of our
              services. It’s a simple, impactful way to help others while growing with us!
            </div>
          </div>
        </div>
      </div>
    </section>

    <div class="q-pa-md flex flex-center" style="min-height: 100vh">
      <!-- Actual section with fit-content -->
      <section class="mobile-sm-padding" style="max-width: 1200px; width: 100%; border-radius: 16px">
        <!-- Row with main content -->

        <!-- Bottom Text -->
        <div class="row">
          <div class="q-pa-md q-mx-auto info-box">
            <div class="row q-col-gutter-md q-mb-md">
              <!-- Left Column -->
              <div class="col-12 col-md-6">
                <div class="bg-white rounded-borders mobile-sm-padding">
                  <div id="no_schedule" class="text-white text-h6 rounded-borders" style="background-color: #496bb4">
                    <div :class="isMobile ? ['text-center', 'q-pa-sm'] : ['q-pa-md']">What will you Gain as a Classcipe School Ambassador</div>
                  </div>
                  <div class="q-mt-sm">
                    <div class="font-16 q-pt-md" style="font-weight: normal">
                      As a Classcipe School Ambassador, you'll enjoy exclusive access to premium learning experiences, earn cash rewards through referrals, and
                      grow your leadership skills while making a real impact in your school community.
                    </div>
                  </div>
                </div>
              </div>

              <!-- Right Column -->
              <div class="col-12 col-md-6">
                <div class="bg-white rounded-borders mobile-sm-padding">
                  <q-card class="flex flex-center" style="height: 250px; border-radius: 12px; overflow: hidden">
                    <video controls poster="" class="fit" style="object-fit: unset; border-radius: 12px">
                      <source src="https://r2dev.classcipe.com/acbc5ea29f5116ab5f1e0a05577cbd5c07474a0f" type="video/mp4" />
                      Your browser does not support the video tag.
                    </video>
                  </q-card>
                </div>
              </div>
            </div>

            <q-list class="q-pa-sm q-mx-auto">
              <div v-for="(service, index) in services" :key="index">
                <q-card flat class="q-mb-md mentoring-div">
                  <q-card-section v-if="isDesktopOnly" class="q-pa-md">
                    <div class="column">
                      <div class="row items-center q-gutter-sm">
                        <div style="width: 10px; height: 10px; background-color: #496bb4; border-radius: 2px"></div>
                        <div class="text-subtitle1 text-bold" style="color: black">
                          {{ service.title }}
                        </div>
                      </div>
                      <div class="text-body2 q-mt-xs" style="white-space: pre-line">
                        {{ service.description }}
                      </div>
                    </div>
                  </q-card-section>
                </q-card>
                <div v-if="isMobile" class="row justify-center items-center">
                  <div style="padding-left: auto; padding-right: auto">
                    <q-expansion-item
                      class="shadow-1 overflow-hidden"
                      style="border-radius: 30px; border: 2px solid #496bb4; background-color: #ceddff2b"
                      icon="explore"
                      label="Counter"
                      header-class="custom-exp-header">
                      <template v-slot:header>
                        <div class="row items-center q-gutter-sm" style="flex-wrap: nowrap">
                          <span style="width: 10px; height: 10px; background-color: #496bb4; border-radius: 2px; flex-shrink: 0"></span>
                          <div class="text-bold" style="overflow: hidden">
                            {{ service.title }}
                          </div>
                        </div>
                      </template>
                      <q-card>
                        <q-card-section class="q-pa-sm">
                          <div class="text-body1 q-mb-sm">
                            {{ service.description }}
                          </div>
                        </q-card-section>
                      </q-card>
                    </q-expansion-item>
                  </div>
                </div>
              </div>
            </q-list>

            <div class="row items-start q-col-gutter-md q-mb-md">
              <!-- Left Column -->
              <div class="col-12">
                <div class="bg-white rounded-borders q-pa-sm mobile-sm-padding">
                  <div id="no_schedule" class="text-white text-h6 rounded-borders" style="background-color: #496bb4">
                    <div :class="isMobile ? ['text-center', 'q-pa-sm'] : ['q-pa-md']">Ambassador Responsibilities & Actions</div>
                  </div>
                  <div class="q-mt-sm">
                    <div class="font-16 q-pt-md" style="font-weight: normal">
                      As a Classcipe School Ambassador, you'll enjoy exclusive access to premium learning experiences, earn cash rewards through referrals, and
                      grow your leadership skills while making a real impact in your school community.
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="text-primary text-h6 flex items-center info-header">
              <div class="info-title font-16">For Both Students and Teachers:</div>
            </div>

            <q-list class="q-pa-sm q-mx-auto">
              <div v-for="(point, index) in points2" :key="index">
                <q-card v-if="isDesktopOnly" flat bordered class="q-mb-md mentoring-div">
                  <q-card-section class="q-pa-md">
                    <div class="column">
                      <div class="row items-center q-gutter-sm">
                        <div style="width: 10px; height: 10px; background-color: #496bb4; border-radius: 2px"></div>
                        <div class="text-subtitle1 text-bold" style="color: black">
                          {{ point.title }}
                        </div>
                      </div>
                      <div class="text-body2 q-mt-xs" style="white-space: pre-line">
                        {{ point.description }}
                      </div>
                    </div>
                  </q-card-section>
                </q-card>
                <div v-if="isMobile" class="row justify-center items-center">
                  <div class="q-mt-sm" style="width: 100%; padding-left: auto; padding-right: auto">
                    <q-expansion-item
                      class="shadow-1 overflow-hidden"
                      style="border-radius: 30px; border: 2px solid #496bb4; background-color: #ceddff2b"
                      icon="explore"
                      label="Counter"
                      header-class="custom-exp-header">
                      <template v-slot:header>
                        <div class="row items-center q-gutter-sm" style="flex-wrap: nowrap">
                          <div style="width: 10px; height: 10px; background-color: #496bb4; border-radius: 2px; flex-shrink: 0"></div>
                          <div class="text-bold" style="overflow: hidden">
                            {{ point.title }}
                          </div>
                        </div>
                      </template>
                      <q-card>
                        <q-card-section class="q-pa-sm">
                          <div class="text-body1 q-mb-sm">
                            {{ point.description }}
                          </div>
                        </q-card-section>
                      </q-card>
                    </q-expansion-item>
                  </div>
                </div>
              </div>
            </q-list>

            <div class="text-primary text-h6 flex items-center info-header">
              <div class="info-title font-16">Exclusive for Teachers:</div>
            </div>

            <q-list class="q-pa-sm q-mx-auto">
              <div v-for="(point, index) in point3" :key="index">
                <q-card v-if="isDesktopOnly" flat bordered class="q-mb-md mentoring-div">
                  <q-card-section class="q-pa-md">
                    <div class="column">
                      <div class="row items-center q-gutter-sm">
                        <div style="width: 10px; height: 10px; background-color: #496bb4; border-radius: 2px"></div>
                        <div class="text-subtitle1 text-bold" style="color: black">
                          {{ point.title }}
                        </div>
                      </div>
                      <div class="text-body2 q-mt-xs" style="white-space: pre-line">
                        {{ point.description }}
                      </div>
                    </div>
                  </q-card-section>
                </q-card>
                <div v-if="isMobile" class="row justify-center items-center">
                  <div style="padding-left: auto; padding-right: auto">
                    <q-expansion-item
                      class="shadow-1 overflow-hidden"
                      style="border-radius: 30px; border: 2px solid #496bb4; background-color: #ceddff2b"
                      icon="explore"
                      label="Counter"
                      header-class="custom-exp-header">
                      <template v-slot:header>
                        <div class="row items-center q-gutter-sm" style="flex-wrap: nowrap">
                          <div style="width: 10px; height: 10px; background-color: #496bb4; border-radius: 2px; flex-shrink: 0"></div>
                          <div class="text-bold" style="overflow: hidden">
                            {{ point.title }}
                          </div>
                        </div>
                      </template>
                      <q-card>
                        <q-card-section class="q-pa-sm">
                          <div class="text-body1 q-mb-sm">
                            {{ point.description }}
                          </div>
                        </q-card-section>
                      </q-card>
                    </q-expansion-item>
                  </div>
                </div>
              </div>
            </q-list>

            <div class="row items-start q-col-gutter-md q-mb-md">
              <!-- Left Column -->
              <div class="col-12 col-md-6">
                <div class="bg-white rounded-borders mobile-sm-padding">
                  <div id="no_schedule" class="text-white text-h6 rounded-borders" style="background-color: #496bb4">
                    <div :class="isMobile ? ['text-center', 'q-pa-sm'] : ['q-pa-md']">How to verify as a School Ambassador?</div>
                  </div>
                  <div class="q-mt-sm">
                    <div class="font-16 q-pt-md" style="font-weight: normal">
                      To get verified as a Classcipe School Ambassador, you'll need to submit a few simple documents to confirm your identity, role, and intent.
                      This helps us ensure a trusted and authentic ambassador network.
                    </div>
                  </div>
                </div>
              </div>

              <!-- Right Column -->
              <div class="col-12 col-sm-6">
                <q-card class="flex flex-center" style="height: 250px; border-radius: 12px; overflow: hidden">
                  <video controls poster="" class="fit" style="object-fit: unset; border-radius: 12px">
                    <source src="https://r2dev.classcipe.com/87f0e045585f844d07c50f38512acd82f437c0eb" type="video/mp4" />
                    Your browser does not support the video tag.
                  </video>
                </q-card>
              </div>
            </div>
            <div class="q-pa-md flex flex-center footer">
              <section class="bg-white q-mt-sm" style="max-width: 1200px; width: 100%; border-radius: 16px">
                <div style="padding: 0 0px 40px 0px; border-radius: 16px; background-color: #ffffff">
                  <div class="q-gutter-md flex justify-center">
                    <div style="text-align: left; max-width: 400px; width: 100%">
                      <div class="flex items-center" style="flex-wrap: nowrap" v-if="!pub.user?.agreedToAmbassadorTerms">
                        <q-checkbox class="custom-checkbox" v-model="acceptTerm" />
                        <div>
                          I have read and agree to the
                          <a href="#" @click.prevent="openTerms" style="color: #027be3">terms and conditions</a>
                        </div>
                      </div>

                      <div v-if="fromVerification">
                        <q-checkbox v-model="showAgain" label="Please do not show again" />
                      </div>

                      <div class="q-mt-md" style="text-align: center">
                        <q-btn label="Apply Now!" style="background: rgb(73, 107, 180); color: #fff" :disable="!acceptTerm" @click="onOKClick" />
                        <div v-if="!acceptTerm" style="margin-top: 10px; color: red">Please accept the terms and conditions to proceed.</div>
                      </div>
                    </div>
                  </div>
                </div>
              </section>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
  <q-btn
    v-show="showScrollTop"
    @click="scrollToTop"
    icon="keyboard_arrow_up"
    class="scroll-top-btn"
    round
    style="background-color: #496bb4; color: white"
    unelevated />
</template>

<script setup>
import {ref, onMounted, onUnmounted, computed} from 'vue'
import {useQuasar} from 'quasar'
import {useRouter, useRoute} from 'vue-router'
import {LocalStorage} from 'quasar'
import {pubStore} from 'stores/pub'
// const {dialogRef} = useDialogPluginComponent()

const agreed = ref(false)
const doNotShowAgain = ref(false)
const showAgain = ref(false)

const router = useRouter()
const route = useRoute()

const isMobile = computed(() => $q.screen.lt.sm)
const isDesktopOnly = computed(() => $q.screen.gt.xs)

const $q = useQuasar()
const showScrollTop = ref(false)
const showOnMobile = ref(false)
const pub = pubStore()
const acceptTerm = ref(false)

const props = defineProps({
  fromVerification: {
    type: Boolean,
    default: false,
  },
})

const services = [
  {
    title: 'Free Acecess to Premium Workshops',
    description: `As an ambassador, you'll get exclusive, free access to Classcipe's latest premium workshops—covering cutting-edge topics in research, STEAM,
  career development, and more.
  Stay ahead in your academic or teaching journey while experiencing world-class content at no cost.`,
  },
  {
    title: 'Earn Cash Rewards Through Referrals',
    description: `Help others benefit from Classcipe just like you!

  When someone from your school or network purchases a service using your referral, you’ll earn real cash rewards.

  Whether you’re a teacher sharing with colleagues or a student recommending to friends, your voice makes an impact—and earns you something in return.`,
  },
]

const points2 = [
  {
    title: 'Share Our Posters',
    description: `Promote Classcipe by sharing posters of ongoing or upcoming services, programs, and offers with your school and social networks to increase
  visibility and engagement.`,
  },
  {
    title: 'Promote Monthly Premium Workshops',
    description: `Help your peers and fellow educators stay updated by sharing our monthly premium workshop posters and encouraging participation.`,
  },
  {
    title: 'Share Content You Believe In',
    description: `Love something on Classcipe? Share any content—like sessions, events, or learning materials—you find valuable with your classmates, friends, or
  colleagues.`,
  },
]

const point3 = [
  {
    title: 'Refer to Your School principal',
    description: `As a teacher ambassador, you'll have the unique opportunity to refer your school principal to explore Classcipe's full platform and services for
  school-wide implementation.`,
  },
]

const handleScroll = () => {
  showScrollTop.value = window.scrollY > 200
}
const scrollToTop = () => {
  window.scrollTo({top: 0, behavior: 'smooth'})
}
onMounted(() => {
  window.addEventListener('scroll', handleScroll)
  showOnMobile.value = $q.screen.xs
  acceptTerm.value = pub.user?.agreedToAmbassadorTerms || false
})
onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
function handleArrowClick(section) {
  localStorage.setItem('verification_category', section)

  setTimeout(() => {
    props.dialogRef?.hide()
  }, 100)
  router.push({path: '/account/teacher/auth/edit/verification', query: route.query})
}
const openTerms = () => {
  const host = location?.host ?? 'classcipe.com' // default if needed
  const protocol = location?.protocol ?? 'https:'
  let url = '/v2/com/agreement/agents/ambassador_agreement'
  const path = `${protocol}//${host}${url}`
  window.open(path, '_blank')
}
const onOKClick = async () => {
  if (showAgain.value) {
    LocalStorage.set('ambassador_verification', '1')
  }
  if (acceptTerm.value) {
    const res = await App.service('users').patch(pub.user._id, {agreedToAmbassadorTerms: true})
    pub.user = {...pub.user, ...res}
  }
  router.push('/account/ambassador/verification')
}
</script>

<style scoped>
.bg-block-1 {
  top: 100px;
  left: 0;
}

.bg-block-2 {
  top: 80px;
  right: 0;
}

.bg-block-3 {
  top: 80px;
  left: 0;
}

.bg-block-4 {
  top: 400px;
  right: 0;
}

.grid-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  /* Max 3 items per row */
  grid-auto-rows: auto;
  gap: 20px;
  justify-items: center;
  max-width: 900px;
  margin: auto;
}

.frame-39477 {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 32px;
  gap: 23px;

  width: 260px;
  height: 224px;

  background: linear-gradient(180deg, rgba(242, 255, 253, 0.3) 0%, rgba(254, 255, 245, 0.3) 100%);
  border: 2px solid rgba(9, 165, 167, 0.28);
  border-radius: 30px;
}

.info-box {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 20px 30px;
  gap: 20px;
  width: 100%;
  max-width: 848px;
  background: #ffffff;
  border: 1px solid #496bb4;
  border-radius: 30px;
  margin-bottom: 24px;
  font-family: 'Arimo', sans-serif;
}

.info-header {
  display: flex;
  align-items: center;
  gap: 17px;
}

.info-title {
  font-weight: 700;
  font-size: 18px;
  line-height: 140%;
  color: #496bb4;
}

.info-body {
  font-size: 18px;
  line-height: 1.5;
  color: #333;
}

.container {
  display: flex;
  flex-direction: column;
  margin-top: 30px;
  margin-left: 30px;
  align-items: center;
  padding: 0;
  gap: 80px;
  width: 900px;
}

.content-wrapper {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  gap: 35px;
  width: calc(100% - 60px);
  height: 333px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.rounded-borders {
  border-radius: 15px;
}

.font-16 {
  font-size: 16px;
}

.scroll-top-btn {
  position: fixed;
  bottom: 40px;
  right: 30px;
  z-index: 1000;
  transition: opacity 0.3s ease;
}

.pa-top-0 {
  padding-top: 0px;
}

video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.custom-card-style {
  background-color: #ffffff;
  width: 100%;
  border-radius: 8px;
  padding: 24px;
  box-sizing: border-box;
}

.mentoring-div {
  border: 1px solid #496bb4;
  border-radius: 12px;
  box-shadow: none;
  background-color: transparent;
}

.footer {
  width: 100%;
}

@media (max-width: 599px) {
  .custom-card-style {
    padding: 0;
  }
  .footer {
    padding: 16px;
    margin-bottom: 60px;
  }
  .info-box {
    padding: 8px !important;
  }
  .custom-expansion-item .q-item {
    justify-content: center;
    border: 1px solid #496bb4;
    border-radius: 20px;
  }
  .mentoring-div {
    border: none;
  }

  ::v-deep(.q-focus-helper) {
    display: none !important;
  }

  ::v-deep(.custom-exp-header) {
    border-radius: 30px;
    padding: 8px 16px;
    justify-content: space-between;
  }

  .mobile-sm-padding {
    padding: 10px;
  }
  .info-body {
    font-size: 14px;
  }
}
.custom-checkbox >>> .q-checkbox__inner {
  color: rgb(73, 107, 180);
}
</style>
