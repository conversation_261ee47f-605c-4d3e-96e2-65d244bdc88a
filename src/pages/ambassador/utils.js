export const TabMap = {
  setting: {value: 'setting', label: 'Setting'},
  pending: {value: 'pending', label: 'Pending'},
  approved: {value: 'approved', label: 'Approved'},
  reject: {value: 'reject', label: 'Reject'},
}
export const TabOptions = Object.values(TabMap)
export const TabStatusMap = {
  pending: 1,
  approved: 2,
  reject: -1,
}

export const RoleMap = {
  teacher: {value: 'teacher', label: 'Teacher'},
  student: {value: 'student', label: 'Student'},
}

export const VerificationList = [
  {label: 'Teacher Ambassador', value: 'teacher', bgColor: 'bg-blue-1', btnColor: 'green'},
  {label: 'Student Ambassador', value: 'student', bgColor: 'bg-orange-1', btnColor: 'green'},
  // {label: 'Student', value: 'student', bgColor: 'bg-orange-1', btnColor: 'green'},
]
export const VerificationMap = VerificationList.reduce((acc, cur) => {
  if (cur?.value) acc[cur.value] = cur
  return acc
}, {})
