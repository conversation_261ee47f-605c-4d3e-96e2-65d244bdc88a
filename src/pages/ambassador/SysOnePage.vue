<template>
  <div>
    <PubTopBanner :title="`Teacher Verification`" :useBack="true" :back="`${$route.query?.back}`" />

    <q-page class="">
      <div class="pc-max q-px-md q-mt-xs">
        <main v-if="data" class="column no-wrap full-width relative-position">
          <!-- Test: change status -->
          <!-- <q-card v-if="data?._id" class="bg-grey full-width q-pa-md flex justify-between items-center q-my-md">
          <div>Teacher verification status: {{ data?.status }}</div>
          <div class="flex q-gutter-md">
            <q-btn rounded flat no-caps label="Reset" class="bg-blue text-white" @click="() => onResetVerification(data._id)" />
            <q-btn rounded flat no-caps label="Reject" class="bg-red text-white" @click=";(isDeleteDialogShow = true), (rejectReason = '')" />
            <q-btn rounded flat no-caps label="Approve" class="bg-green text-white" @click="() => onApproveVerification(data._id)" />
          </div>
        </q-card> -->

          <q-card v-if="data?.status === 1" class="bg-blue full-width q-pa-md flex justify-between items-center q-my-md">
            <div>Application status: <b>Pending</b></div>
            <div class="flex q-gutter-md">
              <MessageButton :isBannerButton="true" :item="data" :isSmallButton="true" type="ambassador-auth" />
              <q-btn rounded flat no-caps label="Reject" class="bg-red text-white" @click="rejectMessage" />
              <q-btn rounded flat no-caps label="Approve" class="bg-green text-white" @click="onApproveVerificationClick(data._id)" />
            </div>
          </q-card>

          <div v-if="data?.status === 2">
            <q-card class="bg-green full-width q-pa-md q-my-md flex justify-between items-center">
              <div>Application status: <b>Approved</b></div>
              <div class="flex q-gutter-md">
                <MessageButton :isBannerButton="true" :item="data" :isSmallButton="true" type="ambassador-auth" />
                <!--              <q-btn rounded flat no-caps label="Reject" class="bg-red text-white" @click=";(isRejectDialogShow = true), (rejectReason = '')" />-->
                <q-btn rounded flat no-caps label="Reject" class="bg-red text-white" @click="rejectMessage" />
              </div>
            </q-card>
            <!-- <div v-if="data?.reason" class="text-teal text-subtitle2 q-my-md" style="font-size: 1rem">{{ data.reason }}</div> -->
          </div>

          <div v-if="data?.status === -1">
            <q-card class="bg-red full-width q-pa-md q-my-md flex justify-between items-center">
              <div>Application status: <b>Rejected</b></div>
              <div class="flex q-gutter-md">
                <MessageButton :isBannerButton="true" :item="data" :isSmallButton="true" type="ambassador-auth" />
                <q-btn rounded flat no-caps label="Approve" class="bg-green text-white" @click="() => onApproveVerificationClick(data._id)" />
              </div>
            </q-card>
            <div v-if="data?.reason" class="text-red text-subtitle2 q-my-md" style="font-size: 1rem">{{ data.reason }}</div>
          </div>

          <div>
            <hr class="divider" />
            <div class="flex justify-between items-center q-my-md">
              <div v-if="data?.owner" class="flex items-center q-gutter-md">
                <PubAvatar :src="data?.owner?.avatar" size="3rem" />
                <div>
                  <div class="text-subtitle2">{{ nameFormatter(data.owner) }}</div>
                  <div class="text-grey-8">{{ data?.owner?.email ?? '0' }}</div>
                </div>
                <MessageButton :item="data" :isSmallButton="true" type="ambassador-auth" />
              </div>
              <div v-if="data?.createdAt" class="text-grey-8">{{ date.formatDate(data.createdAt, TIME_FORMAT) }}</div>
            </div>
          </div>

          <hr class="divider" />

          <div class="flex no-wrap justify-start items-start q-my-md">
            <q-checkbox dense :modelValue="true" :disable="true" />
            <div class="q-ml-sm">
              Signed agreement:
              <AgreementItem type="agents" mode="ambassador_agreement" />
            </div>
          </div>

          <hr class="divider" />

          <div class="q-my-md">
            <hr class="divider" />
            <div class="text-h6">Verification materials provided</div>
            <div v-if="config?.attachmentType?.length && data?.attachments?.length" class="q-my-md">
              <div v-for="pair in Object.entries(getAttachmentType(config?.attachmentType, currentKey))" :key="pair[0]">
                <div class="flex items-center q-mt-md q-gutter-sm">
                  <div class="text-bold">{{ pair[0] }}</div>
                  <q-icon v-if="pair[1]?.desc" name="help_outline" size="1.25rem" color="primary">
                    <q-tooltip>* {{ pair[1].desc }}</q-tooltip>
                  </q-icon>
                </div>
                <div class="flex q-ml-md q-gutter-md">
                  <div v-for="item in pair[1].child" :key="item.label" class="full-width">
                    <div class="flex items-center q-gutter-sm">
                      <div>{{ item.label }}</div>
                      <q-icon v-if="item?.desc" name="help_outline" size="1.25rem" color="primary">
                        <q-tooltip>{{ item.desc }}</q-tooltip>
                      </q-icon>
                    </div>
                    <div v-if="data?.attachments.filter((e) => e?.type === item.label)?.length" class="q-my-md">
                      <div
                        v-for="item in data.attachments.filter((e) => e?.type === item.label).map(mapToMaterial)"
                        :key="item._id"
                        class="item-wrapper relative-position q-pb-sm q-my-sm">
                        <div class="flex justify-between items-center q-px-sm q-mt-sm">
                          <div class="flex no-wrap items-center">
                            <q-icon class="q-mr-sm" :name="getFileIcon(item)" color="teal-4" size="sm" />
                            <TextItem :text="item?.filename" :maxTextLength="30" maxToolTipWidth="20rem" />
                          </div>
                          <div class="flex q-gutter-xs">
                            <q-btn flat rounded dense icon="crop_free" color="teal" style="background: rgba(0, 0, 0, 0.1)" @click="() => onPreview(item)" />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <q-expansion-item
              v-if="data?.attachments?.filter((e) => !config?.attachmentType?.includes(e?.type))?.length"
              v-model="isHistoricalEvidenceExpend"
              style="border: 1px solid #888"
              no-caps
              class="rounded-md"
              label="Historical evidence">
              <div class="flex q-mx-md q-gutter-md">
                <div class="full-width">
                  <div
                    v-for="item in data?.attachments
                      .filter((e) => !config?.attachmentType?.includes(e?.type))
                      .map((e) => ({...e, _id: e.hash, key: e.hash, desc: e.filename}))"
                    :key="item._id"
                    class="item-wrapper relative-position q-pb-sm q-my-sm">
                    <div class="flex justify-between items-center q-px-sm q-mt-sm">
                      <div class="flex no-wrap items-center">
                        <q-icon class="q-mr-sm" :name="getFileIcon(item)" color="teal-4" size="sm" />
                        <TextItem :text="item?.filename" :maxTextLength="30" maxToolTipWidth="20rem" />
                      </div>
                      <div class="flex q-gutter-xs">
                        <q-btn flat rounded dense icon="crop_free" color="teal" style="background: rgba(0, 0, 0, 0.1)" @click="() => onPreview(item)" />
                      </div>
                    </div>
                    <!-- <MaterialItem :item="item" class="full-width" style="height: 12rem" /> -->
                  </div>
                </div>
              </div>
            </q-expansion-item>
          </div>
          <div class="q-my-md" v-if="data?.takeaway">
            <q-btn rounded no-caps label="Check takeaway" :to="data?.takeaway" target="_blank" color="primary" />
            <span class="text-grey q-ml-md">generated {{ date.formatDate(data?.takeawayCreatedAt, 'MM/DD/YYYY HH:mm:ss') }}</span>
          </div>
        </main>
        <NoData v-else class="q-mt-lg" messageColor="grey-8" size="20rem" />
      </div>

      <!-- dialogs -->
      <MaterialDialog class="z-max" v-model="isPreviewDialogShow">
        <MaterialItem :item="currentPreviewData" style="width: 90%; height: 90%" :isPreviewBig="true" :isDialog="true" />
      </MaterialDialog>

      <q-dialog v-model="isRejectDialogShow" persistent>
        <div class="column no-wrap flex-center bg-teal-1 q-pa-md" style="width: 30rem">
          <div class="q-px-lg q-pb-lg full-width">
            <div class="text-subtitle2 q-mb-sm">Reject reason*</div>
            <q-input class="full-width" v-model="rejectReason" outlined autogrow placeholder="Enter your rejected reason" />
          </div>
          <div class="self-end flex q-gutter-md">
            <q-btn rounded flat no-caps label="Cancel" class="text-grey-7" style="border: 1px solid" @click="isRejectDialogShow = false" />
            <q-btn
              rounded
              flat
              no-caps
              label="Confirm"
              class="bg-teal-4 text-white"
              @click="() => onRejectVerification(data._id)"
              :disable="!rejectReason?.length" />
          </div>
        </div>
      </q-dialog>

      <q-dialog v-model="isApproveDialogShow" persistent>
        <div class="column no-wrap flex-center bg-teal-1 q-pa-md" style="width: 30rem">
          <div class="q-pa-lg full-width">
            <div>Approve as a "{{ data?.role === 'teacher' ? 'Teacher' : 'Student' }} Amabassador"</div>
          </div>
          <div class="self-end flex q-gutter-md">
            <q-btn rounded flat no-caps label="Cancel" class="text-grey-7" style="border: 1px solid" @click="isApproveDialogShow = false" />
            <q-btn rounded flat no-caps label="Confirm" class="bg-teal-4 text-white" @click="() => onApproveVerification(data._id)" />
          </div>
        </div>
      </q-dialog>

      <q-dialog v-model="isServiceRoleDialogShow" persistent>
        <div class="column no-wrap flex-center bg-teal-1 q-pa-md" style="width: 30rem">
          <div class="q-pa-lg full-width">
            <div class="text-subtitle2 q-mb-sm">Choose service roles</div>
            <div class="column">
              <div v-for="option in computedServiceRoles" :key="option.value" class="flex items-center">
                <div v-if="option.value === 'onCampus' && !isCampusVerified" class="flex items-center" style="margin-left: 2.5rem">
                  <div>{{ option.label }}</div>
                  <q-btn v-if="option?.desc" icon="o_help_outline" dense no-caps round flat color="grey" @click.stop="() => onServiceRoleHelpClick(option)" />
                </div>
                <q-checkbox
                  v-else
                  v-model="currentServiceRoles"
                  :val="option.value"
                  :label="option.label"
                  :disable="checkAtLastOneServiceRole(option) || (serviceRolesUser.length && !serviceRolesUser.includes(option.value))">
                </q-checkbox>
                <q-btn v-if="option?.desc" icon="o_help_outline" dense no-caps round flat color="grey" @click.stop="() => onServiceRoleHelpClick(option)">
                </q-btn>
              </div>
            </div>
          </div>
          <div class="self-end flex q-gutter-md">
            <q-btn rounded flat no-caps label="Cancel" class="text-grey-7" style="border: 1px solid" @click="isServiceRoleDialogShow = false" />
            <q-btn rounded flat no-caps label="Confirm" class="bg-teal-4 text-white" @click="() => onPatchServiceRoles(data._id)" />
          </div>
        </div>
      </q-dialog>

      <q-dialog v-model="isServiceRoleHelpDialogShow">
        <q-card>
          <q-card-section class="row items-center q-pb-none">
            <div class="text-h6">{{ currentServiceRoleItem?.descTitle || '-' }}</div>
            <q-space />
            <q-btn icon="close" flat round dense v-close-popup />
          </q-card-section>
          <q-card-section>
            <q-separator class="q-mb-md" />
            <p class="text-subtitle2">{{ currentServiceRoleItem?.desc || '-' }}</p>
          </q-card-section>
        </q-card>
      </q-dialog>
    </q-page>
  </div>
</template>

<script setup>
import {ref, computed, onMounted} from 'vue'
import {useRoute} from 'vue-router'
import {date} from 'quasar'
import {TIME_FORMAT, getAttachmentType, getFileIcon} from 'src/boot/const'
import nameFormatter from 'src/utils/formatters/nameFormatter'
import MaterialItem from 'src/components/material/MaterialItem.vue'
import MaterialDialog from 'src/pages/class/components/MaterialDialog.vue'

import TextItem from 'src/components/utils/TextItem.vue'
import AgreementItem from 'src/components/utils/AgreementItem.vue'
import MessageButton from 'src/pages/teacher-verification/components/MessageButton.vue'
import useSchool from 'src/composables/common/useSchool'
import useAmbassador from 'src/composables/account/ambassador/useAmbassador'
import useTeacherVerificationConfig from 'src/composables/account/teacher-verification/useTeacherVerificationConfig'
import ChatMessage from 'src/components/ChatMessage/Index.vue'

const $route = useRoute()
const {userId} = useSchool()
const {getOneById, patchOneById, isHistoricalEvidenceExpend} = useAmbassador()
const {getTeacherVerificationConfig} = useTeacherVerificationConfig()

const currentId = computed(() => $route.params.id)
const data = ref(null)
const currentKey = ref('ambassador')
const config = ref(null)
async function getCurrentConfig() {
  const key = currentKey.value
  $q.loading.show()
  const res = await getTeacherVerificationConfig(key)
  config.value = res
  $q.loading.hide()
}

onMounted(async () => {
  await getCurrentConfig()
  await init()
})

async function init() {
  const res = await getOneById(currentId.value)
  if (!Array.isArray(res?.countryCode)) {
    if (res?.countryCode && typeof res?.countryCode === 'string') res.countryCode = [res?.countryCode]
    else res.countryCode = []
  }
  data.value = res
}

const rejectReason = ref('')
const isRejectDialogShow = ref(false)

async function onRejectVerification(id) {
  const dto = {status: -1, reason: rejectReason.value}
  dto.$set = {
    'approval.approved': new Date(),
    'approval.approver': userId.value,
  }
  await patchOneById(id, dto)
  await init()
  isRejectDialogShow.value = false
}

const approveReason = ref('')
const isApproveDialogShow = ref(false)

function onApproveVerificationClick() {
  isApproveDialogShow.value = true
  approveReason.value = ''
}

async function onApproveVerification(id) {
  try {
    $q.loading.show()
    const dto = {}
    if (data.value.status !== 2) dto.status = 2
    dto.$set = {
      'approval.approved': new Date(),
      'approval.approver': userId.value,
    }
    await patchOneById(id, dto)
    await init()
  } catch (error) {
    console.error(error)
  } finally {
    isApproveDialogShow.value = false
    $q.loading.hide()
  }
}

const isPreviewDialogShow = ref(false)
const currentPreviewData = ref(null)

function onPreview(item) {
  currentPreviewData.value = item
  isPreviewDialogShow.value = true
}

function mapToMaterial(e) {
  const dto = {...e, _id: e.hash, key: e.hash, desc: e.filename}
  delete dto.type
  return dto
}

const currentServiceRoles = ref([])
const isServiceRoleDialogShow = ref(false)
async function onPatchServiceRoles(id) {
  try {
    $q.loading.show()
    const dto = {serviceRoles: currentServiceRoles.value}
    await patchOneById(id, dto)
    await init()
  } catch (error) {
    console.error(error)
  } finally {
    isServiceRoleDialogShow.value = false
    $q.loading.hide()
  }
}
const currentServiceRoleItem = ref(null)
const isServiceRoleHelpDialogShow = ref(false)
function onServiceRoleHelpClick(item) {
  currentServiceRoleItem.value = item
  isServiceRoleHelpDialogShow.value = true
}

async function getMessageList() {
  const res = await App.service('service-auth-message').find({
    query: {
      rid: currentId.value,
      $limit: 500,
      $sort: {updatedAt: 1},
      type: 'ambassador-auth',
    },
  })
  const list = res?.data?.map((item) => {
    return {
      message: item?.message,
      time: item?.createdAt,
      avatar: item?.isAdmin ? '/v2/img/logo2.png' : item?.userInfo?.avatar,
      send: item?.isAdmin,
      attachments: item?.attachments,
    }
  })
  return list
}

const rejectMessage = async () => {
  await getMessageList()
  $q.dialog({
    component: ChatMessage,
    componentProps: {
      role: 'admin',
      rid: data.value?._id,
      type: 'service-auth',
      mode: 'reject',
    },
  }).onOk(async (dataRes) => {
    try {
      $q.loading.show()
      rejectReason.value = dataRes?.message || dataRes || ''
      onRejectVerification(data.value?._id)
      $q.notify({type: 'positive', message: 'Reject successfully'})
    } catch (error) {
      console.error(error)
      $q.notify({type: 'negative', message: error.message})
    } finally {
      $q.loading.hide()
    }
  })
}
</script>

<style lang="scss" scoped>
.item-wrapper {
  border: 1px solid #ddd;
  border-radius: 0.5rem;
  background: #fafafa;
  overflow: hidden;
}
.pre_box {
  position: relative;
  .btn {
    position: absolute;
    right: 0;
    bottom: 0;
  }
}
</style>
