<template>
  <div class="row items-center justify-between">
    <div class="text-weight-medium col text-h5">
      <q-btn flat round dense size="lg" icon="arrow_back" @click="back"></q-btn>
      {{ packageData.name }}
    </div>
    <div class="text-subtitle2 text-teal" v-if="!allPricesAreHidden">USD {{ lowestPrice }}</div>
  </div>

  <q-banner inline-actions rounded class="bg-amber-2 text-black q-my-md" v-if="!bannerClosed && bannerMessage?.message">
    {{ bannerMessage.message }}
    <template v-slot:action>
      <q-btn flat rounded icon="close" class="q-pa-sm" @click="bannerClosed = true" />
    </template>
  </q-banner>

  <div class="q-my-md">
    <q-img spinner-color="white" fit="cover" class="fit rounded-borders-md" :ratio="16 / 5" :src="hashToUrl(packageData?.cover) || '/v2/img/avatar.png'">
    </q-img>
  </div>
  <div class="text-negative" v-if="bannerMessage?.approved">Status: Approved</div>
  <template v-if="!readonly">
    <div class="row justify-between q-py-sm" v-if="!isAdmin && (userEnrollStatus || packageBought)">
      <div class="text-negative" v-if="userEnrollStatus">Status: {{ userEnrollStatus }}</div>
      <div class="text-negative hidden" v-if="packageBought">Purchased</div>
    </div>
    <div class="row q-gutter-md">
      <q-btn v-if="btnOfferShow" :to="toOffer()" class="col" color="primary" rounded no-caps label="View offer"></q-btn>
      <q-btn
        v-for="(action, index) in computedActions"
        :key="index"
        @click="action.fn"
        class="col"
        color="primary"
        :disable="disableAllActions"
        rounded
        no-caps
        :label="action.label"></q-btn>
      <q-btn
        v-if="isAdmin || !applyActions"
        @click="toApply"
        class="col"
        color="primary"
        :disable="disableAllActions"
        rounded
        no-caps
        :label="isAdmin ? 'Share to apply' : 'Apply'"></q-btn>
    </div>
    <template v-if="showCountdown">
      <div class="text-center q-mt-md" v-if="deadline">
        <CommonCountdown
          v-if="!expired && new Date(deadline) > new Date()"
          @expired="onExpired"
          :deadline="deadline"
          :prepend="applicationApproved || interviewIsInvited ? 'Enrollment close on' : 'Application close on'"
          ticking></CommonCountdown>
        <div v-else>{{ applicationApproved || interviewIsInvited ? 'Enrollment' : 'Application' }} expired</div>
      </div>
      <div class="text-center q-mt-md" v-else-if="interviewPurchaseExpired">Enrollment expired</div>
    </template>
  </template>
  <div class="q-my-md">
    <div class="text-subtitle1">The current service package can provide service for the following subjects</div>
    <q-card class="q-mt-md q-py-md rounded-borders-md">
      <q-card-section class="q-py-none">
        <PackageChips :pack="packageData" :is-educator="false"></PackageChips>
      </q-card-section>
    </q-card>
  </div>
  <PackagePacket :packageData="packageData" :duration-only="false"></PackagePacket>
  <q-card class="rounded-borders-md q-my-md">
    <q-card-section class="text-subtitle1"> Value of the service package </q-card-section>
    <q-card-section class="q-pt-none">
      <div class="q-my-sm text-body2" v-for="(point, i) in packageData?.points" :key="i">
        {{ point }}
      </div>
    </q-card-section>
  </q-card>
  <q-card class="rounded-borders-md q-my-md" v-if="packageData?.attachments?.length">
    <q-card-section class="text-subtitle1"> Promotional material</q-card-section>
    <q-card-section class="q-pt-none">
      <PreviewLists :files="packageData.attachments"></PreviewLists>
    </q-card-section>
  </q-card>
  <div v-for="item in list" :key="item._id" class="q-mt-md">
    <template v-if="item?.type === 'lecture'">
      <div class="text-primary text-weight-medium q-pb-sm">
        {{ sysMap[item.premium?.subject]?.name }}
      </div>
      <ServicePrePackage
        @updateData="onLectureUpdate($event, item)"
        :mentorPrice="!allPricesAreHidden"
        :lecturePrice="!allPricesAreHidden"
        nonumber
        :item="item"
        :discount="totalDiscount" />
    </template>
    <template v-if="item?.type === 'carer'">
      <CarerPackage @updateData="onCarerUpdate($event, item)" :carerPrice="!allPricesAreHidden" :item="item" :discount="totalDiscount" />
    </template>
  </div>
  <div class="row justify-end q-py-md" v-if="!discountEnded && packageData?.discountConfig?.end && !schoolPriceData._id">
    <div class="text-light-blue-6">
      Premium lecture discount ends in
      <CountDown :deadTime="packageData.discountConfig.end" @end="onDiscountEnd" />
    </div>
  </div>
  <div class="rounded-borders-md q-my-md">
    <q-card-section>
      <q-item dense class="q-pa-none">
        <q-item-section>
          <q-item-label>No. of service</q-item-label>
        </q-item-section>
        <q-item-section side>
          <q-item-label>
            {{ contentOrientatedEnableNoOfService }}
          </q-item-label>
        </q-item-section>
      </q-item>
    </q-card-section>
  </div>
  <div class="q-py-md">
    <Policy />
  </div>
</template>

<script setup>
import {ref, onMounted, inject, watch, computed, onUnmounted, openBlock} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {date} from 'quasar'
import {pubStore} from 'stores/pub'
import {pointStore} from 'stores/point'
import {UserEnrollStatus} from 'src/pages/premCpack/consts'
import PackagePacket from 'components/PackagePacket.vue'
import PackageChips from 'components/PackageChips.vue'
import useSubject from 'src/composables/account/academic/useSubject'
import PromptPage from 'components/PromptPage.vue'
import CountDown from 'src/pages/order/CountDown.vue'
import useSchool from 'src/composables/common/useSchool'
import CommonCountdown from 'components/CommonCountdown.vue'
import ServicePrePackage from './ServicePrePackage.vue'
import CarerPackage from 'src/pages/order/components/CarerPackage.vue'

import PreviewLists from 'src/components/PreviewLists.vue'
import {calServiceDiscount} from 'src/pages/sys/package/const'
import Policy from 'src/pages/service/Policy.vue'

/*
  consts
*/

const props = defineProps({
  item: Object,
  back: Function,
  readonly: Boolean,
})
const pub = pubStore()
const route = useRoute()
const router = useRouter()
const id = ref(route.params.id)

const packageData = ref({...props.item})

const {sysMap} = useSubject()
const {isLogin, isAdmin, isStudent, isSchool, schoolId} = useSchool()

const sharedSchool = ref(route?.query?.sharedSchool)

const schoolPriceData = ref({})
const servicePackApply = ref({})
const applyActions = ref(null)
const btnOfferShow = ref(false)
const list = ref([])

const shareLink = ref(null)
const discountEnded = ref(false)

const expired = ref(false)
const applicationApproved = ref(false)
const interviewIsInvited = ref(false)
const packageBought = ref(false)
const bannerClosed = ref(false)
const updatePrice = ref(null)
const updatedItems = ref([])

/*
  computeds
*/

const totalDiscount = computed(() => {
  return calServiceDiscount(packageData.value?.discountConfig)
})

const showCountdown = computed(() => {
  return (
    !isAdmin.value &&
    !isViewApplication.value &&
    !(servicePackApply.value.status === 1 && (!sharedSchool.value || (sharedSchool.value && schoolPriceData.value?._id && !schoolPriceData.value.priceEnable)))
  )
})

const isViewApplication = computed(() => {
  return servicePackApply.value?._id && servicePackApply.value.status == 0 && !servicePackApply.value.interviewInvited
})

const userEnrollStatus = computed(() => {
  return UserEnrollStatus(servicePackApply.value)
})

const isB2C = computed(() => {
  return !!servicePackApply.value?.sharedSchool || !!sharedSchool.value
})

const contentOrientatedEnableNoOfService = computed(() => {
  return packageData.value.contentOrientated.reduce((sum, item) => sum + item.times, 0)
})

const bannerMessage = computed(() => {
  let message = ''
  let approved = false
  if (applicationNotApprovedAndInterviewInvited.value) {
    //https://github.com/zran-nz/bug/issues/5052#issuecomment-2337497742
    //https://github.com/zran-nz/bug/issues/5192#issuecomment-2335022351
    message = 'We suggest to complete the interview within 3 days, otherwise it may result in enrolment failure.'
  }

  return {message, approved}
})

const computedActions = computed(() => {
  if (isAdmin.value && schoolId.value && schoolPriceData.value?._id) {
    return [{label: 'Buy', fn: toBuy}]
  } else if (!isAdmin.value && applyActions.value) {
    return applyActions.value
  }

  return null
})

const allPricesAreHidden = computed(() => {
  return isB2C.value && (!schoolPriceData.value.priceEnable || (schoolPriceData.value.priceEnable && !applyActions.value))
})

const interviewPurchaseExpired = computed(() => {
  return servicePackApply.value?.interviewPurchaseExpired
})

const applicationNotApprovedAndInterviewInvited = computed(() => {
  return servicePackApply.value?.status == 0 && servicePackApply.value?.interviewInvited
})

const disableAllActions = computed(() => {
  return (expired.value || new Date(deadline.value) < new Date()) && !isAdmin.value && (isB2C.value || interviewIsInvited.value)
})

const deadline = computed(() => {
  //若从管理员分享链接进入（通过B端分销给C端）的详情页，最上方显示管理员所设置的报名倒计时】
  //https://github.com/zran-nz/bug/issues/4616
  //https://github.com/zran-nz/bug/issues/5192#issuecomment-2350925949
  return applicationApproved.value
    ? servicePackApply.value.purchaseExpireAt
    : applicationNotApprovedAndInterviewInvited.value
      ? servicePackApply.value.interviewPurchaseExpireAt
      : schoolPriceData.value?.deadline
})

const packageDiscount = computed(() => {
  return packageData.value?.discount
})

const lowestPrice = computed(() => {
  //https://github.com/zran-nz/bug/issues/5297#issuecomment-2373603286
  const price = updatePrice.value
    ? updatePrice.value
    : isAdmin.value
      ? packageData.value.contentOrientatedConfig?.schoolPrice
      : packageData.value.contentOrientatedConfig?.price
  return ((price || 0) / 100).toFixed(2)
})

/*
  methods
*/

const onLectureUpdate = (e, item) => {
  if (!updatedItems.value.includes(item._id)) {
    updatedItems.value.push(item._id)
    updatePrice.value += (e?.lecture?.price || 0) + (e?.mentoring?.price || 0)
  }
}

const onCarerUpdate = (e, item) => {
  if (!updatedItems.value.includes(item._id)) {
    updatedItems.value.push(item._id)
    updatePrice.value += e?.carer?.price || 0
  }
}

const onExpired = () => {
  expired.value = true
  if (applicationNotApprovedAndInterviewInvited.value) {
    location.reload()
  }
}

const validate = () => {
  if (!isLogin.value) {
    router.push({path: '/login', query: {back: location.pathname + location.search}})
    return false
  }
  let authorized = false
  const role = packageData.value.serviceRoles
  const serviceType = packageData.value.mentoringType
  const consultantType = packageData.value.consultant?.type
  const isForEducators = ['teacherTraining', 'teacherTrainingSubject'].includes(serviceType)

  //~https://github.com/zran-nz/bug/issues/4968#issuecomment-2205167481~
  //~https://github.com/zran-nz/bug/issues/5126#issuecomment-2285227528~
  //https://github.com/zran-nz/bug/issues/5126#issuecomment-2339862361
  //https://github.com/zran-nz/bug/issues/5342
  if (
    role == 'mentoring' &&
    (isAdmin.value ||
      (!isForEducators &&
        isStudent.value &&
        (!sharedSchool.value ||
          (sharedSchool.value &&
            (!schoolPriceData.value?.withinSchool || (schoolPriceData.value?.withinSchool && schoolPriceData.value?.students?.includes(pub.user._id)))))) ||
      (isForEducators &&
        !isStudent.value &&
        (!sharedSchool.value ||
          (sharedSchool.value &&
            (!schoolPriceData.value?.withinSchool || (schoolPriceData.value?.withinSchool && schoolPriceData.value?.teachers?.includes(pub.user._id)))))))
  ) {
    authorized = true
  }

  console.log(pub.user._id, role, serviceType, authorized, '<======================')
  if (!authorized) {
    $q.dialog({
      component: PromptPage,
      componentProps: {
        type: 'unauthorized',
        title: 'You have no access to the page',
      },
    })
    return false
  } else {
    return true
  }
}

function onDiscountEnd() {
  discountEnded.value = true
}

const toApply = async () => {
  if (!validate()) {
    return
  }

  const {backgroundCheck, interviewPack} = packageData.value

  const path = isAdmin.value ? `/premcpack/schoolPrice/${packageData.value._id}` : `/premcpack/enroll/${packageData.value._id}`

  router.push({
    path,
    query: {
      back: route.path,
      inviteCode: route?.query?.inviteCode,
      sharedSchool: sharedSchool.value,
      inviteSource: route?.query?.inviteSource,
      inviteSourceId: route?.query?.inviteSourceId,
      schoolInviter: route?.query?.schoolInviter,
    },
  })
}

const toOffer = () => {
  if (!validate()) {
    return
  }
  return {path: '/detail/offer/' + servicePackApply.value._id}
}

const toApplication = async () => {
  if (!validate()) {
    return
  }
  const query = {
    back: route.path,
    sharedSchool: sharedSchool.value,
  }
  router.push({
    path: `/premcpack/enroll/${id.value}`,
    query,
  })
}

const toBuyInterview = async () => {
  const query = {
    back: route.query.back,
    servicePackApply: servicePackApply.value._id,
    inviteCode: route?.query?.inviteCode,
    inviteSource: route?.query?.inviteSource,
    inviteSourceId: route?.query?.inviteSourceId,
    schoolInviter: route?.query?.schoolInviter,
  }

  orderCheck(false, packageData.value.interviewPack._id, () => {
    router.push({
      path: `/order/confirm/service/${packageData.value.interviewPack._id}`,
      query,
    })
  })
}

const orderCheck = async (isPremium, goodsId, cb) => {
  const checkQuery = isPremium
    ? {
        servicePremium: goodsId,
        sharedSchool: sharedSchool.value,
        links: [],
      }
    : {
        links: [{id: goodsId, style: 'service'}],
      }

  $q.loading.show()
  const orderCheck = await App.service('order').get('checkLinks', {
    query: checkQuery,
  })
  $q.loading.hide()
  if (orderCheck?.unpaidOrderId?.length > 0) {
    router.push({path: `/order/detail/${orderCheck?.unpaidOrderId?.[0]}`, query: {back: route.query.back}})
    return
  }
  if (cb) {
    cb()
  }

  setTimeout(() => {
    $q.loading.hide()
  }, 3000)
}

const toBuy = async () => {
  if (!validate()) {
    return
  }

  const query = {
    back: route.query.back,
    inviteCode: route?.query?.inviteCode,
    inviteSource: route?.query?.inviteSource,
    inviteSourceId: route?.query?.inviteSourceId,
    schoolInviter: route?.query?.schoolInviter,
  }

  if (sharedSchool.value) {
    query.sharedSchool = sharedSchool.value
  }

  const checkQuery = {
    servicePremium: id.value,
    sharedSchool: sharedSchool.value,
    links: [],
  }

  $q.loading.show()
  const orderCheck = await App.service('order').get('checkLinks', {
    query: checkQuery,
  })
  $q.loading.hide()
  if (orderCheck?.unpaidOrderId?.length > 0) {
    router.push({path: `/order/detail/${orderCheck?.unpaidOrderId?.[0]}`, query: {back: route.query.back}})
    return
  }
  router.push({
    path: `/order/confirm/service_premium/${id.value}`,
    query,
  })

  setTimeout(() => {
    $q.loading.hide()
  }, 3000)
}

const getSchoolPriceData = async () => {
  const rs = await App.service('service-pack-school-price').find({
    query: {
      school: sharedSchool.value ? sharedSchool.value : isAdmin.value && schoolId.value ? schoolId.value : null,
      servicePack: id.value,
    },
  })
  if (rs?.data?.[0]) {
    schoolPriceData.value = rs.data[0]
  }
}

const getBoughtStatus = async () => {
  const resCheck = await App.service('order').get('checkLinks', {
    query: {
      links: [],
      servicePremium: id.value,
      sharedSchool: sharedSchool.value,
    },
  })
  if (resCheck?.orderId?.length) {
    packageBought.value = true
  }
}

const getApplyStatus = async () => {
  const rs = await App.service('service-pack-apply').find({
    query: {
      uid: pub.user?._id,
      sharedSchool: sharedSchool.value ? sharedSchool.value : null,
      servicePack: id.value,
    },
  })
  if (rs?.data?.[0]) {
    //https://github.com/zran-nz/bug/issues/5192#issuecomment-2350925949
    servicePackApply.value = rs.data[0]
    //console.log(servicePackApply.value, '<==============servicePackApply')
    const {status, interviewInvited} = rs.data[0]
    let actions = null
    if (status === 0) {
      // not approve
      if (interviewInvited) {
        actions = [{label: 'Buy interview package', fn: toBuyInterview}]
        interviewIsInvited.value = true
      } else {
        actions = [{label: 'View application', fn: toApplication}]
      }
    } else if (status === 1) {
      // approved
      applicationApproved.value = true
      // actions = [{label: 'View offer', fn: toOffer}]
      btnOfferShow.value = true
      if (!isB2C.value || (isB2C.value && schoolPriceData.value.priceEnable)) {
        actions = [{label: 'Buy', fn: toBuy}]
      }
    } else if (status === -1) {
      // rejected
      actions = [{label: 'View application', fn: toApplication}]
    }
    applyActions.value = actions
  }
}

const getContentOrientated = async () => {
  const data = packageData.value
  const premiumIds = data?.contentOrientated.map((e) => e.premium)
  const servicePackIds = data?.contentOrientated.map((e) => e.servicePack)

  if (premiumIds?.length) {
    let rs = await App.service('service-auth').get('unit', {query: {_id: {$in: premiumIds}}})

    rs?.data?.map((f) => {
      const content = data.contentOrientated.find((e) => f._id == e.premium)
      const item = {
        ...content,
        ...{
          premium: f,
        },
      }
      list.value.push(item)
    })
  }

  if (servicePackIds?.length) {
    let rs = await App.service('service-pack').find({query: {_id: {$in: servicePackIds}}})
    const associatedTaskIds = rs.data.map((sp) => sp.associatedTask?._id).filter(Boolean)

    let associatedTaskPacks = []

    if (associatedTaskIds.length) {
      const associatedResponse = await App.service('service-pack').find({
        query: {_id: {$in: associatedTaskIds}},
      })
      associatedTaskPacks = associatedResponse.data
    }
    list.value.map((e) => {
      const pack = rs.data?.find((f) => f._id == e.servicePack)
      if (pack?._id) {
        e.servicePack = pack
        const assocPack = associatedTaskPacks.find((asp) => asp._id == pack.associatedTask?._id)
        if (assocPack) {
          e.associatedTaskPack = assocPack
        }
      }
    })
  }

  //https://github.com/zran-nz/bug/issues/5297#issuecomment-2367711344
  if (servicePackApply.value?.contentOrientated?.length && applyActions.value) {
    const priceMap = new Map(
      //schoolPriceData.value.contentOrientated.map((item) => {
      servicePackApply.value.contentOrientated.map((item) => {
        return [`${item.premium}-${item.servicePack || 0}`, item.price]
      })
    )
    list.value = list.value
      .map((item) => {
        const key = `${item.premium._id}-${item.servicePack?._id || 0}`
        if (priceMap.has(key)) {
          return {...item, price: priceMap.get(key)} // Update price
        }
        return null // Mark for removal
      })
      .filter((item) => item !== null)
  }

  if (data?.carerPack?._id) {
    let carerPackInfo

    carerPackInfo = await App.service('service-pack').get(data?.carerPack._id)
    if (carerPackInfo) {
      data.carerPackInfo = carerPackInfo
    }
  }

  list.value.map((e) => {
    if (!sharedSchool.value) {
      e.discountConfig = data?.discountConfig
    }
    e.type = 'lecture'
    e.splitSale = data.splitSale
    e.choose = true
  })

  if (data?.carerPackInfo) {
    list.value.push({
      ...data?.carerPackInfo,
      type: 'carer',
      times: data.carerPack.times,
    })
  }
  console.log('list', list.value)
}

const isSchoolAdmin = () => {
  return pub.schoolUserList?.some((e) => e.school == route.query.school && e.role?.includes('admin'))
}

onMounted(async () => {
  if (!props.readonly && pub?.user?._id) {
    if (sharedSchool.value || (isAdmin.value && schoolId.value)) {
      await getSchoolPriceData()
    }
    await getApplyStatus()
    await getBoughtStatus()
  }
  await getContentOrientated()
})
</script>
