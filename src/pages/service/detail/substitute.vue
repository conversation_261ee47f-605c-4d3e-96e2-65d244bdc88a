<template>
  <div class="row items-center justify-between">
    <div class="text-weight-medium col text-h5">
      <q-btn flat round dense size="lg" icon="arrow_back" @click="back"></q-btn>
      {{ packageData.name }}
    </div>
    <div class="text-subtitle2 text-teal">From {{ subLowestPrice }}/hour</div>
  </div>

  <div class="q-my-md">
    <q-img spinner-color="white" fit="cover" class="fit rounded-borders-md" :ratio="16 / 5" :src="hashToUrl(packageData?.cover) || '/v2/img/avatar.png'">
    </q-img>
  </div>
  <div class="q-my-md">
    <div class="text-subtitle1">The current service package can provide service for the following subjects</div>
    <q-card class="q-mt-md q-py-md rounded-borders-md">
      <q-card-section class="q-py-none">
        <PackageChips :pack="packageData" :is-educator="false"></PackageChips>
      </q-card-section>
    </q-card>
  </div>
  <q-card class="rounded-borders-md q-my-md">
    <q-card-section class="text-subtitle1"> Value of the service package </q-card-section>
    <q-card-section class="q-pt-none">
      <div class="q-my-sm text-body2" v-for="(point, i) in packageData?.points" :key="i">
        {{ point }}
      </div>
    </q-card-section>
  </q-card>
  <q-card class="rounded-borders-md q-my-md" v-if="packageData?.attachments?.length">
    <q-card-section class="text-subtitle1"> Promotional material</q-card-section>
    <q-card-section class="q-pt-none">
      <PreviewLists :files="packageData.attachments"></PreviewLists>
    </q-card-section>
  </q-card>
  <q-card class="rounded-borders-md q-my-md">
    <template v-if="packageData?.isOnCampus">
      <q-card-section class="q-pb-none row justify-between">
        <q-tabs
          v-model="onCampusTab"
          stretch
          @update:model-value="onCampusTabChange"
          align="left"
          indicator-color="primary"
          active-color="primary"
          inline-label
          mobile-arrows
          shrink
          class="col-12 q-mb-md">
          <q-tab v-for="o in packageData?.onCampusPrice" :key="o.city" :name="o.city" :label="o.city" no-caps />
        </q-tabs>
        <div>Choice of package size</div>
      </q-card-section>
      <q-card-section class="q-mb-lg">
        <q-item v-for="(discount, i) in onCampusPrice?.discount" :key="i" class="col row">
          <q-item-section class="">
            <q-item-label class="text-subtitle1"> {{ discount.count }} hours </q-item-label>
          </q-item-section>
          <q-item-section side>
            <q-item-label class="text-secondary text-h5 text-weight-bold"> USD {{ (discount.count * 100 * onCampusPrice?.price) / 10000 }} </q-item-label>
          </q-item-section>
        </q-item>
      </q-card-section>
    </template>
    <template v-else>
      <q-card-section class="q-pb-none row justify-between">
        <div>Choice of package size</div>
      </q-card-section>
      <q-card-section class="q-mb-lg">
        <q-item v-for="(discount, i) in packageDiscount" :key="i" class="col row">
          <q-item-section class="">
            <q-item-label class="text-subtitle1"> {{ discount.count }} hours </q-item-label>
          </q-item-section>
          <q-item-section side>
            <q-item-label class="text-secondary text-h5 text-weight-bold"> USD {{ (discount.count * 100 * packageData?.price) / 10000 }} </q-item-label>
          </q-item-section>
        </q-item>
      </q-card-section>
    </template>
  </q-card>
  <div class="q-gutter-md q-pr-md q-mb-md" v-if="!readonly">
    <q-btn @click="toBuy" class="fit" color="primary" outline rounded no-caps label="Buy" />
    <Policy />
  </div>
</template>

<script setup>
import {ref, onMounted, inject, watch, computed, onUnmounted, openBlock} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {date} from 'quasar'
import {pubStore} from 'stores/pub'
import PackageChips from 'components/PackageChips.vue'
import useSubject from 'src/composables/account/academic/useSubject'

import PromptPage from 'components/PromptPage.vue'

import useSchool from 'src/composables/common/useSchool'

import PreviewLists from 'src/components/PreviewLists.vue'
import Policy from 'src/pages/service/Policy.vue'

/*
  consts
*/

const props = defineProps({
  item: Object,
  back: Function,
  readonly: Boolean,
})

const pub = pubStore()
const route = useRoute()
const router = useRouter()
const id = ref(route.params.id)

const {isLogin, isAdmin, isStudent, isSchool, schoolId} = useSchool()

const packageData = ref({...props.item})

const onCampusTab = ref(null)
const onCampusPrice = ref({})

/*
  computeds
*/

const packageDiscount = computed(() => {
  return packageData.value?.discount
})

const subLowestPrice = computed(() => {
  if (packageData.value?.isOnCampus) {
    return (onCampusPrice.value?.price / 100).toFixed(2)
  } else {
    return (packageData.value?.price / 100).toFixed(2)
  }
})

/*
  methods
*/

const validate = () => {
  if (!isLogin.value) {
    router.push({path: '/login', query: {back: location.pathname + location.search}})
    return false
  }
  let authorized = false
  const role = packageData.value.serviceRoles
  const serviceType = packageData.value.mentoringType
  if (
    role == 'substitute' &&
    ['academic', 'essay', 'overseasStudy', 'teacherTraining', 'teacherTrainingSubject', 'steam'].includes(serviceType) &&
    (isStudent.value || isAdmin.value)
  ) {
    authorized = true
  }

  console.log(pub.user._id, role, serviceType, authorized, '<======================')
  if (!authorized) {
    $q.dialog({
      component: PromptPage,
      componentProps: {
        type: 'unauthorized',
        title: 'You have no access to the page',
      },
    })
    return false
  } else {
    return true
  }
}

const toBuy = async () => {
  if (!validate()) {
    return
  }

  const checkQuery = {
    links: [{id: id.value, style: 'service'}],
  }

  $q.loading.show()
  const orderCheck = await App.service('order').get('checkLinks', {
    query: checkQuery,
  })
  $q.loading.hide()
  if (orderCheck?.unpaidOrderId?.length > 0) {
    router.push({path: `/order/detail/${orderCheck?.unpaidOrderId?.[0]}`, query: {back: route.query.back}})
    return
  } else {
    router.push({
      path: `/order/confirm/service_substitute/${id.value}`,
      query: {
        back: route.query.back,
        inviteCode: route?.query?.inviteCode,
        inviteSource: route?.query?.inviteSource,
        inviteSourceId: route?.query?.inviteSourceId,
        schoolInviter: route?.query?.schoolInviter,
      },
    })
  }

  setTimeout(() => {
    $q.loading.hide()
  }, 3000)
}

const onCampusTabChange = (tab) => {
  onCampusPrice.value = packageData.value.onCampusPrice.find((e) => e.city == tab)
}

onMounted(async () => {
  if (packageData.value?.isOnCampus) {
    const tab = packageData.value?.onCampusPrice?.[0]?.city
    onCampusTab.value = tab
    onCampusTabChange(tab)
  }
})
</script>
