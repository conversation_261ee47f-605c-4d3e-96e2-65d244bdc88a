<template>
  <div class="row items-center justify-between">
    <div class="text-weight-medium col text-h5">
      <q-btn flat round dense size="lg" icon="arrow_back" @click="back"></q-btn>
      {{ packageData.name }}
    </div>
    <div class="text-subtitle2 text-teal">
      <ShowPoint v-if="isPointMode" :price="lowestPrice * 100" :type="categoryMap[packageData.serviceRoles]" />
      <template v-else> From USD {{ lowestPrice }} </template>
    </div>
  </div>
  <div class="q-my-md">
    <q-img spinner-color="white" fit="cover" class="fit rounded-borders-md" :ratio="16 / 5" :src="hashToUrl(packageData?.cover) || '/v2/img/avatar.png'">
    </q-img>
  </div>
  <div class="q-my-md">
    <div class="text-subtitle1">The current service package can provide service for the following subjects</div>
    <q-card class="q-mt-md q-py-md rounded-borders-md">
      <q-card-section class="q-py-none">
        <PackageChips :pack="packageData" :is-educator="false"></PackageChips>
      </q-card-section>
    </q-card>
  </div>
  <PackagePacket :packageData="packageData" :duration-only="false"></PackagePacket>
  <q-card class="rounded-borders-md q-my-md">
    <q-card-section class="text-subtitle1"> Value of the service package </q-card-section>
    <q-card-section class="q-pt-none">
      <div class="q-my-sm text-body2" v-for="(point, i) in packageData?.points" :key="i">
        {{ point }}
      </div>
    </q-card-section>
  </q-card>
  <q-card class="rounded-borders-md q-my-md" v-if="packageData?.attachments?.length">
    <q-card-section class="text-subtitle1"> Promotional material</q-card-section>
    <q-card-section class="q-pt-none">
      <PreviewLists :files="packageData.attachments"></PreviewLists>
    </q-card-section>
  </q-card>

  <q-card class="rounded-borders-md q-my-md">
    <q-card-section class="q-pb-none row justify-between">
      <div>Choice of package size</div>
      <div class="text-light-blue-6" v-if="!discountEnded && packageData?.discountConfig?.end">
        Discount ends in
        <CountDown :deadTime="packageData.discountConfig.end" @end="onDiscountEnd" />
      </div>
    </q-card-section>
    <q-card-section class="q-mb-lg">
      <q-item v-for="(discount, i) in packageDiscount" :key="i" class="col row">
        <template v-if="discount._id === 'customize_point'">
          <q-item-section class="">
            <q-item-label class="text-subtitle1">
              <q-input
                :rules="[(val) => (val && val >= 0) || 'Value must be non-negative']"
                style="width: 120px; display: inline-block"
                v-model="pointPackageDiscount.count"
                @update:modelValue="onPointPackageDiscountUpdate"
                outlined
                dense
                type="number" />
              sessions
            </q-item-label>
          </q-item-section>
          <q-item-section side>
            <q-item-label><ShowPoint :price="discount?.price" :type="categoryMap[packageData.serviceRoles]" /></q-item-label>
          </q-item-section>
        </template>
        <template v-else>
          <q-item-section class="">
            <q-item-label class="text-subtitle1"> {{ discount.gifts ? discount.gifts + discount.count : discount.count }} sessions </q-item-label>
            <q-item-label v-if="discount.gifts" class="flex items-center text-yellow-9 text-body2">
              <q-icon name="redeem" class="q-pr-xs" size="xs"></q-icon>
              {{ ` Additional ${discount.gifts} free sessions as gifts` }}
            </q-item-label>
          </q-item-section>
          <q-item-section side v-if="isPointMode">
            <q-item-label v-if="!discountEnded && packageData.discountConfig.enable">
              <ShowPoint
                :price="(((discount.count * (100 - discount.discount) * (100 - packageData.discountConfig.discount)) / 100) * packageData?.price) / 100"
                :type="categoryMap[packageData.serviceRoles]" />
            </q-item-label>
            <q-item-label v-else class="text-secondary text-h5 text-weight-bold">
              <ShowPoint :price="(discount.count * (100 - discount.discount) * packageData?.price) / 100" :type="categoryMap[packageData.serviceRoles]" />
            </q-item-label>
          </q-item-section>
          <q-item-section side v-else>
            <q-item-label v-if="!discountEnded && packageData.discountConfig.enable">
              <span class="text-secondary text-h5 text-weight-bold">
                USD
                {{ (discount.count * (100 - discount.discount) * packageData?.price * (100 - packageData.discountConfig.discount)) / 100 / 10000 }}
              </span>
              <del class="q-pt-sm q-ml-sm">USD {{ (discount.count * (100 - discount.discount) * packageData?.price) / 10000 }}</del>
            </q-item-label>
            <q-item-label v-else class="text-secondary text-h5 text-weight-bold">
              USD {{ (discount.count * (100 - discount.discount) * packageData?.price) / 10000 }}
            </q-item-label>
          </q-item-section>
        </template>
      </q-item>
    </q-card-section>
  </q-card>
  <div v-if="associatedTask" class="q-mb-md">
    <div class="q-mb-md text-bold text-subtitle1">Associated Task</div>
    <TaskCard
      :isView="true"
      :isBuy="true"
      :task="associatedTask"
      :clickAllowed="true"
      :associateTaskDiscount="!discountEnded ? packageData?.discountConfig?.discount : 0" />
  </div>
  <div class="q-gutter-md q-pr-md q-mb-md" v-if="!readonly">
    <q-btn @click="toBuy" class="fit" color="primary" outline rounded no-caps :label="isPointMode ? 'Claim now' : 'Buy'" />
    <template v-if="isPointMode">
      <q-btn
        class="fit"
        color="primary"
        flat
        rounded
        no-caps
        label="Points redemption policy"
        target="_blank"
        href="/v2/com/agreement/all_users/points_redemption" />
    </template>
    <template v-else>
      <Policy />
    </template>
  </div>
</template>

<script setup>
import {ref, onMounted, inject, watch, computed, onUnmounted, openBlock} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {date} from 'quasar'
import {pubStore} from 'stores/pub'
import {pointStore} from 'stores/point'
import PackagePacket from 'components/PackagePacket.vue'
import PackageChips from 'components/PackageChips.vue'
import PromptPage from 'components/PromptPage.vue'
import CountDown from 'src/pages/order/CountDown.vue'
import ShowPoint from 'src/pages/point/components/ShowPoint.vue'
import useSchool from 'src/composables/common/useSchool'

import PreviewLists from 'src/components/PreviewLists.vue'
import {calServiceDiscount} from 'src/pages/sys/package/const'
import Policy from 'src/pages/service/Policy.vue'
import TaskCard from '../../../components/ServiceTask/TaskCard.vue'

/*
  consts
*/

const props = defineProps({
  item: Object,
  back: Function,
  readonly: Boolean,
  associatedTask: Object,
})

const pub = pubStore()
const route = useRoute()
const router = useRouter()
const id = ref(route.params.id)

const {isLogin, isAdmin, isStudent, isSchool, schoolId} = useSchool()

const isPointMode = ref(!!route?.query?.isPointMode)

const pStore = pointStore()

const packageData = ref({...props.item})

const discountEnded = ref(false)

const pointPackageDiscount = ref({
  count: 0,
  oriPrice: 0,
  price: 0,
  gifts: 0,
  _id: 'customize_point',
})

const categoryMap = {mentoring: 'service', correcting: 'correcting_service', premium: 'Premium content'}

/*
  computeds
*/

const packageDiscount = computed(() => {
  return isPointMode.value ? [...(packageData.value?.discount || []), pointPackageDiscount.value] : packageData.value?.discount || []
})

const lowestPrice = computed(() => {
  if (packageData.value?.discount?.length) {
    let fromPrice = 0
    let packagePrice
    packageData.value?.discount.map((discount) => {
      packagePrice = (discount.count * (100 - (packageData.value.discountConfig?.enable && !discountEnded.value ? discount.discount : 0))) / 100
      if (fromPrice === 0 || packagePrice <= fromPrice) {
        fromPrice = packagePrice
      }
    })
    return ((fromPrice * packageData.value?.price) / 100).toFixed(2)
  }
  return 0
})

/*
  methods
*/

const validate = () => {
  if (!isLogin.value) {
    router.push({path: '/login', query: {back: location.pathname + location.search}})
    return false
  }
  let authorized = false
  const role = packageData.value.serviceRoles
  const serviceType = packageData.value.mentoringType
  const consultantType = packageData.value.consultant?.type
  const isForEducators = ['teacherTraining', 'teacherTrainingSubject'].includes(serviceType)

  console.log(isStudent.value, 'isStudent222')
  console.log(packageData.value, '<======================')
  console.log(isForEducators, 'isForEducators')
  if (
    (role == 'mentoring' && ((!isForEducators && isStudent.value) || (isForEducators && !isStudent.value))) ||
    (role == 'correcting' &&
      ((!isStudent.value && !isSchool.value && ['academic', 'teacherTraining', 'teacherTrainingSubject', 'steam', 'essay'].includes(serviceType)) ||
        (isAdmin.value && ['academic'].includes(serviceType)))) ||
    (role == 'consultant' &&
      ((!['teacherTraining', 'teacherTrainingSubject'].includes(serviceType) && consultantType == 'interview' && isStudent.value) ||
        (consultantType == 'interviewTeacher' && !isStudent.value)))
  ) {
    authorized = true
  }

  console.log(pub.user._id, role, serviceType, authorized, '<======================')

  if (!authorized) {
    $q.dialog({
      component: PromptPage,
      componentProps: {
        type: 'unauthorized',
        title: 'You have no access to the page',
      },
    })
    return false
  } else {
    return true
  }
}

const onPointPackageDiscountUpdate = (choiceCount) => {
  if (choiceCount >= 0) {
    const itemList = (packageData.value?.discount || []).sort((a, b) => a.count - b.count)

    const nowDate = new Date()
    const endDate = new Date(packageData.value?.discountConfig?.end || nowDate.getTime() + 200)
    const diff = endDate.getTime() - nowDate.getTime()

    const hasDiscount = packageData.value?.discountConfig?.enable && diff > 0
    const isDiscount = packageData.value?.discountConfig?.enable && packageData.value?.discountConfig?.discount
    const price = hasDiscount ? (packageData.value?.price * (100 - packageData.value?.discountConfig?.discount)) / 100 : packageData.value?.price || 0
    let choiceItem = {}
    if (choiceCount < itemList[0].count) {
      choiceItem = {
        count: +choiceCount,
        discount: 0,
      }
    } else {
      for (let i = 0; i < itemList.length; i++) {
        if (choiceCount <= itemList[i].count) {
          choiceItem = itemList[i]
          break
        } else if (choiceCount > itemList[itemList.length - 1].count) {
          choiceItem = itemList[itemList.length - 1]
        }
      }
    }

    pointPackageDiscount.value.price = (choiceCount * (100 - choiceItem.discount) * price) / 100
  }
}

function onDiscountEnd() {
  discountEnded.value = true
}

const toBuy = async () => {
  if (!validate()) {
    return
  }
  let ChoiceSession = undefined
  if (route?.query?.isPointMode && pointPackageDiscount.value?.count > 0) {
    ChoiceSession = pointPackageDiscount.value?.count
  }

  const checkQuery = {
    links: [{id: id.value, style: 'service'}],
  }

  $q.loading.show()
  const orderCheck = await App.service('order').get('checkLinks', {
    query: checkQuery,
  })
  $q.loading.hide()
  if (orderCheck?.unpaidOrderId?.length > 0) {
    router.push({path: `/order/detail/${orderCheck?.unpaidOrderId?.[0]}`, query: {back: route.query.back}})
    return
  } else {
    router.push({
      path: `/order/confirm/service/${id.value}`,
      query: {
        back: route.query.back,
        inviteCode: route?.query?.inviteCode,
        isPointMode: route?.query?.isPointMode,
        inviteSource: route?.query?.inviteSource,
        inviteSourceId: route?.query?.inviteSourceId,
        schoolInviter: route?.query?.schoolInviter,
        ChoiceSession,
      },
    })
  }
  setTimeout(() => {
    $q.loading.hide()
  }, 3000)
}

onMounted(async () => {
  console.log('props', props.item)
})
</script>
