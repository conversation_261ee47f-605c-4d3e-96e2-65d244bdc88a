<template>
  <q-btn class="fit" color="primary" flat rounded no-caps label="Terms and policy" @click="dialog = true"></q-btn>
  <q-dialog v-model="dialog">
    <q-card>
      <q-card-section>
        <q-toolbar>
          <q-toolbar-title class="col text-center text-h6">Cancellation policy</q-toolbar-title>
        </q-toolbar>
      </q-card-section>
      <q-card-section class="col q-mx-lg text-subtitle1">
        <br />Associated task/Substitute service package <br />Within 30 days of purchase: Full refund of the amount paid for unused sessions or teaching time.
        <br />30 days after purchase: 70% refund of the amount for the unused sessions or teaching time (30% penalty applies).
        <br />
        <br />Other service packages <br />Free cancellation of the unused sessions (excluding gift sessions) within 14 days of purchase.
        <br />
        <q-btn class="q-pa-xs" flat color="primary" no-caps label="More" target="_blank" href="/v2/com/agreement/all_users/cancellation" />
      </q-card-section>

      <q-separator></q-separator>
      <q-card-section inset>
        <q-btn class="full-width bg-teal text-white" no-caps rounded label="I got it" v-close-popup> </q-btn>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script setup>
import {ref} from 'vue'

/*
  consts
*/

const dialog = ref(false)
</script>
