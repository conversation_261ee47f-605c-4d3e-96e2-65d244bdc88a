<template>
  <q-card class="bg-white rounded-borders-md">
    <q-card-section>
      <section @click="toLecture">
        <div class="row q-col-gutter-md">
          <div class="col-xs-12 col-sm-5">
            <q-img
              class="full-width rounded-borders-md"
              :ratio="$q.screen.gt.xs ? 16 / 12 : 16 / 9"
              fit="cover"
              :src="hashToUrl(item?.premium?.cover || item?.premium?.image || item?.premium?.unitSnapshot?.cover) || '/v2/img/no-img.png'" />
          </div>
          <div class="col-xs-12 col-sm-7">
            <div class="text-yellow-9">Premium lecture</div>
            <div class="ellipsis text-h6">
              {{ item?.premium?.unit?.name || item?.premium?.name || item?.premium?.desc }}
            </div>
            <PackageChips :pack="item?.premium" />
            <div class="row q-mt-md items-center" v-if="!nonumber">
              <div class="col-8 text-grey-7 text-caption">No of sessions required to complete this course</div>
              <div class="col-4 text-right">{{ item?.times }}</div>
            </div>
            <div class="q-mt-md text-right" v-if="lecturePrice">
              <div class="text-grey-6 text-body2 text-strike" v-if="lecture?.hasDiscount">USD {{ (lecture?.oriPrice / 100).toFixed(2) }}</div>
              <div class="text-bold text-primary q-mt-sm">USD {{ (lecture?.price / 100).toFixed(2) }}</div>
            </div>
          </div>
        </div>
      </section>
      <section class="q-mt-md" v-if="item?.servicePack" @click="toMentor">
        <div class="row q-col-gutter-md">
          <div class="col-xs-12 col-sm-5">
            <q-img
              class="full-width rounded-borders-md"
              :ratio="$q.screen.gt.xs ? 16 / 12 : 16 / 9"
              fit="cover"
              :src="hashToUrl(item?.servicePack?.cover || item?.servicePack?.image || item?.servicePack?.unitSnapshot?.cover) || '/v2/img/no-img.png'" />
          </div>
          <div class="col-xs-12 col-sm-7">
            <div class="text-yellow-9">1v1 mentor</div>
            <div class="ellipsis text-h6">
              {{ item?.servicePack?.name || item?.servicePack?.desc }}
            </div>
            <PackageChips :pack="item?.servicePack" />
            <div class="row q-mt-md items-center">
              <div class="col-12 text-grey-7 text-caption">
                duration
                <span class="q-ml-md text-black">{{ item?.servicePack?.duration }} mins with {{ item?.servicePack?.break }} mins break</span>
              </div>
              <div class="col-12 text-grey-7 text-caption">
                No of service
                <span class="q-ml-md text-black">{{ mentoring?.totalCount }}</span>
              </div>
              <div class="col-12 text-yellow-9" v-if="mentoring?.gifts">
                <q-icon name="redeem" size="xs" />
                {{ ` Additional ${mentoring?.gifts} free sessions as gifts` }}
              </div>
            </div>
            <div class="q-mt-md text-right" v-if="mentorPrice">
              <div class="text-bold text-primary q-mt-sm">
                USD {{ (mentoring?.price / 100).toFixed(2) }}
                <q-icon v-if="mentoring?.hasDiscount" class="cursor-pointer q-ml-sm" color="primary" size="1.5rem" name="help_outline">
                  <q-tooltip>
                    Original price: USD {{ (mentoring?.oriPrice / 100).toFixed(2) }}<br />
                    discount ends in
                    <template v-if="item?.servicePack?.discountConfig?.end">
                      {{ date.formatDate(item?.servicePack?.discountConfig?.end, 'MM/DD/YYYY HH:mm') }}
                    </template>
                  </q-tooltip>
                </q-icon>
              </div>
            </div>
          </div>
        </div>
      </section>
    </q-card-section>
  </q-card>
</template>
<script setup>
import {ref, onMounted, inject, computed, watchEffect} from 'vue'
import PackageChips from 'components/PackageChips.vue'
import {calPremiumLecture, calPremiumMentor} from 'src/pages/order/consts'
import {pubStore} from 'stores/pub'
import {date} from 'quasar'
import {mentorChips} from 'src/pages/premCpack/consts'
import {useRoute, useRouter} from 'vue-router'
const pub = pubStore()
const route = useRoute()
const router = useRouter()

const props = defineProps({
  item: Object,
  readonly: Boolean,
  nonumber: Boolean,
  mentorPrice: {
    type: Boolean,
    default: true,
  },
  lecturePrice: {
    type: Boolean,
    default: true,
  },
  discount: Number,
})

const emit = defineEmits(['updateData'])

const lecture = computed(() => {
  const {discountConfig, price, times, schoolPrice} = props?.item
  return calPremiumLecture({
    discountConfig,
    price: pub?.user?.school ? schoolPrice : price,
    times,
  })
})

const mentoring = computed(() => {
  const {times, servicePack} = props?.item
  if (!servicePack) {
    return null
  }
  return calPremiumMentor({
    servicePack,
    count: times,
    discount: props?.discount,
  })
})


watchEffect(() => {
  const data = {
    lecture: lecture.value,
    mentoring: mentoring.value,
  }

  if (data.lecture || data.mentoring) {
    emit('updateData', data)
  }
})

const toLecture = () => {
  if (props?.readonly) {
    return
  }
  router.push({path: `/detail/content/limit/${props?.item?.premium?.unit?._id}`, query: {authId: props.item.premium._id, times: props.item.times, back: route.fullPath}})
}

const toMentor = () => {
  if (props?.readonly) {
    return
  }
  // router.push({path: `/detail/booking/limit/${props?.item?.servicePack?._id}`, query: {back: route.fullPath}})
  router.push({path: `/service/pack/${props?.item?.servicePack?._id}`, query: {back: route.fullPath, type:'view'}})
}

onMounted(() => {
  console.log('ServicePrePackage mounted', props.item)
})
</script>
<style lang="sass" scoped>
.package-bg
  background: #F1F9F9 !important
</style>
