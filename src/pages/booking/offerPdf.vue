<template>
  <div class="pdf">
    <div class="color-bar bg-teal"></div>
    <div class="q-pa-xl">
      <div class="flex q-mt-sm items-center justify-center">
        <img src="~assets/img/logo.png" width="78px" fix="contain" class="q-mr-xl logo" />
        <div class="text-h4 text-bold">OFFER OF PLACE</div>
      </div>
      <div class="text-bold text-right">Date:{{ detail.date }}</div>
      <div class="q-mt-xl text-body1">
        Congratulations! Classcipe is pleased to offer you a place in the following program. The details of this offer are as follows
      </div>
      <div class="q-mt-xl text-body1">Student First Name : {{ detail?.userInfo?.name[0] }}</div>
      <div class="q-mt-md text-body1">Student Last Name : {{ detail?.userInfo?.name[1] }}</div>
      <div class="q-mt-xl text-body1">Name of the Course : {{ detail?.servicePackInfo?.name }}</div>
      <!-- <div class="q-mt-xl text-body1">Name of the Certificate :</div>
            <div class="q-mt-sm text-body2">Upon successful completion of the course, you will receive a certificate issued by the organisation</div>
            <div class="q-mt-xl text-body1">Signed for the organisation</div>
            <div class="q-mt-xl text-body1 text-underline">sign</div>
            <div class="q-mt-sm q-ml-md text-body1">John Smith, Co-founder</div> -->
      <img src="~assets/img/logo-stamp.png" width="195px" fix="contain" />
      <div class="q-mt-sm text-body2">
        <span class="text-bold">Disclaimer :</span> This Offer is valid at the date of issue and any changes to policy or degree regulations, which may occur
        after the date of this letter, may affect the terms of this offer. Classcipe has taken due care to ensure that the information contained in this OOP is
        correct. However, in the event that an error is subsequently discovered, Classcipe retains the right to amend and re-issue this offer of place.
      </div>
    </div>
  </div>
</template>
<script setup>
/*
  imports
*/
let props = defineProps({
  detail: {
    type: Object,
    default: {},
  },
})
</script>
<style lang="scss" scope>
.pdf {
  position: absolute;
  top: 0;
  right: 10000px;
  width: 860px;
  margin: 0 auto;
  background-color: #fff;
  .logo {
    margin-left: -126px;
  }
  .color-bar {
    height: 32px;
  }

  .text-underline {
    text-decoration: underline;
  }
}
</style>
