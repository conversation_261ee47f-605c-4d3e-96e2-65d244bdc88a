<template>
  <div>
    <q-layout view="hHh LpR fFf" style="min-height: auto" class="offer-page">
      <q-page-container class="pc-md">
        <q-page class="q-pb-xl" style="min-height: auto">
          <div class="color-bar bg-teal"></div>
          <div class="q-pa-xl">
            <div class="flex q-mt-sm items-center justify-center">
              <q-img src="~assets/img/logo.png" width="78px" fix="contain" class="q-mr-xl logo" />
              <div class="text-h4 text-bold">OFFER OF PLACE</div>
            </div>
            <div class="text-bold text-right">Date:{{ detail.date }}</div>
            <div class="q-mt-xl text-body1">
              Congratulations! Class<PERSON><PERSON> is pleased to offer you a place in the following program. The details of this offer are as follows
            </div>
            <div class="q-mt-xl text-body1">Student First Name : {{ detail?.userInfo?.name[0] }}</div>
            <div class="q-mt-md text-body1">Student Last Name : {{ detail?.userInfo?.name[1] }}</div>
            <div class="q-mt-xl text-body1">Name of the Course : {{ detail?.servicePackInfo?.name }}</div>
            <!-- <div class="q-mt-xl text-body1">Name of the Certificate :</div>
            <div class="q-mt-sm text-body2">Upon successful completion of the course, you will receive a certificate issued by the organisation</div>
            <div class="q-mt-xl text-body1">Signed for the organisation</div>
            <div class="q-mt-xl text-body1 text-underline">sign</div>
            <div class="q-mt-sm q-ml-md text-body1">John Smith, Co-founder</div> -->
            <q-img src="~assets/img/logo-stamp.png" width="195px" fix="contain" />
            <div class="q-mt-sm text-body2">
              <span class="text-bold">Disclaimer :</span> This Offer is valid at the date of issue and any changes to policy or degree regulations, which may
              occur after the date of this letter, may affect the terms of this offer. Classcipe has taken due care to ensure that the information contained in
              this OOP is correct. However, in the event that an error is subsequently discovered, Classcipe retains the right to amend and re-issue this offer
              of place.
            </div>
            <div class="q-mt-xl">
              <q-btn color="primary" label="Download Offer" @click="pdf" />
            </div>
          </div>
        </q-page>
      </q-page-container>
    </q-layout>
  </div>
  <offerPdf id="offer-pdf" :detail="detail"></offerPdf>
</template>
<script setup>
/*
  imports
*/
import {onMounted, ref} from 'vue'
import {useRoute} from 'vue-router'
import moment from 'moment'
import {downloadPdf} from 'src/utils/pdf/pdf'
import offerPdf from './offerPdf.vue'

const route = useRoute()
// const router = useRouter()
const id = ref(route.params.id)

const detail = ref({})

const loading = ref(false)

// const goBack = (hash) => {
//   router.go(-1)
// }

const main = async () => {
  loading.value = true
  const res = await App.service('service-pack-apply').get(id.value)
  res.date = moment(res.approvedAt || new Date()).format('DD/MM/YYYY')
  detail.value = res

  loading.value = false
}

const pdf = async () => {
  await downloadPdf('offer-pdf', `offer`)
}

onMounted(main)
</script>
<style lang="scss" scope>
.offer-page {
  .color-bar {
    height: 32px;
  }
  .logo {
    margin-left: -126px;
  }
  @media screen and (max-width: 580px) {
    .logo {
      margin: 0;
    }
  }
  .text-underline {
    text-decoration: underline;
  }
}
</style>
