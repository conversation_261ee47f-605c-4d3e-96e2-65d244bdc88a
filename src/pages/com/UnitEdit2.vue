<template>
  <q-layout view="hHh LpR fFf">
    <q-header class="bg-white text-black" v-if="isAddon">
      <q-toolbar class="col-md-3">
        <q-btn flat round dense icon="navigate_before" @click="router.go(-1)"></q-btn>
      </q-toolbar>
    </q-header>

    <q-header elevated class="bg-white text-black" v-if="!loadingPage && !isVideo" :class="{hidden: isAddon}">
      <div class="row no-wrap">
        <q-toolbar class="col-md-3">
          <q-btn flat round dense icon="menu" @click="toggleLeftDrawer" class="lt-md" />
          <q-btn flat round dense icon="navigate_before" @click="goBack"></q-btn>
          <q-avatar :color="ContentsType?.[contentType]?.['color']" text-color="white" :icon="ContentsType?.[contentType]?.['icon']" />
          <div class="ellipsis q-pl-sm gt-sm" style="max-width: 300px">
            <div class="ellipsis text-body1 hidden">{{ one?.name }}</div>
            <div class="ellipsis text-body1">
              <span v-if="isPD"> Service </span>
              <span v-else> {{ curriculum.pubList[one.curriculum] ?? 'Others' }} </span>
            </div>
            <div class="ellipsis text-caption text-grey">Updated:{{ new Date(one.updatedAt).toLocaleString() }}</div>
          </div>
          <q-chip v-if="isTask && one?.sessionType" dense color="secondary" text-color="white" square>{{ sessionTypes[one.sessionType] }}</q-chip>
          <q-toolbar-title class="ellipsis text-center lt-md" style="max-width: 170px">{{ getFieldNameById(step) }}</q-toolbar-title>
        </q-toolbar>
        <div class="col gt-sm">
          <q-tabs v-model="step" inline-label outside-arrows mobile-arrows class="full-height" indicator-color="primary">
            <q-tab
              v-for="(o, i) in fields"
              :key="i"
              :name="o.id"
              :icon="`svguse:/v2/icons.svg#${o.icon}`"
              :label="o.name"
              @click.stop="moveToStep(o.id)"
              no-caps>
              <TaskEditStatus v-if="checkTriggered && o.id !== 'reflection'" :data="doneList?.[o.id]" class="q-pr-none" />
            </q-tab>
          </q-tabs>
          <q-space />
        </div>
        <div class="col-md-2 row justify-end items-center q-pr-md">
          <div>
            <q-btn :disable="templateNotExist" round icon="diversity_3" @click="invitePeople" size="0.7rem"></q-btn>
          </div>
        </div>
      </div>
    </q-header>

    <q-drawer
      v-if="!loadingPage && !isVideo"
      show-if-above
      :width="280"
      :mini-width="80"
      v-model="leftDrawerOpen"
      :breakpoint="600"
      :mini="$q.screen.lt.md && miniState"
      :mini-to-overlay="$q.screen.lt.md"
      @click.capture="drawerClick"
      side="left"
      bordered>
      <template v-slot:mini>
        <q-scroll-area class="fit mini-slot cursor-pointer">
          <div class="q-py-lg">
            <q-list padding>
              <q-item class="q-mb-lg" v-for="(o, i) in fields" :key="i" clickable v-ripple :active="step == o.id" active-class="bg-grey-3 text-black">
                <q-item-section avatar class="items-center" @click.stop="moveToStep(o.id)">
                  <q-icon :name="`svguse:/v2/icons.svg#${o.icon}`" />
                  <div class="ellipsis" style="max-width: 60px">{{ o.name }}</div>
                </q-item-section>
              </q-item>
            </q-list>
          </div>
        </q-scroll-area>
      </template>
      <UnitEditLeftDrawerContent
        :step="step"
        :linkList="linkList"
        :loadingPage="loadingPage"
        :one="one"
        :doneList="doneList"
        :fields="fields"
        :defaultTpl="defaultTpl"
        :inLinkContentView="inLinkContentView"
        :inOutlineView="inOutlineView"
        :outlineData="outlineData"
        :sectionTitle="sectionTitle"
        :icantedit="icantedit"
        :isTask="isTask"
        :checkTriggered="checkTriggered"
        :groupIsEmpty="groupIsEmpty"
        :groupNotAllFilled="groupNotAllFilled"
        :targetIsFilled="targetIsFilled"
        @isInViewTop="isInViewTop"
        @scrollToItem="scrollToItem"
        @updateOne="updateOne"
        @setSectionTitle="setSectionTitle"
        @addUserGroup="addUserGroup"
        @moveToStep="moveToStep"
        @setMiniState="setMiniState" />
    </q-drawer>

    <q-drawer
      @before-hide="beforeRightDrawerHide"
      :breakpoint="600"
      :width="$q.screen.gt.sm ? 350 : 300"
      v-model="rightDrawerOpen"
      side="right"
      v-if="!isVideo"
      class="q-px-sm">
      <CommonResponse :disable="disableReflection" :mode="activeType" :section="activeSection" @fullscreen="showAllReflection = true"></CommonResponse>
    </q-drawer>

    <q-page-container v-if="!isVideo" class="unit-page-container" :class="{addon: isAddon}">
      <div class="hidden full-width flex justify-center q-pt-lg" v-if="loadingPage">
        <q-spinner-dots color="primary" size=".5em" class="full-width" />
      </div>
      <q-page v-if="!loadingPage && id" class="flex justify-center items-start">
        <div class="full-width q-px-md">
          <q-banner inline-actions rounded class="bg-amber-2 text-black q-mt-sm" v-if="!bannerClosed && icantedit">
            You cannot edit this content because the owner has set it to view only.
            <template v-slot:action>
              <q-btn flat rounded icon="close" class="q-pa-sm" @click="bannerClosed = true" />
            </template>
          </q-banner>
        </div>
        <q-stepper
          v-model="step"
          @before-transition="beforeTransition"
          :class="{'unit-stepper': !$q.screen.gt.sm}"
          header-nav
          ref="stepper"
          color="primary"
          inactive-color="teal"
          error-color="negative"
          animated>
          <q-step
            v-for="(o, i) in fields"
            :done="doneList?.[o.id]?.['errors']?.length == 0"
            :error="doneList?.[o.id]?.['errors']?.length > 0"
            :key="i"
            :id="o.id"
            :name="o.id"
            :prefix="i + 1"
            :title="$q.screen.gt.sm ? o.name : ''">
            <template v-if="['basic', 'inquiry', 'applying', 'question', 'reflection'].includes(o.id)">
              <template v-if="defaultTpl[o.id] && defaultTpl[o.id].length">
                <div class="row q-ma-md" v-if="o.id == 'reflection'">
                  <q-space></q-space>
                  <q-btn @click="showAllReflection = true" color="primary" icon="fullscreen" rounded outline no-caps>Reflection Summary</q-btn>
                </div>
                <template v-for="(sd, si) in defaultTpl[o.id]" :key="si">
                  <template v-if="!(sd.tags && sd.isTagExisting === false)">
                    <div
                      class="column with-actions"
                      v-if="sd.enable && (!sd.code || sd.code !== 'words')"
                      :id="`${o.id}-${si}`"
                      :data-seq="i * 100 + si"
                      v-intersection="{handler: onIntersection, cfg: {threshold: [0.6]}}">
                      <div
                        class="row actions q-pb-sm"
                        v-if="sd.group !== 'reflection' && !['name', 'cover', 'grade', 'subject', 'stage', 'duration'].includes(sd.code)">
                        <q-space></q-space>
                        <div class="text-grey-8 q-gutter-xs">
                          <q-btn
                            flat
                            dense
                            round
                            v-for="(type, index) in actionTypes"
                            :key="index"
                            :icon="type.icon"
                            :class="{'bg-primary text-white active': activeType == type.code && activeSection == sd}"
                            @click="toggleRightDrawer(type.code, `${o.id}-${si}`, sd)">
                            <q-badge color="red" rounded floating v-if="reflectionsStore.getMainReflectionsByModeAndRkey(type.code, sd.code)?.length">
                              {{ reflectionsStore.getMainReflectionsByModeAndRkey(type.code, sd.code).length }}
                            </q-badge>
                          </q-btn>
                        </div>
                      </div>
                    </div>
                    <UnitSectionCentralIdea
                      v-if="sd.code && sd.code === 'idea'"
                      :ideaData="sd"
                      :wordsData="wordsData"
                      v-model:idea-value="one['idea']"
                      v-model:words-value="one['words']"
                      :oneData="one"
                      :sectionId="sd._id"
                      :templateId="defaultTplId"
                      :curriculumCode="one.curriculum"
                      :isCooperating="icantedit"
                      :isValid="!(checkTriggered && !itemIsFilled(sd, one))"
                      :isContentEditing="isInViewTop(o.id + '-' + si)"
                      @update:ideaValue="onUpdate($event, sd)"
                      @update:wordsValue="onUpdate($event, wordsData)"
                      @change="onChange($event, sd)" />
                    <UnitSection
                      v-else-if="sd.code && sd.code !== 'words' && sd.code !== 'connection'"
                      v-model="one[sd.code]"
                      :oneData="one"
                      :sectionData="sd"
                      :curriculumCode="one.curriculum"
                      :isCooperating="icantedit"
                      :isFloatButtonsShow="false"
                      :isValid="!(checkTriggered && !itemIsFilled(sd, one))"
                      :isContentEditing="isInViewTop(o.id + '-' + si)"
                      @update:data="(dto) => onUpdateData(dto, sd, defaultTpl[o.id], si)"
                      @update:modelValue="onUpdate($event, sd)"
                      @change="onChange($event, sd)"></UnitSection>
                    <UnitSection
                      v-else-if="sd.code && sd.code == 'connection'"
                      v-model="one['ext'][sd.code]"
                      :oneData="one"
                      :sectionData="sd"
                      :curriculumCode="one.curriculum"
                      :isCooperating="icantedit"
                      :isFloatButtonsShow="false"
                      :isContentEditing="isInViewTop(o.id + '-' + si)"
                      @update:data="(dto) => onUpdateData(dto, sd, defaultTpl[o.id], si)"
                      @update:modelValue="onUpdate($event, sd)"
                      @change="onChange($event, sd)"></UnitSection>
                    <UnitSection
                      v-else-if="!sd.code && sd.tags"
                      :key="`${one._id}:${sd.tags}:${sd._id}`"
                      v-model="one['ext'][sd._id]"
                      :oneData="one"
                      :sectionData="sd"
                      :curriculumCode="one.curriculum"
                      :isCooperating="icantedit"
                      :isFloatButtonsShow="false"
                      :isContentEditing="isInViewTop(o.id + '-' + si)"
                      @update:data="(dto) => onUpdateData(dto, sd, defaultTpl[o.id], si)"
                      @update:modelValue="onUpdate($event, sd)"
                      @change="onChange($event, sd)"></UnitSection>
                    <UnitSection
                      v-else-if="!sd.code && sd._id"
                      v-model="one['ext'][sd._id]"
                      :oneData="one"
                      :sectionData="sd"
                      :curriculumCode="one.curriculum"
                      :isCooperating="icantedit"
                      :isFloatButtonsShow="false"
                      :isContentEditing="isInViewTop(o.id + '-' + si)"
                      @update:data="(dto) => onUpdateData(dto, sd, defaultTpl[o.id], si)"
                      @update:modelValue="onUpdate($event, sd)"
                      @change="onChange($event, sd)"></UnitSection>
                    <CommonResponse :disable="disableReflection" v-if="sd.group == 'reflection'" overall mode="refl" :section="sd"></CommonResponse>
                  </template>
                </template>
              </template>
              <NoData v-else></NoData>
            </template>
            <template v-else-if="o.id === 'slides'">
              <div class="row" v-for="(f, fi) in o.child" :key="fi">
                <div class="col-12 col-md col-sm q-pa-sm">
                  <div class="shadow-3 rounded-borders">
                    <TaskEditPPT v-if="f.type === 'ppt'" :disable="icantedit" :field="f" :one="one" :call="isOkFn" :toPage="toPage" />
                  </div>
                </div>
                <div class="col-12 col-md-5 col-sm-5 text-center q-pa-sm" v-if="!one.isEdit && one.sid && !one.sid.includes('hash:')">
                  <TaskEditAddon
                    @changeTab="onChangeTab"
                    :disable="icantedit"
                    :slideId="one.sid"
                    :page="page"
                    :unitId="one._id"
                    :isPD="isPD"
                    :rubricsCriteria="one.rubricsCriteria" />
                </div>
              </div>
            </template>
            <template v-else>
              <q-item v-for="(f, fi) in o.child" :key="fi" class="q-px-none">
                <q-item-section>
                  <TaskOutline
                    v-if="f.type === 'outline'"
                    :disable="icantedit"
                    :invalid="doneList.achievementObjectives?.['invalid'] || doneList.learningObjectives?.['invalid']"
                    :one="{id, mode: one.mode, curriculum: one.curriculum, participants: one.service?.participants, serviceType: serviceType}"
                    :call="isOkFn"
                    :modified="outlineModified"
                    :withAction="toggleRightDrawer"
                    @intersect="onOutlineIntersect" />
                  <UnitEditLink
                    v-else-if="f.type === 'link'"
                    :disable="icantedit"
                    v-model="one['link']"
                    :one="one"
                    :check="checkTriggered"
                    :rightDrawerOpen="rightDrawerOpen"
                    :groups="one['linkGroup']"
                    @intersect="onLinkContentIntersect"
                    :addGroup="addUserGroup"
                    :withAction="toggleRightDrawer"
                    :call="
                      (value) => {
                        linkList = value
                      }
                    " />
                </q-item-section>
              </q-item>
            </template>
            <q-stepper-navigation v-if="step !== 'task' && step !== 'slides'">
              <div style="min-height: 80vh"></div>
            </q-stepper-navigation>
          </q-step>
        </q-stepper>
      </q-page>
    </q-page-container>

    <q-footer reveal elevated class="bg-white" v-if="!loadingPage && !isVideo" :class="{hidden: isAddon}">
      <q-toolbar>
        <q-space></q-space>
        <q-btn
          color="primary"
          no-caps
          outline
          rounded
          :disable="one.del || route.query.view == 1 || one.owner?._id !== pub.user._id"
          :loading="loadingTpl"
          :label="$q.screen.gt.xs ? 'Change Template' : ''"
          icon="svguse:/v2/icons.svg#frame"
          class="q-mx-sm"
          :class="{invisible: isPD}"
          @click="changeTemplateFn" />
        <q-btn
          class="q-mx-sm"
          color="primary"
          v-if="action === 'schedule'"
          :label="$q.screen.lt.sm ? '' : 'Schedule'"
          :disable="one.del || loading"
          icon="svguse:/v2/icons.svg#schedule"
          rounded
          no-caps
          @click="onFinishClick(false, true)">
        </q-btn>
        <q-btn
          class="q-mx-sm"
          color="primary"
          v-if="action === 'publish' && (!one?.sourceUid || one?.sourceUid == pub.user._id) && one?.owner?._id == pub.user._id"
          :label="$q.screen.lt.sm ? '' : one.status ? 'Republish to library' : 'Publish to library'"
          :disable="one.del"
          :loading="publishDialogLoading"
          icon="svguse:/v2/icons.svg#publish"
          rounded
          no-caps
          @click="onFinishClick(true, false)"></q-btn>
        <q-btn
          class="q-mx-sm"
          color="primary"
          :label="$q.screen.lt.sm ? '' : 'Save'"
          v-if="!['publish', 'schedule'].includes(action) || one.source"
          icon="o_save"
          rounded
          no-caps
          @click="goBack"></q-btn>
      </q-toolbar>
    </q-footer>

    <InviteDialog
      :show="showInviteDialog && !loadingPage"
      :one="one"
      unit
      :lists="linkList"
      :commonSchoolList="commonSchoolList"
      @back="goBack"
      @hide="onInviteDialogHide" />
    <PublishDialog :show="showPublishDialog" :one="one" :premium="isPremiumContent" @show="onPublishDialogShow" @hide="onPublishDialogHide" />

    <q-dialog v-model="modified" @hide="onSyncConfirmDialogHide" persistent>
      <q-card>
        <q-card-section class="row items-center">
          <q-avatar icon="update" color="primary" text-color="white" />
          <span class="q-ml-sm">There is a modification, do you need to update to the latest version?</span>
        </q-card-section>
        <q-card-actions align="right">
          <q-btn flat label="Cancel" color="primary" v-close-popup />
          <q-btn flat label="Confirm" color="primary" @click="confirmUpdate" />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <ReflectionDialog
      :disable="disableReflection"
      v-model="one"
      :show="showAllReflection"
      :close="() => (showAllReflection = false)"
      :allItems="allItemsInLeftDrawer" />
  </q-layout>
</template>

<script setup>
import {computed, watch, inject, ref, onMounted, onUnmounted, onBeforeUnmount, nextTick} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {QSpinnerBall} from 'quasar'
import {useI18n} from 'vue-i18n'

import InviteDialog from 'components/InviteDialog.vue'
import PublishDialog from 'components/PublishDialog.vue'
import UnitEditLink from './UnitEditLink.vue'
import CommonResponse from 'components/CommonResponse.vue'
import ReflectionDialog from 'components/ReflectionDialog.vue'
import TaskOutline from './TaskOutline.vue'
import TaskEditStatus from './TaskEditStatus.vue'
import TaskEditPPT from './TaskEditPPT.vue'
import PromptPage from 'components/PromptPage.vue'
import UnitSection from 'pages/account/unit-plan-template/components/UnitSection.vue'
import UnitSectionCentralIdea from 'pages/account/unit-plan-template/components/UnitSectionCentralIdea.vue'
import TaskEditAddon from './TaskEditAddon.vue'
import UnitEditLeftDrawerContent from 'src/components/unit/UnitEditLeftDrawerContent.vue'
import useSchool from 'src/composables/common/useSchool'
import useTeacherVerificationAuth from 'src/composables/account/teacher-verification/useTeacherVerificationAuth'
import useUnit from 'src/composables/account/unit/useUnit.js'
import {itemIsFilled} from 'src/components/unit/utils.js'
import {curriculumStore} from 'stores/curriculum'
import {pubStore} from 'stores/pub'
import {addonStore} from 'stores/addon'
import {subjectsStore} from 'stores/subjects'
import {useReflectionsStore} from 'stores/reflections'

import useUnitPlanTemplate from 'src/composables/account/unit-plan-template/useUnitPlanTemplate'
const {getUnitPlanTemplateById, getSysServiceTemplates} = useUnitPlanTemplate({isInit: false})
// import {formatMap} from 'pages/account/unit-plan-template/utils'
// import ChooseTemplate from 'components/ChooseTemplate.vue'

import {currentEditingParticipant, currentEditingUnitOwnerId, currentEditingUnitSchoolUserId} from 'src/composables/useCurrentEditing'
import {interactiveVideoStore} from 'stores/interactiveVideo'
import {useCriteriasStore} from 'stores/criterias'

/*
  consts
*/
const {list: authList, getList: getAuthList} = useTeacherVerificationAuth()
const videoStore = interactiveVideoStore()
const {t} = useI18n({useScope: 'global'})
const TaskSessionTypes = inject('TaskSessionTypes')
const readyToShowInviteDialog = ref(false)
const sessionTypes = ref({})
const curriculum = curriculumStore()
const addon = addonStore()
const subjects = subjectsStore()
const criterias = useCriteriasStore()
const reflectionsStore = useReflectionsStore()
const linkList = ref([])

const pub = pubStore()
const route = useRoute()
const router = useRouter()
const {userId, schoolIdOrUserId} = useSchool()
const {getOneById, patchOneById} = useUnit()

const loading = ref(true)
const loadingTpl = ref(false)
const loadingPage = ref(true)
const id = computed(() => route.params?.id || '')
const showInviteDialog = ref(false)
const showPublishDialog = ref(false)
const modified = ref(false)
const showAllReflection = ref(false)
const outlineModified = ref(false)
const serviceType = ref(null)

const icantedit = ref(true)
const page = ref(0)
const refs = {}
const defaultTpl = ref({})

const intervalId = ref(null)

const checkCurriculum = ref(false)

const action = ref(route.query.action)
const unauthorized = ref(false)
const actionTypes = inject('actionTypes')

const contentType = ref('unit')
const basic = {
  id: 'basic',
  icon: 'basic',
  name: 'Basic',
  child: [],
}
const inquiry = {id: 'inquiry', icon: 'inquiry', name: 'Inquiry', child: []}
const question = {id: 'question', icon: 'inquiry', name: 'Key questions', child: []}
const applying = {id: 'applying', icon: 'applying', name: 'Applying', child: []}
const achievementObjectives = {
  id: 'achievementObjectives',
  name: 'Achievement Objectives',
  icon: 'achievementObjectives',
  child: [{label: 'Achievement Objectives', key: 'outline', type: 'outline', required: true, enable: true}],
}
const learningObjectives = {
  id: 'learningObjectives',
  icon: 'achievementObjectives',
  name: 'Learning Objectives',
  child: [{label: 'Learning Objectives', key: 'outline', type: 'outline', required: true, enable: true}],
}

const slides = {id: 'slides', icon: 'applying', name: 'Edit Slides', child: [{label: 'PPT', key: 'pages', type: 'ppt', required: true, enable: true}]}

const linkContent = {id: 'task', icon: 'task', name: 'Link Content', child: [{label: 'Unit', key: 'task', type: 'link', required: false, enable: true}]}
// const reflection = {id: 'reflection', icon: 'reflection', name: 'Reflection', child: []}

const fields = []
const isTask = ref(false)
const isLive = ref(false)
const isPD = ref(false)

const addonRef = ref(null)
const isAddon = ref(false)
const templateNotExist = ref(false)
const bannerClosed = ref(false)

const one = ref({})
function updateOne(partial) {
  one.value = {...one.value, ...partial}
}

/*
  methods
*/
const goBack = () => {
  if (route.query?.go) {
    router.go(-1)
  } else {
    router.replace(route.query.back || '/home/<USER>')
  }
}

const verificationList = computed(() => {
  return authList.value.filter((e) => e.status === 2 && e.type == 'workshop')
})

const isPremiumContent = computed(() => {
  if (verificationList.value?.length) {
    return verificationList.value.some(
      (e) =>
        (one.value.curriculum == 'pd' && e.curriculum == 'pd' && one.value.service?.type?.includes(e.subject)) ||
        (one.value.curriculum !== 'pd' && one.value.curriculum == e.curriculum)
    )
  }

  return false
})

const allItemsInLeftDrawer = computed(() => {
  const allItems = []
  const obj = {
    ...defaultTpl.value,
    achievementObjectives: [
      {code: 'assess', _id: 'assess', name: 'Standards'},
      {code: 'outline', _id: 'outline', name: 'Topics'},
      {code: 'skills', _id: 'skills', name: 'Skills'},
    ],
    ...{task: one.value.linkGroup},
  }

  for (const prop in obj) {
    if (Object.hasOwn(obj, prop)) {
      let name = getFieldNameById(prop)
      obj[prop]?.forEach((it) => {
        it.parentName = name
        allItems.push(it)
      })
    }
  }

  return allItems
})

const getFieldNameById = (id) => {
  let field = null
  Object.keys(fields).forEach(function (key) {
    if (fields[key]['id'] == id) {
      field = fields[key]['name']
    }
  })
  return field
}

const checkTriggered = ref(false)

const PPTSynchronized = computed(() => {
  return one.value?.pages?.every((page) => page.pic && page.pic.length === 40)
})

const doneList = computed(() => {
  if (loadingPage.value) return console.log('loading...')

  const list = {}
  let allFilled = true
  fields.map((v) => {
    list[v.id] = {status: null, list: [], errors: []}
    if (['slides'].includes(v.id)) {
      list[v.id].status = true
      if (!one.value.sid || (action.value == 'publish' && one.value.sid.includes('hash:')) || one.value.isEdit) {
        list[v.id].status = false
        allFilled = false
        list[v.id].errors.push('Please complete Slides')
      }
    } else if (['task'].includes(v.id)) {
      list[v.id].status = true
      const count = unfilledCount()
      if (
        !groupsAllFilled() &&
        (!isTask.value || (isTask.value && count > 0)) //||
      ) {
        list[v.id].status = false
        allFilled = false
        if (!isTask.value && linkList.value.length == 0) {
          list[v.id].errors.push('Please link at least one linked content. ')
        }

        if (!groupsAllFilled() && count) {
          list[v.id].errors.push(`There are ${count} linked content${count > 1 ? 's' : ''} having incompleted fields`)
        }
      }
    } else if (['achievementObjectives', 'learningObjectives'].includes(v.id)) {
      list[v.id].status = true
      list[v.id].invalid = {}
      const verifyData = addon.outlineVerify({pd: addon.outlines?.pd?.data, outline: addon.outlines?.outline?.data})
      const types = ['outline']
      types.forEach((item) => {
        if (!verifyData[item] || !outlineTargetsAllFilled(item)) {
          list[v.id].status = false
          allFilled = false
          list[v.id].errors.push(`Please complete ${t('outline.' + item)}`)
          list[v.id].invalid[item] = true && checkTriggered.value
        } else {
          list[v.id].invalid[item] = false && checkTriggered.value
        }
      })
    } else if (step.value) {
      list[v.id].status = true
      v.child?.map((vc) => {
        if (
          vc.code == 'words' ||
          !vc.required ||
          (vc.required && vc.enable && vc.code !== 'idea' && !Acan.isEmpty(one.value[vc.code]) && itemIsFilled(vc, one.value))
        )
          return // is check ok

        if (vc.code == 'idea' && !Acan.isEmpty(one.value.idea) && !Acan.isEmpty(one.value.words)) {
          return
        }

        list[v.id].status = false
        allFilled = false
        list[v.id].list.push(vc)
        list[v.id].errors.push(`${vc.name ? vc.name : vc.origin} can not be empty`)
      })
    }
  })
  if (one.value && allFilled !== one.value.filled) {
    updateUnitFilled(allFilled)
  }
  return list
})

const updateUnitFilled = (filled) => {
  console.trace('filled up', filled)
  one.value.filled = filled
  patchOneById(id.value, {filled})
}

/*layout*/
const leftDrawerOpen = ref(false)
const toggleLeftDrawer = () => {
  leftDrawerOpen.value = !leftDrawerOpen.value
}

const rightDrawerOpen = ref(false)
const activeSection = ref(null)
const activeType = ref(null)

const toggleRightDrawer = (type, id, item) => {
  if (activeSection.value == item && activeType.value == type) {
    rightDrawerOpen.value = false
    activeSection.value = null
    activeType.value = null
  } else {
    rightDrawerOpen.value = true
    scrollToItem(id)
    activeSection.value = item
    activeType.value = type
  }
}

onBeforeUnmount(() => {
  if (intervalId.value) {
    clearInterval(intervalId.value)
  }
  $q.loading.hide()
})

onUnmounted(() => {
  // window.removeEventListener('message', onPost)
  App.service('auth').get('leave', {query: {key: `collab/${id.value}`}})
  window._postCall = {}
})

/*stepper*/
const step = ref('basic')
const stepper = ref(null)

const beforeRightDrawerHide = () => {
  activeSection.value = null
}

const beforeTransition = (newVal) => {
  if (step.value == 'slides' || (step.value == 'task' && isTask.value && !isLive.value)) {
    leftDrawerOpen.value = false
  } else if (!$q.screen.lt.sm) {
    leftDrawerOpen.value = true
  }
  rightDrawerOpen.value = false
  window.scrollTo({top: 0})
  inView.value = inView.value.filter((item) => {
    return item.indexOf(newVal) !== -1
  })
  if (!loadingPage.value) {
    router.replace({query: {...route.query, ...{step: step.value}}})
  }
}

const onChangeTab = (tab) => {
  moveToStep(tab)
}

const moveToStep = (val) => {
  if ($q.screen.lt.sm) {
    leftDrawerOpen.value = false
  }
  step.value = val
}

const loadVideoFromOne = () => {
  videoStore.setVideoData(one.value.video)
  const currentPath = route.fullPath
  const [queryString] = currentPath.split('?')
  if (!queryString) {
    router.go(-1)
    return
  }
  const newPath = `/video/edit/${one.value._id}`
  nextTick(() => {
    let from = queryString || ''
    if (queryString.includes('=')) {
      from = queryString.split('=')?.[1]
    } else {
      from = queryString
    }
    router.replace({path: newPath, query: {from}})
  })
}

watch(
  () => one.value,
  (newOne) => {
    if (newOne?.video?.videoId) {
      loadVideoFromOne()
    }
  }
)

async function updateCriteria(newList) {
  await criterias.getDefault()
  const rubricsCriteria = newList?.map((criteria) => ({
    ...criteria,
    default: criteria._id === criterias.default?._id,
  }))
  const rs = await patchOneById(one.value._id, {rubricsCriteria})
  one.value.rubricsCriteria = rs.rubricsCriteria
}

watch(
  () => criterias.change,
  async () => {
    await updateCriteria(criterias.updatedList.data)
  }
)

const miniState = ref(true)
const setMiniState = (bool) => (miniState.value = bool)
const drawerClick = () => {
  if (miniState.value) {
    setMiniState(false)
  }
}

watch(
  () => route.params.id,
  () => {
    window.location.reload()
  }
)

const isVideo = computed(() => one.value?.mode === 'video')

onMounted(async () => {
  await init()
  await getAuthList(true, {$skip: 0, $limit: 2000})
  reflectionsStore.init(route.params.id, null, 'comment')

  if (route.query.pop == 'collab') {
    readyToShowInviteDialog.value = true
  }

  if (one.value.service?.type?.[0]) {
    const rs = await subjects.get(one.value.service?.type?.[0])
    // serviceType.value = rs?.name ?? ''
    serviceType.value = rs?.subjectCode ?? ''
  }

  if (action.value == 'schedule' && isTask.value) {
    intervalId.value = setInterval(checkPPT, 10000)
  }

  if (isVideo.value) {
    setTimeout(() => {
      loadingPage.value = false
      $q.loading.hide()
    }, 1000)
  }
})

const checkPPT = async () => {
  if (PPTSynchronized.value && !one.value.isEdit) {
    clearInterval(intervalId.value)
    if (scheduling.value) {
      $q.loading.hide()
      router.push(`/com/schedule/${id.value}`)
    }
  } else {
    const rs = await getOneById(id.value)
    one.value.pages = rs.pages
  }
}

const initFields = () => {
  if (['task', 'pdTask'].includes(one.value.mode)) {
    isTask.value = true
  }

  if (['pdTask', 'pdUnit'].includes(one.value.mode)) {
    isPD.value = true
  }

  if (one.value.sessionType == 'live') {
    isLive.value = true
  }
  if (isTask.value || isVideo.value) {
    contentType.value = route.params.type
    if (one.value.mode == 'pdTask') {
      fields.push(basic, learningObjectives, slides)
    } else {
      fields.push(basic, inquiry, learningObjectives, slides)
    }

    if (isLive.value) {
      fields.push(linkContent)
    }
  } else {
    if (one.value.mode == 'pdUnit') {
      fields.push(basic, learningObjectives, linkContent)
    } else {
      fields.push(basic, inquiry, applying, achievementObjectives, linkContent)
    }
  }

  fields.map((v) => {
    refs[v.id] = ref(null)
  })
}

const disableReflection = ref(false)
const init = async () => {
  if (!pub?.user?._id) {
    await sleep(300)
    return init()
  }

  initCollab()
  await find()
  if (!one.value._id) return // not found content
  await checkICanEdit()
  if (unauthorized.value) {
    $q.dialog({
      component: PromptPage,
      componentProps: {
        fullscreen: true,
        title: 'You are no longer on the collaborating list',
        subtitle: 'Please contact the owner.',
        back: true,
      },
    })
    $q.loading.hide()
    return
  }

  initFields()

  sessionTypes.value = TaskSessionTypes.reduce((result, {label, title}) => {
    result[label] = title
    return result
  }, {})

  mergeData()
}

const initStep = async () => {
  for (const item of fields) {
    if (!['basic', 'inquiry', 'applying'].includes(item.id)) {
      //if (item.id == 'learningObjectives' && noPDSubject) continue
      step.value = item.id
      await sleep(400)
    }
  }

  if (['inquiry', 'applying', 'learningObjectives', 'achievementObjectives', 'slides', 'task'].includes(route.query.step)) {
    step.value = route.query.step
    if (route.query.step == 'slides') {
      leftDrawerOpen.value = false
    }
  } else {
    step.value = 'basic'
  }

  await sleep(500)

  if (one.value.del && route.query.view !== '1') {
    disableReflection.value = true
  }

  if (route.query.addon) {
    isAddon.value = true
    leftDrawerOpen.value = false
  }

  loadingPage.value = false
  $q.loading.hide()

  if (['publish', 'schedule', 'check'].includes(action.value)) {
    onFinishClick(false, false, true)
  }
}

const checkICanEdit = async () => {
  if (route.query.view == 1 || one.value.del) {
    icantedit.value = true
  } else if (one.value.uid === pub.user._id) {
    icantedit.value = false
  } else {
    const {members} = await App.service('collab').get(id.value, {query: {type: one.value.mode}})
    unauthorized.value = true
    members.forEach((member) => {
      if (member.email === pub.user.email && member.status == true && member.role == 'write') {
        icantedit.value = false
      }
      if (member.email === pub.user.email && member.status) {
        unauthorized.value = false
      }
    })
  }
}

const latestOne = ref(null)
const initCollab = () => {
  App.service('auth').get('join', {query: {key: `collab/${id.value}`}})
  App.service('unit').removeAllListeners(['patched'])
  App.service('reflection').removeAllListeners(['patched'])
  App.service('reflection').on('patched', () => {
    reflectionsStore.init(route.params.id)
  })
  App.service('unit').on('patched', (json) => {
    latestOne.value = json
  })
}

const scheduling = ref(false)
const onFinishClick = async (publish, scheduleForMyStudentsOrAsPublic, publishOrSchedule) => {
  checkTriggered.value = true
  if (canPublish()) {
    if (publishOrSchedule) {
      // check only
    } else if (scheduleForMyStudentsOrAsPublic) {
      if (PPTSynchronized.value || !isTask.value) {
        router.push(`/com/schedule/${id.value}`)
      } else {
        scheduling.value = true
        $q.loading.show({message: 'PPT is loading, please be patient.', boxClass: 'bg-grey-2 text-grey-9', spinner: QSpinnerBall, spinnerColor: 'primary'})
      }
    } else if (publish) {
      publishDialogLoading.value = true
      showPublishDialog.value = true
    } else {
      router.replace(route.query.back || '/home/<USER>')
    }
  } else {
    let message = 'Please complete the required fields'
    if (action.value == 'schedule') {
      message += ' before scheduling'
    } else if (action.value == 'publish') {
      message += ' before publishing'
    }
    $q.notify({type: 'info', message})
  }
}

const canPublish = () => {
  let can = 1
  if (!doneList.value) {
    console.trace('no doneList', doneList.value)

    return 0
  }
  Object.keys(doneList.value).map((key) => {
    if (doneList.value[key].status === false) {
      console.log(key, doneList.value)
      can = 0
    }
  })
  return can
}

const isOkFn = async (post = {}) => {
  if (!Acan.isEmpty(post)) {
    await patchOneById(id.value, post)
  }
}

const defaultTplId = ref(null)
const find = async () => {
  loading.value = true
  let rs = null
  try {
    rs = await getOneById(id.value)
  } catch (e) {
    rs = null
  }
  if (!rs) {
    $q.dialog({
      component: PromptPage,
      componentProps: {fullscreen: true, type: 'unavailable'},
    })
    $q.loading.hide()
    return
  }

  if (rs?.del) {
    router.replace({path: `/detail/content/arch/${id.value}`, query: {back: '/home/<USER>'}})
    return
  }

  if (!rs?.ext) {
    rs.ext = {}
  }

  const ownerId = rs?.owner?._id ?? ''
  if (rs?.uid !== userId.value) {
    // share by others
    currentEditingUnitOwnerId.value = ownerId
  }

  try {
    const schoolId = one.value.templateInfo?.school || ''
    const res = await App.service('school-user').find({query: {uid: ownerId, school: schoolId, $sort: {_id: -1}, $limit: 500}})
    const schoolUserId = res?.data?.[0]?._id || ownerId
    currentEditingUnitSchoolUserId.value = schoolUserId || schoolIdOrUserId.value
  } catch (e) {
    console.error(e)
  }
  one.value = rs
  router.replace({query: {...route.query, format: one.value.mode}})

  if (!rs.linkGroup || !rs.linkGroup.length) {
    sectionTitle.value = one.value.mode == 'unit' ? 'Week1' : 'Category name'
    await addUserGroup()
  }

  checkCurriculum.value = true
  loading.value = false
}
watch(one, () => (currentEditingParticipant.value = one.value?.service?.participants || ''))

const commonSchoolList = ref([])
import ChangeTemplate from 'pages/unit/dialog/ChangeTemplate.vue'
function changeTemplateFn() {
  $q.dialog({
    component: ChangeTemplate,
    componentProps: {one: one.value},
  })
}
const wordsData = ref({})
const mergeData = async () => {
  const isService = one.value?.curriculum === 'pd'
  let templateData = null
  if (isService) {
    const tplDoc = await App.service('unit-tpl').get(one.value.mode)
    templateData = tplDoc.data
  } else {
    templateData = one.value?.template || []
  }
  wordsData.value = templateData.find((e) => e.code === 'words') || null
  defaultTpl.value = templateData.reduce(function (r, a) {
    r[a.group] = r[a.group] || []
    if (!(['choice', 'choice-mark'].includes(a.type) && !a.tags) || a.code == 'connection') {
      r[a.group].push(a)
    }
    return r
  }, Object.create(null))

  basic.child = defaultTpl.value['basic']
  inquiry.child = defaultTpl.value['inquiry']
  question.child = defaultTpl.value['question']

  if (loadingPage.value) {
    await initStep()
  }
}

const toPage = (i) => {
  page.value = i
  router.replace({query: {...route.query, ...{page: page.value}}})
  const {_id} = one.value.pages[i]
  addonRef.value?.[0].contentWindow?.postMessage({act: 'toggle', page_id: _id, index: page.value})
}

const scrollToItem = async (id) => {
  if ($q.screen.lt.sm) {
    leftDrawerOpen.value = false
    await sleep(100)
  }

  if (step.value == 'task') {
    document.getElementById(id).scrollIntoView({behavior: 'smooth'})
  } else {
    const offset = step.value === 'achievementObjectives' || step.value === 'learningObjectives' ? 25 : 60
    scrollIntoViewWithOffset(id, offset)
  }
}

const scrollIntoViewWithOffset = (id, offset) => {
  const top = document.getElementById(id).getBoundingClientRect().top - document.body.getBoundingClientRect().top - offset
  window.scrollTo({behavior: 'smooth', top})
}

const inView = ref([])
const onIntersection = (entry) => {
  if (entry.isIntersecting === true) {
    add(entry.target.id)
  } else {
    remove(entry.target.id)
  }
}

const add = (i) => {
  remove(i)
  inView.value.push(i)
  inView.value.sort(sortAtoi)
}

const remove = (i) => {
  let index
  while ((index = inView.value.indexOf(i)) > -1) {
    inView.value.splice(index, 1)
    inView.value.sort(sortAtoi)
  }
}

const sortAtoi = (a, b) => {
  const seqa = document.getElementById(a)?.dataset.seq
  const seqb = document.getElementById(b)?.dataset.seq
  return seqa - seqb
}

const isInViewTop = (id) => {
  if (inView.value.length && inView.value[0] === id) {
    return true
  } else {
    return false
  }
}

const outlineData = computed(() => {
  const obj = {}
  const _isPD = ['pdTask', 'pdUnit'].includes(one.value.mode)
  const _required = !_isPD && one.value.service?.participants !== 'educators' && serviceType.value !== 'overseasStudy'
  //#4824 !_isPD
  if (_required) {
    obj.assess = []
  }
  obj.outline = []

  //#3127
  if (_required || (_isPD && one.value.service?.participants !== 'educators')) {
    obj.skills = []
  }
  for (const key in addon.outlines) {
    if (['skills', 'assess', 'outline'].includes(key) && obj[key] && addon.outlines[key]?.['data']) {
      obj[key] = addon.outlines[key]['data']
    }
  }
  return obj
})

const inOutlineView = ref([])
const onOutlineIntersect = (list) => {
  inOutlineView.value = list
}

const inLinkContentView = ref([])
const onLinkContentIntersect = (list) => {
  inLinkContentView.value = list
}

const outlineTargetsAllFilled = (type) => {
  let allFilled = true
  const data = addon.outlines?.[type]?.['data']
  for (const key in data) {
    if (Acan.isDefined(data[key])) {
      if (!targetIsFilled(type, key)) {
        allFilled = false
      }
    }
  }
  return allFilled
}
const targetIsFilled = (type, target) => {
  let filled = true
  const outlines = addon.outlines
  if (!Acan.isEmpty(outlines[type]['data'])) {
    const targetChild = outlines[type]['data'][target]['child']
    const targetCustom = outlines[type]['data'][target]['custom']
    const tags = outlines[type]['tags']
    let customIsAllNamed = !Acan.isEmpty(targetCustom)
    targetCustom?.forEach((item) => {
      if (Acan.isEmpty(item.name)) {
        customIsAllNamed = false
      }
    })

    if (Acan.isEmpty(targetChild) && !customIsAllNamed) {
      filled = false
    }

    if (!Acan.isEmpty(targetChild) && ['pd', 'outline'].includes(type)) {
      const idsWithoutChild = getIdWithoutChild(outlines[type]['data'][target])
      idsWithoutChild.forEach((item) => {
        if (Acan.isEmpty(tags?.[item])) {
          filled = false
        }
      })
    }

    if (!Acan.isEmpty(targetCustom) && ['pd', 'outline'].includes(type)) {
      targetCustom.forEach((item) => {
        if (Acan.isEmpty(tags?.[item._id])) {
          filled = false
        }
      })
    }
  }

  return filled
}

const getIdWithoutChild = (obj) => {
  const result = []
  if (obj._id && Acan.isEmpty(obj.child)) {
    result.push(obj._id)
  }
  if (!Acan.isEmpty(obj.child)) {
    obj.child.forEach((item) => {
      result.push(...getIdWithoutChild(item))
    })
  }
  return result
}

const groupIsEmpty = (group) => {
  let empty = true
  one.value.link.forEach((item) => {
    if (item.group == group._id) {
      empty = false
    }
  })
  return empty
}
const groupNotAllFilled = (group) => {
  let allFilled = true
  linkList.value.forEach((item) => {
    if (!item.filled && item.group == group._id) {
      allFilled = false
    }
  })

  return !allFilled
}
const groupsAllFilled = () => {
  let allFilled = true
  one.value.linkGroup?.forEach((group) => {
    if (groupIsEmpty(group) || groupNotAllFilled(group)) {
      allFilled = false
    }
  })

  return allFilled
}

const unfilledCount = () => {
  let count = 0
  linkList.value.forEach((item) => {
    if (!item.filled || (['task', 'pdTask'].includes(item.mode) && item.sid?.includes('hash:'))) {
      count += 1
    }
  })
  return count
}

const sectionTitle = ref(null)
const setSectionTitle = (str) => (sectionTitle.value = str)
const addUserGroup = async () => {
  loading.value = true
  if (sectionTitle.value) {
    if (!one.value?.linkGroup) one.value.linkGroup = []
    const group = {name: sectionTitle.value, alias: ''}
    const rs = await patchOneById(one.value._id, {$addToSet: {linkGroup: [group]}})
    one.value.linkGroup = rs.linkGroup
    sectionTitle.value = null
  }
  loading.value = false
}

const invitePeople = () => {
  // https://github.com/zran-nz/bug/issues/5232
  if (one.value.sid?.includes('hash:')) {
    $q.dialog({
      message: "Please initiate your slides first before using the 'Add Collaborator' function.",
      ok: {
        label: 'I got it',
        noCaps: true,
      },
    }).onOk(() => {})
  } else if (one.value.sid || !isTask.value) {
    showInviteDialog.value = true
  } else {
    $q.dialog({
      message: "Please create your slides first before using the 'Add Collaborator' function.",
      ok: {
        label: 'I got it',
        noCaps: true,
      },
    }).onOk(() => {})
  }
}

const onPublishDialogHide = () => {
  showPublishDialog.value = false
}

const publishDialogLoading = ref(false)
const onPublishDialogShow = () => {
  publishDialogLoading.value = false
}

const onInviteDialogHide = () => {
  showInviteDialog.value = false
}

const onSyncConfirmDialogHide = () => {
  modified.value = false
  outlineModified.value = false
}

const confirmUpdate = async () => {
  loading.value = true
  modified.value = false
  outlineModified.value = true
  if (latestOne.value) {
    Object.assign(one.value, latestOne.value)
  }
  loading.value = false
}

const onUpdate = async (evt, sd) => {
  if (sd.code == 'idea' || sd.code == 'words') {
    doUpdate(evt, sd)
    one.value[sd.code] = evt
  } else if (sd.type !== 'text' && sd.type !== 'textarea') {
    doUpdate(evt, sd)
  }
}

const onUpdateData = async (dto, sd, target, index) => {
  sd = {...sd, ...dto}
  target[index] = sd
}

const onChange = (evt, sd) => {
  if (sd.type == 'text' || sd.type == 'textarea') {
    doUpdate(evt.target.value, sd)
  }

  if (sd.code == 'connection') {
    doUpdate(one.value.ext[sd.code], sd)
  }
}

const doUpdate = async (data, sd) => {
  loading.value = true
  let post = {}
  if (sd.code && sd.code != 'connection') {
    post[sd.code] = data
    await isOkFn(post)
  } else {
    if (sd.tags) {
      post[`ext.${sd._id}`] = data
    } else if (sd.code == 'connection') {
      post[`ext.${sd.code}`] = data
    } else {
      post[`ext.${sd._id}`] = data
    }
    await patchOneById(id.value, post)
  }

  if (data.layer === true || data.layer === false) {
    addon.upLearningGoal(data, sd)
  }
  loading.value = false
}
</script>

<style lang="sass">
.with-actions
  .actions
    visibility: hidden
  .actions
    .active
      visibility: visible
  &:hover
    .actions
      visibility: visible
.mini-slot
  transition: background-color .28s
  &:hover
    background-color: rgba(0, 0, 0, .04)
.mini-icon
  font-size: 1.718em
  padding: 2px 16px
  & + &
    margin-top: 18px
.unit-stepper
  .q-stepper__tab
    width: 20% !important
.unit-page-container
  .q-stepper
    box-shadow: none
  .q-stepper__header
    display: none
  .q-stepper__step-inner
    padding-bottom: 0
    & > div
      padding-bottom: 0
      padding-top: 0
  &.addon
    .q-stepper__step-inner
      padding-top: 0
      padding-bottom: 0
.group-edit
  .q-field__control
    height: 31px
</style>

<style lang="sass" scoped>
.slides
  height: calc(100vh - 124px)
  overflow: hidden
.ghost
  opacity: 0.4
  background: #c8ebfb
.q-card
  width: 600px
.section-caption
  font-size: 0.75rem
  font-weight: 400
  letter-spacting: 0.03333em
  opacity: 0.6
.q-stepper
  width: 100%
  max-width: 1040px
.q-checkbox
  min-width:200px
.unit-edit
  .__label
    font-weight: bold
    font-size: 1rem
    color: $grey-8
  .q-field__label
    font-weight: bold
    transform: translateY(-60%) scale(1)
</style>
