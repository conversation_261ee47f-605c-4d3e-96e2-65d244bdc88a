<template>
  <div v-for="(layer, i) in child" :key="i">
    <div>
      <q-item :style="layer.haveErrorData && score.rubric && layer.score > 0 && $q.screen.lt.sm ? 'border: 1px solid red; border-radius: 5px' : ''">
        <q-item-section :style="{marginLeft: `${(level + 1) * 1}rem`}">
          {{ layer.name }}
        </q-item-section>
        <q-item-section class="text-right" :class="$q.screen.lt.md ? 'col-2' : 'col-1'">
          <div class="text-subtitle2">
            {{ layer.score }}
          </div>
        </q-item-section>
      </q-item>
    </div>
    <QuestionScoreRubric :level="level + 1" :score="score" :child="layer.child" />
  </div>
</template>

<script setup>
import {computed, ref} from 'vue'

const props = defineProps({
  score: {type: Object},
  child: {
    type: Array,
  },
  level: {
    type: Number,
    default: 0,
  },
})

const checkLayerEnable = (obj, isCurrentLayer = false) => {
  if (obj) {
    if (obj.enable && !isCurrentLayer) return true
    const findChild = obj.child
    if (findChild) {
      for (const child of findChild) {
        if (checkLayerEnable(child)) return true
      }
      return false
    } else {
      return false
    }
  }
  return false
}
</script>

<style scoped>
.min-height-text {
  min-height: 72px;
}

.rubric-tooltip {
  position: absolute;
  top: -150px;
}
</style>
