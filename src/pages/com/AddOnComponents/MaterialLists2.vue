<template>
  <q-toolbar>
    <q-item-label v-if="isTipList" class="q-ml-sm text-subtitle2"> Tips </q-item-label>
    <q-item-label v-else class="text-h6">
      <!-- {{ `Material (${allMaterials?.length ? allMaterials.length : 0})` }} -->
    </q-item-label>
    <q-icon v-if="isTipList && (addon.question[0].tips || materialList?.length)" class="text-teal" name="check_circle"></q-icon>
    <q-space />
    <q-btn v-if="!preview" class="text-grey-5" flat round icon="upload" @click="clickUploadMaterial(isTipList ? 'tips' : 'materials')" />
  </q-toolbar>
  <div v-if="isTipList && addon.question[0].tips && !preview">
    <q-item-label class="q-ml-lg text-caption">{{ addon.question[0].tips }}</q-item-label>
    <br />
  </div>
  <div class="row text-center" v-if="materialList?.length && (!preview || !isTipList)">
    <div
      class="q-pl-sm q-mb-sm"
      :class="readOnly || (preview && route.path.includes('prompts')) ? 'col-2' : $q.screen.lt.sm ? 'col-2' : 'col-1'"
      v-for="(material, i) in materialList"
      :key="i"
      @click.prevent="viewMaterialDetail(material)"
      style="max-height: 40px; overflow: hidden">
      <div v-if="material.type === 'image'">
        <q-img :src="hashToUrl(material.key)" fit="fill" style="max-width: 60px; height: 40px"></q-img>
      </div>
      <div v-else-if="material.type === 'youtube'">
        <q-img :src="youtubeIdThumb(material.url)" fit="fill" style="max-width: 60px; height: 40px">
          <q-icon class="absolute-center text-red-7" name="fa-brands fa-youtube" size="1rem"></q-icon>
        </q-img>
      </div>
      <div v-else-if="material.type === 'video'">
        <video class="full-width vertical-middle" muted preload="metadata">
          <source :src="hashToUrl(material.key)" />
        </video>
      </div>
      <div v-else-if="material.type === 'mp3' || material.type === 'audio'">
        <q-icon class="bg-teal" color="white" name="mic" size="2.5rem" />
      </div>
      <div v-else-if="material.type === 'pdf'">
        <q-icon color="teal" name="picture_as_pdf" size="2.5rem" />
      </div>
    </div>
  </div>

  <!-- <div v-else-if="!isTipList" class="q-ml-md">No Materials</div> -->

  <div class="text-teal" v-if="!isTipList && showMoreMaterialShow">
    <div v-if="showMoreMaterial" @click="clickShowMore(false)">
      Less
      <q-icon name="keyboard_arrow_up"></q-icon>
    </div>
    <div v-else @click="clickShowMore(true)">
      More
      <q-icon name="chevron_right"></q-icon>
    </div>
  </div>
</template>

<script setup>
import {defineProps, ref} from 'vue'
import ViewMaterialDialog from './ViewMaterialDialog.vue'

import {useRoute} from 'vue-router'

const route = useRoute()
const {youtubeIdThumb} = Fn
const props = defineProps({
  allMaterials: Array,
  materialList: Array,
  preview: Boolean,
  readOnly: Boolean,
  showMoreMaterialShow: Boolean,
  showMoreMaterial: Boolean,
  clickUploadMaterial: Function,
  clickShowMore: Function,
  addon: Object,
  isTipList: {type: Boolean, default: false},
})

function viewMaterialDetail(materialDetail) {
  $q.dialog({
    component: ViewMaterialDialog,
    componentProps: {
      materialDetail,
    },
  })
}
</script>
