<template>
  <q-dialog ref="dialogRef" @hide="onDialogHide" :maximized="$q.screen.lt.sm" >
    <q-card style="width: 400px">
      <q-card-section>
        <q-toolbar>
          <div class="text-h6"><span v-if="!preview">Edit</span> Correct Answer</div>
          <q-space />
          <q-btn flat round icon="close" v-close-popup></q-btn>
        </q-toolbar>
        <q-separator></q-separator>
        <q-item class="row" v-for="(option, i) in question.options" :key="i">
          <q-item-section avatar class="col-1" v-if="preview">
            <q-icon v-if="option.on" color="teal" name="check_circle"></q-icon>
          </q-item-section>
          <q-item-section avatar class="col-2" v-else>
            <q-checkbox v-if="question.multi" v-model="option.on" color="teal"></q-checkbox>
            <q-radio v-else :model-value="selectedOptionIndex" :val="i" @update:model-value="setSelectedOption(i)" color="teal"></q-radio>
          </q-item-section>
          <q-item-section avatar class="col-1">
            <q-badge color="blue-3" rounded :label="alphabetC[i]"></q-badge>
          </q-item-section>
          <q-item-section>
            <q-item-label>
              {{ option.val }}
            </q-item-label>
          </q-item-section>
        </q-item>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script setup>
import {defineProps, defineEmits, computed} from 'vue'
import {useDialogPluginComponent} from 'quasar'
defineEmits([...useDialogPluginComponent.emits])
const {dialogRef, onDialogHide, onDialogOK, onDialogCancel} = useDialogPluginComponent()

const props = defineProps(['question', 'preview'])

const alphabetC = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'

const selectedOptionIndex = computed(() => {
  return props.question.options.findIndex((opt) => opt.on)
})

const setSelectedOption = (index) => {
  props.question.options.forEach((opt, i) => {
    opt.on = i === index
  })
}

</script>
