<template>
  <q-dialog ref="dialogRef" @hide="onDialogHide" :maximized="$q.screen.lt.sm" >
  <q-card style="width: 500px">
    <q-card-section>
      <q-toolbar>
        <q-toolbar-title>{{ materialDetail.desc || 'Material Detail' }}</q-toolbar-title>
        <q-space />
        <q-btn flat round icon="close"  v-close-popup/>
      </q-toolbar>
      <q-separator />
      <br />
      <div>
        <div v-if="materialDetail.type === 'image'">
          <q-img :src="hashToUrl(materialDetail.key)" />
        </div>
        <div v-else-if="materialDetail.type === 'youtube'">
          <q-video :src="`https://www.youtube.com/embed/${materialDetail.url}?autoplay=1&start=${materialDetail.ext?.start}`" :ratio="16 / 10" />
        </div>
        <div v-else-if="materialDetail.type === 'video'">
          <video class="full-width vertical-middle" controls muted controlslist="nodownload">
            <source :src="hashToUrl(materialDetail.key)" />
          </video>
        </div>
        <div v-else-if="materialDetail.type === 'mp3' || materialDetail.type === 'audio'">
          <video class="full-width vertical-middle" controls muted controlslist="nodownload" style="height: 3rem" :src="hashToUrl(materialDetail.key)" />
        </div>
        <div v-else-if="materialDetail.type === 'pdf'" class="row flex flex-center">
          <q-btn flat class="text-teal" icon="picture_as_pdf" size="4rem" @click="openPDF()">
            <q-tooltip>Open PDF</q-tooltip>
          </q-btn>
        </div>
      </div>
      <q-separator />
    </q-card-section>
  </q-card>
  </q-dialog>
</template>

<script setup>
import {defineProps, defineEmits} from 'vue'
import {useDialogPluginComponent} from 'quasar'
defineEmits([...useDialogPluginComponent.emits])
const {dialogRef, onDialogHide, onDialogOK, onDialogCancel} = useDialogPluginComponent()

const props = defineProps({
  materialDetail: {type: Object, required: true},
})

function openPDF() {
  window.open(Fn.hashToUrl(props.materialDetail.key), '_blank')
}

</script>
