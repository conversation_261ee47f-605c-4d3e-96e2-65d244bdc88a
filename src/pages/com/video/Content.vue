<template>
  <q-drawer :breakpoint="600" :width="$q.screen.gt.sm ? 350 : 300" v-model="rightDrawerOpen" side="right" class="q-pr-md">
    <CommonResponse :mode="activeType" :section="activeSection" @fullscreen="false"></CommonResponse>
  </q-drawer>
  <q-toolbar
    class="q-ma-md text-white rounded-borders-lg"
    :class="toolbarColor(sectionKey)"
    style="max-height: 30px; min-height: 0%"
    @click="eclipseSection(sectionKey)">
    <q-toolbar-title>{{ section.name }}</q-toolbar-title>
    <q-space />
    <!-- {{ console.log('value of section is ', section) }} -->
    <!-- {{ console.log('value of key', sectionKey) }} -->
    <q-avatar :icon="arrowIcon" />
  </q-toolbar>

  <!-- for the vedio preview -->
  <div v-if="sectionKey === 'video' && isActive" class="q-ma-md">
    <div v-if="unitData.video">
      <VideoPlayerPreviewComponent :readOnly="isReadonly" width="20vh" height="20vh" />
    </div>
    <div v-else>
      <NoData />
      <q-btn
        outline
        rounded
        class="no-uppercase custom-outline flex-j"
        style="margin-top: 10px"
        color="white"
        text-color="primary"
        label="Go to edit video"
        @click="goToEditVideo" />
    </div>
  </div>
  <!-- for basic -->
  <div v-if="sectionKey === 'basic' && isActive">
    <q-toolbar>
      <div class="text-subtitle1 text-weight-bold">Curriculum: {{ localUnitData.curriculum }}</div>
      <q-space></q-space>
      {{ console.log('value of isInit', isInit) }}
      <q-btn icon="edit" flat round @click="changeTamplate" :disable="isInit"></q-btn>
    </q-toolbar>

    <div v-if="unitData" :key="`${unitData._id}:${currentEditingParticipant}`" class="q-ma-md">
      <div v-for="(section, i) in tplBasic" :key="i">
        <div class="column with-actions" v-if="section.enable && (!section.code || section.code !== 'words')">
          <div class="row actions q-pb-sm" v-if="!['name', 'cover', 'grade', 'subject', 'stage', 'duration'].includes(section.code)">
            <q-space></q-space>
            <div class="text-grey-8 q-gutter-xs">
              <q-btn
                flat
                dense
                round
                v-for="(type, index) in actionTypes"
                :key="index"
                :icon="type.icon"
                :class="{'bg-primary text-white active': activeType == type.code && activeSection == section}"
                @click="toggleRightDrawer(type.code, section)">
              </q-btn>
            </div>
          </div>
        </div>

        <UnitSection
          v-model="localUnitData[section.code]"
          :oneData="localUnitData"
          :sectionData="section"
          :curriculumCode="localUnitData.curriculum"
          :isCooperating="isReadonly"
          :isFloatButtonsShow="false"
          :isValid="!(checkReady && !itemIsFilled(section))"
          @update:modelValue="onUpdate($event, section)"
          @change="onChange($event, section)">
        </UnitSection>
        <!-- {{ console.log('value of is ReadOnly', isReadonly) }} -->
      </div>
    </div>
  </div>

  <!-- for inquery -->
  <div v-if="sectionKey === 'inquery' && isActive">
    <div v-if="unitData" class="q-ma-md">
      <div v-for="section in tplInquery" :key="getSectionKey(section, unitData)">
        <div class="column with-actions" v-if="section.enable && (!section.code || section.code !== 'words')">
          <div class="row actions q-pb-sm" v-if="!['name', 'cover', 'grade', 'subject', 'stage', 'duration'].includes(section.code)">
            <q-space></q-space>
            <div class="text-grey-8 q-gutter-xs">
              <q-btn
                flat
                dense
                round
                v-for="(type, index) in actionTypes"
                :key="index"
                :icon="type.icon"
                :class="{'bg-primary text-white active': activeType == type.code && activeSection == section}"
                @click="toggleRightDrawer(type.code, section)">
              </q-btn>
            </div>
          </div>
        </div>
        <UnitSection
          v-if="shouldRenderSection(section)"
          :modelValue="getSectionModel(section)"
          @update:modelValue="(val) => updateSectionModel(section, val)"
          :oneData="localUnitData"
          :sectionData="section"
          :curriculumCode="localUnitData.curriculum"
          :isCooperating="isReadonly"
          :isFloatButtonsShow="false"
          :isValid="!(checkReady && !itemIsFilled(section))"
          @change="onChange($event, section)" />
      </div>
    </div>
  </div>

  <!-- Learning objectives -->
  <div v-if="sectionKey === 'learningObjectives' && isActive" class="q-ma-md">
    <!-- {{ console.log('value of isReadonly:', isReadonly) }}
    {{ console.log('value of checkLearningObjectiveValid()', checkLearningObjectiveValid()) }}
    {{ console.log('value of task id', taskId) }} -->
    <TaskOutline
      :disable="isReadonly"
      :invalid="checkLearningObjectiveValid()"
      :one="{
        id: taskId,
        mode: localUnitData.mode,
        curriculum: localUnitData.curriculum,
        participants: localUnitData.service?.participants,
        serviceType: localUnitData.service?.type?.[0],
        serviceCode: localUnitData.service?.type?.[0],
      }"
      :call="isOkFn"
      :modified="false" />
  </div>

  <!-- score and rubrics setting -->
  <div v-if="sectionKey === 'score' && isActive">
    <div v-if="videoQuestions?.length">
      <q-toolbar>
        <q-space></q-space>
        <q-btn :disable="isReadonly" icon="edit" rounded outline label="Edit outlines" class="text-teal" no-caps @click="editScoreDialogOpen()"></q-btn>
      </q-toolbar>
      <div v-if="unitData" class="q-ma-md">
        <VideoScore
          :set-section-status="setSectionStatus"
          :videoData="videoData"
          :unitData="unitData"
          :taskId="taskId"
          :isPD="isPD"
          :readOnly="true"
          :questions="videoQuestions"></VideoScore>
      </div>
    </div>
    <div v-else>
      <NoData></NoData>
    </div>
  </div>

  <q-dialog v-if="!section['learningObjectives']" v-model="editScore" persistent :maximized="$q.screen.lt.sm">
    <q-card :style="$q.screen.lt.sm ? '' : 'width: 60vw; max-width: 60vw; max-height: 80vh'">
      <q-card-section>
        <VideoScore
          :videoData="videoData"
          :unitData="unitData"
          :taskId="taskId"
          :isPD="isPD"
          :questions="videoQuestions"
          @closeEditDialog="closeEditDialog"
          :set-section-status="setSectionStatus"></VideoScore>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script setup>
import {computed, ref, inject, watch, onMounted, defineExpose} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {pubStore} from 'stores/pub'
import {useReflectionsStore} from 'stores/reflections'
import VideoPlayerPreviewComponent from '../../InteractiveVideo/components/VideoPlayerPreviewComponent.vue'
import NoData from '../../../components/pub/NoData.vue'
import {currentEditingParticipant} from 'src/composables/useCurrentEditing'
import useUnit from 'src/composables/account/unit/useUnit.js'
import UnitSection from '../../account/unit-plan-template/components/UnitSection.vue'
import TaskOutline from '../TaskOutline.vue'
import VideoScore from './VideoScore.vue'
import CommonResponse from 'components/CommonResponse.vue'
import TemplateSelectionModal from '../../InteractiveVideo/components/TemplateSelectionModal.vue'

const tplBasic = ref()
const tplInquery = ref()
const router = useRouter()
const route = useRoute()
const reflectionsStore = useReflectionsStore()
const pub = pubStore()
const isReadonly = ref(true)
const {patchOneById} = useUnit()

const emit = defineEmits(['toggle-active', 'read-only-change', 'update-video-questions', 'can-publish'])
const actionTypes = inject('actionTypes')
const TaskSessionTypes = inject('TaskSessionTypes')

const props = defineProps({
  section: {
    type: Object,
    required: true,
  },
  sectionKey: {
    type: String,
    required: true,
  },
  unitData: {
    type: Object,
    required: true,
  },
  outlineData: {
    type: Object,
    required: true,
  },
  taskId: {
    type: String,
    required: true,
  },
  videoQuestions: {
    type: Object,
    required: true,
  },
  videoData: {
    type: Object,
    required: true,
  },
  isActive: {
    type: Boolean,
    required: true,
  },
  isInit: Boolean,
  init: Function,
  isPD: Boolean,
  template: Array,
  triggerCheck: Number,
})
const localUnitData = ref(props.unitData ? JSON.parse(JSON.stringify(props.unitData)) : {})

const toggleVideo = ref(false)
const toggleBasic = ref(false)
const toggleInquery = ref(false)
const toggleScore = ref(false)
const learningObjectives = ref(false)
const checkReady = ref(true)
const editScore = ref(false)
const activeSection = ref(null)
const rightDrawerOpen = ref(false)
const activeType = ref(null)

const toolbarColor = (key) => {
  switch (key) {
    case 'video':
      return 'bg-indigo'
    case 'basic':
      return 'bg-teal'
    case 'inquery':
      return 'bg-pink'
    case 'learningObjectives':
      return 'bg-amber'
    case 'score':
      return 'bg-blue'
    default:
      return 'bg-grey'
  }
}

const arrowIcon = computed(() => {
  return props.section.active ? 'arrow_drop_up' : 'arrow_drop_down'
})

function toggleRightDrawer(type, item) {
  if (activeSection.value == item && activeType.value == type) {
    rightDrawerOpen.value = false
    activeSection.value = null
    activeType.value = null
  } else {
    rightDrawerOpen.value = true
    activeSection.value = item
    activeType.value = type
  }
}
function beforeRightDrawerHide() {
  activeSection.value = null
}

const eclipseSection = (key) => {
  emit('toggle-active', key)
  emit('read-only-change', changeReadOnly())

  if (key === 'basic') {
    const template = localUnitData.value?.template
    if (Array.isArray(template)) {
      tplBasic.value = template.filter((section) => section.group === 'basic')
    } else {
      console.warn('unitData.template is not an array:', template)
      tplBasic.value = []
    }
    console.log('value of tplBasic:', tplBasic.value)
  } else if (key === 'inquery') {
    const template = localUnitData.value?.template
    if (Array.isArray(template)) {
      tplInquery.value = template.filter((section) => section.group === 'inquiry')
    } else {
      console.warn('unitData.template is not an array:', template)
      tplInquery.value = []
    }

    if (!localUnitData.value.ext) {
      localUnitData.value.ext = {}
    }

    tplInquery.value.forEach((e) => {
      if (!e?.code) {
        localUnitData.value.ext[e._id] = ''
      }
    })
  }
  console.log('clicked on :', key)
}

const goToEditVideo = () => {
  router.replace(`/video/edit/${route.params.id}?back=/home/<USER>
}

async function changeTamplate() {
  const items = TaskSessionTypes.map(({title, label}) => ({label: title, value: label}))
  $q.dialog({
    component: TemplateSelectionModal,
    componentProps: {currentUnitData: localUnitData.value},
  }).onOk(async ({data}) => {
    console.warn('video', data)
    // showChooseTemplate.value = true
    const dto = {...data, mode: 'video', sessionType: 'student'}
    await onTemplateChoosed(dto)
    await init()
    cantChaneIt()
    // location.reload()
  })
}
const getSectionModel = (section) => {
  if (section.code && section.code !== 'words' && section.code !== 'connection') {
    return localUnitData.value[section.code]
  } else if (section.code === 'connection') {
    return localUnitData.value.ext?.[section.code]
  } else if (!section.code && section.tags) {
    return localUnitData.value.ext?.[section._id]
  } else if (!section.code && section._id) {
    return localUnitData.value.ext?.[section._id]
  }
  return null
}

const shouldRenderSection = (section) => {
  // Skip 'words' section
  return !(section.code === 'words')
}

const getSectionKey = (section, localUnitData) => {
  if (!section.code && section.tags) {
    return `${localUnitData._id}:${section.tags}:${section._id}`
  }
  return section._id || section.code
}
const updateSectionModel = (section, value) => {
  if (section.code && section.code !== 'words' && section.code !== 'connection') {
    localUnitData.value[section.code] = value
  } else if (section.code === 'connection') {
    localUnitData.value.ext[section.code] = value
  } else if (!section.code && section.tags) {
    localUnitData.value.ext[section._id] = value
  } else if (!section.code && section._id) {
    localUnitData.value.ext[section._id] = value
  }
}

const checkCanPublish = () => {
  let can = 1
  // console.log('intial value of can', can)

  if (!localUnitData.value.video) can = 0
  // console.log('localUnitData.value.video value is:', localUnitData.value.video)

  const learningObjectivesReady = checkLearningObjectiveValid()
  if (!learningObjectivesReady.outline) can = 0
  // console.log('learningObjectivesReady.outline valeu is : ', learningObjectivesReady.outline)
  // console.log('final value of can', can)

  localUnitData.value.template?.forEach((item) => {
    if (item.required && !itemIsFilled(item)) {
      can = 0
    }
  })

  console.log('value of can which is passed from checkCanPublish:', can)

  emit('can-publish', can)
}

function checkLearningObjectiveValid() {
  const prr = []
  const orr = []
  // console.log('outline data : ', props.outlineData)

  let outline = props.outlineData?.data?.[0]?.outline?.data

  // console.log('outline value: ', outline)

  for (const key in outline) {
    if (outline[key]?.custom) orr.push(...outline[key].custom)
    if (outline[key]?.child) orr.push(...getChildId(outline[key].child))
  }
  // console.log('outlineTragestsAllFilled:', outlineTargetsAllFilled('outline'))
  // console.log('Acan value: ', !Acan.isEmpty(orr))

  if (!Acan.isEmpty(orr) && outlineTargetsAllFilled('outline')) {
    return {outline: true && checkReady.value}
  } else {
    return {outline: false && checkReady.value}
  }
}

function getChildId(arr) {
  const id = []
  arr.map((v) => {
    if (Acan.isEmpty(v.child)) {
      id.push(v._id)
    } else {
      id.push(...getChildId(v.child))
    }
  })
  return id
}

function itemIsFilled(item) {
  let filled = true
  if (item.code && (item.code == 'idea' || item.code == 'words')) {
    if (Acan.isEmpty(localUnitData.value.idea) || Acan.isEmpty(localUnitData.value.words)) {
      filled = false
    }
  } else if (item.code && ['grades', 'subjects', 'duration'].includes(item.code)) {
    if (Acan.isEmpty(localUnitData.value[item.code])) {
      filled = false
    } else if (item.code == 'duration') {
      if (!localUnitData.value.duration?.unit || !localUnitData.value.duration?.value) {
        filled = false
      }
    } else {
      localUnitData.value[item.code].forEach((_item) => {
        //if (!_item.label || !_item.value) {
        if (!_item.value) {
          filled = false
        }
      })
    }
  } else if (
    (item.code && !Acan.isEmpty(localUnitData.value?.[item.code])) ||
    (item.code && !Acan.isEmpty(localUnitData.value?.ext?.[item.code])) ||
    (!item.code &&
      item.tags &&
      !Acan.isEmpty(localUnitData.value.ext) &&
      !Acan.isEmpty(localUnitData.value.ext[item._id]) &&
      !Acan.isEmpty(localUnitData.value.ext[item._id][item._id])) ||
    (!item.code && !item.tags && !Acan.isEmpty(localUnitData.value.ext) && !Acan.isEmpty(localUnitData.value.ext[item._id]))
  ) {
    if (item?.type == 'choice' || item?.type == 'choice-mark') {
      let values = []
      let obj = null
      if (item.code && localUnitData.value[item.code]) {
        obj = localUnitData.value[item.code]
      } else if (item.code && localUnitData.value.ext[item.code]) {
        obj = localUnitData.value.ext[item.code]
      } else {
        obj = localUnitData.value.ext[item._id]
      }
      values = Object.values(obj)

      for (const property in obj) {
        if (obj[property]?.length === 0) {
          filled = false
        }
      }
    }
  } else {
    filled = false
  }

  return filled
}

function outlineTargetsAllFilled(type) {
  let allFilled = true
  const data = props.outlineData?.data?.[0]?.[type]?.['data']
  for (const key in data) {
    if (data.hasOwnProperty(key)) {
      if (!targetIsFilled(type, key)) {
        allFilled = false
      }
    }
  }

  return allFilled
}
function targetIsFilled(type, target) {
  let filled = true
  const outlines = props.outlineData?.data?.[0]
  if (!Acan.isEmpty(outlines[type]['data'])) {
    const targetChild = outlines[type]['data'][target]['child']
    const targetCustom = outlines[type]['data'][target]['custom']
    const tags = outlines[type]['tags']

    let customIsAllNamed = !Acan.isEmpty(targetCustom)

    targetCustom?.forEach((item) => {
      if (Acan.isEmpty(item.name)) {
        customIsAllNamed = false
      }
    })

    if (Acan.isEmpty(targetChild) && !customIsAllNamed) {
      filled = false
    }

    if (!Acan.isEmpty(targetChild)) {
      const idsWithoutChild = getIdWithoutChild(outlines[type]['data'][target])
      idsWithoutChild.forEach((item) => {
        if (Acan.isEmpty(tags?.[item])) {
          filled = false
        }
      })
    }

    if (!Acan.isEmpty(targetCustom)) {
      targetCustom.forEach((item) => {
        if (Acan.isEmpty(tags?.[item._id])) {
          filled = false
        }
      })
    }
  }

  return filled
}

function getIdWithoutChild(obj) {
  const result = []
  if (obj.hasOwnProperty('_id') && (!obj.hasOwnProperty('child') || obj.child.length == 0)) {
    result.push(obj._id)
  }
  if (obj.hasOwnProperty('child') && obj.child.length) {
    obj.child.forEach((item) => {
      result.push(...getIdWithoutChild(item))
    })
  }
  return result
}

const cantChaneIt = async () => {
  if (route.query.view == 1 || localUnitData.value.del) {
    isReadonly.value = true
  } else if (localUnitData.value?.owner?._id === pub.user._id) {
    isReadonly.value = false
  } else {
    const {_id, members} = await App.service('collab').get(route.params.id, {query: {type: localUnitData.value.mode}})
    isReadonly.value = !members.some((member) => member.email === pub.user.email && member.status === true && member.role === 'write')
  }
  if (isReadonly.value) {
    let newPath = route.path.includes('/video/edit/') ? route.path.replace('/video/edit/', '/video/view/') : ''
    router.replace({path: newPath, query: route.query, params: route.params})
  } else {
    let newPath = route.path.includes('/video/view/') ? route.path.replace('/video/view/', '/video/edit/') : ''
    router.replace({path: newPath, query: route.query, params: route.params})
  }
}
const changeReadOnly = (isRead) => {
  isReadonly.value = isRead
  if (isRead) {
    let newPath = route.path.includes('/video/edit/') ? route.path.replace('/video/edit/', '/video/view/') : ''
    router.replace({path: newPath, query: route.query, params: route.params})
  }
}

const loading = ref(false)
function onUpdate(evt, sd) {
  if (sd.code == 'idea' || sd.code == 'words') {
    doUpdate(evt, sd)
    localUnitData.value[sd.code] = evt
  } else if (sd.type !== 'text' && sd.type !== 'textarea') {
    doUpdate(evt, sd)
  }
}
const doUpdate = async (data, sd) => {
  console.log('Do update is running')
  loading.value = true
  let post = {}
  if (sd.code && sd.code != 'connection') {
    post[sd.code] = data
    await isOkFn(post)
  } else {
    if (sd.tags) {
      // post[`ext.${sd.tags}`] = data
      post[`ext.${sd._id}`] = data
    } else if (sd.code == 'connection') {
      post[`ext.${sd.code}`] = data
    } else {
      post[`ext.${sd._id}`] = data
    }
    await patchOneById(props.taskId, post)
  }
  if (data.layer === true || data.layer === false) {
    upLearningGoal(data, sd)
  }
  loading.value = false
}

async function upLearningGoal(data, template) {
  let deleteIndex
  props.videoQuestions?.map(async (question) => {
    let goalOutline = question.outlines?.goal
    if (goalOutline?.length) {
      if (data.layer) {
        goalOutline?.map((goalTop) => {
          if (goalTop._id === template._id) {
            goalTop.child?.map((goalLayer) => {
              let matchLayer = data[template._id]?.find((layer) => layer._id === goalLayer._id)
              if (matchLayer) {
                goalLayer.child?.map((goalChild) => {
                  let matchChild = data[template._id + ':' + goalLayer._id]?.find((c) => c._id === goalChild._id)
                  if (!matchChild) {
                    deleteIndex = goalLayer.child.find((c) => c._id === goalChild._id)
                    goalLayer.child.splice(deleteIndex, 1)
                    if (!goalLayer.child.length) {
                      deleteIndex = goalTop.child.findIndex((l) => l._id === goalLayer._id)
                      goalTop.child.splice(deleteIndex, 1)
                      if (!goalTop.child.length) {
                        deleteIndex = goalOutline.findIndex((l) => l._id === goalTop._id)
                        goalOutline.splice(deleteIndex, 1)
                      }
                    }
                  }
                })
              } else {
                deleteIndex = goalTop.child.findIndex((l) => l._id === goalLayer._id)
                goalTop.child.splice(deleteIndex, 1)
                if (!goalTop.child.length) {
                  deleteIndex = goalOutline.findIndex((l) => l._id === goalTop._id)
                  goalOutline.splice(deleteIndex, 1)
                }
              }
            })
          }
        })
      } else {
        goalOutline?.map((goalTop) => {
          if (goalTop._id === template._id) {
            goalTop.child?.map((goalChild) => {
              let matchChild = data[template._id]?.find((c) => c._id === goalChild._id)
              if (!matchChild) {
                deleteIndex = goalTop.child.find((c) => c._id === goalChild._id)
                goalTop.child.splice(deleteIndex, 1)
                if (!goalTop.child.length) {
                  deleteIndex = goalOutline.findIndex((l) => l._id === goalTop._id)
                  goalOutline.splice(deleteIndex, 1)
                }
              }
            })
          }
        })
      }
    }
    await App.service('questions').patch(question._id, question)
  })

  // await App.service('interactive-videoes').patch(videoData.value._id, {quizes: videoData.value.quizes})
}

function onChange(evt, sd) {
  if (sd.type == 'text' || sd.type == 'textarea') {
    doUpdate(evt.target.value, sd)
  }
  if (sd.code == 'connection') {
    doUpdate(localUnitData.value.ext[sd.code], sd)
  }
}

async function isOkFn(post = {}) {
  if (!Acan.isEmpty(post)) {
    await patchOneById(props.taskId, post)
  }
}
const onTemplateChoosed = async (template) => {
  const obj = {curriculum: template?.curriculum, tpl: template?._id}
  if (obj.curriculum === 'pd') {
    selectedSubject.value = template
  }
  if (template.curriculum !== 'pd') {
    obj.service = {}
  }
  const rs = await patchOneById(localUnitData.value._id, obj)
  if (mode === 'video' && template.curriculum === 'pd') {
    const post = {}
    const prepend = 'pd'
    const label = selectedSubject.value?.label ?? ''
    const value = selectedSubject.value?.value ?? ''
    const subjectCode = selectedSubject.value?.code ?? ''
    const objectives = ['outline']
    post.task = rs._id
    if (subjectCode === 'essay') {
      //objectives.push(...['assess', 'skills'])
      //https://github.com/zran-nz/bug/issues/3969
    }
    objectives.forEach((item) => {
      post[`${item}.curr`] = prepend
      post[`${item}.data`] = {}
      post[`${item}.data`][`${prepend}:${value}`] = {
        child: [],
        curr: 'Service',
        code: `${prepend}:${value}`,
        name: label,
      }
    })
    await patchOneById(rs._id, {outlineSubjects: [label]})
    const res = props.outlineData
    const target = res?.data?.[0] || null
    if (target) {
      await App.service('task-outline').patch(target._id, post)
    }
  }
  await cantChaneIt()
}

function editScoreDialogOpen() {
  editScore.value = true
}

function closeEditDialog(qs) {
  emit('update-video-questions', qs)
  // videoData.value = video
  editScore.value = false
}

function setSectionStatus(newStatus) {
  section.value = {...section.value, ...newStatus}
}

onMounted(() => {
  emit('read-only-change', changeReadOnly)
})

watch(
  () => props.triggerCheck,
  () => {
    checkCanPublish()
  }
)

watch(
  () => props.unitData,
  (newVal) => {
    localUnitData.value = JSON.parse(JSON.stringify(newVal))

    const template = newVal?.template || []

    if (Array.isArray(template)) {
      tplBasic.value = template.filter((section) => section.group === 'basic')
      tplInquery.value = template.filter((section) => section.group === 'inquiry')
    } else {
      tplBasic.value = []
      tplInquery.value = []
    }

    // Handle ext initialization here
    if (!localUnitData.value.ext) {
      localUnitData.value.ext = {}
      tplInquery.value.forEach((e) => {
        if (!e?.code) {
          localUnitData.value.ext[e._id] = ''
        }
      })
    }
  },
  {deep: true, immediate: true}
)
</script>
