<template>
  <div class="flex items-center q-gutter-md" style="margin-left: 20px">
    <q-checkbox style="font-size: 14px; margin: 20px 0 0 10px" v-model="isListening" label="Speech" color="primary" />

    <q-select
      v-model="selectedLang"
      :options="languageOptions"
      emit-value
      map-options
      option-label="name"
      option-value="code"
      color="primary"
      borderless
      style="font-size: 14px; margin: 20px 0 0 10px" />

    <!-- Modal Dialog -->
    <q-dialog v-model="showSpeechModal" persistent>
      <q-card>
        <q-card-section class="text-h6"> {{ isListening ? 'Speech Mode Enabled' : 'Speech Mode Disabled' }} </q-card-section>

        <q-card-section> {{ isListening ? 'Please mute Zoom to avoid echo or overlapping audio.' : 'Please turn on zoom to speak' }} </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="OK" color="primary" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup>
import {ref, watch, onMounted, onBeforeUnmount} from 'vue'
import axios from 'axios'
import {roomStore} from 'stores/rooms'

const rooms = roomStore()

const translatedText = ref('')
const isListening = ref(false)
const selectedLang = ref('en-US')
const fullText = ref('')
const lastWord = ref('')
const recognition = ref(null)
const pauseTimer = ref(null)
const restartTimer = ref(null)
const showSpeechModal = ref(false)
const languageOptions = [
  {code: 'en-US', name: 'English (US)'},
  {code: 'de-DE', name: 'German (Germany)'},
  {code: 'it-IT', name: 'Italian (Italy)'},
  {code: 'ru-RU', name: 'Russian (Russia)'},
  {code: 'ja-JP', name: 'Japanese (Japan)'},
  {code: 'ko-KR', name: 'Korean (South Korea)'},
  {code: 'hi-IN', name: 'Hindi (India)'},
  {code: 'zh-CN', name: 'Chinese (Simplified, China)'},
]

// Translate text using Google Translate API
const synth = window.speechSynthesis

// Watch isListening
watch(isListening, async (newVal) => {
  showSpeechModal.value = true
  try {
    const response = await App.service('auth').patch('syncTeacher', {
      commit: 'student/setSpeakChecked',
      data: newVal,
    })
    if (newVal) {
      showSpeechModal.value = true
      startRecognition()
    } else {
      stopRecognition()
    }
  } catch (error) {
    console.error('Error in patch:', error)
  }
})

// Watch selectedLang
watch(selectedLang, () => {
  isListening.value = false
})

const translateText = async (text) => {
  try {
    const response = await App.service('translate').create({
      text: text,
      target: selectedLang.value,
    })
    translatedText.value = response?.translatedText
    console.log('Translated:', translatedText.value)
  } catch (error) {
    console.error('Translation Error:', error)
  }
}

// Watch for changes in spoken text
watch(
  () => rooms.textFromSpeech,
  async (val) => {
    if (val?.spokenText) {
      await translateText(val.spokenText)
    }
  },
  {immediate: true, deep: true}
)

watch(
  () => rooms.speakChecked,
  (val) => {
    isListening.value = val
  }
)

function initRecognition() {
  const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition
  if (!SpeechRecognition) {
    alert('Speech Recognition not supported.')
    return
  }

  recognition.value = new SpeechRecognition()
  recognition.value.continuous = true
  recognition.value.interimResults = true

  recognition.value.onresult = (event) => {
    const result = event.results[event.results.length - 1]
    const transcript = result?.[0]?.transcript?.trim()
    if (!transcript) return
    console.log('transcriopt')
    fullText.value = transcript

    if (pauseTimer.value) clearTimeout(pauseTimer.value)

    pauseTimer.value = setTimeout(() => {
      if (fullText.value && isListening.value) {
        sendSpokenText(fullText.value)
        fullText.value = ''
      }
    }, 1000)
  }

  recognition.value.onend = () => {
    if (isListening.value) {
      startRecognition()
    }
  }
  recognition.value.onaudioend = () => console.warn('Audio input ended')
  recognition.value.onspeechend = () => console.warn('Speech ended')
  recognition.value.onsoundend = () => console.warn('Sound ended')
}

function startRecognition() {
  if (!recognition.value) {
    initRecognition()
  }

  recognition.value.lang = selectedLang.value
  recognition.value.start()

  restartTimer.value = setTimeout(() => {
    if (isListening.value) {
      recognition.value.stop()
    }
  }, 50000)
}

function stopRecognition() {
  isListening.value = false

  if (recognition.value) {
    const originalOnEnd = recognition.value.onend
    recognition.value.onend = null

    try {
      recognition.value.stop()
    } catch (e) {
      console.warn('Error stopping recognition:', e)
    }

    setTimeout(() => {
      recognition.value.onend = originalOnEnd
    }, 1000)
  }

  if (pauseTimer.value) {
    clearTimeout(pauseTimer.value)
    pauseTimer.value = null
  }

  if (restartTimer.value) {
    clearTimeout(restartTimer.value)
    restartTimer.value = null
  }
}

async function sendSpokenText(spokenText) {
  try {
    const response = await App.service('auth').patch('syncTeacher', {
      commit: 'student/setStudentSpokenText',
      data: {spokenText, selectedLang: selectedLang.value},
    })
  } catch (error) {
    console.error('Error in patch:', error)
  }
}

onBeforeUnmount(() => {
  stopRecognition()
})

// Text-to-Speech using Web Speech API
const textToSpeech = (text) => {
  if (!text) return
  const utterance = new SpeechSynthesisUtterance(text)
  utterance.lang = selectedLang.value
  utterance.pitch = 1
  utterance.rate = 1
  window.speechSynthesis.speak(utterance)
}

watch(translatedText, (val) => {
  if (val) {
    textToSpeech(val)
  }
})

// Detect browser language on mount
onMounted(async () => {
  const browserLang = navigator.language || navigator.userLanguage
  let matchedLang = languageOptions.find((opt) => opt.code === browserLang)
  if (!matchedLang) {
    const baseLang = browserLang.split('-')[0]
    matchedLang = languageOptions.find((opt) => opt.code.startsWith(baseLang))
  }
  if (matchedLang) {
    selectedLang.value = matchedLang.code
  }
  initRecognition()
  const voices = synth.getVoices()
  if (voices.length === 0) {
    synth.onvoiceschanged = () => {
      synth.getVoices()
    }
    setTimeout(() => {
      synth.getVoices()
    }, 100)
  }
})
</script>
