<template>
  <q-dialog ref="dialogRef" @hide="onDialogHide" :persistent="persistent" style="border-radius: 24px">
    <q-card style="width: 500px; max-width: 90vw" class="bg-teal-1 q-pa-md">
      <q-card-section>
        <div class="text-h6 text-bold">Would you like to assign the associated task to the teacher of this section?</div>
      </q-card-section>
      <q-separator color="grey-5" dark />
      <q-card-section>
        <q-item class="row justify-end">
          <q-item-section class="col-6">
            <q-btn class="text-white bg-teal" rounded label="Assign" icon="check" style="font-size: 16px" no-caps @click="onOKClick"></q-btn>
          </q-item-section>
          <q-item-section class="col-6">
            <q-btn class="bg-white text-teal" outline rounded label="Not now" icon="arrow_back" style="font-size: 16px" no-caps @click="onDialogCancel"></q-btn>
          </q-item-section>
        </q-item>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script setup>
import {ref, computed, onMounted} from 'vue'
import {useDialogPluginComponent} from 'quasar'
import {servicePackageStore} from 'stores/service-package'
import {useRoute, useRouter} from 'vue-router'
import {pubStore} from 'stores/pub'

const servicePackage = servicePackageStore()
const pub = pubStore()
const router = useRouter()
const route = useRoute()
const props = defineProps({persistent: Boolean, id: String})

const isStudent = ref(pub.user?.roles?.includes('student'))
defineEmits([
  // REQUIRED; need to specify some events that your
  // component will emit through useDialogPluginComponent()
  ...useDialogPluginComponent.emits,
])

const {dialogRef, onDialogHide, onDialogOK, onDialogCancel} = useDialogPluginComponent()

const onOKClick = async () => {
  if (isStudent.value) {
    router.replace({
      path: `/study/purchased/view/${props.id}`,
      query: {
        tab: 'myAssociatedTask',
        subtab: 'unassigned',
        viewDetails: 'true',
        back: '/study/purchased?tab=myAssociatedTask&subtab=unassigned',
      },
    })
  } else {
    router.replace({
      path: `/home/<USER>/view/${props.id}`,
      query: {
        tab: 'myAssociatedTask',
        subtab: 'unassigned',
        viewDetails: 'true',
        back: '/home/<USER>',
      },
    })
  }
  onDialogOK()
}
</script>

<style scoped></style>
