<template>
  <q-dialog ref="dialogRef" @hide="onDialogHide" :persistent="persistent">
    <q-card style="width: 500px; max-width: 90vw; border-radius: 24px" class="bg-teal-1 q-px-lg q-py-md">
      <q-card-section v-if="isAbove12hours" class="q-py-none">
        <q-card class="bg-teal-1 q-pa-md" flat bordered style="width: 100%; border-color: orange; border-radius: 8px">
          <div class="col">
            <div class="row items-center q-mb-xs">
              <q-icon name="warning" color="orange" size="sm" class="q-mr-sm" />
              <div class="text-weight-medium text-subtitle1 text-orange">Warning</div>
            </div>
            <div class="text-grey-8 text-weight-medium q-mr-md" style="font-size: 16px">
              Cancelling the task after 12 hours of assigning will result in a 20% credit penality on the ongoing sections.
            </div>
          </div>
        </q-card>
      </q-card-section>
      <q-card-section>
        <div class="text-bold" style="font-size: 20px">Are you sure you want to cancel the task?</div>
      </q-card-section>
      <q-separator color="grey-5" dark />
      <q-card-section>
        <q-item class="row justify-end">
          <q-item-section class="col-6">
            <q-btn class="bg-white text-teal" outline rounded label="Not now" icon="arrow_back" style="font-size: 17px" no-caps @click="onDialogCancel"></q-btn>
          </q-item-section>
          <q-item-section class="col-6">
            <q-btn class="text-white bg-teal" rounded label="Cancel Task" icon="check" style="font-size: 17px" no-caps @click="onOKClick"></q-btn>
          </q-item-section>
        </q-item>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script setup>
import {ref, computed, onMounted} from 'vue'
import {useDialogPluginComponent} from 'quasar'
import {servicePackageStore} from 'stores/service-package'
import {useRoute, useRouter} from 'vue-router'
import {pubStore} from '../../../../stores/pub'

const servicePackage = servicePackageStore()
const router = useRouter()
const route = useRoute()
const pub = pubStore()
const isStudent = ref(pub.user?.roles?.includes('student'))

const props = defineProps({persistent: Boolean, isAbove12hours: Boolean, bookingId: String})

defineEmits([
  // REQUIRED; need to specify some events that your
  // component will emit through useDialogPluginComponent()
  ...useDialogPluginComponent.emits,
])

const {dialogRef, onDialogHide, onDialogOK, onDialogCancel} = useDialogPluginComponent()

const onOKClick = async () => {
  await App.service('service-booking').patch('associateCancel', {
    _id: props.bookingId,
    isDeduction: props.isAbove12hours,
  })
  if (isStudent.value) {
    router.replace({
      path: `/study/purchased`,
      query: {
        tab: 'myAssociatedTask',
        subtab: 'unassigned',
      },
    })
  } else {
    router.replace({
      path: `/home/<USER>
      query: {
        tab: 'myAssociatedTask',
        subtab: 'unassigned',
      },
    })
  }
  onDialogOK()
}
</script>

<style scoped></style>
