<template>
  <q-dialog ref="dialogRef" @hide="onDialogHide" :persistent="persistent">
    <q-card style="width: 550px; max-width: 90vw; border-radius: 24px" class="bg-blue-1 q-pa-md">
      <q-card-section>
        <q-btn flat icon="close" class="q-pa-sm" style="position: absolute; right: 0px; top: 2px; font-size: 16px" @click="onDialogCancel" />
        <div class="text-h6 text-bold">Please choose the service provider to assign the associated task</div>
      </q-card-section>
      <q-card-section class="q-py-none">
        <q-card class="bg-teal-1 q-pa-sm" flat bordered style="width: 100%">
          <div class="col">
            <div class="row items-center q-mb-xs">
              <q-icon name="info" color="primary" size="sm" class="q-mr-sm" />
              <div class="text-weight-bold text-subtitle1 text-primary">Info</div>
            </div>
            <div class="text-grey-8 text-weight-medium q-mr-md">
              The following service providers have been active and successfully completed consulting sessions within the last five days. Please review the list
              and select your preferred provider to assign to the associated task
            </div>
          </div>
        </q-card>
      </q-card-section>
      <q-card-section class="q-my-md q-mx-md" style="max-height: 200px; overflow-y: auto">
        <div v-if="loading" class="text-center q-pa-xl text-grey">
          <q-spinner-ball color="primary" size="2em" class="full-width" />
        </div>
        <NoData v-else-if="isEmpty(teachers)" message="No teacher" size="10rem" :messageClass="text - subtitle2" />
        <div v-else class="col" v-for="teacher in teachers" :key="teacher.uid">
          <q-radio v-model="selected" :val="teacher.uid" class="q-mb-lg q-ml-xs">
            <div class="row items-center q-gutter-sm q-ml-md">
              <q-avatar size="32px">
                <img :src="teacher.owner.avatar" />
              </q-avatar>
              <span class="text-subtitle1 text-weight-medium">{{ nameFormatter(teacher.owner) }}</span>
            </div>
          </q-radio>
        </div>
      </q-card-section>
      <q-separator color="grey-5" dark />
      <q-card-section>
        <q-item class="row justify-end">
          <q-item-section class="col-6">
            <q-btn
              class="text-white bg-teal"
              rounded
              label="Confirm"
              style="font-size: 16px"
              :disable="!selected"
              no-caps
              @click="handleConfirmTeacher"></q-btn>
          </q-item-section>
          <q-item-section class="col-6">
            <q-btn class="bg-white text-teal" outline rounded label="Assign to others" style="font-size: 16px" no-caps @click="handleAssignOthers">
              <q-tooltip max-width="350px" style="font-size: 13px"
                >If you wish to consider other service providers beyond the listed educators above, please ensure you book a mentoring session first to help you
                choose your preferred provider.</q-tooltip
              >
            </q-btn>
          </q-item-section>
        </q-item>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script setup>
import {ref, computed, onMounted} from 'vue'
import {useDialogPluginComponent} from 'quasar'
import {servicePackageStore} from 'stores/service-package'
import {useRoute, useRouter} from 'vue-router'
import {pubStore} from 'stores/pub'
import {EducatorGrades, GradeGroupMap} from 'src/boot/const'
import nameFormatter from 'src/utils/formatters/nameFormatter'

const servicePackage = servicePackageStore()
const router = useRouter()
const route = useRoute()
const pub = pubStore()

const props = defineProps({persistent: Boolean, id: String, parent: Object})

defineEmits([
  // REQUIRED; need to specify some events that your
  // component will emit through useDialogPluginComponent()
  ...useDialogPluginComponent.emits,
])

const {dialogRef, onDialogHide, onDialogOK, onDialogCancel} = useDialogPluginComponent()

const selected = ref(null)
const loading = ref(true)
const teachers = ref([])
const isStudent = ref(pub.user?.roles?.includes('student'))

const handleConfirmTeacher = async () => {
  await createBooking()
  if (isStudent.value) {
    router.replace({
      path: `/study/purchased`,
      query: {
        tab: 'myAssociatedTask',
        subtab: 'ongoing',
      },
    })
  } else {
    router.replace({
      path: `/home/<USER>
      query: {
        tab: 'myAssociatedTask',
        subtab: 'ongoing',
      },
    })
  }
}

const createBooking = async () => {
  const serviceBooking = {
    packUser: props.id,
    servicer: selected.value,
    start: new Date(),
    booker: pub.user._id,
  }

  console.log('fdkfkkf', serviceBooking)
  // return
  const res = await App.service('service-booking').create(serviceBooking)
  return res
}

const handleAssignOthers = () => {
  router.replace({
    path: `/detail/booking/my/${props.parent}`,
    query: {
      tab: 'purchased',
      back: route.fullPath,
      associatedTaskStatus: teachers.value.length > 0 ? 1 : 2,
    },
  })
}

const gradeOptions = (snapshot) => {
  const mentoringType = snapshot?.mentoringType
  if (['teacherTraining', 'teacherTrainingSubject'].includes(mentoringType)) {
    return EducatorGrades.map((value) => {
      return {label: value, value: value}
    }).filter((e) => snapshot?.gradeGroup?.includes(e.value))
  } else {
    if ((mentoringType == 'academic' && snapshot.curriculum) || mentoringType !== 'academic') {
      return Object.entries(GradeGroupMap)
        .map(([key, value]) => {
          value.key = key
          return value
        })
        .filter((e) => snapshot?.gradeGroup?.includes(e.key))
    } else {
      return false
    }
  }
}

onMounted(async () => {
  loading.value = true
  const packData = await App.service('service-pack-user').get(props.parent)
  const grades = gradeOptions(packData?.snapshot)
  const dto = {
    query: {
      packUserId: props.parent,
      gradeGroup: grades.map((e) => e.key || e.value),
    },
  }

  const fiveDaysAgo = new Date()
  fiveDaysAgo.setDate(fiveDaysAgo.getDate() - 5)

  const sessionQuery = {
    del: false,
    associatedTask: props.id,
    status: 'ended',
    ended: {$gte: fiveDaysAgo},
    students: pub.user._id,
    $sort: {ended: -1},
    $select: ['uid'],
  }

  const [availableTeachers, sessionRs] = await Promise.all([
    App.service('service-conf').get('teachersByPack', dto),
    App.service('session').find({query: sessionQuery}),
  ])

  const availableTeachersIds = availableTeachers.data.map((t) => t._id)
  console.log('dkkdkfk', availableTeachers.data, sessionRs.data, availableTeachersIds)

  const uniqueteachers = []
  const seen = new Set()

  for (const obj of sessionRs.data) {
    if (!seen.has(obj.uid)) {
      seen.add(obj.uid)
      uniqueteachers.push(obj)
    }
  }
  teachers.value = uniqueteachers.filter((t) => availableTeachersIds.includes(t.uid)).slice(0, 5)

  console.log('Filtered Available Teachers in Session Order:', teachers.value, uniqueteachers)
  loading.value = false
})
</script>

<style scoped>
.custom-tooltip {
  max-width: 220px;
  font-size: 12px;
  white-space: normal;
}
</style>
