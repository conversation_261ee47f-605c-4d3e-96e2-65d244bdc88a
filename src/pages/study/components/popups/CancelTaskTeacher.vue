<template>
  <q-dialog ref="dialogRef" @hide="onDialogHide" :persistent="persistent">
    <q-card style="width: 550px; max-width: 90vw; border-radius: 24px" class="bg-teal-1 q-px-lg q-py-md">
      <q-card-section class="q-py-none q-mt-md">
        <q-card class="bg-teal-1 q-pa-md" flat bordered style="width: 100%; border-color: orange; border-radius: 8px">
          <div class="col">
            <div class="row items-center q-mb-xs">
              <q-icon name="warning" color="orange" size="sm" class="q-mr-sm" />
              <div class="text-weight-medium text-subtitle1 text-orange">Warning</div>
            </div>
            <div class="text-grey-8 text-weight-medium q-mr-md" style="font-size: 16px">
              <span v-if="isDeduction">
                Cancelling this associated task will incur a 10% deduction from the income of completed sections. Are you sure you want to proceed with the
                cancellation?
              </span>
              <span v-else> Cancelling this associated task will negatively affect your performance rating and reduce your chances of future bookings. </span>
            </div>
          </div>
        </q-card>
      </q-card-section>
      <q-card-section>
        <div class="text-bold" style="font-size: 20px">Are you sure you want to proceed?</div>
      </q-card-section>
      <q-separator color="grey-5" dark />
      <q-card-section>
        <q-item class="row justify-end">
          <q-item-section class="col-6">
            <q-btn class="bg-white text-teal" outline rounded label="Not now" icon="arrow_back" style="font-size: 17px" no-caps @click="onDialogCancel"></q-btn>
          </q-item-section>
          <q-item-section class="col-6">
            <q-btn class="text-white bg-teal" rounded label="Cancel Task" icon="check" style="font-size: 17px" no-caps @click="onOKClick"></q-btn>
          </q-item-section>
        </q-item>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script setup>
import {ref, computed, onMounted} from 'vue'
import {useDialogPluginComponent} from 'quasar'
import {useRoute, useRouter} from 'vue-router'

const router = useRouter()
const route = useRoute()

const props = defineProps({persistent: Boolean, isDeduction: Boolean, bookingId: String})

defineEmits([
  // REQUIRED; need to specify some events that your
  // component will emit through useDialogPluginComponent()
  ...useDialogPluginComponent.emits,
])

const {dialogRef, onDialogHide, onDialogOK, onDialogCancel} = useDialogPluginComponent()

const onOKClick = async () => {
  await App.service('service-booking').patch('associateCancel', {
    _id: props.bookingId,
  })
  router.replace({
    path: `/home/<USER>
    query: {
      tab: 'taskManagement',
      subtab: props.isDeduction ? 'section-tracking' : 'ongoing',
    },
  })
  onDialogOK()
}
</script>

<style scoped></style>
