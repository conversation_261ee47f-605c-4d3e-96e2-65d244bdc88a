<template>
  <q-layout view="hHh LpR fFf"
    ><div class="shadow-1" style="background-color: #fffbfe">
      <q-sticky>
        <q-toolbar>
          <q-btn flat round icon="chevron_left" @click="goBack" />
          <q-toolbar-title class="text-bold">Task tracking</q-toolbar-title>
        </q-toolbar>
      </q-sticky>
    </div>
    <q-page-container class="pc-md">
      <q-page class="q-ma-na">
        {{ console.log('service-package: ', servicePackage) }}
        <q-banner v-if="isCreditDeficit" inline-actions class="bg-amber-2 text-black q-mb-md q-mt-sm q-mx-xs"
          >You do not have enough credits to assign this task
          <template v-slot:action> <q-btn flat icon="close" class="q-pa-sm" @click="isCreditDeficit = false" /> </template
        ></q-banner>
        <ProductDetail :taskData="taskData" />
        <div v-if="route.query.subtab === 'unassigned'">
          <q-btn
            v-if="isCreditDeficit"
            class="q-mx-sm q-mb-md full-width bg-white text-teal-7"
            rounded
            no-caps
            label="Purchase More"
            @click="handlePurchaseMore" />
          <q-btn v-else class="q-mx-sm q-mb-md full-width bg-teal-4 text-white" rounded no-caps label="Assign" @click="handleAssign" />
        </div>
        <Sections :sections="sections" />
        <div class="row justify-center q-mt-lg">
          <q-btn
            class="q-mx-sm q-mb-md"
            color="#26A69A"
            text-color="primary"
            rounded
            no-caps
            :style="{
              width: '1088px',
              height: '40px',
              fontFamily: 'Roboto',
              fontWeight: '500',
              fontSize: '14px',
              lineHeight: '20px',
              color: '#F1F9F9',
              boxShadow: '0px 1px 3px 1px #00000026, 0px 1px 2px 0px #00000026',
            }"
            @click="handleCancelTask">
            Cancel
          </q-btn>
        </div>
      </q-page>
    </q-page-container>
  </q-layout>
</template>

<script setup>
//imports
import {ref, onMounted} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {servicePackageStore} from 'stores/service-package'
import HeaderDrawer from '../../components/HeaderDrawer.vue'
import PackageEditPage from '../sys/package/service-task/PackageEditPage.vue'
import ProductDetail from 'src/components/ServiceTask/ProductDetail.vue'
import Sections from 'src/components/ServiceTask/Sections.vue'
import AssignTeacher from './components/popups/AssignTeacher.vue'
import CancelTaskStudent from './components/popups/CancelTaskStudent.vue'

//state
const route = useRoute()
const router = useRouter()
const servicePackage = servicePackageStore()
const isCreditDeficit = ref(false)
const taskData = ref({})
const sections = ref([])

//functions
function handleAssign() {
  $q.dialog({
    component: AssignTeacher,
    componentProps: {},
  })
    .onOk(() => {})
    .onCancel(() => {})
}

function handlePurchaseMore() {
  router.push(`/study/service-tasks?tab=unassigned`)
}

function goBack() {
  router.back()
}

function handleCancelTask() {
  $q.dialog({
    component: CancelTaskStudent,
    componentProps: {
      persistent: true,
    },
  })
    .onCancel(() => {
      console.log('Cancel')
    })
    .onOk(() => {
      router.push(`/study/service-tasks?tab=unassigned`)
    })
}

async function main() {
  if (route.params?.id) {
    // const packageData = await servicePackage.getOnePackage(route.query?.id)
    // if (packageData) {
    //   const packCurriculum = {}
    //   let gradeGroup = {
    //     label: '',
    //     grades: packageData.gradeGroup,
    //   }
    //   servicePackage.loadData(packageData, packCurriculum, gradeGroup)
    //   // if (servicePackage.contentOrientatedEnable) {
    //   //   await getBundledDetail()
    //   // }
    // }
    const rs = await App.service('service-pack-user').get(route.params?.id)
    const rs1 = await App.service('section').find({query: {serviceTaskId: route.params.id}})
    console.log('rs value', rs.data)
    console.log('rs1 value', rs1.data)
    taskData.value = rs.snapshot
    sections.value = rs1.data
  }
}

onMounted(async () => {
  $q.loading.show()

  try {
    servicePackage.type = 'serviceTask'
    await servicePackage.init()
    console.log('fjgfjgjgjgj', servicePackage.serviceRoles, servicePackage.state)

    if (servicePackage.serviceRoles || route.query?.id) {
      await main()
      await sleep(500)
    }
  } catch (err) {
    console.error('Error loading service package', err)
  } finally {
    $q.loading.hide()
  }
})
</script>
