<template>
  <q-dialog v-model="isModalVisible" ref="dialogRef" @hide="onHide" class="dialog">
    <q-card class="bg-teal-1">
      <q-card-section>
        <q-form @submit="onSubmit" @reset="onReset" class="q-gutter-md">
          <template v-for="(item, index) in list" :key="index">
            <div>{{ item.question }}</div>
            <q-input v-model="item.answer" filled type="textarea" outlined placeholder="Enter your answer" />
          </template>
        </q-form>
      </q-card-section>

      <q-separator />
      <q-card-actions>
        <div class="flex q-gutter-md full-width">
          <q-btn rounded outline class="flex-grow" color="primary" label="Cancel" icon="arrow_back" @click="onHide" />
          <q-btn rounded class="flex-grow" color="primary" :disable="!list.every((item) => item.answer)" label="Submit" icon="done" @click="onSubmit" />
        </div>
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import {ref, computed, watchEffect, onMounted} from 'vue'
import {useDialogPluginComponent} from 'quasar'
import nameFormatter from 'src/utils/formatters/nameFormatter'
import useGrade from 'src/composables/account/academic/useGrade'
import {pubStore} from 'stores/pub'
import useSchool from 'src/composables/common/useSchool'

const pub = pubStore()
const {schoolId, isAdmin} = useSchool()
const emit = defineEmits([...useDialogPluginComponent.emits, 'save'])
const {dialogRef, onDialogHide, onDialogOK, onDialogCancel} = useDialogPluginComponent()
const {list: gradeList} = useGrade()
const classList = ref([])
const studentList = ref([])
const studentDict = ref({})
const activeGrade = ref()
const activeClass = ref()
const checkedAll = ref(false)
const list = ref([])

const props = defineProps({
  isVisible: {
    type: Boolean,
  },
  setIsVisible: {
    type: Function,
  },

  questions: {
    type: Array,
    default: () => [],
  },
})

const onHide = (e) => {
  isModalVisible.value = false
}

const isModalVisible = computed({
  get() {
    return props.isVisible
  },
  set(newValue) {
    props.setIsVisible(newValue)
  },
})

watchEffect(() => {
  list.value = props.questions.map((item) => {
    return {
      question: item,
      answer: '',
    }
  })
})

const onSubmit = async () => {
  emit('save', list.value)
  onHide()
}
</script>
<style lang="scss">
body.screen--sm,
body.screen--md {
  .dialog {
    .q-card {
      min-width: 600px;
    }
  }
}
.flex-grow {
  flex-grow: 1;
}
</style>
