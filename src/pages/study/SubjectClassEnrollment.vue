<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div>
    <PubTopBanner title="Subject class enrollment" :isShowMenu="false" />
    <q-page-container>
      <q-page class="column pc-max">
        <NoData message="No available subject class for enrollment" messageColor="grey" v-if="isEmpty(list)" />
        <div class="q-mt-md" v-for="curriculum in list" :key="curriculum.curriculum._id">
          <div class="text-primary text-h4">{{ curriculum.curriculum.name }}</div>
          <div v-for="subject in curriculum.subjectList" :key="subject.subject._id" class="q-mt-md">
            <div class="text-primary text-h6">{{ subject.subject.name }}</div>
            <div class="row q-col-gutter-lg">
              <div class="col-6" v-for="subjectClass in subject.classList" :key="subjectClass._id">
                <div class="border-grey rounded-borders-md overflow-hidden">
                  <q-img
                    :src="hashToUrl(subjectClass.attachmentsCover?.hash) || '/v2/img/no-img.png'"
                    class="full-width cursor-pointer"
                    style="height: 260px"
                    @click="goToDetail(subjectClass._id)"></q-img>
                  <div class="q-pa-sm">
                    <div class="text-h5 ellipsis">
                      {{ subjectClass.name }}
                      <q-tooltip anchor="top middle"> {{ subjectClass.name }} </q-tooltip>
                    </div>
                    <div class="row">
                      <div class="text-red">Enrolment ends in: <CountDown :deadTime="subjectClass?.deadline" /></div>
                      <div class="q-ml-md row items-center">
                        <q-icon name="group" size="18px" />
                        <span class="q-ml-sm">Enrolled</span>
                        <span class="q-ml-sm">
                          {{ subjectClass.count.student }}{{ subjectClass.maxParticipants ? `/${subjectClass.maxParticipants}` : '' }}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </q-page>
    </q-page-container>
  </div>
</template>

<script setup>
import {ref, watch, computed, watchEffect, onMounted} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import AppTab from 'src/components/pub/AppTab.vue'

import StudentList from 'components/account/students/SubjectList.vue'
import useClasses from 'src/composables/account/school/useClasses'
import useSchool from 'src/composables/common/useSchool'
import {pubStore} from 'stores/pub'
import CountDown from 'src/pages/order/CountDown.vue'

const $router = useRouter()
const $route = useRoute()
const pub = pubStore()
const classId = ref($route.query?.classId)
const {schoolId} = useSchool()
const list = ref([])

const getClasses = async () => {
  $q.loading.show()
  const res = await App.service('classes').get('subjectClassApplyList', {query: {classId: classId.value, school: schoolId.value}})
  list.value = res.list
  $q.loading.hide()
}

const goToDetail = (id) => {
  $router.push({path: 'subjectClassDetail/' + id})
}

onMounted(async () => {
  await getClasses()
})
</script>

<style lang="scss">
.border-grey {
  border: 1px solid #aaa;
}
</style>
