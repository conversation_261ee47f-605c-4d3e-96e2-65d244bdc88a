<template>
  <q-dialog ref="dialogRef" @hide="onDialogHide" :maximized="$q.screen.lt.sm">
    <q-card class="q-pa-md" style="width: 840px">
      <q-card-section class="q-pt-none">
        <div class="q-mt-md" >
          <q-btn rounded class="full-width q-mb-md" color="orange-3" align="between"
            :icon-right="basicShow ? 'keyboard_arrow_up' : 'keyboard_arrow_down'" no-caps label="Applicant basic"
            @click="basicShow = !basicShow" />
          <q-slide-transition>
            <section class="" v-show="basicShow">
              <div class="flex justify-between items-center q-my-md">
                <div class="flex items-center q-gutter-md">
                  <PubAvatar :src="detail?.userInfo?.avatar" size="3rem" />
                  <div>
                    <div class="text-subtitle2">{{ detail?.name.join('') }}</div>
                    <div class="text-grey-8">{{ gender[detail?.gender] || detail?.gender }}</div>
                  </div>
                  <Chat :item="detail" role="admin" @cb="main" />
                </div>
                <div class="text-grey">{{ date.formatDate(detail?.updatedAt, 'MM/DD/YYYY HH:mm:ss') }}</div>
              </div>
              <div class="q-ml-md">
                <div class="text-subtitle2">Email</div>
                <div class="text-grey-8 q-mt-sm">{{ detail?.email }}</div>
              </div>
              <div class="q-ml-md q-mt-md">
                <div class="text-subtitle2">Phone number</div>
                <div class="text-grey-8 q-mt-sm">{{ detail?.mobile }}</div>
              </div>
            </section>
          </q-slide-transition>
          <template v-if="detail?.needAcademic">
            <q-btn rounded class="full-width q-mb-md q-mt-md" color="teal-3" align="between"
              :icon-right="acCheckShow ? 'keyboard_arrow_up' : 'keyboard_arrow_down'" no-caps
              label="Academic check items" @click="acCheckShow = !acCheckShow" />
            <q-slide-transition>
              <section class="" v-show="acCheckShow">
                <div class="">
                  <div class="text-subtitle">Academic requirements</div>
                  <div class="text-body2 q-mt-sm">{{ detail?.servicePackInfo?.requirements }}</div>
                </div>
                <div class="text-h6 q-mt-md">Verification materials provided</div>
                <div class="q-mt-md"
                  v-for="item in formatAcBgCheckData(detail?.servicePackInfo?.requirementsItems || [])"
                  :key="item.category">
                  <div class="text-subtitle text-bold text-primary">{{ item?.category }}</div>
                  <div class="q-mt-sm">
                    <div class="q-mb-md" v-for="inItem in item?.child" :key="inItem.category">
                      <div class="">{{ inItem?.category }}</div>
                      <div class="row">
                        <q-card v-for="item in detail.attachments.filter((e) => e?.type === inItem?.category)"
                          :key="item._id" class="item-wrapper relative-position q-pa-sm q-my-sm col-12">
                          <div class="flex justify-between items-center">
                            <div class="flex no-wrap items-center">
                              <q-icon class="q-mr-sm" :name="getFileIcon(item)" color="teal-4" size="sm" />
                              <div>{{ item?.filename }}</div>
                            </div>
                            <div>
                              <Preview :item="item">
                                <q-btn flat rounded dense icon="crop_free" color="teal"
                                  style="background: rgba(0, 0, 0, 0.1)" />
                              </Preview>

                            </div>
                          </div>
                        </q-card>
                      </div>
                    </div>
                  </div>
                </div>
              </section>
            </q-slide-transition>
          </template>
          <div v-if="detail?.takeaway" class="q-mt-lg">
            <q-btn rounded no-caps label="Check takeaway" :to="detail?.takeaway" target="_blank" color="primary" />
          </div>
        </div>
      </q-card-section>
      <q-card-actions class="row q-col-gutter-md">
        <div class="col-6"></div>
        <div class="col-6">
          <q-btn class="full-width" color="primary" outline no-caps rounded label="Cancel" v-close-popup />
        </div>
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { computed, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { pubStore } from 'stores/pub'
import { formatAcBgCheckData } from 'src/pages/sys/package/const.js'
import { getFileIcon } from 'src/boot/const'
import PackageChips from 'components/PackageChips.vue'
import { date } from 'quasar'
import { useDialogPluginComponent } from 'quasar'
import Preview from 'src/pages/account/OnCampusVerify/Preview.vue'

const props = defineProps({
  detail: Object,
})

const basicShow = ref(true)
const acCheckShow = ref(true)

const gender = {
  0: 'Male',
  1: 'Female',
}


defineEmits([...useDialogPluginComponent.emits])

const { dialogRef, onDialogHide, onDialogOK, onDialogCancel } = useDialogPluginComponent()

const main = async () => {
  console.log('props', props?.detail)
}

onMounted(main)
</script>
