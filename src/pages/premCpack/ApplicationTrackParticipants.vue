<template>
  <q-layout view="hHh LpR fFf" class="bg-white">
    <q-header class="bg-teal-1 text-black" elevated>
      <q-toolbar class="">
        <q-btn dense flat icon="navigate_before" round @click="router.back()"></q-btn>
        <q-toolbar-title class=" text-h6">
          {{ servicePack?.name }}
        </q-toolbar-title>
      </q-toolbar>
    </q-header>
    <q-page-container class="pc-sm page-box">
      <q-page class="q-pa-md q-mt-md q-pb-xl">
        <q-banner inline-actions rounded class="bg-amber-2 text-black q-mt-md" v-if="!tipUnPubClose">
          Below shows all approved participants of your school, selected participants will be able to view this course
          under school’s lecture room. Enrolment ends 2 hours before the start of every session.
          <template v-slot:action>
            <q-btn flat icon="close" class="q-pa-sm" @click="tipUnPubClose = true" />
          </template>
        </q-banner>
        <div class="row justify-between items-center q-mb-xs q-mt-lg">
          <div class="col-sm-4 col-xs-12">
            <div class="row q-col-gutter-x-lg">
              <div class="col-12">
                <q-input class="bg-teal-1 rounded-borders-xl" outlined rounded dense color="teal" label="Search by name"
                  v-model="search" @keyup.enter="doSearch()" @clear="doSearch()" clearable>
                  <q-btn flat icon="search" @click="doSearch()"></q-btn>
                </q-input>
              </div>
            </div>
          </div>
          <div class="q-my-md text-left col-sm-7 col-xs-12 q-pl-xl">
            <q-option-group inline :options="schoolOptions" type="checkbox" v-model="schoolType"
              @update:modelValue="doSearch">
              <template v-slot:label="opt">
                <div class="">
                  <span>{{ opt.label }}</span>
                  <template v-if="schoolTypeCount[opt.type] > 0"> ({{ schoolTypeCount[opt.type] }})</template>
                </div>
              </template>
            </q-option-group>
          </div>
        </div>
        <DetailView ref="child" :load="handleLoad" class="q-mt-lg" @ready="onLoad">
          <div class="rounded-borders-sm q-mt-md q-border-1 q-pa-md bg-white shadow-1">
            <div>
              <div class="row text-grey-6">
                <div class="col">Name</div>
                <div class="col">Status</div>
                <div class="col">Type</div>
                <div class="col">Payment</div>
                <div class="col">Last update</div>
              </div>
              <div class="" v-for="item in child.data" :key="item._id">
                <div class="row q-py-sm items-center">
                  <div class="col text-primary text-capitalize cursor-pointer">
                    <div class="row items-center">
                      <PubAvatar :src="item?.userInfo?.avatar" size="1.5rem" />
                      <div class="text-bold q-ml-sm">{{ item?.userInfo?.name.join(' ') }}</div>
                    </div>
                  </div>
                  <div class="col">
                    <q-chip class="text-weight-medium" :ripple="false" size="12px" :color="chipColor(item).color"
                      :text-color="chipColor(item).text" :label="sysEnrollStatus(item)" />
                  </div>
                  <div class="col">
                    {{ item?.withinSchool ? 'From school' : 'Outside' }}
                    <q-icon v-if="!item?.withinSchool" name="add_circle_outline" class="cursor-pointer" color="primary"
                      size="sm" @click="toInvite(item)" />
                  </div>
                  <div class="col">
                    <template v-if="item?.needOrder">
                      <q-chip v-if="item?.order?.length > 0" class="text-weight-medium" :ripple="false" size="12px"
                        color="teal-1" text-color="primary" label="Paid" />
                      <q-chip v-else class="text-weight-medium" :ripple="false" size="12px" color="red-2"
                        text-color="red" label="Unpaid" />
                    </template>
                    <template v-else>-</template>
                  </div>
                  <div class="col">{{ date.formatDate(item.updatedAt, 'MM/DD/YYYY HH:mm:ss') }}</div>
                </div>
                <q-separator class="q-mt-sm" />
              </div>
            </div>
          </div>
        </DetailView>
      </q-page>
    </q-page-container>
  </q-layout>
</template>
<script setup>
/*
  imports
*/
import { computed, onMounted, ref } from 'vue'
import DetailView from 'components/DetailView.vue'
import { useRoute, useRouter } from 'vue-router'
import { pubStore } from 'stores/pub'
import Header from './Header.vue'
import { sysEnrollStatus, chipColor } from 'src/pages/premCpack/consts'


import { date } from 'quasar'


const route = useRoute()
const router = useRouter()

const tab = ref(+route.query.tab || 0)

const schoolOptions = [
  { label: 'From school', value: true, type: 'within' },
  { label: 'Outside school', value: false, type: 'outside' },
]
const pub = pubStore()

const id = ref(route.params.id)
const servicePack = ref(null)


const search = ref('')

const child = ref(null)
const schoolType = ref([])
const schoolTypeCount = ref({})
const schoolPriceDetail = ref(null)


const tipUnPubClose = ref(false)


const doSearch = () => {
  child.value.onReset()
  onLoad()
}


const onLoad = async () => {
  child.value.onLoad()
}

const handleLoad = async () => {
  const query = {
    $limit: 100,
    $sort: { updatedAt: -1 },
    status: tab.value,
    sharedSchool: pub?.user?.school,
    servicePack: id.value,
    status: 1,
  }


  if (schoolType.value?.length > 0) {
    query.withinSchool = {
      $in: schoolType.value,
    }
  }

  if (search.value) {
    query['nickname'] = {
      $regex: search.value.toLowerCase(),
    }
  }

  console.log('query', query)
  getSchoolTypeCount(query)
  return App.service('service-pack-apply').find({ query })
}


const getSchoolTypeCount = async (query) => {
  const data = await App.service('service-pack-apply').get('count', {
    query: {
      ...query,
      $limit: 1000,
    }
  })
  console.log('count', data)
  if (data) {
    schoolTypeCount.value = data
  }
}

const toInvite = async (item) => {
  console.log('item', item)
  router.push({
    path: `/account/student/add`,
    query: {
      schoolId: item?.sharedSchool,
      studentId: item?.uid,
      packApplyId: item?._id,
      back: route.fullPath,
    },
  })
}

const getServicePack = async () => {
  const data = await App.service('service-pack').get(id.value)
  if (data) {
    servicePack.value = { ...data }
  }
}

onMounted(() => {
  getServicePack()
})
</script>
<style lang="scss" scoped>
.page-box {
  padding-left: 0 !important;
}
</style>
