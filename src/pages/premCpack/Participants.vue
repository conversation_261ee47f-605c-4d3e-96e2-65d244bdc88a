<template>
  <q-layout view="hHh LpR fFf" class="bg-white">
    <q-header class="bg-teal-1 text-black" elevated>
      <q-toolbar class="">
        <q-btn dense flat icon="navigate_before" round @click="goBack"></q-btn>
        <q-btn v-if="$q.screen.lt.md" flat @click="() => { drawer = !drawer }" round dense icon="menu" />
        <div class="text-bold q-ml-md">Set participants</div>
      </q-toolbar>
    </q-header>
    <q-page-container class="pc-sm page-box">
      <q-form @submit="onSubmit" @validation-error="onSubmitError">
        <q-drawer v-model="drawer" :width="250" :breakpoint="500" bordered>
          <div class="q-pa-md">
            <div class=" row items-center justify-between q-mb-md">
              <q-img src="~/assets/img/zoom.png" style="height: 30px; width: 75px"></q-img>
              <q-toggle v-model="form.enabled" :disable="isOnline"></q-toggle>
            </div>
            <q-separator class="q-mt-md"></q-separator>
            <q-expansion-item default-opened>
              <template v-slot:header>
                <q-item-section class="text-bold">Participants</q-item-section>
              </template>
              <div class=" row items-center justify-between q-mb-md">
                <div>Max participants</div>
                <q-toggle v-model="form.maxParticipants"></q-toggle>
              </div>
              <q-input v-if="form.maxParticipants" color="primary" square outlined label="Max" type="number"
                v-model.number="form.max" :rules="[maxRule]">
                <template v-slot:append>
                  <span class="text-subtitle2 text-black" v-if="form.enabled">/100</span>
                  <q-btn flat rounded no-caps dense icon="help_outline">
                    <q-tooltip class="text-caption">
                      It can not exceed the participant limit of your zoom account <br>
                      and it can not be less than the minimal number
                    </q-tooltip>
                  </q-btn>
                </template>
              </q-input>
              <div class="q-my-md">Minimal participants</div>
              <q-input color="primary" square outlined label="Min" type="number" v-model.number="form.min"
                :rules="[minRule]">
                <template v-slot:append>
                  <q-btn flat rounded no-caps dense icon="help_outline">
                    <q-tooltip class="text-caption">
                      If the enrolled participants have not reached this number, <br>
                      the session will be automatically end on the enrolment deadline. <br>
                      Existing enrolments will be cancelled with refund and noticement. <br>
                      You might relaunch it again.
                    </q-tooltip>
                  </q-btn>
                </template>
              </q-input>
              <q-input readonly class="q-mt-md" outlined model-value="2 hours before session starts"
                label="Enrolment deadline" stack-label />
            </q-expansion-item>
          </div>
        </q-drawer>
        <q-page class="q-pa-md ">
          <q-banner inline-actions rounded class="bg-amber-2 text-black q-mt-md" v-if="bannerTip">
            <template v-if="tipObject === 'teacher'">
              Below shows all approved educators of your school, selected participants will be able to view this course
              under the page of “I participate_Mentor”. Enrolment ends 2 hours before the start of every session.
            </template>
            <template v-else>
              Below shows all approved participants of your school, selected participants will be able to view this
              course under school’s lecture room. Enrolment ends 2 hours before the start of every session.
            </template>
            <template v-slot:action>
              <q-btn flat icon="close" class="q-pa-sm" @click="bannerTip = false" />
            </template>
          </q-banner>
          <div class="text-right q-mt-md">
            <q-btn class="text-primary q-mr-lg" outline no-caps rounded label="Go to application tracking"
              :to="`/premcpack/applicationTrackParticipants/${route.query?.packId}`" />
            <q-checkbox left-label v-model="isAll" label="All" @update:model-value="setAll()" />
            <q-btn flat color="primary" no-caps label="Clear all" @click="clearAll" />
          </div>
          <q-list class="row q-mt-md" v-if="list?.length > 0">
            <q-item v-for="item in list" :key="item._id" tag="label" v-ripple class="col-sm-6 col-xs-12">
              <q-item-section avatar>
                <PubAvatar :src="item?.userInfo?.avatar" size="2.5rem" />
              </q-item-section>
              <q-item-section>
                <div>
                  {{ item?.userInfo?.nickname }}
                  <template v-if="item?.needOrder">
                    <q-chip v-if="item?.order?.length > 0" class="text-weight-medium" :ripple="false" size="12px"
                      color="teal-1" text-color="primary" label="Paid" />
                    <q-chip v-else class="text-weight-medium" :ripple="false" size="12px" color="red-2" text-color="red"
                      label="Unpaid" />
                  </template>
                </div>
              </q-item-section>
              <q-item-section avatar>
                <q-checkbox v-model="form.participants" :val="item.uid" color="teal" />
              </q-item-section>
            </q-item>
          </q-list>
          <NoData v-if="isEmpty(list)" class="q-mt-md" message="No participants to choose" message-color="black" />

          <q-btn rounded class="full-width q-mt-md" color="primary" no-caps label="Confirm" icon="check"
            type="submit" />
        </q-page>
      </q-form>
    </q-page-container>
  </q-layout>
</template>
<script setup>
/*
  imports
*/
import { computed, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { date } from 'quasar'
import AccountMenu from 'components/AccountMenu.vue'
import { pubStore } from 'stores/pub'
import { servicePackageStore } from 'stores/service-package'



const pub = pubStore()
const servicePackage = servicePackageStore()
const route = useRoute()
const router = useRouter()

const form = ref({
  enabled: true,
  maxParticipants: true,
  min: 1,
  participants: [],
})

const isAll = ref(true)

const id = ref(route.params.id)

const bannerTip = ref(true)
const packDetail = ref(null)
const packUser = ref(null)

const drawer = ref(true)
const list = ref([])



const isOnline = computed(() => {
  return !packDetail.value?.isOnCampus
})

const tipObject = computed(() => {
  const serviceRole = servicePackage.mentoringTypeList.find((e) => e.value === packDetail.value?.mentoringType)
  return serviceRole?.educator ? 'teacher' : 'student'
})


const clearAll = () => {
  isAll.value = false
  form.value.participants = []
}


const maxRule = (val) => {
  if (form.value.enabled) {
    return Number.isInteger(val) && val >= 1 && val <= 100 ? true : 'Value must be an integer between 1 and 100'
  }
  return Number.isInteger(val) && val >= 1 ? true : 'Value must be an integer between 1 and 100'
}

const minRule = (val) => {
  return Number.isInteger(val) && val >= 1 ? true : 'Value must be an integer greater than 0'
}


const goBack = (hash) => {
  router.go(-1)
}

const toApplication = () => {
  router.push({
    path: `/premcpack/applicationTrackDetail/${id.value}`,
  })
}

const setAll = () => {
  form.value.participants = isAll.value ? list.value.map((item) => item.uid) : []
}

const onSubmitError = (errors) => {
  console.log('onSubmitError', errors)
  drawer.value = true
}

const onSubmit = async () => {
  console.log('submitFn')
  if (form.value.participants?.length < 1) {
    $q.notify({ type: 'negative', message: 'Please select participants' })
    return
  }
  const data = {
    participants: form.value.participants,
    zoom: { ...form.value, participants: undefined },
  }
  console.log('data', data)
  await App.service('service-pack-user').patch(id.value, data,{ query: { $sys: 1 } }).then((res) => {
    $q.notify({ type: 'positive', message: 'Set participants successfully' })
    goBack()
  }).catch((err) => {
    $q.notify({ type: 'negative', message: err?.message })
  })

}


const getParticipants = async () => {
  const query = {
    $limit: 100,
    sharedSchool: pub?.user?.school,
    servicePack: route.query?.packId,
    archive: false,
    status: 1,
    contentOrientated: {
      $elemMatch: {
        premium: route.query?.lectureId,
      },
    },
  }

  $q.loading.show()
  let res = await App.service('service-pack-apply').find({ query })
  $q.loading.hide()
  if (res) {
    list.value = res?.data || []
  }
}


const getServicePack = async () => {
  await servicePackage.findServiceTypes()
  let res = await App.service('service-pack').get(route.query?.packId)
  if (res) {
    packDetail.value = res || {}
    if (!res.isOnCampus) {
      form.value.enabled = true
    }
  }
}


const getPackUser = async () => {
  const res = await App.service('service-pack-user').get(id.value)
  if (res) {
    packUser.value = res || {}
    const {
      participants,
      zoom,
    } = res
    if (participants) {
      form.value = {
        ...zoom,
        participants: participants,
      }
    }
  }
}


const main = async () => {
  getParticipants()
  await getServicePack()
  await getPackUser()
}

onMounted(main)
</script>
<style lang="scss" scoped></style>
