<template>
  <div class="row justify-between q-py-sm text-subtitle1">
    <div class="items-center flex">
      <span class=""> Gift card </span>
    </div>
    <div class="row items-center">
      <div v-if="isApplied" class="text-primary">-${{ (applicableGiftAmount / 100).toFixed(2) }}</div>
      <div class="arrow-gift" v-if="isApplyOpen"><q-icon name="arrow_drop_up" size="30px" @click="isApplyOpen = !isApplyOpen" class="cursor-pointer" /></div>
      <div class="arrow-gift" v-else><q-icon name="arrow_drop_down" size="30px" @click="isApplyOpen = !isApplyOpen" class="cursor-pointer" /></div>
    </div>
  </div>

  <div v-if="isLoading" class="row justify-center q-py-sm text-subtitle1">
    <q-spinner color="primary" size="32px" />
  </div>
  <div v-if="isApplyOpen && !isLoading">
    <div class="q-py-sm text-subtitle1 text-weight-bold">
      <div class="items-center flex">
        <span class=""> Gift card balance: ${{ ((giftCardBalance - applicableGiftAmount) / 100).toFixed(2) }} </span>
      </div>
    </div>
    <div class="row q-py-sm text-subtitle1 q-pa-none">
      <q-checkbox v-model="isApplied" size="32px" style="margin: 0; padding: 0" :disable="giftCardBalance <= 0" />
      <q-icon name="card_giftcard" size="32px" color="primary" />
      <div class="q-ml-sm text-subtitle1 text-weight-bold">Apply gift cards</div>
    </div>
  </div>
</template>
<script setup>
import {onMounted, ref, watch, watchEffect} from 'vue'

const emit = defineEmits(['balance'])
const isApplied = ref(false)
const isApplyOpen = ref(false)
const giftCardBalance = ref(0)
const isLoading = ref(false)

const emitAppliedBalance = () => {
  const valueToEmit = isApplied.value ? giftCardBalance.value : 0
  emit('balance', valueToEmit)
}

const props = defineProps({
  applicableGiftAmount: {
    type: Number,
    default: 0,
  },
  school: {
    type: Number,
    default: undefined,
  },
})

//when isApply is true call the api and load the gift card balance
const getGiftCardBalance = async () => {
  isLoading.value = true
  try {
    let rs = null
    if (props.school) {
      rs = await App.service('wallet-balance').get('balance', {query: {uid: props.school, balanceType: 'giftCard'}})
    } else {
      rs = await App.service('wallet-balance').get('balance', {query: {balanceType: 'giftCard'}})
    }
    giftCardBalance.value = rs.availableBalance
  } catch (error) {
    console.error(error)
  } finally {
    isLoading.value = false
  }
}

watch(isApplied, emitAppliedBalance)
watch(isApplyOpen, (newValue) => {
  if (newValue === true) {
    getGiftCardBalance()
  }
})

defineExpose({giftCardBalance})
</script>
<style scoped>
.arrow-gift {
  position: absolute;
  right: 4px;
}
</style>
