<template>
  <q-dialog ref="dialogRef" @hide="onDialogHide">
    <q-card style="max-width: 100%; border-radius: 15px; width: 600px">
      <div class="row justify-between items-center q-my-md q-mx-md">
        <div class="text-h6">Choose</div>
        <q-icon name="clear" size="24px" class="cursor-pointer" v-close-popup />
      </div>
      <q-separator style="height: 1.5px" />
      <q-card-section class="q-pt-none">
        <div class="text-grey-9 q-mt-md">Please select the Lecture service(s) you wish to cancel.</div>

        <q-option-group class="q-mt-md" :options="list" type="checkbox" v-model="chooseType">
          <template v-slot:label="opt">
            <div class="row items-center justify-between full-width">
              <div class="col">
                <div>
                  <div class="text-weight-bold">{{ opt.label }}</div>
                </div>
              </div>
            </div>
          </template>
        </q-option-group>
      </q-card-section>
      <q-separator />

      <q-card-actions align="right" class="q-gutter-sm q-pa-md">
        <q-btn :disabled="chooseType.length < 1" color="primary" label="Confirm" no-caps rounded @click="confirm" />
        <q-btn color="primary" outline no-caps rounded label="Cancel" v-close-popup />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import {ref, onMounted, computed} from 'vue'
import {useRoute, useRouter} from 'vue-router'

import {pubStore} from 'stores/pub'

import {calVouchersService} from 'src/pages/premCpack/consts'
import {formatVoucherName} from 'src/pages/order/consts'
const pub = pubStore()
const route = useRoute()
const router = useRouter()
import {useDialogPluginComponent} from 'quasar'

const list = ref([])
const chooseType = ref([])

const props = defineProps({
  data: Array,
})

defineEmits([...useDialogPluginComponent.emits])

const {dialogRef, onDialogHide, onDialogOK, onDialogCancel} = useDialogPluginComponent()

const main = async () => {
  {
    {
      console.log('props.data', props.data)
    }
  }
  list.value = props?.data?.map((item) => {
    return {
      value: item.id,
      label: item.name,
    }
  })
}

const confirm = () => {
  onDialogOK(chooseType.value)
}

onMounted(main)
</script>
