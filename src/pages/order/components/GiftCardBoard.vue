<template>
  <q-item tag="label" class="bg-white q-pa-md rounded-borders-md shadow-1">
    <q-item-section>
      <div class="row q-col-gutter-md items-start">
        <div class="col-12 col-sm-auto">
          <div
            :style="{
              borderRadius: '15px',
              width: '242px',
              height: '130px',
              margin: $q.screen.lt.sm ? '0 auto' : '0',
            }">
            <q-img :ratio="16 / 7" class="fit" fit="cover" :src="item?.cover || '/v2/img/no-img.png'" style="border-radius: 10px" />
          </div>
        </div>

        <div class="col-12 col-sm">
          <div class="text-weight-medium q-ml-none text-left text-h6 q-mt-sm q-mt-sm-sm-none">Gift Card</div>
          <div v-if="item?.giftCardData?.giftMessage?.length" class="q-mt-sm">
            <span class="text-bold text-subtitle2">Message: </span>
            <div class="text-14 text-weight-regular" style="color: #aeaaae; white-space: pre-wrap; word-break: break-word">
              {{ item?.giftCardData?.giftMessage }}
            </div>
          </div>
        </div>
      </div>
    </q-item-section>
  </q-item>
</template>
<script setup>
import {ref, onMounted, inject, computed} from 'vue'
import {date} from 'quasar'
import {useRoute, useRouter} from 'vue-router'

/*
  consts
*/

const route = useRoute()
const router = useRouter()

const props = defineProps({
  item: Object,
  price: {
    type: Number,
    default: 20,
  },
})

onMounted(() => {})
</script>
