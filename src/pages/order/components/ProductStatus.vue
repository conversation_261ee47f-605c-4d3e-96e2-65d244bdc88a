<template>
  <div class="relative-position">
    <slot></slot>
    <q-img v-if="item.removed" src="~assets/img/order_refunded.png" spinner-color="white" style="width: 52px" class="absolute-top-right" />
  </div>
</template>

<script setup>
defineProps({
  item: {
    type: Object,
    default: () => ({removed: false}),
  },
})
</script>

<style scoped>
.absolute-top-right {
  top: 7px;
  right: 6px;
  transform: rotate(-25deg);
}
</style>
