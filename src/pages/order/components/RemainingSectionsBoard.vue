<template>
  <!-- <q-item tag="label" class="bg-white q-pa-md rounded-borders-md shadow-1">
    <q-item-section>
      <div class="row q-col-gutter-md items-center">
        <div class="col-xs-12 col-sm-5">
          <q-img
            :ratio="16 / 7"
            class="fit"
            fit="cover"
            :src="hashToUrl(item?.snapshot?.cover) || '/v2/img/no-img.png'"
            style="border-radius: 10px; width: 100%; height: 130px" />
        </div>

        <div class="col-xs-12 col-sm-7" style="padding: 10px">
          <div class="row items-center">
            <div class="text-orange text-subtitle2">Associated task</div>
            <q-item-label class="text-h7 text-weight-bold text-uppercase text-primary q-ml-sm">
              {{ item?.snapshot?.mentoringType }}
            </q-item-label>
          </div>
          <div class="ellipsis text-h6">
            {{ item?.snapshot?.name }}
          </div>
        </div>
      </div>
    </q-item-section>
  </q-item> -->
  {{ console.log('item', item) }}
  <q-item tag="label" class="bg-white q-pa-md rounded-borders-md shadow-1">
    <q-item-section>
      <div class="row q-col-gutter-md items-start">
        <div class="col-12 col-sm-auto">
          <div
            :style="{
              borderRadius: '15px',
              width: '242px',
              height: '130px',
              margin: $q.screen.lt.sm ? '0 auto' : '0',
            }">
            <q-img :ratio="16 / 7" class="fit" fit="cover" :src="hashToUrl(item?.snapshot?.cover) || '/v2/img/no-img.png'" style="border-radius: 10px" />
          </div>
        </div>

        <div class="col-12 col-sm">
          <div class="row items-center">
            <div class="text-orange text-subtitle2">Associated task</div>
            <q-item-label class="text-h7 text-weight-bold text-uppercase text-primary q-ml-sm">
              {{ item?.snapshot?.mentoringType }}
            </q-item-label>
          </div>
          <div class="ellipsis text-h6">
            {{ item?.snapshot?.name }}
          </div>
        </div>
      </div>
    </q-item-section>
  </q-item>
</template>
<script setup>
import {ref, onMounted, inject, computed} from 'vue'
import {date} from 'quasar'
import {useRoute, useRouter} from 'vue-router'

/*
  consts
*/

const route = useRoute()
const router = useRouter()

const props = defineProps({
  item: Object,
  price: {
    type: Number,
    default: 20,
  },
})

onMounted(() => {
  console.log('props', props)
})
</script>
