<template>
  <div class="q-px-md q-mb-md" v-if="detail">
    <q-banner inline-actions rounded class="bg-amber-2 text-black q-mb-md" v-if="service_task_tips">
      You are about to purchase the deficiency amount of this unit
      <template v-slot:action>
        <q-btn flat icon="close" class="q-pa-sm" @click="service_task_tips = false" />
      </template>
    </q-banner>

    <div class="row q-col-gutter-md">
      <div class="col-sm-8 col-xs-12">
        <q-item style="padding: 0" v-for="item in list" :key="item._id" class="row q-mt-md">
          <q-item-section class="col">
            <q-item-label>
              <q-card class="bg-white rounded-borders-md">
                <q-card-section>
                  <div class="row q-col-gutter-md">
                    <div class="col-xs-12 col-sm-5">
                      <q-img
                        class="full-width rounded-borders-md"
                        :ratio="$q.screen.gt.xs ? 16 / 12 : 16 / 9"
                        fit="cover"
                        :src="hashToUrl(item?.taskDetails?.cover) || '/v2/img/no-img.png'" />
                    </div>
                    <div class="col-xs-12 col-sm-7">
                      <div class="row">
                        <div class="text-yellow-9">Associated task</div>
                        <q-item-label class="text-h7 text-weight-bold text-uppercase text-primary q-ml-sm">
                          {{ item?.taskDetails?.mentoringType }}
                        </q-item-label>
                      </div>
                      <div class="ellipsis text-h6">
                        {{ item?.name }}
                      </div>

                      <div class="q-mt-md text-right">
                        <div class="text-bold text-primary q-mt-sm">USD {{ (totalPrice / 100).toFixed(2) }}</div>
                      </div>
                    </div>
                    <div class="col-12 text-grey-7 text-h9">Cancelled on {{ item?.lastCancelledTime ? formatDate(item?.lastCancelledTime) : '' }}</div>
                  </div>
                </q-card-section>
              </q-card>
            </q-item-label>
          </q-item-section>
        </q-item>

        <q-card class="rounded-borders-md q-mt-xl bg-white">
          <q-card-section>
            <!-- <template v-if="isPointMode">
              <div class="text-h6 q-mb-md">Points redemption policy</div>
              <q-btn class="q-pa-xs" flat color="primary" no-caps label="More" target="_blank" href="/v2/com/agreement/all_users/points_redemption" />
            </template> -->
            <div class="text-h6">Cancellation policy</div>
            <CancellationPolicy type="associatedTask" />
            <q-btn class="q-pa-xs" flat color="primary" no-caps label="More" target="_blank" href="/v2/com/agreement/all_users/cancellation" />
          </q-card-section>
        </q-card>
      </div>
      <div class="col-sm-4 col-xs-12">
        <q-card class="rounded-borders-md">
          <q-card-section style="padding: 16px 34px 16px 18px">
            <div class="text-h6">Order summary</div>
            <div class="row justify-between q-py-sm text-subtitle1">
              <div class="items-center flex">
                <span class=""> Subtotal(1 items) </span>
              </div>
              <div>${{ (totalPrice / 100).toFixed(2) }}</div>
            </div>
            <div class="row justify-between q-py-sm text-subtitle1">
              <div class="items-center flex">
                <span class=""> Credit for {{ list?.[0]?.name }} </span>
              </div>
              <div>${{ (totalPrice / 100).toFixed(2) }}</div>
            </div>
            <GiftCardApply @balance="getGiftCardBalance" :applicableGiftAmount="applicableGiftAmount" />

            <div class="row justify-between q-py-sm text-subtitle1 text-weight-bold">
              <div class="items-center flex">
                <span class=""> Total </span>
              </div>
              <div>${{ (totalPrice / 100).toFixed(2) }}</div>
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import {onMounted, ref, watchEffect, computed} from 'vue'
import {useRoute} from 'vue-router'
import {serviceTaskStore} from '../../../stores/serviceTask'
import CancellationPolicy from 'src/pages/order/CancellationPolicy.vue'
import {pubStore} from 'stores/pub'
import GiftCardApply from '../components/GiftCardApply.vue'

const serviceTask = serviceTaskStore()
const route = useRoute()
const ids = ref(route.params.ids)
const detail = ref({})
const service_task_tips = ref(true)
const list = ref([])
const pub = pubStore()
const totalPrice = ref(0)
const dialogShow = ref(false)
const giftCardBalance = ref(0)
const applicableGiftAmount = ref(0)

const emit = defineEmits(['updateData'])

function formatDate(dateStr) {
  const date = new Date(dateStr)
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  const hours = date.getHours()
  const minutes = date.getMinutes().toString().padStart(2, '0')
  const seconds = date.getSeconds().toString().padStart(2, '0')

  return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`
}

const finalPrice = computed(() => {
  applicableGiftAmount.value = Math.min(giftCardBalance.value, totalPrice.value)
  return Math.max(totalPrice.value - applicableGiftAmount.value, 0)
})

const getGiftCardBalance = (balanceFromChild) => {
  giftCardBalance.value = balanceFromChild
}

const checkoutData = async () => {
  console.log('ordering')
  return {
    linksQuery: {
      links: [
        {
          id: ids.value,
          style: 'section_top_up',
        },
      ],
    },
    orderQuery: {
      link: [
        {
          id: ids.value,
          style: 'section_top_up',
        },
      ],
      giftCard: applicableGiftAmount.value,
    },
  }
}

const main = async () => {
  const {deficitCredit, section} = await App.service('section').get('deficitCredit', {query: {sectionId: ids.value}})
  detail.value = section
  list.value = [detail.value]
  totalPrice.value = deficitCredit
  console.log('deficitCredit', deficitCredit)
  console.log('section', section)
}

const initData = () => {
  main().catch((err) => {
    dialogShow.value = true
    console.log('err', err)
  })
}

watchEffect(() => {
  const data = {
    totalPrice: finalPrice.value,
    dialogShow: dialogShow.value,
  }
  emit('updateData', data)
})

defineExpose({checkoutData, initData})

onMounted(initData)
</script>
