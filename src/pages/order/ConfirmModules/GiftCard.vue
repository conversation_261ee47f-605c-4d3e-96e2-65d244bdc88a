<template>
  <div class="q-px-md">
    <div class="row q-col-gutter-md">
      <div class="col-sm-8 col-xs-12 q-mt-sm">
        <div tag="label" class="bg-white q-pa-md rounded-borders-md shadow-1">
          <div class="row q-col-gutter-md items-start">
            <div class="col-12 col-sm-auto">
              <div
                :style="{
                  borderRadius: '15px',
                  width: '242px',
                  height: '130px',
                  margin: $q.screen.lt.sm ? '0 auto' : '0',
                }">
                <q-img :ratio="16 / 7" class="fit" fit="cover" :src="selectedGiftCardDesign?.url || '/v2/img/no-img.png'" style="border-radius: 10px" />
              </div>
            </div>

            <div class="col-12 col-sm">
              <div class="text-weight-medium q-ml-none text-left text-h6 q-mt-sm q-mt-sm-sm-none">Gift Card</div>
              <div class="q-mt-sm" v-if="message?.length">
                <span class="text-bold text-subtitle2">Message: </span>
                <div class="text-14 text-weight-regular" style="color: #aeaaae; white-space: pre-wrap; word-break: break-word">
                  {{ message }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <q-card class="rounded-borders-md q-mt-md bg-white">
          <q-card-section>
            <div class="text-h6 q-mb-md">Cancellation policy</div>
            <div class="text-grey-7 p-mt-md">
              Gift card:<br />
              Non-refundable
            </div>
            <q-btn style="margin: 0; padding: 0" flat color="primary" no-caps label="More" target="_blank" href="/v2/com/agreement/all_users/cancellation" />
          </q-card-section>
        </q-card>
      </div>

      <div class="col-sm-4 col-xs-12">
        <q-card class="rounded-borders-md q-mt-sm">
          <q-card-section>
            <div class="text-h6">Order Summary</div>
            <div class="row justify-between q-py-sm text-subtitle1">
              <div class="details-center flex">
                <span class=""> Subtotal(1 details) </span>
              </div>

              <div>${{ (selectedAmount / 100).toFixed(2) }}</div>
            </div>
            <q-separator />
            <div class="row justify-between q-py-sm text-subtitle1 text-weight-bold text-primary">
              <div class="details-center flex">
                <span class=""> Total </span>
              </div>
              <div>${{ (selectedAmount / 100).toFixed(2) }}</div>
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </div>
</template>
<script setup>
import {defineProps, defineExpose, watchEffect, computed, ref} from 'vue'
import {pubStore} from 'stores/pub'

const pub = pubStore()

const emit = defineEmits(['updateData'])
const props = defineProps({
  selectedGiftCardDesign: {
    type: String,
    default: null,
  },
  selectedAmount: {
    type: Number,
    default: null,
  },
  message: {
    type: String,
    default: null,
  },
  sender: {
    type: String,
    default: null,
  },
  recipient: {
    type: String,
    default: null,
  },
  email: {
    type: String,
    default: null,
  },
  isGift: {
    type: Boolean,
    default: false,
  },
})

const checkoutData = async () => {
  const isAdmin = pub?.user?.schoolUser?.role?.includes('admin') || pub?.user?.isSubjectCoordinator
  let school = (isAdmin && pub?.user?.school) || undefined
  let isSchool = isAdmin && !!pub?.user?.school
  console.log('ordering')
  return {
    linksQuery: {
      links: [
        {
          id: 'new_gift_card',
          style: 'gift_card',
        },
      ],
    },
    orderQuery: {
      link: [
        {
          id: 'new_gift_card',
          style: 'gift_card',
          cover: props.selectedGiftCardDesign.url,
          name: 'Gift Card',
          giftCardData: {
            amount: props.selectedAmount,
            isGift: props.isGift,
            recipientEmail: props.email,
            senderName: props.sender,
            recipientName: props.recipient,
            giftMessage: props.message,
            image: props.selectedGiftCardDesign.url,
          },
        },
      ],
      school,
      isSchool,
    },
  }
}

defineExpose({checkoutData})
</script>
