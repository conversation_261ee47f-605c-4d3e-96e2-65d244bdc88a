<template>
  <div class="q-mt-lg">
    <div class="text-h6">Recommended Product</div>
    <div class="box q-mt-md" v-if="child">
      <div @click="toShopDetail(item)" class="q-mt-sm box" v-for="item in child.data" :key="item._id">
        <div class="q-mb-md text-right">
          <q-btn
            v-if="isSendShow"
            :color="isSendDisable(item) ? 'grey' : 'primary'"
            :disabled="isSendDisable(item)"
            class="q-mr-sm"
            no-caps
            rounded
            size=""
            @click.stop="sendPurchaseLink(item)">
            <q-icon left size="1.5em" name="navigation" class="rotate" />
            <div>Send purchase link</div>
          </q-btn>
        </div>
        <template v-if="isCustomerStudent">
          <SessionBoard
            isPrompt
            noRemove
            :onLinkItemClick="() => {}"
            isMyPurchased
            :isMyWorkshop="false"
            :isMyClass="false"
            :subtab="'featured'"
            :session="item"
            :isLectureRoom="false"
            :isSelfStudy="false"
            :categories="categoryOptions"
            isListing />
        </template>
        <template v-else>
          <SessionBoard
            isPrompt
            :onLinkItemClick="() => {}"
            isMyPurchased
            :isMyWorkshop="false"
            :isMyClass="false"
            :subtab="'featured'"
            :categories="categoryOptions"
            isListing
            :session="item" />
        </template>
      </div>
      <NoData v-if="child.data.length === 0" />
    </div>
  </div>
</template>
<script setup>
/*
  imports
*/
import {computed, onMounted, ref} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import SessionBoard from 'components/SessionBoard.vue'
import {pubStore} from 'stores/pub'
import {followUpStore} from 'stores/followUpSales'
import {PATH_PREFIX} from '../../../boot/const'
import {copyToClipboard} from 'quasar'

const isCustomerStudent = ref(null)

const pub = pubStore()
const followStore = followUpStore()

const child = ref(null)

const route = useRoute()
const router = useRouter()

const categoryOptions = ref([])

const detail = computed(() => followStore.detail)

const isSendShow = computed(() => {
  return pub?.user?._id === detail.value?.sales
})

// const isSendDisable = computed(() => {
//   return followStore.detail?.shareGoods?.includes(item._id) || !followStore.detail?.servicePackInfo.status
// })

const isSendDisable = (item) => {
  return followStore.detail?.shareGoods?.includes(item._id) || !followStore.detail?.servicePackInfo.status
}

const toShopDetail = (item) => {
  router.push({
    path: `/detail/session/${item?._id}`,
    query: {
      back: route.fullPath,
    },
  })
}

const sendPurchaseLink = async (item) => {
  $q.loading.show()
  await App.service('sales-follow-up')
    .get('share', {
      query: {
        id: detail.value?._id,
        goodsId: item?._id,
        style: 'session',
      },
    })
    .then(() => {
      followStore.getFollowUpDetail(detail.value?._id)
      $q.notify({type: 'positive', message: 'Send successfully'})
    })
  $q.loading.hide()
}

const onLoad = async () => {
  handleLoad()
}

const handleLoad = async () => {
  let query = {}

  if (isCustomerStudent.value) {
    query = {
      del: false,
      $sort: {_id: -1},
      $limit: 10,
      $skip: 0,
      type: {$in: ['taskWorkshop', 'unitCourses']},
      regDate: {$gte: new Date()},
      isLib: true,
      premium: true,
      status: 'scheduled',
    }
  } else {
    query = {
      pid: {$exists: false},
      del: false,
      $sort: {_id: -1},
      status: 'scheduled',
      $limit: 10,
      $skip: 0,
      premium: true,
      regDate: {$gte: new Date()},
      isLib: true,
      type: {$in: ['workshop', 'pdCourses', 'teacherTool']},
      sessionType: 'live',
      uid: {$ne: pub.user._id},
    }
  }
  await App.service('session')
    .find({query})
    .then((rs) => {
      child.value = rs
    })
}

import {subjectsStore} from 'stores/subjects'
const subjects = subjectsStore()
const getSubjects = async () => {
  const pdOptions = await subjects.getPubPdOptions()
  if (isCustomerStudent.value) {
    categoryOptions.value.push(...pdOptions.filter((v) => v.participants === 'students'))
    categoryOptions.value.unshift({label: 'Academic', value: 'academic'})
  } else {
    categoryOptions.value.push(...pdOptions)
  }
}

const getRecommendDetail = () => {
  const isStudent = detail.value?.customerInfo?.roles?.includes('student')
  isCustomerStudent.value = isStudent
}

onMounted(async () => {
  getRecommendDetail()
  await getSubjects()
  onLoad()
})
</script>
<style lang="scss" scoped>
.box {
  position: relative;

  .send_btn {
    position: absolute;
    right: 10px;
    top: 10px;

    .rotate {
      transform: rotate(90deg);
    }
  }

  .btn_repalce {
    position: absolute;
    width: 100px;
    height: 40px;
    background-color: #fff;
    right: 12px;
    bottom: 14px;
  }

  &:hover {
    .btn_repalce {
      background-color: #e8e8e8;
    }
  }
}
</style>
