
<template>
  <q-dialog ref="dialogRef" @hide="onDialogHide" >
    <div class="bg-white rounded-lg q-ma-md" style="width: 600px">
      <div class="q-pa-md q-mt-sm">
        <div class="q-mb-md text-bold">Message to {{detail?.salesName}}</div>
        <RecordInput v-model="messageText" :counter="true" :maxlength="1000" outlined placeholder="The message will show in their inbox" />
      </div>
      <q-separator class="q-mt-md" />
      <div class="q-pa-md text-right">
        <q-btn :disable="!messageText" color="primary" label="Send" no-caps rounded size="" @click="onConfirm"></q-btn>
      </div>
    </div>
  </q-dialog>
</template>

<script setup>
import {ref, onMounted, computed} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import RecordInput from 'src/components/utils/inputs/RecordInput.vue'
import {followUpStore} from 'stores/followUpSales'
const route = useRoute()
const router = useRouter()
import {useDialogPluginComponent} from 'quasar'

const messageText = ref('')
const followStore = followUpStore()

const detail = computed(() => followStore.detail)

defineEmits([...useDialogPluginComponent.emits])

const {dialogRef, onDialogHide, onDialogOK, onDialogCancel} = useDialogPluginComponent()


const onConfirm = async () => {
  $q.loading.show()
  await App.service('sales-follow-up').get('sendInbox', { query: { uid: followStore?.detail?.sales, content: messageText.value } })
    .then((res) => {
      $q.notify({type: 'positive', message: 'Send successfully'})
      onDialogOK()
    })
  $q.loading.hide()
}

</script>

<style lang="sass">
.subjeccts-dialog
  .q-card
    body.screen--sm &
      min-width: 500px
    body.screen--md &
      min-width: 500px
  .scroll
    body.screen--sm &
      max-height: 50vh
    body.screen--md &
      max-height: 50vh
</style>
