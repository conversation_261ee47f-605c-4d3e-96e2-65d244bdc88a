<template>
  <q-tabs v-model="tab" stretch @update:model-value="tabChange" align="left" indicator-color="primary" active-color="primary" inline-label mobile-arrows shrink>
    <q-tab v-for="o in tabs" :key="o.value" :name="o.value" :label="o.label.toFirstUpperCase()" no-caps />
  </q-tabs>
  <q-separator />
  <div class="row justify-between items-center q-mb-xs q-mt-lg">
    <div class="col-sm-4 col-xs-12">
      <Filters v-model="filters" @update:modelValue="doSearch" />
    </div>
  </div>
  <DetailView ref="child" :load="handleLoad" showMore class="q-mt-lg">
    <div class="rounded-borders-sm q-mt-md q-border-1 q-pa-md bg-white shadow-1">
      <div>
        <div class="row text-grey-6">
          <div class="col-2">Customer</div>
          <div class="col-3">Last contact time</div>
          <div class="col-4">Session name</div>
          <div class="col-2">Status</div>
          <div class="col-1">Action</div>
        </div>
        <div class="row q-py-sm" v-for="item in child.data" :key="item._id" @click="toDetail(item)">
          <div class="row col-12 q-pa-sm" :class="{'bg-grey-3': !item?.servicePackInfo?.status}">
            <div class="col-2 text-primary text-capitalize cursor-pointer">{{ item?.customerName }}</div>
            <div class="col-3">
              {{ item?.contactLog?.[0]?.createdAt && date.formatDate(item?.contactLog?.[0]?.createdAt, 'MM/DD/YYYY HH:mm:ss') }}
            </div>
            <div class="col-4 text-primary text-capitalize cursor-pointer">{{ item?.servicePackName }}</div>
            <div class="col-2" style="white-space: pre-line">
              <div v-if="item?.sales">
                Claimed by me
                <div class="q-my-xs">
                  <PubAvatar class="q-ml-sm" :src="item?.salesInfo?.avatar" size="1.5rem" />
                  me
                </div>
              </div>
              {{ StatusText(item, pub?.user?._id) }}
            </div>
            <div class="col-1" v-if="item?.type === 'following'">
              <template v-if="item?.status === 1">
                <span v-if="item?.sales === pub?.user?._id">
                  <BtnStop :item="item" @cb="doSearch" />
                </span>
              </template>
            </div>
          </div>
          <div class="col-12">
            <q-separator class="q-mt-sm" />
          </div>
        </div>
      </div>
    </div>
  </DetailView>
</template>
<script setup>
/*
  imports
*/
import {computed, onMounted, ref} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import DetailView from 'components/DetailView.vue'
import {date} from 'quasar'
import {StatusText} from './consts'
import {pubStore} from 'stores/pub'
import Filters from './Filters.vue'
import BtnStop from './BtnStop.vue'

const route = useRoute()
const router = useRouter()

const pub = pubStore()

const tabs = [
  {value: 'following', label: 'Following'},
  {value: 'completed', label: 'Completed'},
]

const filters = ref({})

const tab = ref(route.query.tab || 'following')
const child = ref(null)
const skip = ref(0)
const search = ref('')

const doSearch = () => {
  skip.value = 0
  child.value.onReset()
  onLoad()
}

const tabChange = () => {
  doSearch()
  router.replace({
    query: {
      ...route.query,
      tab: tab.value,
    },
  })
}

const onLoad = async () => {
  child.value.onLoad()
}

const handleLoad = async () => {
  const $limit = 10
  let searchList = []
  const query = {
    $limit,
    $skip: skip.value,
    $sort: {updatedAt: -1},
    type: tab.value,
    sales: pub?.user?._id,
  }
  skip.value += $limit

  if (filters.value.search) {
    query['$or'] = [
      {
        customerName: {
          $regex: filters.value.search.toLowerCase(),
          $options: 'i',
        },
      },
      {
        servicePackName: {
          $regex: filters.value.search.toLowerCase(),
          $options: 'i',
        },
      },
    ]
  }

  return App.service('sales-follow-up').find({query})
}

const toDetail = (item) => {
  router.push({
    path: `/order/userFollowUpSales/${item?._id}`,
    query: {},
  })
}
const main = () => {
  onLoad()
}

onMounted(main)
</script>
<style lang="sass" scope>

.page-box
  padding-left: 0 !important
</style>
