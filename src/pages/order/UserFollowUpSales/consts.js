import {date} from 'quasar'

export const StatusText = (item, id) => {
  let isMyself = item?.sales === id
  let content = ''
  if (item?.type === 'following') {
    if (item.status) {
      const endTime = new Date(item?.followedAt).getTime() + 1000 * 60 * 60 * 24 * 14
      // content = `Claimed by ${isMyself ? 'me' : item?.salesName} \n (Release in ${countdown(endTime)})`
      content = `(Release in ${countdown(endTime)})`
    } else {
      const releasedOn = item?.releasedAt || item?.updatedAt
      content =`Unclaimed \n (Released on ${date.formatDate(releasedOn, 'HH:mm:ss MM/DD/YYYY ')})`
    }
  } else if (item?.type === 'completed') {
    content = `Completed by ${item?.sales ? (isMyself ? 'me' : item?.salesName) : 'via direct purchase'} \n (Completed on  ${date.formatDate(
      item?.updatedAt,
      'HH:mm:ss MM/DD/YYYY '
    )})`
  }
  return content
}

const countdown = (end) => {
  const beginDate = new Date()
  const endDate = new Date(end)
  const diff = endDate.getTime() - beginDate.getTime()

  // 这里计算如果是负数的处理
  const sec = diff < 0 ? Math.abs(diff / 1000) * -1 : diff / 1000
  if (sec < 0) {
    return 0
  }
  return formatTime(sec)
}

function formatTime(times) {
  let time = Math.abs(times)
  const d = parseInt(time / (60 * 60 * 24))
  const h = parseInt((time / 3600) % 24)
  const minute = parseInt((time / 60) % 60)
  return `${d} day ${h} h ${minute} min`
}
