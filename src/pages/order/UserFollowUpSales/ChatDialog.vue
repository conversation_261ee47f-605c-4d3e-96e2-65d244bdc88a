<template>
  <q-dialog ref="dialogRef" @hide="onDialogHide" position="right" full-height :maximized="$q.screen.lt.sm">
    <q-card style="width: 400px; height: 100vh">
      <q-card-section class="row items-center q-pb-none">
        <q-space />
        <q-btn icon="close" flat round dense v-close-popup />
      </q-card-section>
      <q-card-section class="scroll" style="height: calc(100vh - 150px)">
        <template v-for="item in chatList" :key="item._id">
          <q-chat-message name="me" :text="[item?.data?.text]" sent />
        </template>
      </q-card-section>
      <q-card-section class="q-pa-md">
        <q-input v-model="messageText" type="textarea" rows="5" autogrow outlined clearable placeholder="Leave a message...">
          <template v-slot:append>
            <q-btn round dense flat color="primary" icon="file_upload" @click="sendMessage" />
          </template>
        </q-input>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script setup>
import {ref, onMounted, computed} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {followUpStore} from 'stores/followUpSales'
const route = useRoute()
const router = useRouter()
import {useDialogPluginComponent} from 'quasar'

const messageText = ref('')
const followStore = followUpStore()

const chatList = computed(() => followStore.chatList)

defineEmits([...useDialogPluginComponent.emits])

const {dialogRef, onDialogHide, onDialogOK, onDialogCancel} = useDialogPluginComponent()

const sendMessage = async () => {
  if (!messageText.value) return
  $q.loading.show()
  await App.service('notice-tpl')
    .get('sendSms', {
      query: {
        uid: followStore?.detail?.customerInfo?._id,
        text: messageText.value,
      },
    })
    .then((res) => {
      $q.notify({type: 'positive', message: 'Send sms successfully'})
      messageText.value = ''
      followStore.getChatList()
    })
    .catch((err) => {
      $q.notify({type: 'negative', message: err.message})
    })
    .finally(() => {
      $q.loading.hide()
    })
}

const main = async () => {
  $q.loading.show()
  await followStore.getChatList()
  $q.loading.hide()
}

onMounted(main)
</script>
