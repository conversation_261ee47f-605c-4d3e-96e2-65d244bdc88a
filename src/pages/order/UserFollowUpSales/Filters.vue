<template>
  <div class="fit">
    <div class="row q-col-gutter-x-lg">
      <div class="col-12">
        <q-input
          class="bg-teal-1 overflow-hidden rounded-borders-xl"
          outlined
          rounded
          dense
          :label="'Search by keyword'"
          v-model="filters.search"
          @keyup.enter="onKeyupEnter"
          @update:model-value="searchedAlready = false">
          <template v-slot:prepend>
            <q-btn v-if="searchedAlready" round flat dense icon="close" @click="doClear"></q-btn>
            <q-btn v-else round flat dense icon="search" @click="doSearch"></q-btn>
          </template>
          <template v-slot:append v-if="menuMode">
            <q-btn round flat dense v-if="showMenu" class="rotate-180" icon="arrow_drop_down" @click="showMenu = false"></q-btn>
            <q-btn round flat dense v-else icon="arrow_drop_down" @click="showMenu = true"></q-btn>
          </template>
        </q-input>
      </div>
    </div>
    <div>
      <q-menu max-width="770px" @hide="onMenuHide" v-model="showMenu" fit :offset="[0, 4]" class="shadow-3 rounded-borders-md">
        <q-card>
          <q-card-section class="scroll">
            <div class="q-py-md text-h6">Filtered by</div>
            <q-option-group inline :options="filtersByOptions" type="radio" v-model="filters.filtersBy" @update:modelValue="onFilterUpdate" />
            <q-separator class="q-my-md" />
            <template v-if="filters?.filtersBy === 'sales'">
              <div class="q-py-md text-h6">Classcipe managing personnels</div>
              <q-option-group inline :options="salesList.filter((e) => e.salesType === 'manager')" type="radio" v-model="filters.sales" @update:modelValue="onFilterUpdate" >
                <template v-slot:label="opt">
                  <div class="row items-center">
                    <PubAvatar size="1.5rem" :src="opt?.salesInfo?.avatar"/>
                    <span class="text-grey-6 q-ml-md">{{opt.salesName}}</span>
                    <span class="text-grey-6 q-ml-md">{{ opt.count }}</span>
                  </div>
                </template>
              </q-option-group>
              <div class="q-py-md text-h6">Education consultant</div>
              <q-option-group inline :options="salesList.filter((e) => e.salesType === 'consultant')" type="radio" v-model="filters.sales" @update:modelValue="onFilterUpdate" >
                <template v-slot:label="opt">
                  <div class="row items-center">
                    <PubAvatar size="1.5rem" :src="opt?.salesInfo?.avatar"/>
                    <span class="text-grey-6 q-ml-md">{{opt.salesName}}</span>
                    <span class="text-grey-6 q-ml-md">{{ opt.count }}</span>
                  </div>
                </template>
              </q-option-group>
            </template>

           <template v-if="filters?.filtersBy === 'productType'">
             <q-option-group inline :options="productTypeOptions" type="checkbox" v-model="filters.serviceRoles" @update:modelValue="onFilterUpdate" >
               <template v-slot:label="opt">
                 <div class="row items-center">
                   <span >{{ opt.label }}</span>
                   <span class="text-grey-6 q-ml-md">{{ opt.count }}</span>
                 </div>
               </template>
             </q-option-group>
           </template>
          </q-card-section>
        </q-card>
      </q-menu>
    </div>
    <div v-if="menuMode" class="q-mt-sm">
      <q-chip
        class="q-pa-sm"
        v-if="filters?.sales"
        color="grey-6"
        :ripple="false"
        dense
        @remove="()=>{filters.sales = undefined;updateModelValue()}"
        outline
        removable
      >
        <PubAvatar size="1rem" :src="salesList.find((e) => e.value == filters?.sales)?.salesInfo?.avatar"/>
        {{salesList.find((e) => e.value == filters?.sales)?.salesName}}
      </q-chip>
      <template v-for="values in filters?.serviceRoles" :key="values">
        <q-chip
          class="q-pa-sm"
          color="grey-6"
          :ripple="false"
          dense
          outline
          @remove="onChipRemoveRoles(values)"
          removable
          :label="productTypeOptions.find((e) => e.value == values)?.label"
        />
      </template>
      <q-btn
        v-if="enableClearFilter"
        @click="onClearFiltersClick"
        :disable="!enableClearFilter"
        label="Clear filters"
        class="q-pl-sm"
        icon-right="close"
        size="sm"
        rounded
        no-caps
        flat
        dense
        color="primary"></q-btn>
    </div>
  </div>
</template>
<script setup>
/*
  imports
*/
import {ref, onMounted, watch, computed} from 'vue'

/*
  props && emit
*/
const props = defineProps({
  modelValue: Object,
  salesList: Array,
  productTypeOptions: Array,
  menuMode:{
    type: Boolean,
    default: false,
  },

})

const emit = defineEmits(['update:modelValue'])

/*
  consts
*/

const showMenu = ref(false)
const searchedAlready = ref(false)

const filtersByOptions = [
  {
    label: 'Sales',
    value: 'sales',
  },
  {
    label: 'Product type',
    value: 'productType',
  },
]

const filters = ref({search: '', serviceRoles:[]})

/*
  computeds
*/
const menuMode = ref(props?.menuMode)
// const menuMode = computed(() => {
//   return props.options.length > 0 || props.package
//   //return props.options.length > 3 || props.package
// })

const enableClearFilter = computed(() => {
  return (
    filters.value.sales && filters.value.serviceRoles.length > 0 ||
    (filters.value.search && searchedAlready.value)
  )
})

/*
  methods
*/

const onChipRemoveRoles = (value) => {
  filters.value.serviceRoles = filters.value.serviceRoles.filter((e) => e != value)
  updateModelValue()
}

const onClearFiltersClick = () => {
  filters.value = {search: '', serviceRoles:[]}
  updateModelValue()
}

const onMenuHide = (evt) => {
  updateModelValue()
}

const onKeyupEnter = () => {
  //if ($q.screen.gt.sm) {
  doSearch()
  //}
}

const onFilterUpdate = (val) => {
  if ($q.screen.gt.sm && filters.value.search) {
    searchedAlready.value = true
  }
  if (!menuMode.value) {
    updateModelValue()
  }
}

const updateModelValue = () => {
  emit('update:modelValue', filters.value)
}

const doSearch = () => {
  searchedAlready.value = true
  updateModelValue()
}

const doClear = () => {
  searchedAlready.value = false
  filters.value.search = null
  updateModelValue()
}

/*
  watches
*/


/*
  lifecycles
*/
// onMounted(() => {
//   /// initFilters()
//   updateModelValue()
// })
</script>
