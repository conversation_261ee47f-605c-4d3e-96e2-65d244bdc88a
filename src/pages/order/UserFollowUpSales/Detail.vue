<template>
  <q-layout view="hHh LpR fFf">
    <q-header class="bg-teal-1 text-black" elevated>
      <q-toolbar class="">
        <q-btn dense flat icon="navigate_before" round @click="goBack"></q-btn>
        <q-toolbar-title class="text-h6"> Follow-up sales </q-toolbar-title>
      </q-toolbar>
    </q-header>
    <q-page-container class="pc-sm">
      <q-page class="q-pb-xl">
        <div class="q-px-md q-pt-md">
          <div v-if="loading" class="text-center q-pa-xl text-grey">
            <q-spinner-ball class="full-width" color="primary" size="2em" />
          </div>
          <div v-if="detail && !loading">
            <div class="row q-mt-lg items-center">
              <div class="col">
                <PlivoCall v-if="isShowCall" :detail="detail" />
                <template v-if="detail?.type === 'following'">
                  <template v-if="detail?.status === 1">
                    <span v-if="detail?.sales === pub?.user?._id">
                      <BtnStop :item="detail" @cb="main" />
                    </span>
                  </template>
                </template>
              </div>
              <div class="text-bold text-grey-6">
                {{ StatusText }}
              </div>
            </div>
            <div class="rounded-borders-sm q-mt-lg q-border-1 q-pa-md bg-white shadow-1">
              <div class="text-h6">Customer Profile</div>
              <div class="row q-mt-lg">
                <div class="q-mr-md">
                  <PubAvatar class="q-mr-md" size="5rem" :src="detail?.customerInfo?.avatar" />
                </div>
                <div class="col">
                  <div>Name:{{ detail?.customerName }}</div>
                  <div>
                    Gender: <span class="text-capitalize"> {{ detail?.customerInfo?.gender }}</span>
                  </div>
                </div>
              </div>
              <div class="text-h6 q-mt-lg">Contact history</div>
              <template v-if="contactLogList.length > 0">
                <div class="q-mt-md">
                  <div class="row text-grey-6">
                    <div class="col-3">Last contact time</div>
                    <div class="col-3 text-right">Duration(mins)</div>
                    <div class="col-4 text-right q-px-lg">Logged in by</div>
                    <div class="col-2 text-right q-px-lg">Action</div>
                  </div>
                  <div class="row q-py-sm" v-for="item in contactLogList" :key="item._id">
                    <div class="col-3">
                      {{ date.formatDate(item?.createdAt, 'MM/DD/YYYY HH:mm:ss') }}
                    </div>
                    <div class="col-3 text-right">{{ Math.ceil((item?.duration || 0) / 60) }}</div>
                    <div class="col-4 text-right q-px-lg">{{ item?.salesName }}</div>
                    <div class="col-2 text-right q-px-lg">
                      <q-btn flat round color="primary" dense icon="text_snippet" @click="viewNote(item)" />
                    </div>
                    <div class="col-12">
                      <q-separator class="q-mt-sm" />
                    </div>
                  </div>
                </div>
              </template>
              <NoData v-else />
            </div>
            <div class="q-mt-lg">
              <div class="text-h6">Relevant Product</div>
              <div class="box q-mt-md">
                <div class="text-right q-mb-md">
                  <q-btn
                    v-if="detail?.type === 'following'"
                    round
                    @click="copyFn"
                    icon="content_copy"
                    no-caps
                    color="primary"
                    class="text-white q-mr-sm"></q-btn>
                  <template v-if="detail?.type === 'following'">
                    <q-btn
                      class="q-mr-sm"
                      :color="isSendDisable ? 'grey' : 'primary'"
                      no-caps
                      rounded
                      size=""
                      :disabled="isSendDisable"
                      @click="sendPurchaseLink">
                      <q-icon left size="1.5em" name="navigation" class="rotate" />
                      <q-tooltip class="text-subtitle1" v-if="isSendDisable"> Invitation already sent. Cannot invite again. </q-tooltip>
                      <div>Send purchase link</div>
                    </q-btn>
                  </template>
                </div>
                <PackageCard :pack="detail?.servicePackInfo" category="featured" isCreator :onItemClick="toPack" />
              </div>
            </div>
            <Recommend />
          </div>
        </div>
      </q-page>
    </q-page-container>
  </q-layout>
</template>
<script setup>
/*
  imports
*/
import {computed, onMounted, ref} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {copyToClipboard, date} from 'quasar'
import PackageCard from 'components/PackageCard.vue'

import PlivoCall from './PlivoCall.vue'
import {pubStore} from 'stores/pub'
import {followUpStore} from 'stores/followUpSales'
import BtnStop from './BtnStop.vue'

import InboxDialog from './InboxDialog.vue'
import Recommend from './Recommend.vue'

import {PATH_PREFIX} from 'src/boot/const'

const pub = pubStore()
const followStore = followUpStore()
const route = useRoute()
const router = useRouter()
const id = ref(route.params.id)

const detail = computed(() => followStore.detail)

const isSys = ref(!!route?.query?.isSys)

const loading = ref(false)

const goBack = (hash) => {
  router.go(-1)
}

const sendInbox = () => {
  if (isUnPublish.value) {
    return
  }
  $q.dialog({
    component: InboxDialog,
  })
}

const isShowCall = computed(() => {
  console.log('detail', detail.value)
  let mobile = null

  mobile = detail.value?.customerInfo?.school ? null : detail.value?.customerInfo?.mobile || detail.value?.customerInfo?.emergencyContact

  console.log('mobile', mobile)
  return detail.value?.type === 'following' && mobile
})

const StatusText = computed(() => {
  const status = followStore.detail?.status
  const type = followStore.detail?.type
  let text = ''

  if (type === 'completed') {
    text = `Completed ${isSys.value ? 'by' : ''}`
    if (!followStore.detail?.sales) {
      text = `${text} via direct purchase`
    }
  } else if (type === 'following') {
    if (status === 1) {
      text = `Claimed ${isSys.value ? 'by' : ''}`
    } else {
      text = `${isSys.value ? 'Unclaimed' : ''}`
    }
  }
  return text
})

const isUnPublish = computed(() => {
  return !followStore.detail?.servicePackInfo.status
})

const isSendShow = computed(() => {
  return pub?.user?._id === detail.value?.sales
})

const isSendDisable = computed(() => {
  return detail.value?.shareCount
})

const contactLogList = computed(() => {
  let list = followStore.detail?.contactLog || []
  list.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
  return list
})

const copyFn = () => {
  let url = `${location.origin}${PATH_PREFIX}/service/pack/${detail.value?.servicePackInfo?._id}?inviteCode=${pub?.user?.inviteCode}`
  copyToClipboard(url)
    .then(() => {
      $q.notify({type: 'positive', message: 'Copy successfully'})
    })
    .catch(() => {
      // fail
    })
}

const toPack = () => {
  router.push({
    // path: `/detail/booking/limit/${detail.value?.servicePackInfo?._id}`,
    path: `/service/pack/${detail.value?.servicePackInfo?._id}`,
    query: {
      type: 'view',
    },
  })
}

const sendPurchaseLink = async () => {
  $q.loading.show()
  await App.service('sales-follow-up')
    .get('share', {
      query: {
        id: detail.value?._id,
        goodsId: detail.value?.servicePackInfo?._id,
        style: 'service',
      },
    })
    .then(() => {
      main()
      $q.notify({type: 'positive', message: 'Send successfully'})
    })
  $q.loading.hide()
}

const onDelete = async () => {
  $q.dialog({
    title: `Are you sure to delete this follow-up sales item?`,
    ok: {
      label: 'Yes, delete',
      'no-caps': true,
      rounded: true,
      color: 'primary',
    },
    cancel: {
      label: 'Cancel',
      'no-caps': true,
      rounded: true,
      outline: true,
      color: 'primary',
    },
    persistent: true,
  }).onOk(async () => {
    $q.loading.show()
    await App.service('sales-follow-up')
      .remove(id.value)
      .then(() => {
        $q.notify({type: 'positive', message: 'Delete successfully'})
        goBack()
      })
      .catch((err) => {
        $q.notify({type: 'negative', message: err.message || 'Delete failed'})
      })
    $q.loading.hide()
  })
}

const viewNote = (item) => {
  $q.dialog({
    title: 'Contact Detail',
    message: `
        <div class="row ">
          <div class="col-12 q-mt-md"><span class="q-mr-sm">time:</span>${date.formatDate(item?.createdAt, 'MM/DD/YYYY HH:mm:ss')}</div>
          <div class="col-12 q-mt-md"><span class="q-mr-sm">duration:</span>${Math.ceil((item?.duration || 0) / 60)} mins</div>
          <div class="col-12 q-mt-md"><span class="q-mr-sm">note:</span>${item.note} </div>
          <div class="col-12 q-mt-md"><span class="q-mr-sm">Logged in by:</span>${item?.salesName || ''} </div>
        </div>
    `,
    html: true,
  })
}

const main = async () => {
  loading.value = true
  await followStore.getFollowUpDetail(id.value)
  loading.value = false
}

onMounted(main)
</script>
<style lang="scss" scoped>
.box {
  position: relative;
}
</style>
