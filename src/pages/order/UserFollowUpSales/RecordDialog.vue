
<template>
  <q-dialog ref="dialogRef" @hide="onDialogHide" persistent>
    <div class="bg-white rounded-lg q-ma-md" style="width: 600px">
      <div class="q-pa-md text-h6">Follow success!</div>
      <q-separator />
      <div class="q-pa-md q-mt-sm">
        <div class="q-mb-md">Record your note</div>
        <RecordInput v-model="contactNote" :counter="true" :maxlength="1000" outlined placeholder="Leave a new comment on this media...." />
      </div>
      <q-separator class="q-mt-md" />
      <div class="q-pa-md text-right">
        <q-btn :disable="!contactNote" color="primary" label="Confirm" no-caps rounded size="" @click="onConfirmContact"></q-btn>
      </div>
    </div>
  </q-dialog>
</template>

<script setup>
import {ref, onMounted} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import RecordInput from 'src/components/utils/inputs/RecordInput.vue'
import {followUpStore} from 'stores/followUpSales'
const route = useRoute()
const router = useRouter()
import {useDialogPluginComponent} from 'quasar'

const contactNote = ref('')
const followStore = followUpStore()

const props = defineProps({
  duration: Number,
})

defineEmits([...useDialogPluginComponent.emits])

const {dialogRef, onDialogHide, onDialogOK, onDialogCancel} = useDialogPluginComponent()


const onConfirmContact = async () => {
  $q.loading.show()
  await App.service('sales-follow-up')
    .patch(followStore.detail?._id, {
      $push: {
        contactLog: {
          duration: props.duration,
          sales: followStore?.detail?.sales,
          salesName: followStore?.detail?.salesName,
          createdAt: new Date(),
          note: contactNote.value,
        },
      },
    })
    .then((res) => {
      $q.notify({type: 'positive', message: 'Record successfully'})
      onDialogOK()
    })
  $q.loading.hide()
}

</script>

<style lang="sass">
.subjeccts-dialog
  .q-card
    body.screen--sm &
      min-width: 500px
    body.screen--md &
      min-width: 500px
  .scroll
    body.screen--sm &
      max-height: 50vh
    body.screen--md &
      max-height: 50vh
</style>
