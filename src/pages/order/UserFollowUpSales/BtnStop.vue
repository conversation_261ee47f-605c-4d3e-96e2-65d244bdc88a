<template>
  <q-btn flat round color="negative" dense icon="stop_circle" @click.stop="stop" />
</template>
<script setup>
/*
  imports
*/
import {computed, onMounted, ref} from 'vue'
import {useRoute, useRouter} from 'vue-router'
const props = defineProps({
  item: Object,
})

const isUnPublish = computed(() => {
  return !props?.item?.servicePackInfo?.status
})

const emit = defineEmits(['cb'])

const stop = (item) => {
  $q.dialog({
    title: `Confirm stop`,
    message: `Please confirm that you want to stop it.`,
    cancel: true,
    persistent: true,
  }).onOk(async () => {
    $q.loading.show()
    await App.service('sales-follow-up')
      .get('stop', {query: {id: props?.item?._id}})
      .then(() => {
        $q.notify({type: 'positive', message: 'Stop successfully'})
        emit('cb')
      })
      .catch((err) => {
        $q.notify({type: 'negative', message: err.message || 'Stop failed'})
      })
    $q.loading.hide()
  })
}
</script>
