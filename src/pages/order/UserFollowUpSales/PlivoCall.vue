<template>
  <q-btn class="q-mr-sm" icon="phone" color="primary" label="Call now" no-caps rounded size="" @click="onCall"></q-btn>
  <q-btn class="q-mr-sm" color="primary" label="Message" no-caps rounded size="" @click="sendMessage"></q-btn>
  <q-dialog v-model="dialog" persistent maximized transition-show="slide-up" transition-hide="slide-down">
    <q-card class="bg-black text-white row items-center">
      <div class="pc-sm q-pb-xl">
        <q-card-section class="text-center">
          <PubAvatar size="8rem" :src="detail?.customerInfo?.avatar" />
          <div class="text-h6 q-mt-md">{{ detail?.customerInfo?.nickname }}</div>
          <div class="text-h6 q-mt-md text-capitalize">{{ callStatus }}...</div>
          <div v-if="callStatus === 'speaking'">{{ formatTime(times) }}</div>
          <div class="cursor-pointer" style="display: inline-block" @click="cancelCall">
            <q-btn class="q-mt-lg" size="md" round color="negative" icon="close" />
            <div class="text-bold text-negative q-mt-sm">End call</div>
          </div>
          <div class="q-mt-xl">
            <q-btn flat round color="white" size="lg" class="q-mr-lg" @click="onClickMute" :icon="isMute ? 'mic_off' : 'mic'" />
            <q-btn flat round :color="chatSide ? 'primary' : 'white'" size="lg" icon="chat" @click="onClickChat" />
          </div>
        </q-card-section>
      </div>
    </q-card>
  </q-dialog>
</template>
<script setup>
/*
  imports
*/
import {computed, onBeforeUnmount, onMounted, ref} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import Plivo from 'plivo-browser-sdk'
import {pubStore} from 'stores/pub'
import {followUpStore} from 'stores/followUpSales'
import RecordDialog from './RecordDialog.vue'
import ChatDialog from './ChatDialog.vue'
const pub = pubStore()
const route = useRoute()
const router = useRouter()
const followStore = followUpStore()
const plivoSdk = ref(null)

const callStatus = ref('')

const dialog = ref(false)
const chatSide = ref(false)

const isMute = ref(false)

const times = ref(0)
const timer = ref(null)
const callPhone = ref(null)

const isCallAble = computed(() => {
  return canCall()
})

const canCall = () => {
  return followStore.detail?.servicePackInfo.status && pub?.user?._id === props?.detail?.sales
}

const sendMessage = () => {
  onClickChat()
}

const props = defineProps({
  detail: Object,
})

const main = async () => {
  let mobile = null

  if (props?.detail?.customerInfo?.school) {
  } else {
    mobile = props?.detail?.customerInfo?.mobile || props?.detail?.customerInfo?.emergencyContact
  }

  console.log('mobile', mobile)
  callPhone.value = mobile
  if (!mobile || !canCall()) {
    return
  }

  initPhone()
}

const onClickMute = () => {
  isMute.value = !isMute.value
  if (isMute.value) {
    plivoSdk.value?.client.mute()
  } else {
    plivoSdk.value?.client.unmute()
  }
}

const cancelCall = () => {
  dialog.value = false
  if (plivoSdk.value?.client.callSession) {
    plivoSdk.value?.client.hangup()
  }

  if (times.value > 0) {
    onCallEnd()
  }
}

const onCallEnd = () => {
  dialog.value = false
  callStatus.value = ''
  clearTimer()
  $q.dialog({
    component: RecordDialog,
    componentProps: {
      duration: times.value,
    },
  }).onOk(async () => {
    await followStore.getFollowUpDetail(route.params.id)
  })
}

const onClickChat = async () => {
  $q.dialog({
    component: ChatDialog,
  })
}

const onCall = () => {
  console.log('onCall', plivoSdk.value?.client?.isLoggedIn)
  if (!plivoSdk.value?.client?.isLoggedIn) {
    $q.notify({type: 'negative', message: 'Please login first'})
    return
  }
  dialog.value = true

  // const to = '+6421759877'
  // const to = '+8618367157350'
  const to = props?.detail?.customerInfo?.mobile
  const extraHeaders = {
    'X-PH-conference': 'true',
  }
  plivoSdk.value?.client?.call(to, extraHeaders)
}
const initPhone = () => {
  const options = {
    debug: 'INFO',
    permOnClick: false,
    codecs: ['OPUS', 'PCMU'],
    enableIPV6: false,
    audioConstraints: {optional: [{googAutoGainControl: true}, {googEchoCancellation: true}, {googNoiseSuppression: true}]},
    dscp: true,
    enableTracking: true,
    closeProtection: false,
    maxAverageBitrate: 48000,
    allowMultipleIncomingCalls: false,
    enableNoiseReduction: true,
    dtmfOptions: {sendDtmfType: ['outband', 'inband']},
  }
  let plivoBrowserSdk = new Plivo(options)
  plivoBrowserSdk.client.on('onWebrtcNotSupported', () => {
    console.log('onWebrtcNotSupported')
  })
  plivoBrowserSdk.client.on('onLogin', () => {
    console.log('onLogin')
  })
  plivoBrowserSdk.client.on('onLogout', () => {
    console.log('onLogout')
  })
  plivoBrowserSdk.client.on('onLoginFailed', () => {
    console.log('onLoginFailed')
  })
  plivoBrowserSdk.client.on('onCallRemoteRinging', () => {
    console.log('onCallRemoteRinging')
  })
  plivoBrowserSdk.client.on('onIncomingCallCanceled', () => {
    console.log('onIncomingCallCanceled')
  })
  plivoBrowserSdk.client.on('onCallFailed', (reason, callInfo) => {
    dialog.value = false
    console.log('onCallFailed', reason, callInfo)
  })
  plivoBrowserSdk.client.on('onCallAnswered', () => {
    callStatus.value = 'speaking'
    startTimer()
    console.log('onCallAnswered')
  })
  plivoBrowserSdk.client.on('onCallTerminated', () => {
    console.log('onCallTerminated')
    // onCallEnd()
  })
  plivoBrowserSdk.client.on('onCalling', () => {
    callStatus.value = 'calling'
    console.log('onCalling')
  })
  plivoBrowserSdk.client.on('onIncomingCall', () => {
    console.log('onIncomingCall')
  })
  plivoBrowserSdk.client.on('onMediaPermission', () => {
    console.log('onMediaPermission')
  })
  plivoBrowserSdk.client.on('mediaMetrics', () => {
    console.log('mediaMetrics')
  })
  plivoBrowserSdk.client.setRingTone(true)
  plivoBrowserSdk.client.setRingToneBack(true)
  let username = 'test5757371344908315748634'
  let password = 'test123'
  plivoBrowserSdk.client.login(username, password)
  plivoSdk.value = plivoBrowserSdk
}

const formatTime = (time) => {
  const h = parseInt((time / 3600) % 24)
  const minute = parseInt((time / 60) % 60)
  const second = Math.ceil(time % 60)
  return `${h < 10 ? '0' + h : h}:${minute < 10 ? '0' + minute : minute}:${second < 10 ? '0' + second : second}`
}

const startTimer = () => {
  times.value = 0
  timer.value = setInterval(uptimes, 1000)
}
const uptimes = () => {
  times.value = times.value + 1
}

const clearTimer = () => {
  clearTimeout(timer.value)
  timer.value = null
}

onBeforeUnmount(() => {
  clearTimer()
})

onMounted(main)
</script>
<style lang="scss" scope>
.box {
  position: relative;

  .send_btn {
    position: absolute;
    right: 10px;
    top: 10px;

    .rotate {
      transform: rotate(90deg);
    }
  }
}

.q-dialog__inner--minimized {
  padding: 0px !important;
}
</style>
