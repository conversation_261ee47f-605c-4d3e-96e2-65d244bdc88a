<template>
  <q-layout view="hHh LpR fFf" class="bg-white">
    <LeftDrawer active="Follow-up sales" ref="child" />
    <q-page-container class="pc-sm page-box">
      <q-page  class="scroll " style="height: calc(100vh - 50px)">
        <div class="q-pa-md">
          <div class="text-h6 q-my-md">Follow-up sales</div>
          <SalesList />
        </div>
      </q-page>
    </q-page-container>
  </q-layout>
</template>
<script setup>
/*
  imports
*/
import {onMounted, ref} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import LeftDrawer from '../components/LeftDrawer.vue'
import SalesList from './SalesList.vue'

const route = useRoute()
const router = useRouter()
const child = ref(null)
const goBack = (hash) => {
  router.go(-1)
}

</script>
<style lang="sass" scope>

.page-box
  padding-left: 0 !important
</style>
