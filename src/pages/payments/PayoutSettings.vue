<template>
  <div class="payout-page">
    <!-- Page Loading State -->
    <div v-if="pageLoading" class="loading-container" style="background-color: white">
      <div class="loading-card">
        <q-spinner-dots size="50px" color="primary" />
        <div class="loading-text">Loading...</div>
      </div>
    </div>

    <!-- Main Content -->
    <div v-else class="main-content" style="background-color: white">
      <div class="content-card">
        <!-- Header Section -->
        <div class="header-section">
          <q-icon name="account_balance_wallet" class="main-icon" />
          <h3 class="page-title">Payout Settings</h3>
          <p class="page-subtitle">Set up where you'd like to receive your income and commission payments</p>
        </div>

        <!-- Status Card -->
        <div class="status-card" :class="{configured: hasExistingBeneficiary}">
          <div class="status-content">
            <div class="status-icon">
              <q-icon
                :name="hasExistingBeneficiary ? 'check_circle' : 'add_circle_outline'"
                :color="hasExistingBeneficiary ? 'positive' : 'primary'"
                size="20px" />
            </div>
            <div class="status-text">
              <div class="status-title">
                {{ hasExistingBeneficiary ? 'Bank Details Configured' : 'Bank Details Required' }}
              </div>
              <div class="status-description">
                {{ hasExistingBeneficiary ? 'Ready to receive payments' : 'Add your bank details to get paid' }}
              </div>
            </div>
          </div>
        </div>

        <!-- Action Button -->
        <div class="action-section">
          <q-btn
            unelevated
            color="primary"
            :label="hasExistingBeneficiary ? 'Update Bank Details' : 'Add Bank Details'"
            :icon-right="hasExistingBeneficiary ? 'edit' : 'arrow_forward'"
            @click="openBeneficiaryDrawer"
            class="action-button">
          </q-btn>
        </div>

        <!-- Info Section -->
        <div class="info-section">
          <div class="info-item">
            <q-icon name="info_outline" color="blue-grey-6" size="16px" />
            <span class="info-text">Your income and commission will be paid monthly to this account</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Beneficiary Drawer -->
    <q-drawer v-model="drawerOpen" side="right" overlay behavior="mobile" :width="500" class="modern-drawer">
      <div class="drawer-content">
        <div class="drawer-header">
          <div class="drawer-title">{{ hasExistingBeneficiary ? 'Update Bank Details' : 'Add Bank Details' }}</div>
          <q-btn flat round dense icon="close" @click="closeDrawer" :disable="submitting" class="close-btn" />
        </div>

        <q-separator class="drawer-separator" />

        <!-- Loading State -->
        <div v-if="loading" class="drawer-loading">
          <q-spinner-dots size="40px" color="primary" />
          <div class="loading-text">Loading form...</div>
        </div>

        <!-- Error State -->
        <div v-else-if="error" class="drawer-error">
          <q-icon name="error_outline" size="40px" color="negative" />
          <div class="error-text">{{ error }}</div>
          <q-btn flat color="primary" label="Try Again" @click="initializeBeneficiaryForm" class="retry-btn" />
        </div>

        <!-- Airwallex Form Container -->
        <div>
          <div id="beneficiary-form" class="form-container"></div>

          <div class="drawer-footer">
            <div class="footer-actions">
              <q-btn flat label="Cancel" @click="closeDrawer" :disable="submitting" class="cancel-btn" />
              <q-btn
                unelevated
                color="primary"
                :label="hasExistingBeneficiary ? 'Update' : 'Save'"
                @click="submitBeneficiaryForm"
                :loading="submitting"
                class="submit-btn" />
            </div>
          </div>
        </div>
      </div>
    </q-drawer>
  </div>
</template>

<script setup>
import {ref, onMounted, onUnmounted} from 'vue'
import {createElement, init} from '@airwallex/components-sdk'
import {pubStore} from 'stores/pub'

// Reactive state
const drawerOpen = ref(false)
const loading = ref(false)
const submitting = ref(false)
const error = ref('')
const pageLoading = ref(true)
const existingBeneficiary = ref(null)
const hasExistingBeneficiary = ref(false)
let beneficiaryComponent = null
const schoolId = ref('')
const pub = pubStore()
schoolId.value = pub.user?.schoolInfo?._id || ''

const airwallexTheme = {
  palette: {
    primary: {
      10: '#e0f2f1',
      20: '#b2dfdb',
      30: '#80cbc4',
      40: '#4db6ac',
      50: '#26a69a',
      60: '#009688',
      70: '#00897b',
      80: '#00796b',
      90: '#00695c',
      100: '#004d40',
    },
    gradients: {
      primary: ['#e0f2f1', '#b2dfdb'],
      secondary: ['#80cbc4', '#4db6ac'],
      tertiary: ['#26a69a', '#009688'],
      quaternary: ['#00796b', '#004d40'],
    },
  },
  components: {
    spinner: {
      colors: {
        start: {
          initial: '#b2dfdb',
        },
        stop: {
          initial: '#00796b',
        },
      },
    },
  },
}

// Check for existing beneficiary on component mount
onMounted(async () => {
  await checkExistingBeneficiary()
})

// Check if beneficiary already exists
const checkExistingBeneficiary = async () => {
  try {
    pageLoading.value = true

    const beneficiaryData = await App.service('airwallex').get('beneficiary', {query: {schoolId: schoolId.value}})
    console.log('beneficiaryData', beneficiaryData)

    if (beneficiaryData) {
      existingBeneficiary.value = beneficiaryData
      hasExistingBeneficiary.value = true
    }
  } catch (err) {
    // If beneficiary doesn't exist, that's expected for new users
    console.log('No existing beneficiary found:', err.message)
    hasExistingBeneficiary.value = false
    existingBeneficiary.value = null
  } finally {
    pageLoading.value = false
  }
}

// Open drawer and initialize Airwallex form
const openBeneficiaryDrawer = async () => {
  drawerOpen.value = true
  await initializeBeneficiaryForm()
}

// Initialize Airwallex beneficiary form
const initializeBeneficiaryForm = async () => {
  try {
    loading.value = true
    error.value = ''

    // Get auth code from Feathers service
    const authResponse = await App.service('airwallex').get('auth-code')
    console.log('authResponse', authResponse)
    // // Initialize Airwallex SDK
    await init({
      authCode: authResponse.auth_code,
      env: authResponse.environment,
      clientId: authResponse.client_id,
      codeVerifier: authResponse.code_verifier,
    })

    // Clear previous component if exists
    if (beneficiaryComponent) {
      beneficiaryComponent.destroy()
    }

    // Prepare component options
    const componentOptions = {
      intent: {
        // Add any required intent configuration here
      },
      theme: airwallexTheme,
    }

    // Add default values if editing existing beneficiary
    if (hasExistingBeneficiary.value && existingBeneficiary.value) {
      componentOptions.defaultValues = Acan.clone(existingBeneficiary.value)
    }

    // Create beneficiary component
    beneficiaryComponent = await createElement('beneficiaryForm', componentOptions)
    console.log('beneficiaryComponent', beneficiaryComponent)
    loading.value = false
    // Mount the component
    beneficiaryComponent.mount('#beneficiary-form')

    // Listen for component events
    beneficiaryComponent.on('ready', () => {
      console.log('Beneficiary form is ready')
    })

    beneficiaryComponent.on('error', (event) => {
      console.error('Beneficiary form error:', event)
      $q.notify({
        type: 'negative',
        message: 'Form error: ' + (event.error?.message || 'Unknown error'),
        position: 'top',
      })
    })
  } catch (err) {
    console.error('Failed to initialize beneficiary form:', err)
    error.value = err.message || 'Failed to load form. Please try again.'
    $q.notify({
      type: 'negative',
      message: 'Failed to load form',
      position: 'top',
    })
  } finally {
    loading.value = false
  }
}

// Submit beneficiary form
const submitBeneficiaryForm = async () => {
  if (!beneficiaryComponent) {
    $q.notify({
      type: 'negative',
      message: 'Form not initialized',
      position: 'top',
    })
    return
  }

  try {
    submitting.value = true

    // Submit the Airwallex form
    const dataFromAirwallexForm = await beneficiaryComponent.submit()
    console.log('dataFromAirwallexForm', dataFromAirwallexForm)

    // Send data to your Feathers service - create or update based on existing beneficiary
    if (hasExistingBeneficiary.value) {
      await App.service('airwallex').patch('beneficiary', dataFromAirwallexForm, {query: {schoolId: schoolId.value}})
      console.log('form has been updated')
    } else {
      await App.service('airwallex').create({
        type: 'beneficiary',
        schoolId: schoolId.value,
        beneficiaryData: dataFromAirwallexForm,
      })
      console.log('form has been created')
    }

    // Update local state
    existingBeneficiary.value = dataFromAirwallexForm.values
    hasExistingBeneficiary.value = true

    closeDrawer()

    $q.notify({
      type: 'positive',
      message: hasExistingBeneficiary.value ? 'Payment details updated successfully!' : 'Payment details added successfully!',
      position: 'top',
    })
  } catch (err) {
    console.error('Failed to submit beneficiary form:', err)

    let errorMessage = hasExistingBeneficiary.value ? 'Failed to update payment details' : 'Failed to save payment details'

    // Handle specific Airwallex errors
    if (err.type === 'validation_error') {
      errorMessage = 'Please check your form inputs'
    } else if (err.message) {
      errorMessage = err.message
    }

    $q.notify({
      type: 'negative',
      message: errorMessage,
      position: 'top',
    })
  } finally {
    submitting.value = false
  }
}

// Close drawer and cleanup
const closeDrawer = () => {
  drawerOpen.value = false

  // Clean up component
  if (beneficiaryComponent) {
    beneficiaryComponent.destroy()
    beneficiaryComponent = null
  }

  // Reset state
  loading.value = false
  submitting.value = false
  error.value = ''
}

// Cleanup on component unmount
onUnmounted(() => {
  if (beneficiaryComponent) {
    beneficiaryComponent.destroy()
  }
})
</script>

<style scoped>
.payout-page {
  background-color: white;
  position: relative;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.loading-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 300px;
  gap: 16px;
  padding: 32px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.loading-text {
  font-size: 14px;
  color: #666;
}

.main-content {
  max-width: 600px;
  margin: 30px auto 0 auto;
}

.content-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.header-section {
  padding: 32px 24px 24px;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
}

.main-icon {
  font-size: 32px;
  color: #1976d2;
}

.page-title {
  font-size: 24px;
  font-weight: 500;
  color: #1a1a1a;
  margin: 0;
}

.page-subtitle {
  font-size: 14px;
  color: #666;
  margin: 0;
  line-height: 1.5;
}

.status-card {
  padding: 20px 24px;
  border-bottom: 1px solid #f0f0f0;
}

.status-card.configured {
  background-color: #f8fff8;
}

.status-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.status-icon {
  display: flex;
  align-items: center;
}

.status-text {
  flex: 1;
}

.status-title {
  font-size: 15px;
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 2px;
}

.status-description {
  font-size: 13px;
  color: #666;
}

.action-section {
  padding: 24px;
  display: flex;
  justify-content: center;
  border-bottom: 1px solid #f0f0f0;
}

.action-button {
  min-width: 200px;
  font-weight: 500;
}

.info-section {
  padding: 20px 24px;
  background-color: #fafafa;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-text {
  font-size: 13px;
  color: #666;
}

.modern-drawer {
  box-shadow: -10px 0 30px rgba(0, 0, 0, 0.2);
}

.drawer-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.drawer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  background: #fafafa;
}

.drawer-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
}

.close-btn {
  color: #666;
}

.drawer-separator {
  margin: 0;
}

.drawer-loading,
.drawer-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 24px;
  gap: 16px;
}

.error-text {
  font-size: 14px;
  color: #666;
  text-align: center;
}

.form-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.form-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 16px 24px;
  background-color: #f8fff8;
  border-bottom: 1px solid #e8f5e8;
}

.form-info-text {
  font-size: 12px;
  color: #2e7d32;
}

.form-container {
  flex: 1;
  padding: 0 20px 100px 20px;
  min-height: 400px;
}

.drawer-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background: white;
  padding: 20px;
  border-top: 1px solid #e0e0e0;
  z-index: 1000;
}

.footer-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.cancel-btn {
  color: #666;
}

.submit-btn {
  border-radius: 8px;
  font-weight: 600;
}

@media (max-width: 600px) {
  .payout-page {
    padding: 12px;
  }

  .main-content {
    margin: 0;
  }

  .header-section {
    padding: 24px 20px 20px;
  }

  .page-title {
    font-size: 20px;
  }

  .modern-drawer {
    width: 100% !important;
  }

  .drawer-header {
    padding: 16px 20px;
  }

  .form-container,
  .drawer-footer {
    padding: 20px;
  }
}
</style>
