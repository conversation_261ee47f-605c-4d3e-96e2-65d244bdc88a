<template>
  <q-dialog persistent>
    <q-card class="q-pa-md text-center" style="min-width: 300px; max-width: 400px; border-radius: 8px; background-color: #eef4f6">
      <q-card-section>
        <div class="text-body1" style="color: #313033">
          {{ message }}
        </div>
      </q-card-section>

      <q-card-actions align="center" class="q-mt-sm" style="gap: 12px">
        <q-btn no-caps outline rounded :label="okLabel" color="primary" style="padding: 0 24px" @click="$emit('ok')" v-close-popup />
        <q-btn unelevated no-caps rounded :label="cancelLabel" color="primary" style="padding: 0 24px" v-close-popup />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
defineProps({
  message: {
    type: String,
    default: 'Are you sure?',
  },
  okLabel: {
    type: String,
    default: 'OK',
  },
  cancelLabel: {
    type: String,
    default: 'Cancel',
  },
})
</script>

<style scoped>
.q-btn--outline:before {
  border-color: #9d9d9d;
}
</style>
