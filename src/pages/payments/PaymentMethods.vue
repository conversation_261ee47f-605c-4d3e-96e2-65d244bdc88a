<template>
  <div class="payment-methods q-pa-md">
    <div class="text-h6 q-mb-md" style="color: #49454f">Payment Method</div>

    <q-card flat bordered class="q-pa-md payment-card">
      <q-inner-loading :showing="initialLoading">
        <q-spinner-dots size="50px" color="primary" />
      </q-inner-loading>

      <div class="row items-center justify-between q-mb-sm" v-if="savedCards.length > 0">
        <div style="color: #c9c5ca; font-size: 16px; font-weight: 400">Saved Payment Card</div>
        <div style="color: #c9c5ca; font-size: 12px"><q-icon name="lock" size="xs" /> SECURE PAYMENTS</div>
      </div>

      <div v-if="savedCards.length > 0" style="overflow-y: auto; max-height: 425px">
        <q-card v-for="card in savedCards" :key="card._id" flat bordered class="q-mb-sm" :style="card.isDefault ? 'border-color: #73ddd3' : ''">
          <q-card-section class="q-py-sm">
            <div
              class="row items-center justify-between"
              :class="{'disabled-element': disableAction}"
              :style="'gap: 10px;' + (!card.isDefault ? 'cursor: pointer;' : '')"
              @click="setDefaultCard(card)">
              <div class="row items-center">
                <div class="q-mr-md row items-center">
                  <img v-if="cardIcons[card.cardBrand]" :src="cardIcons[card.cardBrand]" :alt="card.cardBrand" :width="$q.screen.gt.sm ? '40px' : '30px'" />
                  <q-icon v-else name="credit_card" :size="$q.screen.gt.sm ? '40px' : '30px'" color="primary" />
                </div>

                <div class="row items-center" style="gap: 10px">
                  <div>{{ $q.screen.gt.sm ? '•••• ••••' : '' }} •••• {{ card.last4 }}</div>
                  <q-chip v-if="card.isDefault" dense color="grey-3" text-color="grey-8" square class="q-ma-none q-pa-sm">Default</q-chip>
                  <span v-else style="width: 61px" />
                  <span v-if="$q.screen.gt.sm" class="q-ml-md" style="font-size: 14px; color: #000000"
                    >{{ $q.screen.gt.sm ? 'Expiration Date:' : 'Expiry' }} {{ card.expMonth }}/{{ card.expYear }}</span
                  >
                </div>
              </div>

              <div>
                <q-btn
                  v-if="$q.screen.gt.sm"
                  flat
                  color="teal"
                  label="Delete Card"
                  no-caps
                  @click.stop="confirmDeleteCard(card)"
                  :loading="processingId === card._id" />
                <q-btn v-else flat color="teal" icon="delete" @click.stop="confirmDeleteCard(card)" :loading="processingId === card._id" />
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>

      <div v-if="!showAddCardForm && !initialLoading" class="q-mt-md row justify-end">
        <q-btn
          color="teal"
          icon="add"
          :disable="disableAction"
          label="Add New Card"
          no-caps
          rounded
          :size="$q.screen.gt.sm ? 'md' : 'sm'"
          :class="{'full-width': savedCards.length == 0}"
          @click="onAddNewCardClick" />
      </div>

      <div v-if="showAddCardForm" class="q-mt-md">
        <q-inner-loading :showing="formLoading">
          <q-spinner-dots size="50px" class="q-mt-xl" color="primary" />
        </q-inner-loading>

        <div style="color: #c9c5ca; font-size: 16px; font-weight: 400">Add a Card</div>

        <div class="q-mb-md">
          <div class="text-caption q-mb-sm">Cardholder Name</div>
          <q-input v-model="cardholderName" outlined dense placeholder="Enter the name on your card" :disable="disableAction" />
        </div>

        <div class="q-mb-md">
          <div class="text-caption q-mb-sm">Card Number</div>
          <div id="card-number-element" class="stripe-element" :class="{'disabled-element': disableAction}"></div>
          <div id="card-number-errors" class="text-negative text-caption"></div>
        </div>

        <div class="row q-col-gutter-md q-mb-md">
          <div class="col-xs-12 col-sm-6">
            <div class="text-caption q-mb-sm">Expiration Date (MM/YY)</div>
            <div id="card-expiry-element" class="stripe-element" :class="{'disabled-element': disableAction}"></div>
            <div id="card-expiry-errors" class="text-negative text-caption"></div>
          </div>
          <div class="col-xs-12 col-sm-6">
            <div class="text-caption q-mb-sm">CVV</div>
            <div id="card-cvc-element" class="stripe-element" :class="{'disabled-element': disableAction}"></div>
            <div id="card-cvc-errors" class="text-negative text-caption"></div>
          </div>
        </div>

        <div id="card-errors" class="text-negative text-caption q-mb-md"></div>

        <div class="row justify-end q-mt-md">
          <q-btn rounded outline label="Cancel" no-caps class="q-mr-sm" :disable="disableAction" @click="cancelAddCard" />
          <q-btn color="primary" label="Save Card" no-caps rounded :disable="disableAction" :loading="processing" @click="saveCard" />
        </div>
      </div>
    </q-card>
  </div>
</template>

<script setup>
import {ref, onMounted, defineProps, computed} from 'vue'
import {loadStripe} from '@stripe/stripe-js'
import {useConfirmDialog} from './useConfirmDialog'
import {cardIcons} from 'src/components/payments/cardIcons'

const props = defineProps({
  schoolId: String,
})

const savedCards = ref([])
const showAddCardForm = ref(false)
const processing = ref(false)
const processingId = ref(null)
const cardholderName = ref('')
const formLoading = ref(false)
const initialLoading = ref(true)

// Stripe elements
const stripe = ref(null)
const elements = ref(null)
const cardNumberElement = ref(null)
const cardExpiryElement = ref(null)
const cardCvcElement = ref(null)
const setupClientSecret = ref(null)

const disableAction = computed(() => {
  return !!processingId.value || processing.value || formLoading.value
})

const confirmDialog = useConfirmDialog()

const loadSavedCards = async () => {
  try {
    const cards = await App.service('payment-methods').find({query: {schoolId: props.schoolId}})
    savedCards.value = cards.data || []
  } catch (error) {
    $q.notify({
      type: 'negative',
      message: 'Failed to load saved cards',
    })
  } finally {
    initialLoading.value = false
  }
}

const initializeStripe = async () => {
  try {
    stripe.value = await loadStripe(
      process.env.STRIPE_PUBLIC_KEY || 'pk_test_51RKWJK06kjwDIJihRihkUyz1BSbh9y92PzoZ6iLb4C132WwQiiOU7eUNO3BuwfCGTWbVS1d5hPasG8VCdIJSdXM500oqkeQ48d'
    )
  } catch (error) {
    console.error('Error initializing Stripe:', error)
    $q.notify({
      type: 'negative',
      message: 'Failed to initialize payment system',
    })
  }
}

const setupCardElements = async () => {
  if (!stripe.value) return

  formLoading.value = true

  try {
    const {clientSecret} = await App.service('stripe').create({type: 'createSetupIntent', schoolId: props.schoolId, setupFutureUsage: 'on_session'})
    setupClientSecret.value = clientSecret

    elements.value = stripe.value.elements({
      clientSecret: setupClientSecret.value,
    })

    const style = {
      base: {
        color: '#32325d',
        fontFamily: '"Helvetica Neue", Helvetica, sans-serif',
        fontSmoothing: 'antialiased',
        fontSize: '16px',
        '::placeholder': {
          color: '#aab7c4',
        },
        padding: '10px 12px',
      },
      invalid: {
        color: '#fa755a',
        iconColor: '#fa755a',
      },
    }

    // Create individual elements
    cardNumberElement.value = elements.value.create('cardNumber', {
      style,
      placeholder: '1234 1234 1234 1234',
      showIcon: true,
      iconStyle: 'solid',
    })
    cardExpiryElement.value = elements.value.create('cardExpiry', {
      style,
      placeholder: 'MM/YY',
    })
    cardCvcElement.value = elements.value.create('cardCvc', {
      style,
      placeholder: 'CVV',
    })

    cardNumberElement.value.mount('#card-number-element')
    cardExpiryElement.value.mount('#card-expiry-element')
    cardCvcElement.value.mount('#card-cvc-element')

    const displayError = (element, errorElement) => {
      element.on('change', (event) => {
        const errorDisplay = document.getElementById(errorElement)
        if (event.error) {
          errorDisplay.textContent = event.error.message
        } else {
          errorDisplay.textContent = ''
        }
      })
    }

    displayError(cardNumberElement.value, 'card-number-errors')
    displayError(cardExpiryElement.value, 'card-expiry-errors')
    displayError(cardCvcElement.value, 'card-cvc-errors')
  } catch (error) {
    $q.notify({
      type: 'negative',
      message: 'Failed to set up card form',
    })
  } finally {
    formLoading.value = false
  }
}

const onAddNewCardClick = async () => {
  showAddCardForm.value = true
  await setupCardElements()
}

const saveCard = async () => {
  if (!cardholderName.value) {
    $q.notify({
      type: 'negative',
      message: 'Please enter the cardholder name',
    })
    return
  }

  processing.value = true

  try {
    if (!stripe.value || !setupClientSecret.value || !cardNumberElement.value) {
      throw new Error('Card form not initialized')
    }

    const {setupIntent, error: setupError} = await stripe.value.confirmCardSetup(setupClientSecret.value, {
      payment_method: {
        card: cardNumberElement.value,
        billing_details: {
          name: cardholderName.value,
        },
      },
    })

    if (setupError) {
      throw new Error(setupError.message)
    }
    await App.service('payment-methods').create({
      stripePaymentMethodId: setupIntent.payment_method,
      isDefault: savedCards.value.length === 0,
      schoolId: props.schoolId,
    })

    cardholderName.value = ''
    showAddCardForm.value = false
    initialLoading.value = true
    await loadSavedCards()

    $q.notify({
      type: 'positive',
      message: 'Card saved successfully',
    })
  } catch (error) {
    $q.notify({
      type: 'negative',
      message: error.message || 'Failed to save card',
    })
  } finally {
    processing.value = false
  }
}

const cancelAddCard = () => {
  showAddCardForm.value = false
  cardholderName.value = ''

  // Clean up Stripe elements
  if (cardNumberElement.value) {
    cardNumberElement.value.unmount()
    cardNumberElement.value = null
  }
  if (cardExpiryElement.value) {
    cardExpiryElement.value.unmount()
    cardExpiryElement.value = null
  }
  if (cardCvcElement.value) {
    cardCvcElement.value.unmount()
    cardCvcElement.value = null
  }
}

const setDefaultCard = async (card) => {
  if (card.isDefault) {
    return
  }

  confirmDialog({
    message: `Switch this card ending in ${card.last4} as your primary card?`,
    okLabel: 'Switch',
    cancelLabel: 'Cancel',
  }).onOk(async () => {
    processingId.value = card._id

    try {
      await App.service('payment-methods').patch(card._id, {
        isDefault: true,
      })

      await loadSavedCards()

      $q.notify({
        type: 'positive',
        message: 'Default payment method updated',
      })
    } catch (error) {
      $q.notify({
        type: 'negative',
        message: 'Failed to update default payment method',
      })
    } finally {
      processingId.value = null
    }
  })
}

const confirmDeleteCard = (card) => {
  confirmDialog({
    message: `Are you sure you want to delete this card ending in ${card.last4}?`,
    okLabel: 'Delete',
    cancelLabel: 'Cancel',
  }).onOk(async () => {
    processingId.value = card._id

    try {
      await App.service('payment-methods').remove(card._id)
      await loadSavedCards()

      $q.notify({
        type: 'positive',
        message: 'Card deleted successfully',
      })
    } catch (error) {
      $q.notify({
        type: 'negative',
        message: 'Failed to delete card',
      })
    } finally {
      processingId.value = null
    }
  })
}

onMounted(async () => {
  await initializeStripe()
  await loadSavedCards()
})
</script>

<style scoped>
.payment-methods {
  max-width: 800px;
}

.payment-card {
  min-height: 320px;
  border-color: #73ddd3;
  min-width: 700px;
  @media (max-width: 700px) {
    min-width: 90%;
  }
}

.stripe-element {
  padding: 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: white;
  height: 40px;
}

.disabled-element {
  opacity: 0.6;
  pointer-events: none;
}
</style>
