<template>
  <div>
    <PubTopBanner :title="selectedPageTitle" :isShowMenu="false" back="/account/info" useBack>
      <template v-slot:left>
        <q-btn v-if="$q.screen.lt.md" flat round dense icon="menu" @click="drawerOpen = !drawerOpen" />
      </template>
    </PubTopBanner>

    <q-drawer v-model="drawerOpen" class="bg-white" bordered width="280" :breakpoint="1024" show-if-above>
      <q-list class="q-pa-md">
        <q-item
          v-for="item in menu"
          :key="item.page"
          clickable
          v-ripple
          :active="selectedPage === item.page"
          @click="onMenuItemClick(item)"
          class="rounded-borders q-mb-sm"
          active-class="bg-teal-1 text-black text-weight-medium">
          <q-item-section>
            <q-item-label :class="{'text-bold': selectedPage === item.page}">
              {{ item.label }}
            </q-item-label>
          </q-item-section>
        </q-item>
      </q-list>
    </q-drawer>

    <q-page class="column q-pa-sm q-pa-lg-lg full-height">
      <PaymentMethods v-if="selectedPage === 'payment-info'" :schoolId="isSchool ? schoolId : null" />
      <PayoutSettings v-if="selectedPage === 'payout-info'" />
    </q-page>
  </div>
</template>

<script setup>
import {ref, watch, computed, onMounted} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import useSchool from 'src/composables/common/useSchool'
import PubTopBanner from 'src/components/pub/PubTopBanner.vue'
import PaymentMethods from './PaymentMethods.vue'
import PayoutSettings from './PayoutSettings.vue'

const route = useRoute()
const router = useRouter()
const {schoolId, isSchool, isAdmin} = useSchool()
const selectedPage = ref(route.params.page || 'payment-info')
const drawerOpen = ref(true)

const menu = [
  {
    label: 'Payment',
    headerLabel: 'Payment Information',
    page: 'payment-info',
  },
  {
    label: 'Payout',
    headerLabel: 'Payout Information',
    page: 'payout-info',
  },
]

const selectedPageTitle = computed(() => {
  const selectedMenuItem = menu.find((item) => item.page === selectedPage.value)
  return selectedMenuItem ? selectedMenuItem.headerLabel : ''
})

const onMenuItemClick = (item) => {
  selectedPage.value = item.page
  router.push(`/account/payment-payout-settings/${item.page}`)

  if (window.innerWidth < 1024) {
    drawerOpen.value = false
  }
}

watch(
  () => route.params.page,
  (newPage) => {
    if (newPage) {
      selectedPage.value = newPage
    }
  }
)

onMounted(() => {
  if (isSchool.value && !isAdmin.value) {
    router.push('/account/info')
  }
})
</script>

<style scoped>
.q-drawer {
  top: 50px !important;
}

@media (max-width: 1023px) {
  .q-drawer {
    top: 50px !important;
  }
}
</style>
