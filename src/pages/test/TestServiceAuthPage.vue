<template>
  <div class="col full-width column full-height overflow-hidden q-pa-md">
    <q-toolbar class="q-gutter-xs">
      <q-btn @click="$router.back()" label="back"></q-btn>
      <q-btn to="/test" label="test"></q-btn>
      <q-space></q-space>
      <AccountMenu />
    </q-toolbar>
    <div class="row col full-height full-width overflow-hidden">
      <div class="col full-height overflow-auto">
        <q-inner-loading :showing="loading" label-class="text-teal" label-style="font-size: 1.1em" />
        <q-item-section side>
          <!-- <q-img :ratio="16 / 9" :src="one.logo" style="min-width: 10rem"></q-img> -->
        </q-item-section>
        <q-item-section class="bg-teal-2">
          <q-item-label>{{ one._id }}</q-item-label>
          <q-item-label>Name: {{ one.name }}</q-item-label>
          <q-item-label>serviceRoles: {{ one.serviceRoles }}</q-item-label>
          <q-item-label v-if="one.unitSnapshot">
            {{ one.unitSnapshot.name }}
            <q-chip :label="one.unitSnapshot.mode"><q-tooltip>unit.mode</q-tooltip></q-chip>
          </q-item-label>
          <q-item-label>CreatedAt: {{ new Date(one.createdAt).toLocaleString() }}</q-item-label>
          <q-item-label>
            <q-chip :label="one.type"><q-tooltip>type</q-tooltip></q-chip>
            <q-chip v-if="one.mentoringType" :label="one.mentoringType"><q-tooltip>mentoringType</q-tooltip></q-chip>
            <q-chip v-if="one.curriculum" color="green-4" :label="one.curriculum"><q-tooltip>curriculum</q-tooltip></q-chip>
          </q-item-label>
          <OwnerBox :updatedAt="one.createdAt" :owner="one.owner" />
        </q-item-section>
        <q-item-section side>
          <!-- <q-item-label class="q-gutter-xs"> -->
          <q-btn flat label="view" :to="{path: `/test/service/auth/${one._id}`}" target="_blank"></q-btn>
          <q-btn flat label="auth view" :to="{path: `/unit/auth/${one._id}`, query: {}}" target="_blank"></q-btn>
          <q-btn flat icon="delete" @click="rmFn(o)"></q-btn>
          <!-- </q-item-label> -->
        </q-item-section>
        <div class="row">
          <q-card v-for="(o, i) in one.linkSnapshot" :key="i" class="col-6">
            <q-img :src="o.cover"></q-img>
            <q-card-section>
              {{ o.name }}
              {{ o.overview }}
            </q-card-section>
          </q-card>
        </div>
      </div>
      <pre class="col-4 bg-amber-2 full-height overflow-auto">
one:
  {{ one }}
</pre
      >
    </div>
  </div>
</template>
<script setup>
import {ref, onMounted} from 'vue'
import AccountMenu from 'components/AccountMenu.vue'
import OwnerBox from 'components/detail/OwnerBox.vue'

import {useRoute} from 'vue-router'
const route = useRoute()

const Service = App.service('service-auth')
const one = ref({}),
  loading = ref(false)

async function find() {
  loading.value = true
  one.value = await Service.get(route.params.id)
  loading.value = false
}
onMounted(find)
</script>
