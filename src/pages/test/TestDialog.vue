<template>
  <div>
    <q-btn label="RecommendDialog" @click="recommendFn"></q-btn>
  </div>
</template>
<script setup>
import RecommendDialog from 'components/RecommendDialog.vue'
function recommendFn() {
  const data = {
    curriculum: 'pd',
    grades: [
      {
        label: '',
        value: 'Foundation',
        _id: '68803be96e6ecde1288bf89d',
      },
    ],
    serviceType: 'steam',
    participants: 'students',
    liveSessions: 1,
    promotion: true,
    orientated: false,
    educators: false,
    contentOrientatedEnable: false,
  }
  $q.dialog({
    component: RecommendDialog,
    componentProps: data,
  })
}
</script>
