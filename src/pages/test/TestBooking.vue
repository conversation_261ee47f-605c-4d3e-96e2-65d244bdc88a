<template>
  <div class="col full-width column full-height overflow-hidden q-pa-md">
    <q-toolbar class="q-gutter-xs">
      <q-btn @click="$router.back()" label="back"></q-btn>
      <q-btn to="/test" label="test"></q-btn>
      <q-space></q-space>
      <q-select @blur="find()" v-model="queryData.type" label="Type" :options="['', ...Object.keys(ServiceType)]"></q-select>
      <!-- <q-input label="name" v-model="queryData.name.$search" @change="find()"></q-input> -->
      <q-pagination
        class="flex-center q-mr-md"
        v-model="page"
        @click="find"
        color="teal"
        :max="Math.ceil(list.total / list.limit)"
        :max-pages="6"
        :boundary-numbers="true" />
      <AccountMenu />
    </q-toolbar>
    <div class="row col full-height full-width overflow-hidden">
      <div class="col full-height overflow-auto">
        <q-inner-loading :showing="loading" label-class="text-teal" label-style="font-size: 1.1em" />
        <q-list>
          <q-item v-for="(o, i) in list.data" :key="i">
            <q-item-section side v-if="o.session?.image">
              <q-img :ratio="16 / 9" :src="hashToUrl(o.session.image)" style="min-width: 10rem"></q-img>
            </q-item-section>
            <q-item-section side v-else-if="o.servicePackUser?.snapshot?.cover">
              <q-img :ratio="16 / 9" :src="hashToUrl(o.servicePackUser.snapshot.cover)" style="min-width: 10rem"></q-img>
            </q-item-section>
            <q-item-section class="bg-teal-2">
              <q-item-label>{{ o._id }}</q-item-label>
              <q-item-label>PackUser: {{ o.packUser }}</q-item-label>
              <q-item-label v-if="o.session">Session: {{ o.session.name }}</q-item-label>
              <q-item-label>Start: {{ Acan.time('Y-m-d H:i', o.start) }} - {{ Acan.time('H:i', o.end) }}</q-item-label>
              <q-item-label>
                <template v-for="(color, k) in {type: '', mentoringType: '', cancel: 'red-3', times: 'amber', message: 'grey'}">
                  <q-chip v-if="o[k]" :label="o[k]" :color="color">
                    <q-tooltip>{{ k }}</q-tooltip>
                  </q-chip>
                </template>
              </q-item-label>
              <q-item-label>CreatedAt: {{ new Date(o.createdAt).toLocaleString() }}</q-item-label>
              <q-item-label>
                <div class="row q-gutter-xs items-center">Booker: <OwnerBox :owner="o.bookerInfo" /> Servicer: <OwnerBox :owner="o.servicerInfo" /></div>
              </q-item-label>
            </q-item-section>
            <q-item-section side>
              <div class="q-gutter-xs column">
                <q-btn
                  flat
                  label="confirmed"
                  target="_blank"
                  :to="{path: `/booking/booking-confirmed/${o.servicer}/${o.packUser}`, query: {bookingId: o._id}}"
                  no-caps></q-btn>
                <q-btn flat label="import" :to="{path: `/booking/import/${o._id}`}" no-caps></q-btn>
                <q-btn flat icon="delete" @click="rmFn(o)" color="red"></q-btn>
                <q-btn flat label="getHours" @click="getHours(o)" no-caps></q-btn>
                <q-btn flat label="checkServicer" @click="checkServicer(o)" no-caps></q-btn>
              </div>
            </q-item-section>
          </q-item>
        </q-list>
      </div>
      <pre class="col-4 bg-amber-2 full-height overflow-auto">
query:
  {{ queryData }}
list:
  {{ list.data }}
</pre
      >
    </div>
  </div>
</template>
<script setup>
import {ref, onMounted, inject} from 'vue'
import AccountMenu from 'components/AccountMenu.vue'

import OwnerBox from 'components/detail/OwnerBox.vue'
import {useRoute} from 'vue-router'
window.route = useRoute()

// const ServiceType = inject('ServiceType')
const Service = App.service('service-booking')
const list = ref({}),
  page = ref(1),
  loading = ref(false),
  type = ref('')
const queryData = ref({name: {$search: ''}, $limit: 10, $skip: 0, $sort: {_id: -1}})

async function getHours(o) {
  const query = {uid: {$in: [o.servicer]}, start: o.start, end: o.end}
  const rs = await Service.get('hours', {query})
  console.log(query, rs)
}
async function checkServicer(o) {
  const query = {uid: {$in: [o.servicer]}, start: o.start, end: o.end}
  const rs = await Service.get('checkServicersHours', {query})
  console.log(query, rs)
}
async function rmFn(o) {
  await Service.remove(o._id)
  find()
}
async function find() {
  loading.value = true
  list.value = {}
  queryData.value.$skip = page.value * (list.value.limit ?? 20) - 20
  const query = Acan.clone(queryData.value)
  Acan.objClean(query)
  Acan.objClean(query)
  list.value = await Service.find({query})
  loading.value = false
}
onMounted(find)
</script>
