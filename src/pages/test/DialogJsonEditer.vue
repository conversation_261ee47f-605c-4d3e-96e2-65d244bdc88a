<template>
  <q-dialog ref="dialogRef" :maximized="true" @hide="onDialogHide" @before-show="beforeDialogShow">
    <q-card class="q-dialog-plugin create-new-dialog">
      <div id="jsoneditor" class="col full-height full-width overflow-auto" ref="editorRef"></div>
    </q-card>
  </q-dialog>
</template>
<script setup>
import {ref, onMounted} from 'vue'
import {useDialogPluginComponent} from 'quasar'
defineEmits([...useDialogPluginComponent.emits])
const {dialogRef, onDialogHide, onDialogOK, onDialogCancel} = useDialogPluginComponent()

const props = defineProps(['data'])
const editorRef = ref(null)

let editor
onMounted(async () => {
  Aincludes('/v2/plugin/jsoneditor.min.css', 'css')
  await Aincludes('/v2/plugin/jsoneditor.min.js', 'js')
  const options = {mode: 'tree', search: true, modes: ['code', 'form', 'text', 'tree', 'view', 'preview']}
  editor = new JSONEditor(editorRef.value, options, undefined)
  editor.clear()
  editor.expandAll()
  editor.set(props.data)
})
</script>
