<template>
  <div class="col full-width column full-height overflow-hidden q-pa-md">
    <q-toolbar class="q-gutter-xs">
      <q-btn @click="$router.back()" label="back"></q-btn>
      <q-btn to="/test/" label="test"></q-btn>
      <q-space></q-space>
      <q-btn @click="findOne" icon="refresh"></q-btn>
    </q-toolbar>
    <div class="row col full-height full-width overflow-hidden" v-if="one">
      <div class="col full-height overflow-auto">
        <q-inner-loading :showing="loading" label-class="text-teal" label-style="font-size: 1.1em" />
        <q-list>
          <q-expansion-item default-opened>
            <template v-slot:header>
              <q-item-section avatar>
                <q-img :src="one.image" />
              </q-item-section>
              <q-item-section class="bg-teal-2">
                <q-item-label>{{ one._id }}</q-item-label>
                <q-item-label>{{ one.name }}</q-item-label>
                <q-item-label>{{ new Date(Acan.ObjectIdParse(one._id).time * 1000).toLocaleString() }}</q-item-label>
                <q-item-label>Start: {{ Acan.time('Y-m-d H:i:s', one.start) }} - {{ one.end ? Acan.time('Y-m-d H:i:s', one.end) : '' }}</q-item-label>
                <q-item-label>
                  <template v-for="k in ['type', 'mentoringType', 'cancel']" :key="k">
                    <q-chip v-if="!Acan.isEmpty(one[k])" :label="one[k]">
                      <q-tooltip>{{ k }}</q-tooltip>
                    </q-chip>
                  </template>
                </q-item-label>
                <q-item-label class="row">
                  <q-img :src="one.session.image" style="width: 8rem" />
                  <div class="q-pa-sm">
                    <div>Session: {{ one.session.name }}</div>
                    <q-chip :label="one.session.status">
                      <q-tooltip>session status</q-tooltip>
                    </q-chip>
                  </div>
                </q-item-label>
                <q-item-label class="row items-center">
                  Booker:
                  <OwnerBox :updatedAt="one.createdAt" :owner="one.bookerInfo" />
                  Servicer:
                  <OwnerBox :updatedAt="one.createdAt" :owner="one.servicerInfo" />
                </q-item-label>
              </q-item-section>
            </template>

            <q-item-section>
              <div class="row">
                <q-btn dense flat :to="`/test/service/packUser/${one.packUser}`" color="blue" label="packUser" target="_blank" no-caps></q-btn>
                <q-btn v-if="!one.cancel" dense flat color="blue" label="Cancel" @click="cancelFn" no-caps></q-btn>
                <!-- <q-btn dense flat :to="`/detail/${one.type}/${one._id}`" color="blue" label="old" target="_blank" no-caps></q-btn> -->
                <!-- <q-btn icon="delete" color="red" @click="delFn()"></q-btn> -->
              </div>
            </q-item-section>
          </q-expansion-item>
        </q-list>
      </div>
      <pre class="col-4 bg-amber-2 full-height overflow-auto">
One:
  {{ one }}
      </pre>
    </div>
  </div>
</template>
<script setup>
import {ref, onMounted} from 'vue'
import OwnerBox from 'components/detail/OwnerBox.vue'

import {useRoute} from 'vue-router'
const route = useRoute()

const one = ref(null)
const loading = ref(false)
const Service = App.service('service-booking')
// const queryData = ref({pid: {$exists: false}, type: 'session', $limit: 10, $skip: 0, $sort: {_id: -1}})
async function cancelFn() {
  const rs = await Service.patch('cancel', {_id: one.value._id})
  console.log(rs)
}
async function findOne() {
  loading.value = true
  one.value = null
  one.value = await Service.get(route.params.id)
  loading.value = false
}
onMounted(findOne)
</script>
