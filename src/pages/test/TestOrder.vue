<template>
  <div class="col full-width column full-height overflow-hidden q-pa-md">
    <q-toolbar class="q-gutter-xs">
      <q-btn @click="$router.back()" label="back"></q-btn>
      <q-btn to="/test" label="test"></q-btn>
      <q-space></q-space>
      <q-select
        label="type"
        style="width: 10rem"
        v-model="queryData.type"
        :options="['', ...typeOptions]"
        @update:model-value=";(queryData.$skip = 0), find()"></q-select>
      <q-checkbox label="isMy" v-model="isMe" @update:model-value="find()"></q-checkbox>
      <q-input label="name" v-model="queryData.name.$search" @change="find()"></q-input>
      <q-pagination
        class="flex-center q-mr-md"
        v-model="page"
        @click="find"
        color="teal"
        :max="Math.ceil(list.total / list.limit)"
        :max-pages="6"
        :boundary-numbers="true" />
      <AccountMenu />
    </q-toolbar>
    <div class="row col full-height full-width overflow-hidden">
      <div class="col full-height overflow-auto">
        <q-inner-loading :showing="loading" label-class="text-teal" label-style="font-size: 1.1em" />
        <q-list>
          <q-item v-for="(o, i) in list.data" :key="i">
            <q-item-section class="bg-teal-2">
              <OwnerBox :updatedAt="o.updatedAt" :owner="o.buyerInfo" />
              <q-item-label>{{ o._id }}</q-item-label>
              <q-item-label>Name: {{ o.no }}</q-item-label>
              <q-item-label>Status: {{ o.status }}</q-item-label>
              <q-item-label>CreatedAt: {{ new Date(o.createdAt).toLocaleString() }}</q-item-label>
              <q-item-label>
                <q-chip :label="o.type"><q-tooltip>type</q-tooltip></q-chip>
                <q-chip :label="`${o.price} $`"><q-tooltip>price</q-tooltip></q-chip>
                <q-chip :label="`${o.point}`"><q-tooltip>point</q-tooltip></q-chip>
                <q-chip v-if="!Acan.isEmpty(o.payMethod)" :label="o.payMethod"><q-tooltip>payMethod</q-tooltip></q-chip>
              </q-item-label>
              <q-item-label v-if="!Acan.isEmpty(o.refund)" class="bg-red-2">
                <div v-for="(r, ri) in o.refund" :key="ri">
                  refund: {{ r.createdAt }}
                  <q-chip :label="r.method"><q-tooltip>method</q-tooltip></q-chip>
                  <q-chip :label="r.status"><q-tooltip>status</q-tooltip></q-chip>
                  <q-chip :label="r.amount"><q-tooltip>amount</q-tooltip></q-chip>
                </div>
              </q-item-label>
            </q-item-section>
            <q-item-section>
              <div class="column q-gutter-sm">
                <q-card v-for="(link, li) in o.links" :key="li">
                  <div class="q-mb-sm">
                    <q-img :ratio="16 / 9" :src="hashToUrl(link.cover)" style="max-width: 10rem"></q-img>
                    {{ link.name }}
                    <q-chip :label="link.style" color="amber"><q-tooltip>style</q-tooltip></q-chip>
                    <q-chip :label="link.price"><q-tooltip>price</q-tooltip></q-chip>
                    <q-chip v-if="link.mode" :label="link.mode"><q-tooltip>mode</q-tooltip></q-chip>
                    <q-chip v-if="link.type" :label="link.type"><q-tooltip>type</q-tooltip></q-chip>
                    <template v-if="link.style === 'unit'">
                      <template v-if="link.newId">
                        <q-btn flat dense color="blue" :to="`/unit/my/${link.newId}`" label="my" target="_blank"></q-btn>
                        <q-btn
                          flat
                          icon="preview"
                          target="_blank"
                          :to="`/com/${link.mode.toLowerCase().includes('unit') ? 'unit' : 'task'}/edit/${link.newId}`"></q-btn>
                      </template>
                    </template>
                  </div>
                  <q-separator></q-separator>
                  <div>
                    <q-img :ratio="16 / 9" :src="hashToUrl(link.goods.cover || link.goods.image)" style="max-width: 10rem"></q-img>
                    goods: {{ link.goods.name }}
                    <template v-if="link.style === 'unit'">
                      <q-chip :label="link.goods.curriculum"><q-tooltip>curriculum</q-tooltip></q-chip>
                      <q-chip :label="link.goods.mode"><q-tooltip>mode</q-tooltip></q-chip>
                      <q-btn flat dense color="blue" :to="`/unit/${link.goods._id}`" label="lib" target="_blank"></q-btn>
                      <q-btn flat dense color="blue" :to="`/detail/lib/${link.goods._id}`" label="old lib" target="_blank"></q-btn>
                    </template>
                    <template v-else-if="['service', 'service_premium', 'service_substitute'].includes(link.style)">
                      <q-chip :label="link.goods.type"><q-tooltip>type</q-tooltip></q-chip>
                      <q-chip :label="link.goods.curriculum"><q-tooltip>curriculum</q-tooltip></q-chip>
                      <q-chip :label="link.goods.mentoringType"><q-tooltip>mentoringType</q-tooltip></q-chip>
                      <q-chip :label="link.goods.qualification"><q-tooltip>qualification</q-tooltip></q-chip>
                    </template>
                    <template v-else-if="link.style === 'session'">
                      <q-chip :label="link.goods.status" color="teal"><q-tooltip>status</q-tooltip></q-chip>
                      <q-chip :label="link.goods.type"><q-tooltip>type</q-tooltip></q-chip>
                      <q-chip :label="link.goods.task?.curriculum"><q-tooltip>task.curriculum</q-tooltip></q-chip>
                      <q-chip :label="link.goods.sessionType"><q-tooltip>sessionType</q-tooltip></q-chip>
                    </template>
                    <template v-else-if="link.style === 'premium_cloud'">
                      <q-chip :label="link.goods.unit.name"><q-tooltip>unit.name</q-tooltip></q-chip>
                      <q-chip :label="link.goods.type"><q-tooltip>type</q-tooltip></q-chip>
                      <q-chip :label="link.goods.curriculum"><q-tooltip>curriculum</q-tooltip></q-chip>
                    </template>
                  </div>
                </q-card>
              </div>
            </q-item-section>
            <q-item-section side>
              <q-item-label class="q-gutter-xs">
                <q-btn dense icon="delete" @click="rmFn(o)"></q-btn>
              </q-item-label>
            </q-item-section>
          </q-item>
        </q-list>
      </div>
      <pre class="col-4 bg-amber-2 full-height overflow-auto">
query:
  {{ queryData }}
list:
  {{ list.data }}
</pre
      >
    </div>
  </div>
</template>
<script setup>
import {ref, onMounted} from 'vue'
import AccountMenu from 'components/AccountMenu.vue'
import OwnerBox from 'components/detail/OwnerBox.vue'

import {useRoute} from 'vue-router'
window.route = useRoute()

const typeOptions = [
  'unit',
  'session_public',
  'session_self_study',
  'session_service_pack',
  'service_pack',
  'service_premium',
  'service_substitute',
  'premium_cloud',
  'prompt',
]

const Service = App.service('order')
const list = ref({}),
  page = ref(1),
  loading = ref(false)
const queryData = ref({name: {$search: ''}, $limit: 10, $skip: 0, $sort: {_id: -1}})

async function rmFn(o) {
  await Service.remove(o._id)
  find()
}
const isMe = ref(false)
async function find() {
  loading.value = true
  list.value = {}
  queryData.value.$skip = page.value * (list.value.limit ?? 20) - 20
  const query = Acan.clone(queryData.value)
  Acan.objClean(query)
  Acan.objClean(query)
  if (!isMe.value) query.$sys = 1
  list.value = await Service.find({query})
  loading.value = false
}
onMounted(find)
</script>
