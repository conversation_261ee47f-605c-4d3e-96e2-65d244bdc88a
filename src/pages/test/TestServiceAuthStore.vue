<template>
  <div class="col full-width column full-height overflow-hidden q-pa-md">
    <q-toolbar class="q-gutter-xs">
      <q-btn @click="$router.back()" label="back"></q-btn>
      <q-btn to="/test" label="test"></q-btn>
      <q-space></q-space>
      <AccountMenu />
    </q-toolbar>
    <div class="row col full-height full-width overflow-hidden">
      <div class="col full-height overflow-auto">
        <q-inner-loading :showing="loading" label-class="text-teal" label-style="font-size: 1.1em" />
        <template v-if="unit">
          {{ unit.curriculum }}, {{ unit.service }},
          {{ serviceAuth.isWorkshopAuth(unit) }}
        </template>
        <pre>{{ serviceAuth.workshopMap }}</pre>
      </div>
      <pre class="col-4 bg-amber-2 full-height overflow-auto"></pre>
    </div>
  </div>
</template>
<script setup>
import {ref, onMounted} from 'vue'
import AccountMenu from 'components/AccountMenu.vue'

import {serviceAuthStore} from 'stores/service-auth'
const serviceAuth = serviceAuthStore()

const loading = ref(false)
const unit = ref(null)

async function find() {
  loading.value = true
  unit.value = await App.service('unit').get('6805a1ac5e0c9469ec589e58')

  loading.value = false
}
onMounted(find)
</script>
