<template>
  <div class="col full-width column full-height overflow-hidden q-pa-md">
    <q-toolbar class="q-gutter-xs">
      <q-btn @click="$router.back()" label="back"></q-btn>
      <q-btn to="/test" label="test"></q-btn>
      <q-space></q-space>
      <AccountMenu />
    </q-toolbar>
    <div class="row col full-height full-width overflow-hidden">
      <div class="col full-height overflow-auto">
        <q-inner-loading :showing="loading" label-class="text-teal" label-style="font-size: 1.1em" />
        <q-item-section side>
          <!-- <q-img :ratio="16 / 9" :src="one.logo" style="min-width: 10rem"></q-img> -->
        </q-item-section>
        <q-item-section class="bg-teal-2">
          <q-item-label>{{ one._id }}</q-item-label>
          <q-item-label>Name: {{ one.name }}</q-item-label>
          <q-item-label>Unit.name: {{ one.snapshot?.unit?.name }}</q-item-label>
          <q-item-label>Use: {{ one.used }}/{{ one.total }}</q-item-label>
          <q-item-label>CreatedAt: {{ new Date(one.createdAt).toLocaleString() }}</q-item-label>
          <q-item-label>TaskIndex: {{ one.taskIndex }}</q-item-label>
          <q-item-label>Tasks: {{ one.tasks }}</q-item-label>
          <q-item-label v-if="one.snapshot">
            <template v-for="k in ['curriculum', 'type', 'subject']" :key="k">
              <q-chip :label="one.snapshot[k]">
                <q-tooltip>{{ k }}</q-tooltip>
              </q-chip>
            </template>
          </q-item-label>
          <OwnerBox :updatedAt="one.createdAt" :owner="one.owner" />
        </q-item-section>
        <div class="row">
          <!-- <q-item-label class="q-gutter-xs"> -->
          <q-btn flat label="view" :to="{path: `/test/service/auth/${one._id}`}" target="_blank"></q-btn>
          <q-btn flat label="auth view" :to="{path: `/unit/auth/${one._id}`, query: {}}" target="_blank"></q-btn>
          <q-btn flat icon="delete" @click="rmFn(o)"></q-btn>
          <!-- </q-item-label> -->
        </div>
        <div class="row" v-if="one.snapshot">
          <q-card v-for="(o, i) in one.snapshot.linkSnapshot" :key="i" class="col-6">
            <q-img :src="o.cover"></q-img>
            <q-card-section>
              {{ o.name }}
              {{ o.overview }}
            </q-card-section>
          </q-card>
        </div>
        <div class="row q-mt-md">
          <q-list class="col" bordered separator>
            <q-item-label class="q-pa-md text-bold">pack-user-data</q-item-label>
            <q-separator />
            <q-item clickable v-ripple v-for="(o, i) in packData" :key="i">
              <q-item-section side>{{ o.status }}</q-item-section>
              <q-item-section>{{ o.expired }}</q-item-section>
              <q-item-section side>{{ o.payMethod }}</q-item-section>
              <q-item-section side>
                <q-btn icon="delete" @click="dataDelFn(o)"></q-btn>
              </q-item-section>
            </q-item>
          </q-list>

          <q-list class="col" bordered separator>
            <q-item-label class="q-pa-md text-bold">pack-user-logs</q-item-label>
            <q-separator />
            <q-item clickable v-ripple v-for="(o, i) in packLogs" :key="i">
              <q-item-section>
                <q-item-label>{{ o.name }}</q-item-label>
                <q-item-label>{{ o.type }}</q-item-label>
                <q-item-label>{{ Acan.time('Y-m-d H:i', o.createdAt) }}</q-item-label>
                <q-item-label v-if="o.expireSoon" class="text-grey">{{ Acan.time('Y-m-d H:i', o.expireSoon) }}</q-item-label>
              </q-item-section>
              <q-item-section side>
                <q-item-label>
                  {{ o.times }}
                  <q-tooltip>times</q-tooltip>
                </q-item-label>
                <q-item-label>
                  {{ o.remaining }}
                  <q-tooltip>remaining</q-tooltip>
                </q-item-label>
              </q-item-section>
              <q-item-section side>
                <OwnerBox v-if="o.servicer?.avatar" :owner="o.servicer" />
                <q-tooltip>Servicer</q-tooltip>
              </q-item-section>
            </q-item>
          </q-list>
        </div>
      </div>
      <pre class="col-4 bg-amber-2 full-height overflow-auto">
one:
  {{ one }}
</pre
      >
    </div>
  </div>
</template>
<script setup>
import {ref, onMounted} from 'vue'
import AccountMenu from 'components/AccountMenu.vue'
import OwnerBox from 'components/detail/OwnerBox.vue'

import {useRoute} from 'vue-router'
const route = useRoute()

const Service = App.service('service-pack-user')
const one = ref({}),
  loading = ref(false)
const packData = ref([]),
  packLogs = ref([])
async function dataDelFn(o) {
  await App.service('service-pack-user-data').remove(o._id)
  await findData()
}
async function findData() {
  const rs = await App.service('service-pack-user-data').find({query: {packUser: one.value._id}})
  packData.value = rs.data
}
async function findLog() {
  const rs = await App.service('service-pack-user-logs').find({query: {packUser: one.value._id, $sort: {_id: -1}}})
  packLogs.value = rs.data
}
async function find() {
  loading.value = true
  one.value = await Service.get(route.params.id)
  await findData()
  await findLog()
  loading.value = false
}
onMounted(find)
</script>
