const GradesMap = {
  Foundation: {
    au: ['Foundation'],
    us: ['Grade K', 'Grade K2'],
    cbse: ['Nursery', 'Junior KG', 'Senior KG'],
    'ib-pyp': ['EYP Year 1', 'EYP Year 2', 'EYP Year 3'],
    'ib-myp': [],
    'ib-dp': [],
    'cam-prim': [],
    'cam-low': [],
    'cam-as': [],
    nz: ['Year 0'],
    igcse: [],
  },
  'Grade 1': {
    au: ['Year 1'],
    us: ['Grade 1'],
    cbse: ['Class 1'],
    'ib-pyp': ['Grade 1'],
    'ib-myp': [],
    'ib-dp': [],
    'cam-prim': ['Stage 1'],
    'cam-low': [],
    'cam-as': [],
    nz: ['Year 1', 'Year 2'],
    igcse: [],
  },
  'Grade 2': {
    au: ['Year 2'],
    us: ['Grade 2'],
    cbse: ['Class 2'],
    'ib-pyp': ['Grade 2'],
    'ib-myp': [],
    'ib-dp': [],
    'cam-prim': ['Stage 2'],
    'cam-low': [],
    'cam-as': [],
    nz: ['Year 2', 'Year 3'],
    igcse: [],
  },
  'Grade 3': {
    au: ['Year 3'],
    us: ['Grade 3'],
    cbse: ['Class 3'],
    'ib-pyp': ['Grade 3'],
    'ib-myp': [],
    'ib-dp': [],
    'cam-prim': ['Stage 3'],
    'cam-low': [],
    'cam-as': [],
    nz: ['Year 3', 'Year 4'],
    igcse: [],
  },
  'Grade 4': {
    au: ['Year 4'],
    us: ['Grade 4'],
    cbse: ['Class 4'],
    'ib-pyp': ['Grade 4'],
    'ib-myp': [],
    'ib-dp': [],
    'cam-prim': ['Stage 4'],
    'cam-low': [],
    'cam-as': [],
    nz: ['Year 4', 'Year 5'],
    igcse: [],
  },
  'Grade 5': {
    au: ['Year 5'],
    us: ['Grade 5'],
    cbse: ['Class 5'],
    'ib-pyp': ['Grade 5'],
    'ib-myp': [],
    'ib-dp': [],
    'cam-prim': ['Stage 5'],
    'cam-low': [],
    'cam-as': [],
    nz: ['Year 5', 'Year 6'],
    igcse: [],
  },
  'Grade 6': {
    au: ['Year 6'],
    us: ['Grade 6'],
    cbse: ['Class 6'],
    'ib-pyp': ['Grade 6'],
    'ib-myp': ['MYP Year 1'],
    'ib-dp': [],
    'cam-prim': ['Stage 6'],
    'cam-low': [],
    'cam-as': [],
    nz: ['Year 6', 'Year 7'],
    igcse: [],
  },
  'Grade 7': {
    au: ['Year 7'],
    us: ['Grade 7'],
    cbse: ['Class 7'],
    'ib-pyp': [],
    'ib-myp': ['MYP Year 2'],
    'ib-dp': [],
    'cam-prim': [],
    'cam-low': ['Stage 7'],
    'cam-as': [],
    nz: ['Year 7', 'Year 8'],
    igcse: [],
  },
  'Grade 8': {
    au: ['Year 8'],
    us: ['Grade 8'],
    cbse: ['Class 8'],
    'ib-pyp': [],
    'ib-myp': ['MYP Year 3'],
    'ib-dp': [],
    'cam-prim': [],
    'cam-low': ['Stage 8'],
    'cam-as': [],
    nz: ['Year 8', 'Year 9'],
    igcse: [],
  },
  'Grade 9': {
    au: ['Year 9'],
    us: ['Grade 9'],
    cbse: ['Class 9'],
    'ib-pyp': [],
    'ib-myp': ['MYP Year 4'],
    'ib-dp': [],
    'cam-prim': [],
    'cam-low': ['Stage 9'],
    'cam-as': [],
    nz: ['Year 9', 'Year 10'],
    igcse: ['Grade 9'],
  },
  'Grade 10': {
    au: ['Year 10', 'Year 10A'],
    us: ['Grade 10'],
    cbse: ['Class 10'],
    'ib-pyp': [],
    'ib-myp': ['MYP Year 5'],
    'ib-dp': [],
    'cam-prim': [],
    'cam-low': [],
    'cam-as': [],
    nz: ['Year 10', 'Year 11'],
    igcse: ['Grade 10'],
  },
  'Grade 11': {
    au: ['Year 11'],
    us: ['Grade 11'],
    cbse: ['Class 11'],
    'ib-pyp': [],
    'ib-myp': [],
    'ib-dp': ['DP Year 1'],
    'cam-prim': [],
    'cam-low': [],
    'cam-as': ['AS Level'],
    nz: ['Year 11', 'Year 12'],
    igcse: [],
  },
  'Grade 12': {
    au: ['Year 12'],
    us: ['Grade 12'],
    cbse: ['Class 12'],
    'ib-pyp': [],
    'ib-myp': [],
    'ib-dp': ['DP Year 2'],
    'cam-prim': [],
    'cam-low': [],
    'cam-as': ['A Level'],
    nz: ['Year 12', 'Year 13'],
    igcse: [],
  },
  Tertiary: {
    au: [],
    us: [],
    cbse: [],
    'ib-pyp': [],
    'ib-myp': [],
    'ib-dp': [],
    'cam-prim': [],
    'cam-low': [],
    'cam-as': [],
    nz: [],
    igcse: [],
  },
}

// import {GradeGroupMap} from 'src/boot/const'
// GradeGroupMap?.[gradeGroup]?.grades?.[curriculum] ?? []
// // ex:
// GradeGroupMap['preschool'].grades['ib-pyp']
const GradeGroupMap = {
  preschool: {
    label: 'Preschool',
    grades: {
      classcipe: ['Foundation'],
      pd: ['Foundation'],
      au: ['Foundation'],
      us: ['Kindergarten'],
      cbse: ['Nursery', 'Junior KG', 'Senior KG'],
      'ib-pyp': ['EYP Year 1', 'EYP Year 2', 'EYP Year 3'],
      'ib-myp': [],
      'ib-dp': [],
      'cam-prim': [],
      'cam-low': [],
      'cam-as': [],
      nz: ['Year 0'],
      igcse: [],
      // TODO: British national curriculum use `uk` by now
      uk: ['Early years'],
    },
  },
  earlyPrimary: {
    label: 'Early Primary',
    grades: {
      classcipe: ['Grade 1', 'Grade 2'],
      pd: ['Grade 1', 'Grade 2'],
      au: ['Year 1', 'Year 2'],
      us: ['Grade 1', 'Grade 2'],
      cbse: ['Class 1', 'Class 2'],
      'ib-pyp': ['Grade 1', 'Grade 2'],
      'ib-myp': [],
      'ib-dp': [],
      'cam-prim': ['Stage 1', 'Stage 2'],
      'cam-low': [],
      'cam-as': [],
      nz: ['Year 1', 'Year 2'],
      igcse: [],
      uk: ['Year 1', 'Year 2'],
    },
  },
  upperPrimary: {
    label: 'Upper Primary',
    grades: {
      classcipe: ['Grade 3', 'Grade 4', 'Grade 5'],
      pd: ['Grade 3', 'Grade 4', 'Grade 5'],
      au: ['Year 3', 'Year 4', 'Year 5'],
      us: ['Grade 3', 'Grade 4', 'Grade 5'],
      cbse: ['Class 3', 'Class 4', 'Class 5'],
      'ib-pyp': ['Grade 3', 'Grade 4', 'Grade 5', 'Grade 6'],
      'ib-myp': [],
      'ib-dp': [],
      'cam-prim': ['Stage 3', 'Stage 4', 'Stage 5', 'Stage 6'],
      'cam-low': [],
      'cam-as': [],
      nz: ['Year 3', 'Year 4', 'Year 5'],
      igcse: [],
      uk: ['Year 3', 'Year 4', 'Year 5'],
    },
  },
  intermediate: {
    label: 'Intermediate',
    grades: {
      classcipe: ['Grade 6', 'Grade 7', 'Grade 8'],
      pd: ['Grade 6', 'Grade 7', 'Grade 8'],
      au: ['Year 6', 'Year 7', 'Year 8'],
      us: ['Grade 6', 'Grade 7', 'Grade 8'],
      cbse: ['Class 6', 'Class 7', 'Class 8'],
      'ib-pyp': [],
      'ib-myp': ['MYP Year 1', 'MYP Year 2', 'MYP Year 3'],
      'ib-dp': [],
      'cam-prim': [],
      'cam-low': ['Stage 7', 'Stage 8', 'Stage 9'],
      'cam-as': [],
      nz: ['Year 6', 'Year 7', 'Year 8'],
      igcse: [],
      uk: ['Year 6', 'Year 7', 'Year 8'],
    },
  },
  lowerHighSchool: {
    label: 'Lower High School',
    grades: {
      classcipe: ['Grade 9', 'Grade 10'],
      pd: ['Grade 9', 'Grade 10'],
      au: ['Year 9', 'Year 10', 'Year 10A'],
      us: ['Grade 9', 'Grade 10'],
      cbse: ['Class 9', 'Class 10'],
      'ib-pyp': [],
      'ib-myp': ['MYP Year 4', 'MYP Year 5'],
      'ib-dp': [],
      'cam-prim': [],
      'cam-low': [],
      'cam-as': [],
      nz: ['Year 9', 'Year 10'],
      igcse: ['Grade 9', 'Grade 10'],
      uk: ['Year 9', 'Year 10'],
    },
  },
  upperHighSchool: {
    label: 'Upper High School',
    grades: {
      classcipe: ['Grade 11', 'Grade 12'],
      pd: ['Grade 11', 'Grade 12'],
      au: ['Year 11', 'Year 12'],
      us: ['Grade 11', 'Grade 12'],
      cbse: ['Class 11', 'Class 12'],
      'ib-pyp': [],
      'ib-myp': [],
      'ib-dp': ['DP Year 1', 'DP Year 2'],
      'cam-prim': [],
      'cam-low': [],
      'cam-as': ['AS Level', 'A Level'],
      nz: ['Year 11', 'Year 12', 'Year 13'],
      igcse: [],
      uk: ['Year 11', 'Year 12'],
    },
  },
  tertiary: {
    label: 'Tertiary',
    grades: {
      classcipe: ['Tertiary'],
      pd: ['Tertiary'],
      au: [],
      us: [],
      cbse: [],
      'ib-pyp': [],
      'ib-myp': [],
      'ib-dp': [],
      'cam-prim': [],
      'cam-low': [],
      'cam-as': [],
      nz: [],
      igcse: [],
    },
  },
}
const GradeGroupKeys = Object.keys(GradeGroupMap)

// Students
const PlatformGrades = Object.keys(GradesMap)

// Educators
const EducatorGrades = ['Introductory level', 'Intermediate level', 'Advanced level']

const CurriculumGrades = {
  au: [],
  us: [],
  'ib-pyp': [],
  'ib-myp': [],
  'ib-dp': [],
  cbse: [],
  'cam-prim': [],
  'cam-low': [],
  'cam-as': [],
  igcse: [],
  nz: [],
  pd: EducatorGrades,
}
const CurriculumGradesMap = {}
for (const grade of PlatformGrades) {
  for (const curr of Object.keys(GradesMap[grade])) {
    if (!CurriculumGradesMap[curr]) CurriculumGradesMap[curr] = {}
    for (const cg of GradesMap[grade][curr]) {
      if (!CurriculumGradesMap[curr][cg]) CurriculumGradesMap[curr][cg] = [grade]
      else CurriculumGradesMap[curr][cg].push(grade)
      if (!CurriculumGrades[curr].includes(cg)) CurriculumGrades[curr].push(cg)
    }
  }
}

export default {PlatformGrades, EducatorGrades, CurriculumGrades, GradesMap, CurriculumGradesMap, GradeGroupMap, GradeGroupKeys}
