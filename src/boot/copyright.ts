const copyrightNameMap = {
  'cam-prim': `Copyright Cambridge University Press & Assessment`,
  'cam-low': `Copyright Cambridge University Press & Assessment`,
  igcse: `Copyright Cambridge University Press & Assessment`,
  'ib-pyp': `Copyright International Baccalaureate Organization`,
  'ib-myp': `Copyright International Baccalaureate Organization`,
  'ib-dp': `Copyright International Baccalaureate Organization`,
  au: `Copyrights Australian Curriculum, Assessment and Reporting Authority (ACARA)`,
  nz: `Copyrights New Zealand Ministry of Education`,
  cbse: `Copyright Developed By CIET, NCERT | Hosted By NIC`,
  default: `Copyright Classcipe`,
  us: `Copyright National Governors Association Center for Best Practices and Council of Chief State School Officers. All rights reserved`,
}
const countryNameMap = {
  au: `Australia`,
  cbse: `India`,
  us: `USA`,
}
export function generateCopyrightText(code = 'default', subtitle = '') {
  // 'The copyright of this data belongs to the government education sector of " subtitle, country name" and can only be used for teaching, scholarship, education and research.'
  const countryName = countryNameMap?.[code as keyof typeof countryNameMap] || ''
  if (countryName && subtitle) {
    return `"The copyright of this data belongs to the government education sector of "${subtitle}, ${countryName}" and can only be used for teaching, scholarship, education and research."`
  }
  return copyrightNameMap[code as keyof typeof copyrightNameMap]
}
