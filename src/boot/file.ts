interface File {
  mime?: string
}

export const DEFAULT_FILE_TYPE = 'image/*,video/*,audio/*,.pdf'

export const IMAGE_EXTENSION = [
  'jpg',
  'jpeg',
  'png',
  'gif',
  'bmp',
  'svg',
  'svgz',
  'tif',
  'tiff',
  'ico',
  'webp',
  'psd',
  'raw',
  'apng',
  'avif',
  'jfif',
  'pjpeg',
  'pjp',
]

export const VIDEO_EXTENSION = [
  'webm',
  'mkv',
  'flv',
  'vob',
  'ogv',
  'ogg',
  'drc',
  'gifv',
  'mng',
  'avi',
  'mov',
  'qt',
  'wmv',
  'yuv',
  'rm',
  'rmvb',
  'asf',
  'amv',
  'mp4',
  'm4p',
  'm4v',
]

export const AUDIO_EXTENSION = [
  '3gp',
  'aa',
  'aac',
  'act',
  'aiff',
  'alac',
  'amr',
  'ape',
  'au',
  'awb',
  'dss',
  'dvf',
  'flac',
  'gsm',
  'iklax',
  'ivs',
  'm4a',
  'm4b',
  'mmf',
  'movpkg',
  'mp3',
  'mpc',
  '.msv',
  'nmf',
  'raw',
  'rf64',
  'voc',
  'vox',
  'wav',
  'wv',
]
export function getFileIcon(file: File) {
  const mime = file?.mime ?? ''
  if (mime.includes('image')) return 'o_image'
  else if (mime.includes('video')) return 'o_video_file'
  else if (mime.includes('audio')) return 'o_audio_file'
  else if (mime.includes('pdf')) return 'o_picture_as_pdf'
  else 'attachment'
}
