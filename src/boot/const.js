import grades from './grades'

export const UnitModeMap = {
  task: {label: 'Task', color: 'amber', icon: 'task_alt'},
  unit: {label: 'Unit Plan', color: 'secondary', icon: 'category'},
  pdTask: {label: 'Service Session', color: 'amber', icon: 'task_alt'},
  pdUnit: {label: 'Service Module', color: 'secondary', icon: 'category'},
  video: {label: 'Video', color: 'red', icon: 'video_library'},
  tool: {label: ' Assessment Tool', color: 'blue', icon: 'grading'},
}
export const UnitMode = Object.keys(UnitModeMap)

export {generateCopyrightText} from './copyright'
export {
  QUALIFICATION_TYPE,
  QUALIFICATION_OPTIONS,
  SERVICE_ROLE_OPTIONS,
  SERVICE_ROLE_TYPE,
  VERIFICATION_MATERIAL_OPTIONS,
  TEACHER_TRAINING_SUBJECT_VERIFICATION_MATERIAL_OPTIONS,
  OVERSEAS_STUDY_VERIFICATION_MATERIAL_OPTIONS,
  ACADEMIC_PLANNING_VERIFICATION_MATERIAL_OPTIONS,
  PERSONAL_STATEMENT_VERIFICATION_MATERIAL_OPTIONS,
  INTEREST_VERIFICATION_MATERIAL_OPTIONS,
  STEAM_VERIFICATION_MATERIAL_OPTIONS,
  PSYCHOLOGY_VERIFICATION_MATERIAL_OPTIONS,
  STANDARDIZED_ENGLISH_PREP_VERIFICATION_MATERIAL_OPTIONS,
  ESSAY_VERIFICATION_MATERIAL_OPTIONS,
  AMBASSADOR_VERIFICATION_MATERIAL_OPTIONS,
  LEARNING_STAGE_OPTION,
  LEARNING_STAGE_MAP,
  LEARNING_STYLE_OPTION,
  LEARNING_STYLE_MAP,
  OTHER_LEARNING_STYLE_OPTION,
  OTHER_LEARNING_STYLE_MAP,
  hasVideo,
  getAttachmentType,
  getServiceRoleOptions,
} from './teacher-verification.js'
export {DEFAULT_FILE_TYPE, IMAGE_EXTENSION, VIDEO_EXTENSION, AUDIO_EXTENSION, getFileIcon} from './file'

export const {PlatformGrades, EducatorGrades, CurriculumGrades, GradesMap, CurriculumGradesMap, GradeGroupMap, GradeGroupKeys} = grades
export const PATH_PREFIX = '/v2'
export const MMDD_FORMAT = 'MM/DD HH:mm'
export const TIME_FORMAT = 'MM/DD/YYYY HH:mm'
export const DATE_FORMAT = 'YYYY-MM-DD'
export const TIME_FORMAT_NZ = 'DD/MM/YYYY HH:mm'
export const DATE_FORMAT_NZ = 'DD/MM/YYYY'
export const DATE_PICKER_FORMAT = 'DD/MM/YYYY'
export const BOOK_NOR_TIME_LEAD = '3' //NORMAL
export const BOOK_CON_TIME_LEAD = '3' //CONSULTANT: INTERVIEW or CARER

export const mailReg = Acan.regexp.mail
// export const phoneReg = /^[\+]?[(]?[0-9]{3}[)]?[-\s\.]?[0-9]{3}[-\s\.]?[0-9]{0,10}$/im
export const phoneReg = /^[+]?[(]?[0-9]{3}[)]?[-\s.]?[0-9]{3}[-\s.]?[0-9]{0,10}$/im
export const passwordReg = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{8,20}$/
export const passwordRegLabel = '8-20 characters, includes letter & numbers'

export const SubjectOptions = [
  'Life skills',
  'Science',
  'Tech&Computer Science',
  'Professional Development',
  'Social Studies',
  'Math',
  'Fine Arts',
  'Financial Literacy',
  'Health',
  'English Language Arts',
  'World Languages',
  'College Test Prep',
  'Engineering',
  'Others',
]
// allow multiple
export const SteamSubjectOptions = ['Science', 'Tech&Computer Science', 'Math', 'Fine Arts', 'English Language Arts', 'Engineering']

// same as task type
// export const VideoOptions = ['Formative Assessment', 'Summative Assessment', 'Teacher/Learning Activity', 'Internal Assessment']

export const Grades = PlatformGrades
// export const Grades = [
//   'Kindergarten',
//   '1st',
//   '2nd',
//   '3rd',
//   '4th',
//   '5th',
//   '6th',
//   '7th',
//   '8th',
//   '9th',
//   '10th',
//   '11th',
//   '12th',
//   '13th',
//   'Higher Ed',
//   'PD',
//   'Other',
// ]

export const TypeMap = {
  'all-type': undefined,
  topic: 1,
  unit: 2,
  material: 3,
  task: 4,
  lesson: 5,
  evaluation: 6,
  classSessionEvaluation: 7,
  video: 8,
  pd: 9,
}

export const TypeToModel = {
  2: 'unit',
  4: 'task',
  9: 'content',
}

export const StateMap = {
  CONTENTS: '1',
  LIBARARY: '2',
  SCHEDULED: '3',
  PUBLISHED: '4',
  TOOL: '9',
}

export const ModeMap = {
  TASK: '1',
  UNIT: '2',
  PDTASK: '3',
  PDUNIT: '4',
  TOOL: '5',
  ALLTASK: '6',
  ALLUNIT: '7',
  CLASSCIPE: '8',
  VIDEO: '9',
}

export const ModeMapFlip = {
  1: 'task',
  2: 'unit',
  3: 'pdTask',
  4: 'pdUnit',
  5: 'tool',
  6: ['task', 'pdTask'],
  7: ['unit', 'pdUnit'],
  9: 'video',
}

export const TypeList = Object.keys(TypeMap)

export const TaskTypes = [
  {label: 'FA', title: 'Formative Assessment', color: 'teal'},
  {label: 'SA', title: 'Summative Assessment', color: 'red'},
  {label: 'Activity', title: 'Teaching/Learning Activity', color: 'blue'},
  {label: 'IA', title: 'Internal Assessment', color: 'orange'},
]

export const SessionTypes = {
  live: {label: 'Live', color: 'teal'},
  student: {label: 'Self-study', color: 'blue'},
}
export const TaskSessionTypes = [
  {label: 'live', title: 'Live', color: 'teal'},
  {label: 'student', title: 'Self-study', color: 'blue'},
]

export const UnitTypes = [
  {label: 'Single', title: 'Single-subject Unit', color: 'teal'},
  // {label: 'Integrated', title: 'Integrated Unit', color: 'red'},
  {label: 'UOI', title: 'UOI', color: 'amber'},
  {label: 'IDU', title: 'IDU', color: 'blue'},
]

export const UnitProjectTypes = [
  {label: 'Yes', title: 'Yes', color: 'teal'},
  {label: 'No', title: 'No', color: 'amber'},
]

export const UnitStepTypes = [
  {label: 'Basic', value: 'basic'},
  {label: 'Inquiry', value: 'inquiry'},
  {label: 'Applying', value: 'applying'},
]

export const TaskTypeColors = {}
TaskTypes.map((v) => {
  TaskTypeColors[v.label] = v.color
})

export const UnitTypeColors = {}
UnitTypes.map((v) => {
  UnitTypeColors[v.label] = v.color
})

export const UnitTypeTitles = {}
UnitTypes.map((v) => {
  UnitTypeTitles[v.label] = v.title
})

export const BloomLabels = ['Remember', 'Understand', 'Apply', 'Analyze', 'Evaluate', 'Create']
export const BloomColors = ['blue', 'green', 'lime', 'amber', 'orange', 'deep-orange']
export const KnowledgeLabels = ['Factual', 'Conceptual', 'Procedural', 'Megacognitave']

export const SessionStatus = {live: 'Live', student: 'Student-paced', close: 'Close'}
export const SchoolStatus = {0: 'Create', 1: 'Trial', 2: 'Paid', '-1': 'Expired'}
export const SchoolStatusOptions = []
for (const key in SchoolStatus) {
  SchoolStatusOptions.push({label: SchoolStatus[key], value: parseInt(key)})
}
export const SchoolTeacherStatus = {
  0: 'inactive',
  1: 'pending',
  2: 'success',
}

export const ContentsType = {
  task: {service: false, content: true, video: false, task: true, unit: false, tool: false, value: 'task', label: 'Task', color: 'amber', icon: 'task_alt'},
  unit: {
    service: false,
    content: true,
    video: false,
    task: false,
    unit: true,
    tool: false,
    value: 'unit',
    label: 'Unit Plan',
    color: 'secondary',
    icon: 'category',
  },
  video: {
    service: false,
    content: true,
    video: true,
    task: false,
    unit: false,
    tool: false,
    value: 'video',
    label: 'Video',
    color: 'red',
    icon: 'video_library',
  },
  pdTask: {
    service: true,
    content: true,
    video: false,
    task: true,
    unit: false,
    tool: false,
    value: 'pdTask',
    label: 'Service Session',
    color: 'amber',
    icon: 'task_alt',
  },
  pdUnit: {
    service: true,
    content: true,
    video: false,
    task: false,
    unit: true,
    tool: false,
    value: 'pdUnit',
    label: 'Service Module',
    color: 'secondary',
    icon: 'category',
  },
  tool: {
    service: false,
    content: true,
    video: false,
    task: false,
    unit: false,
    tool: true,
    value: 'tool',
    label: 'Assessment Tool',
    color: 'blue',
    icon: 'grading',
  },
  // video: {service: false, content: true, task: false, unit: false, tool: true, value: 'video', label: 'Video', color: 'blue', icon: 'grading'},

  selfStudy: {
    service: false,
    content: false,
    educator: false,
    self: true,
    school: false,
    session: true,
    course: false,
    enroll: true,
    tool: false,
    type: 'workshop',
    value: 'selfStudy',
    label: 'Self Study',
    color: 'amber',
    icon: 'task_alt',
    hidden: true,
  },
  videoSession: {
    service: false,
    content: false,
    educator: false,
    video: true,
    school: false,
    session: true,
    class: true,
    course: false,
    enroll: false,
    tool: false,
    type: 'workshop',
    value: 'videoSession',
    label: 'Video session',
    color: 'red',
    icon: 'video_library',
    hidden: true,
  },
  toolSession: {
    service: false,
    content: false,
    educator: false,
    public: false,
    school: false,
    session: false,
    course: false,
    enroll: false,
    tool: true,
    value: 'toolSession',
    label: 'Assessment Tool',
    color: 'blue',
    icon: 'grading',
    hidden: true,
  },
  taskWorkshop: {
    service: false,
    content: false,
    educator: false,
    public: true,
    school: false,
    session: true,
    course: false,
    enroll: true,
    tool: false,
    type: 'workshop',
    value: 'taskWorkshop',
    label: 'Task',
    color: 'amber',
    icon: 'task_alt',
    hidden: true,
  },
  unitCourses: {
    service: false,
    content: false,
    educator: false,
    public: true,
    school: false,
    session: false,
    course: true,
    enroll: true,
    tool: false,
    type: 'workshop',
    value: 'unitCourses',
    label: 'Unit Plan',
    color: 'secondary',
    icon: 'category',
    hidden: true,
  },
  studentWorkshop: {
    service: true,
    content: false,
    educator: false,
    public: true,
    school: false,
    session: true,
    course: false,
    enroll: true,
    tool: false,
    type: 'workshop',
    value: 'studentWorkshop',
    label: 'Service Session',
    color: 'amber',
    icon: 'task_alt',
    hidden: true,
  },
  studentCourses: {
    service: true,
    content: false,
    educator: false,
    public: true,
    school: false,
    session: false,
    course: true,
    enroll: true,
    tool: false,
    type: 'workshop',
    value: 'studentCourses',
    label: 'Service Module',
    color: 'secondary',
    icon: 'category',
    hidden: true,
  },
  studentTool: {
    service: false,
    content: false,
    educator: false,
    public: true,
    school: false,
    session: false,
    course: false,
    enroll: true,
    tool: true,
    type: 'workshop',
    value: 'studentTool',
    label: 'Assessment Tool',
    color: 'blue',
    icon: 'grading',
    hidden: true,
  },
  taskSchoolWorkshop: {
    service: false,
    content: false,
    educator: false,
    public: true,
    school: true,
    session: true,
    course: false,
    enroll: true,
    tool: false,
    type: 'workshop',
    value: 'taskSchoolWorkshop',
    label: 'Task',
    color: 'amber',
    icon: 'task_alt',
    hidden: true,
  },
  unitSchoolCourses: {
    service: false,
    content: false,
    educator: false,
    public: true,
    school: true,
    session: false,
    course: true,
    enroll: true,
    tool: false,
    type: 'workshop',
    value: 'unitSchoolCourses',
    label: 'Unit Plan',
    color: 'secondary',
    icon: 'category',
    hidden: true,
  },
  pdSchoolStudentWorkshop: {
    service: true,
    content: false,
    educator: false,
    public: true,
    school: true,
    session: true,
    course: false,
    enroll: true,
    tool: false,
    type: 'workshop',
    value: 'pdSchoolStudentWorkshop',
    label: 'Service Session',
    color: 'amber',
    icon: 'task_alt',
    hidden: true,
  },
  pdSchoolStudentCourses: {
    service: true,
    content: false,
    educator: false,
    public: true,
    school: true,
    session: false,
    course: true,
    enroll: true,
    tool: false,
    type: 'workshop',
    value: 'pdSchoolStudentCourses',
    label: 'Service Module',
    color: 'secondary',
    icon: 'category',
    hidden: true,
  },
  studentToolGrade: {
    service: false,
    content: false,
    educator: false,
    public: true,
    school: true,
    session: false,
    course: false,
    enroll: true,
    tool: true,
    type: 'workshop',
    value: 'studentToolGrade',
    label: 'Assessment Tool',
    color: 'blue',
    icon: 'grading',
    hidden: true,
  },
  session: {
    service: false,
    content: false,
    educator: false,
    public: false,
    session: true,
    course: false,
    enroll: false,
    class: true,
    tool: false,
    type: 'teaching',
    value: 'session',
    label: 'Task',
    color: 'amber',
    icon: 'task_alt',
    hidden: true,
  },
  courses: {
    service: false,
    content: false,
    educator: false,
    public: false,
    session: false,
    course: true,
    tool: false,
    enroll: false,
    class: true,
    type: 'teaching',
    value: 'courses',
    label: 'Unit Plan',
    color: 'secondary',
    icon: 'category',
    hidden: true,
  },
  pdClassSession: {
    service: true,
    content: false,
    educator: false,
    public: false,
    session: true,
    course: false,
    enroll: false,
    class: true,
    tool: false,
    type: 'teaching',
    value: 'pdClassSession',
    label: 'Service Session',
    color: 'amber',
    icon: 'task_alt',
    hidden: true,
  },
  pdClassCourses: {
    service: true,
    content: false,
    educator: false,
    public: false,
    session: false,
    course: true,
    enroll: false,
    class: true,
    tool: false,
    type: 'teaching',
    value: 'pdClassCourses',
    label: 'Service Module',
    color: 'secondary',
    icon: 'category',
    hidden: true,
  },
  studentToolSelect: {
    service: false,
    content: false,
    educator: false,
    public: false,
    session: false,
    course: false,
    enroll: false,
    class: true,
    tool: true,
    type: 'teaching',
    value: 'studentToolSelect',
    label: 'Assessment Tool',
    color: 'blue',
    icon: 'grading',
    hidden: true,
  },
  workshop: {
    service: true,
    content: false,
    educator: true,
    public: true,
    school: false,
    session: true,
    course: false,
    enroll: true,
    tool: false,
    type: 'workshop',
    value: 'workshop',
    label: 'Service Session',
    color: 'amber',
    icon: 'task_alt',
    hidden: true,
  },
  pdCourses: {
    service: true,
    content: false,
    educator: true,
    public: true,
    school: false,
    session: false,
    course: true,
    enroll: true,
    tool: false,
    type: 'workshop',
    value: 'pdCourses',
    label: 'Service Module',
    color: 'secondary',
    icon: 'category',
    hidden: true,
  },
  teacherTool: {
    service: false,
    content: false,
    educator: true,
    public: true,
    session: false,
    course: false,
    enroll: true,
    tool: true,
    type: 'workshop',
    value: 'teacherTool',
    label: 'Assessment Tool',
    color: 'blue',
    icon: 'grading',
    hidden: true,
  },
  pdSchoolTeacherWorkshop: {
    service: true,
    content: false,
    educator: true,
    public: true,
    school: true,
    session: true,
    course: false,
    enroll: true,
    tool: false,
    type: 'workshop',
    value: 'pdSchoolTeacherWorkshop',
    label: 'Service Session',
    color: 'amber',
    icon: 'task_alt',
    hidden: true,
  },
  pdSchoolTeacherCourses: {
    service: true,
    content: false,
    educator: true,
    public: true,
    school: true,
    session: false,
    course: true,
    enroll: true,
    tool: false,
    type: 'workshop',
    value: 'pdSchoolTeacherCourses',
    label: 'Service Module',
    color: 'secondary',
    icon: 'category',
    hidden: true,
  },
  teacherToolSchool: {
    service: false,
    content: false,
    educator: true,
    public: true,
    school: true,
    session: false,
    course: false,
    enroll: true,
    tool: true,
    type: 'workshop',
    value: 'teacherToolSchool',
    label: 'Assessment Tool',
    color: 'blue',
    icon: 'grading',
    hidden: true,
  },
  pdSchoolWorkshop: {
    service: true,
    content: false,
    educator: true,
    public: false,
    school: true,
    session: true,
    course: false,
    enroll: false,
    colleague: true,
    tool: false,
    type: 'workshop',
    value: 'pdSchoolWorkshop',
    label: 'Service Session',
    color: 'amber',
    icon: 'task_alt',
    hidden: true,
  },
  pdSchoolCourses: {
    service: true,
    content: false,
    educator: true,
    public: false,
    school: true,
    session: false,
    course: true,
    enroll: false,
    colleague: true,
    tool: false,
    type: 'workshop',
    value: 'pdSchoolCourses',
    label: 'Service Module',
    color: 'secondary',
    icon: 'category',
    hidden: true,
  },
  teacherToolSelect: {
    service: false,
    content: false,
    educator: true,
    public: false,
    school: true,
    session: false,
    course: false,
    enroll: false,
    colleague: true,
    tool: true,
    type: 'workshop',
    value: 'teacherToolSelect',
    label: 'Assessment Tool',
    color: 'blue',
    icon: 'grading',
    hidden: true,
  },
  bookingTask: {
    booking: true,
    service: false,
    content: false,
    educator: true,
    public: false,
    school: false,
    session: true,
    course: false,
    enroll: false,
    tool: false,
    type: 'booking',
    value: 'bookingTask',
    label: 'Booking Task',
    color: 'amber',
    icon: 'task_alt',
    hidden: true,
  },
  bookingPdTask: {
    booking: true,
    service: true,
    content: false,
    educator: true,
    public: false,
    school: false,
    session: true,
    course: false,
    enroll: false,
    tool: false,
    type: 'booking',
    value: 'bookingPdTask',
    label: 'Booking Service Session',
    color: 'amber',
    icon: 'task_alt',
    hidden: true,
  },
  bookingStuTask: {
    booking: true,
    service: false,
    content: false,
    educator: false,
    public: false,
    school: false,
    session: true,
    course: false,
    enroll: false,
    tool: false,
    type: 'booking',
    value: 'bookingStuTask',
    label: 'Booking Student Task',
    color: 'amber',
    icon: 'task_alt',
    hidden: true,
  },
  bookingStuPdTask: {
    booking: true,
    service: true,
    content: false,
    educator: false,
    public: false,
    school: false,
    session: true,
    course: false,
    enroll: false,
    tool: false,
    type: 'booking',
    value: 'bookingStuPdTask',
    label: 'Booking Student Service Session',
    color: 'amber',
    icon: 'task_alt',
    hidden: true,
  },
  jobSeekerTask: {
    seeker: true,
    service: false,
    content: false,
    educator: true,
    public: false,
    school: false,
    session: true,
    course: false,
    enroll: false,
    tool: false,
    type: 'seeker',
    value: 'jobSeekerTask',
    label: 'Job Seeker session',
    color: 'amber',
    icon: 'task_alt',
    hidden: true,
  },
  jobSeekerPdTask: {
    seeker: true,
    service: true,
    content: false,
    educator: true,
    public: false,
    school: false,
    session: true,
    course: false,
    enroll: false,
    tool: false,
    type: 'seeker',
    value: 'jobSeekerPdTask',
    label: 'Job Seeker Service Session',
    color: 'amber',
    icon: 'task_alt',
    hidden: true,
  },
}

export const ServiceType = {
  workshop: 'Workshop',
  teaching: 'Class session',
  mentoring: 'Mentoring',
  substitute: 'Substitute',
  correcting: 'Correcting',
  content: 'Content',
}

export const ServiceTypeList = [
  {label: 'Academic', value: 'academic', mentoring: true, correcting: true, substitute: true, admin: true},
  {label: 'Essay', value: 'essay', mentoring: true, correcting: true, substitute: false},
  {label: 'Overseas Study', value: 'overseasStudy', mentoring: true, substitute: false},
  {label: 'STEAM', value: 'steam', mentoring: true, substitute: true},
  {label: 'Academic planning', value: 'academicPlanning', mentoring: true, substitute: false},
  {label: 'Interest & career practice', value: 'interest', mentoring: true, substitute: false},
  {label: 'Application and visa assistance', value: 'personalStatement', mentoring: true, substitute: false},
  {label: 'Psychology', value: 'psychology', mentoring: true, substitute: false},
  {label: 'Standardized English Prep', value: 'standardizedEnglishPrep', mentoring: true, substitute: false},
  {label: 'Teacher training', value: 'teacherTraining', educators: true, mentoring: true, correcting: true, substitute: false},
  {label: 'Teacher training-subject', value: 'teacherTrainingSubject', educators: true, mentoring: true, correcting: true, substitute: false},
]
export const ServiceTypeWithDoubleTopic = ['teacherTrainingSubject', 'essay']
export const ServiceTypeWithSingleTopic = [
  'teacherTraining',
  'overseasStudy',
  'steam', // the topics of 'steam' is dicided by system, it is also single layer
  'psychology',
  'standardizedEnglishPrep',
  'personalStatement',
  'academicPlanning',
  'interest',
]

export const categoryMap = {mentoring: 'Mentoring', substitute: 'Substitute service', correcting: 'Correcting service', premium: 'Premium content'}
// export const categoryMap2 = {mentoring: 'service', correcting: 'correcting_service', premium: 'Premium content'}
export const mentoringTypeMap = ServiceTypeList.reduce((acc, cur) => {
  acc[cur.value] = cur.label
  return acc
}, {})

export const SessionType = {
  // > for school classroom, need select students
  courses: {from: 'unit', public: false},
  session: {from: 'task', public: false},
  pdClassCourses: {from: 'pdUnit', public: false},
  pdClassSession: {from: 'pdTask', public: false},
  tool: {from: 'tool'},
  // > for school students public, need register (only for internal school students)
  unitSchoolCourses: {from: 'unit', public: true},
  taskSchoolWorkshop: {from: 'task', public: true},
  pdSchoolStudentCourses: {from: 'pdUnit', public: true},
  pdSchoolStudentWorkshop: {from: 'pdTask', public: true},
  studentTool: {from: 'tool'},
  // > for school educators public, need register (only for internal school teachers)
  pdSchoolTeacherCourses: {from: 'pdUnit', public: true, teacher: true},
  pdSchoolTeacherWorkshop: {from: 'pdTask', public: true, teacher: true},
  schoolEducatorTool: {from: 'tool'},
  // > for school educators, need select internal school teachers
  pdSchoolCourses: {from: 'pdUnit', public: false, teacher: true},
  pdSchoolWorkshop: {from: 'pdTask', public: false, teacher: true},
  educatorTool: {from: 'tool'},
  // > for students public, need register (only for students)
  unitCourses: {from: 'unit', public: true},
  taskWorkshop: {from: 'task', public: true},
  studentCourses: {from: 'pdUnit', public: false},
  studentWorkshop: {from: 'pdTask', public: false},
  selfStudy: {from: 'task', public: false}, // to student Self study center
  // 'videoSession',
  // > for educators public, need register (only for teachers)
  workshop: {from: 'pdTask', public: true, teacher: true},
  pdCourses: {from: 'pdUnit', public: true, teacher: true},

  // > for educators booking
  bookingTask: {from: 'task', booking: true},
  bookingPdTask: {from: 'pdTask', booking: true},
  // > for Student booking
  bookingStuTask: {from: 'task', booking: true},
  bookingStuPdTask: {from: 'pdTask', booking: true},
  // > 用于 education consultant - interview for teacher verification
  jobSeekerTask: {from: 'task', booking: true},
  jobSeekerPdTask: {from: 'pdTask', booking: true},
}

export const OfficialOwner = {nickname: 'Classcipe', avatar: '/v2/img/logo2.png'}

export const OutlineTypes = {
  outline: 'topic',
  assess: 'standard',
  skills: 'standard',
  pd: 'standard',
  atl: 'atl',
}
export const OutlineTypesTitle = {
  outline: 'Topics',
  assess: 'Standard',
  skills: '21st Century Skills',
  goal: 'Learning Goals',
}

export const QuestionTypes = {
  text: {label: 'Text', title: 'Text Question', icon: 'text_fields', color: 'red-7'},
  choice: {label: 'Multiple Choice', title: 'Choice Question', icon: 'done_all', color: 'yellow-7'},
  media: {label: 'Multimedia', title: 'Media Question', icon: 'local_see', color: 'blue-7'},
  comment: {label: 'Comment', title: 'Comment Question', icon: 'chat', color: 'grey-10'},
  draw: {label: 'Draw It', title: 'Draw Question', icon: 'draw', color: 'pink-4'},
  website: {label: 'Website', title: 'Website Question', icon: 'language', color: 'teal-7'},
}

export const taskCategoryColors = ['green', 'indigo', 'blue-11', 'red-6', 'pink-5', 'black', 'grey-5', 'orange-6', 'yellow', 'blue-grey']

export const pubCurriculumList = {
  'ib-pyp': 'IB-PYP',
  'ib-myp': 'IB-MYP',
  'ib-dp': 'IB-DP',
  'cam-prim': 'Cambridge-Primary',
  'cam-low': 'Cambridge Lower secondary',
  'cam-as': 'Cambridge AS & A level',
  us: 'US curriculum',
  au: 'AU curriculum',
  nz: 'NZ National',
  cbse: 'CBSE',
  igcse: 'IGCSE',
  others: 'Others',
}

// TODO
export const pubCurriculumToCountryNameList = {
  'ib-pyp': 'IB-PYP',
  'ib-myp': 'IB-MYP',
  'ib-dp': 'IB-DP',
  'cam-prim': 'Cambridge-Primary',
  'cam-low': 'Cambridge Lower secondary',
  'cam-as': 'Cambridge AS & A level',
  au: 'AU curriculum',
  nz: 'New Zealand',
  cbse: 'CBSE',
  igcse: 'IGCSE',
  others: 'Others',
}

export const actionTypes = [
  {code: 'comment', icon: 'o_textsms'},
  //{code: 'comment', icon: 'o_privacy_tip'},
  //{code: 'comment', icon: 'o_collections_bookmark'},
  //{code: 'refl', icon: 'o_published_with_changes'},
]
export const SatisfiedOptions = [
  {label: 'Patient', value: 'patient'},
  {label: 'Addresses disruptions', value: 'addresses_disruptions'},
  {label: 'Encouraging', value: 'encouraging'},
  {label: 'Effectively allocates time', value: 'effectively_allocates_time'},
  {label: 'In-depth subject knowledge', value: 'in-depth_subject_knowledge'},
  {label: 'Positive facial expression', value: 'positive_facial_expression'},
  {label: 'Well-prepared teaching equipment', value: 'well-prepared_teaching_equipment'},
  {label: 'Promotes positive atmosphere', value: 'promotes_positive_atmosphere'},
  {label: 'All questions well answered', value: 'all_questions_well_answered'},
  {label: 'Keeps lessons on track', value: 'keeps_lessons_on_track'},
  {label: 'Gives relevant examples', value: 'gives_relevant_examples'},
  {label: 'Easy to understand', value: 'easy_to_understand'},
  {label: 'Gives clear instructions', value: 'gives_clear_instructions'},
  {label: 'Efficient class management', value: 'efficient_class_management'},
  {label: 'Corrects when necessary', value: 'corrects_when_necessary'},
  {label: 'Starts and finishes on time', value: 'starts_and_finishes_on_time'},
  {label: 'Familiar with the subject(s)', value: 'familiar_with_the_subjects'},
  {label: 'Standard pronunciation', value: 'standard_pronunciation'},
  {label: 'Easy to approach', value: 'easy_to_approach'},
  {label: 'Treats all students fairly', value: 'treats_all_students_fairly'},
  {label: 'Formally dressed', value: 'formally_dressed'},
  {label: 'Proper virtual background', value: 'proper_virtual_background'},
  //{label: 'Regular', value: 'regular'},
  {label: 'Gives prompt and effective feedback', value: 'gives_prompt_and_effective_feedback'},
]

export const UnsatisfiedOptions = [
  {label: 'Indifferent facial expressions', value: 'indifferent_facial_expressions'},
  {label: 'Impatient', value: 'impatient'},
  {label: 'Inappropriate/noisy background', value: 'inappropriate_noisy_background'},
  {label: 'Uses difficult vocabulary', value: 'uses_difficult_vocabulary'},
  {label: 'Unstable internet', value: 'unstable_internet'},
  {label: 'Lacks focus', value: 'lacks_focus'},
  {label: 'Less interaction', value: 'less_interaction'},
  {label: 'Unclear voice', value: 'unclear_voice'},
  {label: 'Inadequate equipment', value: 'inadequate_equipment'},
  {label: 'Very dark background', value: 'very_dark_background'},
  {label: 'Less energetic', value: 'less_energetic'},
  {label: 'Irregular correction/feedback', value: 'irregular_correction_feedback'},
  {label: 'Incomplete lesson content', value: 'incomplete_lesson_content'},
  {label: 'Strong accent', value: 'strong_accent'},
  {label: 'Camera turned off/covered', value: 'camera_turned_off_covered'},
  {label: 'Difficult to approach', value: 'difficult_to_approach'},
  {label: 'Informally dressed', value: 'informally_dressed'},
  {label: 'Lack of class preparation', value: 'lack_of_class_preparation'},
  {label: 'Favouritism', value: 'favouritism'},
  {label: 'Speaks too fast', value: 'speaks_too_fast'},
  {label: 'Teaches without mic/audio', value: 'teaches_without_mic_audio', accident: true},
  {label: 'Joins class late more than 5 minutes', value: 'joins_class_late_more_than_5_minutes', accident: true},
  {label: 'Exits class before the end time', value: 'exits_class_before_the_end_time', accident: true},
  {label: 'More than five minutes of internet disruption', value: 'more_than_five_minutes_of_internet_disruption', accident: true},
  {label: 'Makes offensive comments or gestures', value: 'makes_offensive_comments_or_gestures', accident: true},
  {label: "Teacher didn't show up", value: 'teacher_didnt_show_up', accident: true},
  {label: 'Associated task complains', value: 'associated_task_complains', accident: true},
]

// Youtube
export function getYoutuberUrl(v = '') {
  return `https://www.youtube.com/watch?v=${v}`
}

export function getYoutuberImage(v = '') {
  return `https://i.ytimg.com/vi/${v}/hqdefault.jpg`
}

export function onYoutubeClick(v) {
  const url = getYoutuberUrl(v)
  window.open(url, '_blanck')
}
