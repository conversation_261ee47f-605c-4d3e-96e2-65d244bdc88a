import {ref} from 'vue'
import {loadStripe} from '@stripe/stripe-js'
import {useRouter} from 'vue-router'
import AlertDialog from '../AlertDialog.vue'

export default function useStripePayments(props, emit, {schoolId}) {
  const stripe = ref(null)
  const elements = ref(null)
  const cardNumberElement = ref(null)
  const cardExpiryElement = ref(null)
  const cardCvcElement = ref(null)
  const paymentClientSecret = ref(null)
  const setupClientSecret = ref(null)
  const processing = ref(false)
  const formLoading = ref(false)
  const savedCards = ref([])
  const selectedCard = ref({})
  const cardholderName = ref('')
  const saveCard = ref(true)
  const cardOptions = ref([{value: {_id: 'new'}, label: '+ Add new card'}])
  const showCardPayment = ref(false)
  const initializing = ref(false)

  const router = useRouter()

  const initializeStripe = async (orderId) => {
    if (showCardPayment.value) return

    initializing.value = true
    try {
      // Get payment intent client secret from server
      const {clientSecret: secret} = await App.service('stripe').get('client-secret', {
        query: {
          orderId,
        },
      })

      paymentClientSecret.value = secret

      // Load Stripe using the official package
      stripe.value = await loadStripe(
        process.env.STRIPE_PUBLIC_KEY || 'pk_test_51RKWJK06kjwDIJihRihkUyz1BSbh9y92PzoZ6iLb4C132WwQiiOU7eUNO3BuwfCGTWbVS1d5hPasG8VCdIJSdXM500oqkeQ48d'
      )

      await loadSavedCards()

      showCardPayment.value = true

      // If there is no saved card, setup the card elements
      if (selectedCard.value._id === 'new') {
        setTimeout(() => {
          setupCardElements()
        }, 100)
      }
    } catch (error) {
      console.error('Error initializing Stripe:', error)
      emit('paymentError', 'Failed to initialize payment system')
    } finally {
      initializing.value = false
    }
  }

  const setupCardElements = async () => {
    if (!stripe.value) return

    try {
      if (cardNumberElement.value) {
        mountCardElements()
        return
      }

      formLoading.value = true
      // Create a setup intent
      const {clientSecret} = await App.service('stripe').create({type: 'createSetupIntent', schoolId})

      setupClientSecret.value = clientSecret

      // Create elements instance
      elements.value = stripe.value.elements({
        clientSecret: setupClientSecret.value,
      })

      // Styling for the elements
      const style = {
        base: {
          color: '#32325d',
          fontFamily: '"Helvetica Neue", Helvetica, sans-serif',
          fontSmoothing: 'antialiased',
          fontSize: '16px',
          '::placeholder': {
            color: '#aab7c4',
          },
          padding: '10px 12px',
        },
        invalid: {
          color: '#fa755a',
          iconColor: '#fa755a',
        },
      }

      // Create individual elements
      cardNumberElement.value = elements.value.create('cardNumber', {
        style,
        placeholder: '1234 1234 1234 1234',
        showIcon: true,
        iconStyle: 'solid',
      })
      cardExpiryElement.value = elements.value.create('cardExpiry', {
        style,
        placeholder: 'MM/YY',
      })
      cardCvcElement.value = elements.value.create('cardCvc', {
        style,
        placeholder: 'CVV',
      })

      mountCardElements()
    } catch (error) {
      console.error('Error setting up card elements:', error)
      emit('paymentError', 'Failed to set up card form')
    } finally {
      formLoading.value = false
    }
  }

  const mountCardElements = () => {
    if (!cardNumberElement.value || !cardExpiryElement.value || !cardCvcElement.value) return

    // Mount the elements
    cardNumberElement.value.mount('#card-number-element')
    cardExpiryElement.value.mount('#card-expiry-element')
    cardCvcElement.value.mount('#card-cvc-element')

    // Handle validation errors
    const displayError = (element, errorElement) => {
      element.on('change', (event) => {
        const errorDisplay = document.getElementById(errorElement)
        if (event.error) {
          errorDisplay.textContent = event.error.message
        } else {
          errorDisplay.textContent = ''
        }
      })
    }

    displayError(cardNumberElement.value, 'card-number-errors')
    displayError(cardExpiryElement.value, 'card-expiry-errors')
    displayError(cardCvcElement.value, 'card-cvc-errors')
  }

  const validateOrderBeforePayment = async (orderDetails) => {
    try {
      // Check if the order is for a service premium product
      if (orderDetails?.type === 'service_premium') {
        const latestServicePack = await App.service('service-pack').get(orderDetails?.servicePremium)

        // Compare timestamps to check if the product has been updated
        if (latestServicePack?.updatedAt !== orderDetails?.servicePremiumSnapshot?.updatedAt) {
          return {
            valid: false,
            message: 'The product has been updated, unable to complete payment. Please select the product again.',
          }
        }
      }

      return {valid: true}
    } catch (error) {
      console.error('Error validating order:', error)
      return {
        valid: false,
        message: 'Failed to validate order. Please try again.',
      }
    }
  }

  const processPayment = async () => {
    processing.value = true

    try {
      if (!stripe.value) {
        throw new Error('Stripe not initialized')
      }

      const {valid, message} = await validateOrderBeforePayment(props.orderDetails)

      if (!valid) {
        return new Promise((resolve, reject) => {
          $q.dialog({
            component: AlertDialog,
            componentProps: {
              title: message,
            },
          }).onOk(async () => {
            // Cancel the order
            try {
              await App.service('order')
                .get('cancelBeforePay', {query: {id: props.orderId, status: '404'}})
                .then(() => {
                  router.go(-1)
                  resolve()
                })
            } catch (err) {
              console.error('Error canceling order:', err)
              reject(err)
            }
          })
        })
      }

      if (selectedCard.value._id === 'new' || savedCards.value.length === 0) {
        // Process with new card
        await payWithNewCard()
      } else {
        // Process with saved card
        await payWithSavedCard()
      }
      const paymentIntentId = paymentClientSecret.value.split('_secret_')[0]
      emit('paymentCompleted', paymentIntentId)
    } catch (error) {
      console.error('Payment error:', error)
      emit('paymentError', error.message)
    } finally {
      processing.value = false
    }
  }

  const payWithNewCard = async () => {
    if (!cardNumberElement.value || !setupClientSecret.value) {
      throw new Error('Card form not initialized')
    }

    if (!cardholderName.value) {
      throw new Error('Please enter the cardholder name')
    }

    if (saveCard.value) {
      // Scenario 1: Save card and process payment
      await newCardPayWithSave()
    } else {
      // Scenario 2: Process payment without saving card
      await newCardPayWithoutSave()
    }
  }

  const newCardPayWithSave = async () => {
    // Create payment method first
    const {error: pmError, paymentMethod} = await stripe.value.createPaymentMethod({
      type: 'card',
      card: cardNumberElement.value,
      billing_details: {
        name: cardholderName.value,
      },
    })

    if (pmError) {
      throw new Error(pmError.message)
    }
    emit('paymentStarted')

    // Update payment intent with payment method and setup_future_usage
    const {clientSecret: updatedClientSecret} = await App.service('stripe').patch('payment-intent', {
      clientSecret: paymentClientSecret.value,
      paymentMethodId: paymentMethod.id,
      setupClientSecret: setupClientSecret.value,
      setupFutureUsage: 'on_session',
    })

    // Single confirmation handles both payment and saving
    const {error: paymentError, paymentIntent} = await stripe.value.confirmCardPayment(updatedClientSecret, {
      payment_method: paymentMethod.id,
    })

    if (paymentError) {
      throw new Error(paymentError.message)
    }

    if (paymentIntent.status !== 'succeeded') {
      throw new Error('Payment failed')
    }

    // Save payment method to database after successful payment
    try {
      await App.service('payment-methods').create({
        stripePaymentMethodId: paymentMethod.id,
        isDefault: savedCards.value.length === 0,
        schoolId,
      })
    } catch (dbError) {
      console.warn('Failed to save payment method to database:', dbError)
    }
  }

  const newCardPayWithoutSave = async () => {
    // Step 1: Create payment method without attaching to customer
    const {paymentMethod, error: paymentMethodError} = await stripe.value.createPaymentMethod({
      type: 'card',
      card: cardNumberElement.value,
      billing_details: {
        name: cardholderName.value,
      },
    })

    if (paymentMethodError) {
      throw new Error(paymentMethodError.message)
    }

    emit('paymentStarted')

    // Step 2: Confirm payment directly with the one-time payment method
    const {error: paymentError, paymentIntent} = await stripe.value.confirmCardPayment(paymentClientSecret.value, {
      payment_method: paymentMethod.id,
    })

    if (paymentError) {
      throw new Error(paymentError.message)
    }

    if (paymentIntent.status !== 'succeeded') {
      throw new Error('Payment failed')
    }
  }

  const payWithSavedCard = async () => {
    const selectedCardData = savedCards.value.find((card) => card._id === selectedCard.value._id)

    if (!selectedCardData) {
      throw new Error('Please select a payment method')
    }

    emit('paymentStarted')

    // Create or update payment intent with customer and payment method
    const {clientSecret} = await App.service('stripe').patch('payment-intent', {
      clientSecret: paymentClientSecret.value,
      paymentMethodId: selectedCardData.stripePaymentMethodId,
    })

    // Confirm payment with existing payment method
    const {error, paymentIntent} = await stripe.value.confirmCardPayment(clientSecret, {
      payment_method: selectedCardData.stripePaymentMethodId,
    })

    if (error) {
      throw new Error(error.message)
    }

    if (paymentIntent.status !== 'succeeded') {
      throw new Error('Payment failed')
    }
  }

  const loadSavedCards = async () => {
    try {
      const cards = await App.service('payment-methods').find({query: {schoolId}})
      savedCards.value = cards.data || []

      // Update card options
      cardOptions.value = [
        {value: {_id: 'new'}, label: 'Add new'},
        ...savedCards.value.map((card) => ({
          value: card,
          label: `${card.cardBrand.toUpperCase()} •••• •••• •••• ${card.last4}`,
          paymentMethodId: card.stripePaymentMethodId,
        })),
      ]

      // If no saved cards, default to 'new'
      if (savedCards.value.length === 0) {
        selectedCard.value = {_id: 'new'}
      } else {
        // Select default card if available
        const defaultCard = savedCards.value.find((card) => card.isDefault)
        if (defaultCard) {
          selectedCard.value = defaultCard
        } else {
          // Otherwise select the first saved card
          selectedCard.value = savedCards.value[0]
        }
      }

      return savedCards.value
    } catch (error) {
      console.error('Error loading saved cards:', error)
      selectedCard.value = {_id: 'new'}
      return []
    }
  }

  const onCardSelected = (value) => {
    console.log('change_selectedCard', value)
    selectedCard.value = value

    if (value._id === 'new') {
      setTimeout(() => {
        setupCardElements()
      }, 100)
    }
  }

  const onNewCardClose = () => {
    selectedCard.value = savedCards.value[0]
  }

  return {
    processing,
    formLoading,
    savedCards,
    selectedCard,
    cardholderName,
    saveCard,
    cardOptions,
    showCardPayment,
    initializing,
    initializeStripe,
    processPayment,
    onCardSelected,
    onNewCardClose,
  }
}
