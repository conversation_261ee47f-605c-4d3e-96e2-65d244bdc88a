<template>
  <div class="q-mt-md">
    <!-- Initial Card Payment Button -->
    <q-btn
      color="dark"
      class="full-width text-h6"
      icon="credit_card"
      label="Debit or Credit Card"
      no-caps
      @click="initializeCardPayment"
      :loading="initializing">
    </q-btn>

    <!-- Card Payment UI - Only shown after clicking the button -->
    <div v-if="showCardPayment" class="card-selector">
      <q-select
        v-if="savedCards.length > 0 && selectedCard._id !== 'new'"
        v-model="selectedCard"
        :options="cardOptions"
        option-label="label"
        outlined
        emit-value
        map-options
        stack-label
        label="Saved credit card"
        class="q-mb-md"
        @update:model-value="onCardSelected">
        <template v-slot:prepend>
          <img v-if="cardIcons[selectedCard.cardBrand]" :src="cardIcons[selectedCard.cardBrand]" :alt="selectedCard.cardBrand" width="25px" />
          <q-icon v-else name="credit_card" color="orange" />
        </template>
        <template v-slot:option="scope">
          <q-item v-bind="scope.itemProps">
            <q-item-section avatar>
              <img
                v-if="scope.opt.value.cardBrand && cardIcons[scope.opt.value.cardBrand]"
                :src="cardIcons[scope.opt.value.cardBrand]"
                :alt="scope.opt.value.cardBrand"
                width="25px" />
              <q-icon v-else-if="scope.opt.value._id === 'new'" name="add" size="25px" />
              <q-icon v-else name="credit_card" color="grey" size="25px" />
            </q-item-section>
            <q-item-section>
              <q-item-label>{{ scope.opt.label }}</q-item-label>
            </q-item-section>
          </q-item>
        </template>
        <template v-slot:no-option>
          <q-item>
            <q-item-section class="text-grey">No saved cards</q-item-section>
          </q-item>
        </template>
      </q-select>

      <div v-if="selectedCard._id === 'new' || savedCards.length === 0" class="q-mb-md relative-position">
        <div v-if="selectedCard._id === 'new'" class="row justify-end" :class="{'disabled-element': processing || formLoading}" style="cursor: pointer">
          <q-icon name="close" size="md" @click="onNewCardClose()" />
        </div>
        <q-inner-loading :showing="formLoading">
          <q-spinner-dots size="50px" class="q-mt-xl" color="primary" />
        </q-inner-loading>
        <!-- Custom card form with individual Stripe Elements -->
        <div class="q-mb-md">
          <div class="text-caption q-mb-sm">Cardholder name</div>
          <q-input
            v-model="cardholderName"
            bg-color="white"
            class="card-holder-input"
            outlined
            dense
            placeholder="Enter cardholder name"
            :disable="processing" />
        </div>

        <div class="q-mb-md">
          <div class="text-caption q-mb-sm">Card number</div>
          <div id="card-number-element" class="stripe-element" :class="{'disabled-element': processing}"></div>
          <div id="card-number-errors" class="text-negative text-caption"></div>
        </div>

        <div class="row q-col-gutter-md q-mb-md">
          <div class="col-6">
            <div class="text-caption q-mb-sm">Expiration date</div>
            <div id="card-expiry-element" class="stripe-element" :class="{'disabled-element': processing}"></div>
            <div id="card-expiry-errors" class="text-negative text-caption"></div>
          </div>
          <div class="col-6">
            <div class="text-caption q-mb-sm">CVV</div>
            <div id="card-cvc-element" class="stripe-element" :class="{'disabled-element': processing}"></div>
            <div id="card-cvc-errors" class="text-negative text-caption"></div>
          </div>
        </div>

        <div id="card-errors" class="text-negative text-caption q-mb-md"></div>

        <div class="q-mb-md">
          <q-checkbox v-model="saveCard" label="Save this card" color="teal" :disable="processing" />
        </div>
      </div>

      <q-btn
        :loading="processing"
        color="primary"
        class="full-width q-mt-md"
        :label="'Pay $' + (props.orderAmount / 100).toFixed(2)"
        no-caps
        rounded
        @click="processPayment" />
    </div>
  </div>
</template>

<script setup>
import {defineProps, defineEmits} from 'vue'
import useSchool from '../../composables/common/useSchool'
import useStripePayments from './useStripePayments'
import {cardIcons} from './cardIcons'

const props = defineProps({
  orderId: {
    type: String,
    required: true,
  },
  orderDetails: {
    type: Object,
    required: true,
  },
  orderAmount: {
    type: Number,
    required: true,
  },
})

const emit = defineEmits(['paymentStarted', 'paymentCompleted', 'paymentError'])

// const {schoolId: schoolIdTemp, isSchool} = useSchool()
const {isMainContact} = useSchool()
const schoolId = props.orderDetails?.isSchool && isMainContact.value ? props.orderDetails?.buyer : null

const {
  processing,
  formLoading,
  savedCards,
  selectedCard,
  cardholderName,
  saveCard,
  cardOptions,
  showCardPayment,
  initializing,
  initializeStripe,
  processPayment,
  onCardSelected,
  onNewCardClose,
} = useStripePayments(props, emit, {schoolId})

const initializeCardPayment = async () => {
  await initializeStripe(props.orderId)
}
</script>

<style scoped>
.card-selector {
  max-width: 504px;
  margin: 20px auto 50px auto;
}

.card-holder-input ::placeholder {
  color: #aab7c4;
  font-size: 16px;
}

.stripe-element {
  padding: 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: white;
  height: 40px;
}

.disabled-element {
  opacity: 0.6;
  pointer-events: none;
}
</style>
