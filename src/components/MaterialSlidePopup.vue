<template>
  {{ console.log('ldfkfkkfkf', media) }}
  <q-dialog ref="dialogRef" @hide="onDialogHide">
    <q-card class="q-pa-md bg-white" style="width: 700px; max-width: 90vw; max-height: 90vh; border-radius: 24px">
      <div v-if="dialogVideoUrl" style="position: relative; padding-bottom: 56.25%; height: auto; overflow: hidden; border-radius: 8px">
        <iframe
          v-if="dialogYoutubeId"
          :src="`https://www.youtube.com/embed/${dialogYoutubeId}?autoplay=1`"
          frameborder="0"
          allow="autoplay; encrypted-media"
          allowfullscreen
          style="position: absolute; top: 0; left: 0; width: 100%; height: 100%"></iframe>
        <iframe
          v-else
          :src="dialogVideoUrl"
          frameborder="0"
          allow="autoplay; encrypted-media"
          allowfullscreen
          style="position: absolute; top: 0; left: 0; width: 100%; height: 100%"></iframe>
      </div>
      <div v-else>
        <q-img class="rounded-borders full-width" :ratio="16 / 9" :src="dialogImageUrl"></q-img>
      </div>
    </q-card>
  </q-dialog>
</template>

<script setup>
import {useDialogPluginComponent} from 'quasar'
import {computed, onMounted, ref} from 'vue'

const props = defineProps({
  media: Object,
})
defineEmits([...useDialogPluginComponent.emits])

const {dialogRef, onDialogHide, onDialogOK, onDialogCancel} = useDialogPluginComponent()
const dialogVideoUrl = ref(null)
const dialogYoutubeId = ref(null)
const dialogImageUrl = ref(null)

function setMediaData(media) {
  if (media.type === 'image') {
    dialogVideoUrl.value = null
    dialogImageUrl.value = media.url
  } else {
    dialogImageUrl.value = null
    dialogVideoUrl.value = media.url
    if (dialogVideoUrl.value.includes('www.youtube.com')) {
      dialogYoutubeId.value = media.key
    } else {
      dialogYoutubeId.value = null
    }
  }
}

onMounted(() => {
  setMediaData(props.media)
})
</script>
