<template>
  <div class="relative-position w-full">
    <q-img class="rounded-borders full-height" ref="mediaContainer" :ratio="16 / 9" :src="handlePreview(currentSlide)"> </q-img>
    <div v-for="(media, index) in currentMaterials" :key="index">
      <div v-if="media?.url" class="absolute" :style="getMediaStyles(media)" @click="handleOpenDailog(media)">
        <q-img v-show="false"></q-img>
        <div
          v-if="media.type === 'video'"
          class="absolute-center"
          style="background: rgba(0, 0, 0, 0.5); border-radius: 50%; width: 50px; height: 50px; display: flex; align-items: center; justify-content: center">
          <q-icon name="play_arrow" color="white" size="28px" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import {onBeforeUnmount, onMounted, ref} from 'vue'
import MaterialSlidePopup from './MaterialSlidePopup.vue'

const props = defineProps({
  currentSlide: Object,
  currentMaterials: Array,
  getPreview: Function,
})

const mediaContainer = ref(null)
const containerWidth = ref(0)

const handlePreview = (item) => {
  if (props.getPreview) {
    return props.getPreview(item)
  } else {
    return Fn.hashToUrl(item.pic || item.hash) || item.url || '/v2/img/no-img.png'
  }
}

const getMediaStyles = (media) => {
  if (media?.position && media?.dimensions) {
    const left = media.position.x * (containerWidth.value / media.slideWidth) + 'px'
    const top = media.position.y * ((containerWidth.value * 9) / (media.slideHeight * 16)) + 'px'
    const width = media.dimensions.width * (containerWidth.value / media.slideWidth) + 'px'
    const height = media.dimensions.height * ((containerWidth.value * 9) / (media.slideHeight * 16)) + 'px'
    return {
      left,
      top,
      width,
      height,
      backgroundSize: 'cover',
      cursor: 'pointer',
    }
  }
}

const dialogMedia = ref(null)

const handleOpenDailog = (media) => {
  $q.dialog({
    component: MaterialSlidePopup,
    componentProps: {
      media,
    },
  })
    .onOk(() => {})
    .onCancel(() => {})
}

onMounted(async () => {
  const observer = new ResizeObserver((entries) => {
    for (let entry of entries) {
      containerWidth.value = entry.contentRect.width
      console.log('Main image width (responsive):', containerWidth.value)
    }
  })

  if (mediaContainer.value?.$el) {
    observer.observe(mediaContainer.value.$el)
  }

  onBeforeUnmount(() => {
    observer.disconnect()
  })
})
</script>

<style scoped></style>
