<template>
  <div>
    <q-list separator class="overflow-auto full-height" v-show="!loadingPage">
      <template v-for="(o, i) in fields" :key="i">
        <template v-if="o.id === step">
          <q-item>
            <q-item-section avatar class="lt-md">
              <q-icon :name="`svguse:/v2/icons.svg#${o.icon}`" />
            </q-item-section>
            <q-item-section> {{ o.name }} </q-item-section>
          </q-item>
          <template v-if="['basic', 'inquiry', 'question', 'applying', 'reflection'].includes(o.id)">
            <template v-if="defaultTpl?.[o?.id]">
              <template v-for="(it, ii) in defaultTpl[o.id]" :key="ii">
                <q-item
                  clickable
                  v-if="it.enable && (!it.code || it.code !== 'words') && !(it.tags && it.isTagExisting == false)"
                  v-ripple
                  :active="emit('isInViewTop', o.id + '-' + ii)"
                  active-class="bg-grey-3 text-black"
                  @click.stop="emit('scrollToItem', o.id + '-' + ii)">
                  <q-item-section class="q-pl-md">
                    <q-item-label lines="1"> {{ it.name || it.origin }} </q-item-label>
                  </q-item-section>
                  <q-item-section side class="text-grey-6" v-if="o.id !== 'reflection'">
                    <q-icon
                      size="1.2rem"
                      :name="itemIsFilled(it, one) ? 'done' : checkTriggered ? 'priority_high' : ''"
                      :color="itemIsFilled(it, one) ? 'primary' : it.required && checkTriggered ? 'negative' : ''"></q-icon>
                  </q-item-section>
                </q-item>
              </template>
            </template>
            <q-item v-else>
              <q-item-section class="q-pl-md text-grey"> No data </q-item-section>
            </q-item>
          </template>
          <template v-else-if="['task'].includes(o.id)">
            <q-separator></q-separator>
            <draggable :disabled="icantedit || isTask" @end="onEnd" ghost-class="ghost" class="q-list q-list--separator" :list="one?.linkGroup" item-key="_id">
              <template #item="{element, index}">
                <q-item
                  clickable
                  v-ripple
                  class="bg-white group-edit"
                  :id="element._id"
                  :data-active="isInLinkContentViewTop(element)"
                  :active="isInLinkContentViewTop(element)"
                  active-class="bg-grey-3 text-black"
                  @click.stop="emit('scrollToItem', 'link-' + element._id)">
                  <q-item-section avatar v-if="!icantedit && !isTask">
                    <q-icon name="drag_indicator" style="cursor: move" class="text-grey-6" />
                  </q-item-section>
                  <div class="q-px-sm" v-if="isTask"></div>
                  <q-item-section>
                    <q-item-label lines="1">
                      <q-input
                        v-if="editingId === element._id"
                        @click.stop
                        @blur="onGroupNameChange"
                        @change="onGroupNameChange(element)"
                        filled
                        dense
                        autofocus
                        v-model="element.name"></q-input>
                      <span v-if="editingId !== element._id" :class="{'text-primary text-bold': isInLinkContentViewTop(element)}">{{ element.name }}</span>
                    </q-item-label>
                    <q-item-label lines="1" v-if="element.alias" class="section-caption"> {{ element.alias }} </q-item-label>
                  </q-item-section>
                  <q-item-section top side v-if="!isTask">
                    <div class="text-grey-5 q-gutter-xs">
                      <q-icon
                        size="1.2rem"
                        :name="!groupIsEmpty(element) && !groupNotAllFilled(element) ? 'done' : checkTriggered ? 'priority_high' : ''"
                        :color="!groupIsEmpty(element) && !groupNotAllFilled(element) ? 'primary' : checkTriggered ? 'negative' : ''"></q-icon>
                      <q-btn
                        size="12px"
                        @click.stop
                        flat
                        dense
                        round
                        :disable="icantedit"
                        icon="more_horiz"
                        :color="isInLinkContentViewTop(element) ? 'primary' : ''">
                        <q-menu>
                          <q-list>
                            <q-item v-close-popup clickable @click="onGroupEdit(element)">
                              <q-item-section>Rename</q-item-section>
                            </q-item>
                            <q-item v-close-popup clickable v-if="index != 0" @click="onMove(element, index, -1)">
                              <q-item-section>Move Up</q-item-section>
                            </q-item>
                            <q-item v-close-popup clickable v-if="index + 1 != one?.linkGroup.length" @click="onMove(element, index, 0)">
                              <q-item-section>Move Down</q-item-section>
                            </q-item>
                            <q-item v-close-popup clickable @click.stop="removeGroup(element)">
                              <q-item-section>Delete</q-item-section>
                            </q-item>
                          </q-list>
                        </q-menu>
                      </q-btn>
                    </div>
                  </q-item-section>
                </q-item>
              </template>
            </draggable>
            <template v-if="!icantedit && !isTask">
              <q-separator></q-separator>
              <q-item class="q-mt-md">
                <q-item-section>
                  <q-btn color="primary" label="Add section title" no-caps rounded size="" @click="addSection"></q-btn>
                  <!-- <q-input
                    @click.stop
                    placeholder="Add section title"
                    @change="emit('addUserGroup')"
                    outlined
                    dense
                    :modealValue="sectionTitle"
                    @update:modelValue="(str) => emit('setSectionTitle', str.trim())"></q-input> -->
                </q-item-section>
              </q-item>
            </template>
          </template>
          <template v-else-if="['slides'].includes(o.id)"> </template>
          <template v-else>
            <template v-for="(data, code) in outlineData" :key="code">
              <q-item-label header>
                {{ t('outline.' + code) }}
                <q-icon
                  v-if="checkTriggered && Object.keys(outlineData[code]).length == 0"
                  :color="`${['pd', 'outline'].includes(code) ? 'negative' : 'grey-6'}`"
                  name="priority_high">
                </q-icon>
              </q-item-label>
              <template v-if="Object.keys(outlineData[code]).length > 0">
                <q-item
                  clickable
                  v-ripple
                  v-for="(st, cd) in data"
                  :active="isInOutlineViewTop(code + '-' + cd)"
                  active-class="bg-grey-3 text-black"
                  @click.stop="emit('scrollToItem', code + '-' + cd)"
                  :key="cd">
                  <q-item-section class="q-pl-md">
                    <q-item-label lines="1"> {{ st?.name }} </q-item-label>
                  </q-item-section>
                  <q-item-section side @click.stop>
                    <div class="text-grey-5 q-gutter-xs">
                      <q-icon
                        size="1.2rem"
                        :name="targetIsFilled(code, cd) ? 'done' : checkTriggered ? 'priority_high' : ''"
                        :color="
                          targetIsFilled(code, cd)
                            ? 'primary'
                            : (doneList.achievementObjectives?.errors.length > 0 || doneList.learningObjectives?.errors.length > 0) && checkTriggered
                              ? 'negative'
                              : ''
                        "></q-icon>
                    </div>
                  </q-item-section>
                </q-item>
              </template>
              <q-item v-else>
                <q-item-section class="q-pl-md text-grey">{{ code === 'skills' ? 'No Skill(s)' : 'No Subject(s)' }} </q-item-section>
              </q-item>
            </template>
          </template>
        </template>
        <template v-else-if="$q.screen.lt.md">
          <q-item clickable v-ripple @click.stop="emit('moveToStep', o.id)">
            <q-item-section avatar>
              <q-icon :name="`svguse:/v2/icons.svg#${o.icon}`" />
            </q-item-section>
            <q-item-section> {{ o.name }} </q-item-section>
            <TaskEditStatus v-if="checkTriggered" :data="doneList?.[o.id]" class="q-pr-none" />
          </q-item>
        </template>
      </template>
    </q-list>
    <div class="q-mini-drawer-hide absolute lt-md" style="top: 40vh; right: -17px">
      <q-btn dense round unelevated color="grey" icon="chevron_left" @click="emit('setMiniState', true)" />
    </div>
  </div>
</template>

<script setup>
import {ref, computed} from 'vue'
import {useQuasar} from 'quasar'
import draggable from 'vuedraggable'
import {useI18n} from 'vue-i18n'
import {useRoute} from 'vue-router'

import TaskEditStatus from 'src/pages/com/TaskEditStatus.vue'
import {itemIsFilled} from 'src/components/unit/utils.js'
import useUnit from 'src/composables/account/unit/useUnit.js'

const route = useRoute()
const loading = ref(false)

const props = defineProps({
  loadingPage: {
    type: Boolean,
    default: false,
  },
  one: {
    type: Object,
    require: true,
  },
  doneList: {
    type: Object,
    default: () => {},
  },
  fields: {
    type: Array,
    default: () => [],
  },
  defaultTpl: {
    type: Object,
    default: () => {},
  },
  inLinkContentView: {
    type: Array,
    default: () => [],
  },
  inOutlineView: {
    type: Array,
    default: () => [],
  },
  step: {
    type: String,
    default: '',
  },

  outlineData: {
    type: Object,
    default: () => {},
  },
  sectionTitle: {
    type: String,
    default: '',
  },

  icantedit: {
    type: Boolean,
    default: true,
  },
  isTask: {
    type: Boolean,
    default: false,
  },
  checkTriggered: {
    type: Boolean,
    default: false,
  },

  groupIsEmpty: {
    type: Function,
    require: true,
  },
  groupNotAllFilled: {
    type: Function,
    require: true,
  },
  targetIsFilled: {
    type: Function,
    require: true,
  },
  linkList: {
    type: Array,
    default: () => [],
  },
})

const emit = defineEmits(['isInViewTop', 'scrollToItem', 'updateOne', 'setSectionTitle', 'addUserGroup', 'moveToStep', 'setMiniState'])

const $q = useQuasar()
const {t} = useI18n({useScope: 'global'})
const {patchOneById} = useUnit()
const id = computed(() => route.params?.id || '')

const addSection = () => {
  $q.dialog({
    title: 'Add section title',
    message: 'Please enter the section title',
    prompt: {
      model: '',
      type: 'text',
      isValid: (val) => val.length > 0,
    },
    cancel: true,
    persistent: true,
  }).onOk((data) => {
    if (data && data.trim()) {
      emit('setSectionTitle', data.trim())
      emit('addUserGroup')
    }
  })
}

const isInLinkContentViewTop = (group) => {
  const groupId = 'link-' + group._id
  if (props.inLinkContentView.length && props.inLinkContentView[0] === groupId) {
    return true
  } else {
    return false
  }
}

const isInOutlineViewTop = (id) => {
  if (props.inOutlineView.length && props.inOutlineView[0] === id) {
    return true
  } else {
    return false
  }
}

const removeGroup = async (group) => {
  loading.value = true
  if (props.one.linkGroup.length == 1) {
    $q.notify({type: 'info', message: "Can't delete the last link content section. "})
  } else {
    let message = 'There are content linked under this category, would you like to delete them all?'
    if (props.groupIsEmpty(group)) {
      message = `Are you sure you want to delete this section ${group.name}? `
    }
    $q.dialog({
      title: 'Confirm',
      message,
      cancel: true,
    })
      .onOk(async () => {
        await removeAllLinkListByGroup(group)
        $q.notify({type: 'info', message: 'The section has been successfully deleted.'})
      })
      .onCancel(() => {})
  }
  loading.value = false
}
const removeAllLinkListByGroup = async (group) => {
  const links = props?.linkList.filter((item) => item.group == group._id)
  if (links?.length) {
    const $in = links.map((item) => item._id)
    await patchOneById(props.one._id, {$pull: {link: {_id: {$in}}}})
  }
  const rs = await patchOneById(props.one._id, {$pull: {linkGroup: {_id: group._id}}})
  emit('updateOne', {linkGroup: rs.linkGroup})
  if (links?.length) {
    emit('updateOne', {link: rs.link})
  }
}

// draggable
const editingId = ref(null)
const onGroupNameChange = async (group) => {
  editingId.value = null
  if (group && group.name && group._id) {
    await patchOneById(id.value, {'linkGroup.$.name': group.name}, {query: {'linkGroup._id': group._id}})
  }
}

const onGroupEdit = async (group) => {
  await sleep(100)
  editingId.value = group._id
}

const onEnd = async (evt) => {
  await patchOneById(id.value, {linkGroup: props.one.linkGroup})
  emit('scrollToItem', 'link-' + evt.item.id)
}

const onMove = async (group, index, inc) => {
  const groupActive = isInLinkContentViewTop(group)
  await sleep(200)
  const dto = props.one
  dto.linkGroup[index + inc] = dto.linkGroup.splice(index + 1 + inc, 1, dto.linkGroup[index + inc])[0]
  await patchOneById(id.value, {linkGroup: dto.linkGroup})
  if (groupActive) {
    emit('scrollToItem', 'link-' + group._id)
  }
}
</script>
