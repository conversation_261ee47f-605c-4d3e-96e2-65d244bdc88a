export function itemIsFilled(item, one) {
  let filled = true
  if (item.code && (item.code == 'idea' || item.code == 'words')) {
    if (Acan.isEmpty(one?.idea) || Acan.isEmpty(one?.words)) {
      filled = false
    }
  } else if (item.code && ['grades', 'subjects', 'duration'].includes(item.code)) {
    if (Acan.isEmpty(one?.[item.code])) {
      filled = false
    } else if (item.code == 'duration') {
      if (!one?.duration?.unit || !one?.duration?.value) {
        filled = false
      }
    } else {
      one?.[item.code]?.forEach((_item) => {
        if (!_item.value) {
          filled = false
        }
      })
    }
  } else if (
    (item.code && !Acan.isEmpty(one?.[item.code])) ||
    (item.code && !Acan.isEmpty(one?.ext[item.code])) ||
    (!item.code && item.tags && !Acan.isEmpty(one?.ext) && !Acan.isEmpty(one?.ext?.[item._id]) && !Acan.isEmpty(one?.ext?.[item._id]?.[item._id])) ||
    (!item.code && !item.tags && !Acan.isEmpty(one?.ext) && !Acan.isEmpty(one?.ext?.[item._id]))
  ) {
    if (item.type == 'choice' || item.type == 'choice-mark') {
      let values = []
      let obj = null
      if (item.code && one?.[item.code]) {
        obj = one?.[item.code]
      } else if (item.code && one?.ext[item.code]) {
        obj = one?.ext?.[item.code]
      } else {
        obj = one?.ext?.[item._id]
      }
      values = Object.values(obj)

      for (const property in obj) {
        if (obj[property]?.length === 0) {
          filled = false
        }
      }
    }
  } else {
    filled = false
  }
  return filled
}
