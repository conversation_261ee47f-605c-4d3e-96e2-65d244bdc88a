<template>
  <q-dialog ref="dialogRef" @hide="onDialogHide" :maximized="$q.screen.lt.sm" :persistent="persistent">
    <q-card style="width: 600px" class="q-pa-none bg-cyan-1">
      <q-banner inline-actions rounded class="bg-amber-2 text-black q-mt-md" v-if="isShowTitle">
        {{ title }}
        <template v-slot:action>
          <q-btn flat icon="close" class="q-pa-sm" @click="isShowTitle = false" />
        </template>
      </q-banner>
      <q-card-section>
        <q-scroll-area ref="scrollRef" style="height: calc(100vh - 400px)" class="q-pa-md">
          <div v-for="item in updatedChatList" :key="item?._id">
            <q-chat-message :label="formatTime(item?.createdAt)" />
            <q-chat-message
              v-if="isAssociatedTask && isProvider"
              :avatar="item.isSameServiceProvider ? item?.avatar : noDataIcon"
              :name="item.isSameServiceProvider ? item.name : item?.userInfo?.name[0]"
              :sent="item?.send"
              :bg-color="item?.send ? 'cyan-2' : 'white'">
              <div>
                <template v-if="item?.attachments">
                  <Preview :item="item?.attachments">
                    <img v-if="item?.attachments?.hash" class="cursor-pointer" :src="hashToUrl(item?.attachments?.hash)" width="120px" />
                  </Preview>
                </template>
                <template v-else>
                  <div class="q-my-md text-bold" v-if="item?.message" v-html="item?.message.replace(/\n/g, '<br>')"></div>
                </template>
                <div class="q-mt-sm text-right" v-if="isRecall(item?.createdAt) && mode === 'chat' && item?.send && !isAssociatedTask">
                  <q-btn flat rounded label="Recall" @click="recallMessage(item?._id)" no-caps dense icon="undo" />
                  <CountDown :deadTime="item?.createdAt" @end="getMessageList" />
                </div>
              </div>
            </q-chat-message>
            <q-chat-message v-else :avatar="item?.avatar" :name="item.name" :sent="item?.send" :bg-color="item?.send ? 'cyan-2' : 'white'">
              <div>
                <template v-if="item?.attachments">
                  <Preview :item="item?.attachments">
                    <img v-if="item?.attachments?.hash" class="cursor-pointer" :src="hashToUrl(item?.attachments?.hash)" width="120px" />
                  </Preview>
                </template>
                <template v-else>
                  <div class="q-my-md text-bold" v-if="item?.message" v-html="item?.message.replace(/\n/g, '<br>')"></div>
                </template>
                <div class="q-mt-sm text-right" v-if="isRecall(item?.createdAt) && mode === 'chat' && item?.send">
                  <q-btn flat rounded label="Recall" @click="recallMessage(item?._id)" no-caps dense icon="undo" />
                  <CountDown :deadTime="item?.createdAt" @end="getMessageList" />
                </div>
              </div>
            </q-chat-message>
            <div
              class="q-mt-sm text-right q-mr-xl"
              v-if="isRecall(item?.createdAt) && mode === 'chat' && item?.send && isAssociatedTask"
              style="color: #49454f">
              <q-btn flat rounded label="Recall" @click="recallMessage(item?._id)" no-caps dense icon="undo" />
              <CountDown :deadTime="item?.createdAt" @end="getMessageList" />
            </div>
          </div>
        </q-scroll-area>
      </q-card-section>

      <q-card-section v-if="isAssociatedTask && mode === 'chat'" class="q-pa-md bg-white row items-end q-col-gutter-sm">
        <div class="col relative-position">
          <q-input v-model="messageText" type="textarea" autogrow rows="5" outlined clearable placeholder="Enter your message" />
        </div>
        <q-btn flat rounded color="primary" no-caps icon="o_image" @click="upImgFn" />
        <div>
          <q-btn :disable="!messageText" color="primary" label="Send" no-caps rounded @click="sendMessage(false)" />
        </div>
      </q-card-section>
      <q-card-section class="q-pa-md row items-end q-col-gutter-sm" v-else-if="!isAssociatedTask && mode === 'chat'">
        <div class="col relative-position">
          <q-input v-model="messageText" type="textarea" autogrow rows="5" outlined clearable placeholder="Enter your message" />
          <!-- <q-menu v-model="showDropdown" :offset="[0, 10]" anchor="bottom left" self="top left">
            <q-list style="min-width: 200px">
              <q-item v-for="user in users" :key="user.id" clickable v-close-popup @click="selectUser(user)">
                <q-item-section>
                  <q-item-label>{{ user.name }}</q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
          </q-menu> -->
        </div>
        <q-btn flat rounded color="primary" no-caps icon="o_image" @click="upImgFn" />
        <div>
          <q-btn :disable="!messageText" color="primary" label="Send" no-caps rounded @click="sendMessage(false)" />
        </div>
      </q-card-section>
      <q-card-section class="q-pa-md row items-end q-col-gutter-sm" v-if="mode === 'reject'">
        <div class="col relative-position">
          <q-input v-model="messageText" type="textarea" autogrow rows="5" outlined clearable placeholder="Enter your message" />
        </div>
        <div>
          <q-btn :disable="!messageText" color="primary" label="Send" no-caps rounded @click="reject(false)" />
        </div>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script setup>
import {ref, onMounted, computed, nextTick} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {pubStore} from 'stores/pub'

const route = useRoute()
const router = useRouter()
const pub = pubStore()
import {useDialogPluginComponent} from 'quasar'
import {date} from 'quasar'
import RecordInput from 'src/components/utils/inputs/RecordInput.vue'
import Preview from 'src/pages/account/OnCampusVerify/Preview.vue'
import CountDown from 'src/components/ChatMessage/CountDown.vue'
import noDataIcon from 'src/assets/icons/nodata.svg'

const messageText = ref('')
const scrollRef = ref(null)
const isAssociatedTask = computed(() => route.query.tab === 'myAssociatedTask' || route.query.tab === 'taskManagement')
const isProvider = computed(() => route.query.tab === 'taskManagement')

const props = defineProps({
  mode: {
    type: String,
    default: 'chat', // chat, read, reject
  },
  readFn: {
    type: Function,
    default: () => {},
  },
  persistent: {
    type: Boolean,
    default: false,
  },
  // cancel: {
  //   type: Boolean,
  //   default: true,
  // },
  title: {
    type: String,
    default: '',
  },
  type: {
    // enum: ['service-auth', 'service-conf', 'school-plan']
    type: String,
    default: 'service-auth',
  },
  rid: String,
  role: {
    // enum: ['admin', 'user']
    type: String,
    default: 'user',
  },
  currentSection: {
    type: Object,
    default: null,
  },
})

const isShowTitle = ref(props?.title)
const chatList = ref([])

const showDropdown = ref(false)

const cursorPosition = ref(0)

const users = [
  {id: 1, name: '张三'},
  {id: 2, name: '李四'},
  {id: 3, name: '王五'},
]

defineEmits([...useDialogPluginComponent.emits])

const {dialogRef, onDialogHide, onDialogOK, onDialogCancel} = useDialogPluginComponent()

const isRecall = (time) => {
  return time && new Date(time).getTime() + 5 * 60 * 1000 > new Date().getTime()
}

const updatedChatList = computed(() => {
  if (isAssociatedTask.value && isProvider.value) {
    return (chatList.value || []).map((item) => {
      const isStudentMsg = item?.userInfo?.roles?.includes('student') || item.role === 'student'

      return {
        ...item,
        send: !isStudentMsg,
        isSameServiceProvider: !isStudentMsg ? item.uid === pub?.user?._id : true,
      }
    })
  }

  return chatList.value || []
})

const isYesterday = (myDate) => {
  const compareDate = new Date(myDate)
  const yesterday = new Date()
  yesterday.setDate(yesterday.getDate() - 1)
  return compareDate.toDateString() === yesterday.toDateString()
}

function isToday(myDate) {
  const compareDate = new Date(myDate)
  const today = new Date()
  return compareDate.toDateString() === today.toDateString()
}

const formatTime = (time) => {
  let showTime = ''
  if (isYesterday(time)) {
    showTime = `Yesterday ${date.formatDate(time, 'HH:mm')}`
  } else if (isToday(time)) {
    showTime = `${date.formatDate(time, 'HH:mm')}`
  } else {
    showTime = date.formatDate(time, 'MM/DD/YYYY HH:mm')
  }
  return showTime
}

const upImgFn = async () => {
  $q.loading.show()
  const rs = await Fn.fileUpLoadUiX('image/*')
  console.log('rs', rs)
  $q.loading.hide()
  if (rs) {
    sendMessage({
      filename: rs?.title?.[pub?.user?._id] || '',
      mime: rs.mime,
      hash: rs._id,
    })
  }
}

const reject = async () => {
  onDialogOK(messageText.value)
}

const sendMessage = async (file) => {
  $q.loading.show()
  let data = {
    role: props?.role,
    rid: props?.rid,
    type: props?.type,
  }
  if (file) {
    data.attachments = file
  } else {
    data.message = messageText.value
  }

  await App.service('message')
    .create(data)
    .then((res) => {
      //getMessageList()
      onDialogOK({
        message: messageText.value,
      })
    })
    .catch((err) => {
      $q.notify({type: 'negative', message: err.message || 'Send message failed'})
    })
    .finally(() => {
      $q.loading.hide()
    })
}

const recallMessage = async (id) => {
  console.log('id', id)
  $q.dialog({
    title: 'Confirm',
    message: 'Would you like to recall it?',
    cancel: true,
    persistent: true,
  }).onOk(async () => {
    await App.service('message')
      .remove(id)
      .then(() => {
        getMessageList()
      })
  })
}

const scrollToBottom = () => {
  if (scrollRef.value) {
    console.log('scrollRef.value', scrollRef.value)
    scrollRef.value.setScrollPercentage('vertical', 1, 300)
  }
}

const main = async () => {
  await getMessageList()
  if (props?.readFn) {
    props?.readFn()
  }
  nextTick(() => {
    setTimeout(() => {
      scrollToBottom()
    }, 100)
  })
}

const getMessageList = async () => {
  $q.loading.show()
  const res = await App.service('message')
    .find({
      query: {
        rid: props?.rid,
        $limit: 1000,
        $sort: {createdAt: 1},
        // $role: props?.role,
        type: props?.type,
      },
    })
    .then((res) => {
      console.log('res', res)
      chatList.value = res?.data?.map((item) => {
        const isAdmin = item?.role === 'admin'
        const send = props?.role === 'admin' ? isAdmin : !isAdmin && pub?.user?._id === item?.uid

        return {
          ...item,
          avatar: isAdmin ? '/v2/img/logo2.png' : item?.userInfo?.avatar,
          send,
          name: send ? '' : isAdmin ? 'admin' : item?.userInfo?.nickname,
        }
      })
    })
    .finally(() => {
      $q.loading.hide()
    })
}

const handleInput = (e) => {
  console.log('e', e)
  cursorPosition.value = e.target.selectionStart
  const lastAtIndex = messageText.value.lastIndexOf('@', cursorPosition.value)

  if (lastAtIndex !== -1) {
    const searchText = messageText.value.slice(lastAtIndex + 1, cursorPosition.value)
    showDropdown.value = true
  } else {
    showDropdown.value = false
  }
}

const selectUser = (user) => {
  const lastAtIndex = messageText.value.lastIndexOf('@', cursorPosition.value)
  const prefix = messageText.value.slice(0, lastAtIndex)
  const suffix = messageText.value.slice(cursorPosition.value)

  messageText.value = `${prefix}@${user.name}${suffix}`
  showDropdown.value = false
}

onMounted(main)
</script>
