<template>
  <span class="q-ml-xs">{{ times }}</span>
</template>

<script setup>
import {onBeforeUnmount, onMounted, ref} from "vue"
const props = defineProps({
  deadTime: String,
})

const emit = defineEmits(['end'])


const times = ref('')
const timer = ref(null)

const countdown = (end, begin = getdateTime()) => {
  const beginDate = new Date(begin);
  const endDate = new Date(end);
  const diff = endDate.getTime() + 5*60*1000 - beginDate.getTime();


  const sec = diff < 0 ? (Math.abs(diff / 1000)) * -1 : diff / 1000;
  if(sec < 0){
    clearTimer();
    emit('end')
    return
  }
  return parseInt(sec) + 's';
}

function formatTime(times) {
  let time = Math.abs(times)
  const d = parseInt(time / (60 * 60 * 24))
  const h = parseInt(time / 3600 % 24)
  const minute = parseInt(time / 60 % 60)
  const second = Math.ceil(time % 60)
  const hours = h < 10 ? '0' + h : h
  let days = ''
  if(d > 0){
    days =  d < 10 ? d+'day' : d + 'days'
  }else {
    days = ''
  }
  const formatSecond = second > 59 ? 59 : second

  return `${days} ${hours > 0 ? `${hours}:` : '00:'}${minute < 10 ? '0' + minute : minute}:${formatSecond < 10 ? '0' + formatSecond : formatSecond}`
}

onMounted(() => {
  timer.value = setInterval(uptimes, 1000);
})


const uptimes = () => {
  times.value = countdown(props.deadTime, new Date())
}


const clearTimer = () => {
  clearTimeout(timer.value);
  timer.value = null;
};

onBeforeUnmount(() => {
  clearTimer()
})
</script>

<style lang="scss" scoped>

</style>
