<template>
  <q-dialog :model-value="modelValue" @update:model-value="$emit('update:modelValue', $event)" persistent style="min-height: auto">
    <div class="column no-wrap bg-white rounded-lg full-width" :class="[$q.screen.lt.sm ? 'dialog-width-mobile' : 'dialog-width']">
      <div class="self-end" style="padding: 0 6px">
        <q-btn flat rounded dense icon="close" @click="onClose" />
      </div>
      <div class="relative-position overflow-auto" style="padding: 2px 16px 8px">
        <div :class="[$q.screen.lt.md ? '' : 'flex justify-center items-center']" style="width: 100%; max-height: 100%">
          <div :style="[mediaResponse && !$q.screen.lt.md ? 'width: calc(100% - 20rem)' : 'width: 100%']">
            <QuestionMedia :response="media" :isPreview="true" :key="media?._id" />
          </div>
          <div
            v-if="mediaResponse"
            :style="[$q.screen.lt.md ? 'width: 100%' : 'width: 19rem; padding-left: .75rem']"
            class="q-mt-md"
            :class="$q.screen.lt.md ? '' : 'dialog-teacher-comment'">
            <TeacherComment
              :isMediaDialog="true"
              :sessionTakeawaySnapshotId="sessionTakeawaySnapshotId"
              :comments="comments"
              :response="mediaResponse"
              :isReadonly="!isEditing" />
          </div>
        </div>
      </div>
    </div>
  </q-dialog>
</template>

<script setup>
import {defineProps, defineEmits} from 'vue'
import QuestionMedia from 'src/pages/account/takeaway/components/questions/Media.vue'
import TeacherComment from 'src/pages/account/takeaway/components/TeacherComment.vue'

defineProps({
  modelValue: {
    type: Boolean,
    required: true,
  },
  media: {
    type: Object,
    default: null,
  },
  mediaResponse: {
    type: Object,
    default: null,
  },
  sessionTakeawaySnapshotId: {
    type: String,
    default: '',
  },
  comments: {
    type: Array,
    default: () => [],
  },
  isEditing: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:modelValue'])

const onClose = () => {
  emit('update:modelValue', false)
}
</script>

<style scoped>
.dialog-width {
  max-width: 90%;
  max-height: 95%;
}
.dialog-width-mobile {
  max-width: 97.5%;
  max-height: 97.5%;
}

.dialog-teacher-comment {
  max-height: 560px;
  overflow-y: auto;
}

@media screen and (max-width: 1286px) {
  .dialog-teacher-comment {
    max-height: 500px;
  }
}
</style>
