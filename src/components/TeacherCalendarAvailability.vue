<template>
  <div class="q-pa-md text-center">
    <FullCalendar ref="calendarRef" :options="calendarOptions">
      <template v-slot:eventContent="arg">
        <div class="text-center">
          <div v-if="arg.event.end.getTime() === arg.event.start.getTime() + 7 * 24 * 60 * 60 * 1000">
            <b>{{ weekMap[0] }}-{{ weekMap[6] }}</b>
            <br />
            <b>({{ Acan.time('H:i', arg.event.start) }}-24:00)</b>
          </div>
          <div v-else-if="arg.event.end.getDay() !== arg.event.start.getDay()">
            <b>{{ weekMap[arg.event.start.getDay()] }}-{{ weekMap[arg.event.end.getDay()] }}</b>
            <br />
            <b>({{ Acan.time('H:i', arg.event.start) }}-{{ Acan.time('H:i', arg.event.end) }})</b>
          </div>
          <div v-else>
            <b>{{ Acan.time('H:i', arg.event.start) }}-{{ Acan.time('H:i', arg.event.end) }}</b>
          </div>
        </div>
      </template>
    </FullCalendar>
    <div class="row justify-between items-center q-mt-md">
      <div>
        <q-btn v-if="theHolidayISO?.length" color="primary" :class="{'full-width': $q.screen.lt.sm}" dense rounded flat no-caps @click="onSetHoliday">
          <div style="height: 1.4rem" class="q-px-md">
            Holiday:
            <IntlDate v-if="theHolidayISO?.[0]" :dateISOString="theHolidayISO[0]"></IntlDate>
            -
            <IntlDate v-if="theHolidayISO?.[1]" :dateISOString="theHolidayISO[1]"></IntlDate>
            <q-icon name="o_edit" class="q-pl-sm" size="xs"></q-icon>
          </div>
        </q-btn>
        <q-btn v-else color="primary" :class="{'full-width': $q.screen.lt.sm}" rounded label="Set holiday time" no-caps @click="onSetHoliday"></q-btn>
      </div>
      <div class="col-xs-12 col-sm-6 col-md-6 flex justify-end">
        <div class="gt-xs q-px-sm"></div>
        <q-btn
          :class="{'full-width': $q.screen.lt.sm}"
          rounded
          icon="check"
          color="primary"
          label="Confirm"
          :disable="!currentEvents?.length"
          no-caps
          @click="onConfirmClick"></q-btn>
      </div>
    </div>
  </div>
</template>

<script setup>
import {ref, onMounted} from 'vue'
import FullCalendar from '@fullcalendar/vue3'
import dayGridPlugin from '@fullcalendar/daygrid'
import timeGridPlugin from '@fullcalendar/timegrid'
import interactionPlugin from '@fullcalendar/interaction'
import {useConfStore} from 'stores/service-conf'
import {convertHourIndexToUserTimeZoneDate, convertHoursIndexToUserZone, hoursIndexToHoursIndexRange} from 'src/utils/converters'
import RangeDialog from './RangeDialog.vue'

const confStore = useConfStore()

const emit = defineEmits(['close'])
const props = defineProps({
  hoursIndex: Array,
  holiday: Array,
  verfied: Boolean,
  editable: {
    type: Boolean,
    default: false,
  },
  clickable: {
    type: Boolean,
    default: true,
  },
})

const weekMap = {
  0: 'Sun',
  1: 'Mon',
  2: 'Tue',
  3: 'Wed',
  4: 'Thu',
  5: 'Fri',
  6: 'Sat',
}

const calendarOptions = {
  timeZone: 'local', // the default (unnecessary to specify)
  plugins: [
    dayGridPlugin,
    timeGridPlugin,
    interactionPlugin, // needed for dateClick
  ],
  initialView: 'timeGridWeek',
  dayHeaderFormat: {weekday: 'short'},
  allDaySlot: false,
  headerToolbar: false,
  initialEvents: [], // alternatively, use the `events` setting to fetch from a feed
  editable: props.editable,
  selectable: true,
  selectMirror: true,
  dayMaxEvents: false,
  weekends: true,
  eventOverlap: false,
  // nowIndicator: true, // 现在指示器
  slotDuration: '00:05:00', // 显示时间段的频率
  slotLabelInterval: {hour: 1}, // 时间段的频率
  slotLabelFormat: {
    hour: '2-digit',
    minute: '2-digit',
    meridiem: false,
    hour12: false, // 设置为 24 小时格式
  },
  events: [],
  select: handleDateSelect,
  eventClick: handleEventClick,
  eventsSet: handleEvents,
  /* you can update a remote database when these fire:
    eventAdd:
    eventChange:
    eventRemove:
    */
  firstDay: 0,
  // eventConstraint: {start: new Date()}, // 将事件拖动和调整大小限制为特定的时间窗口
  scrollTime: `00:00:00`,
  // scrollTime: `${new Date().getHours()}:00:00`,
}

let eventGuid = 0
let calendarApi
const theHolidayISO = ref([])
const currentEvents = ref([])
const calendarRef = ref(null)
const showRangeDialog = ref(false)
const SELECTED_COLOR = '#FF8A80'

function createEventId() {
  return String(eventGuid++)
}

async function init() {
  $q.loading.show()
  if (!confStore?.doc?._id) {
    await confStore.init()
  }
  const holidaySet = confStore?.doc.holiday
  if (holidaySet?.[0]?.[0]) {
    theHolidayISO.value = holidaySet[0]
  }
  if (props.hoursIndex?.length) {
    const hoursIndexRange = hoursIndexToHoursIndexRange(convertHoursIndexToUserZone(props.hoursIndex))
    hoursIndexRange?.forEach((hour) => {
      const start = convertHourIndexToUserTimeZoneDate(hour[0], false)
      const end = convertHourIndexToUserTimeZoneDate(hour[1], true)
      calendarApi.addEvent({
        id: createEventId(),
        start,
        end,
        backgroundColor: SELECTED_COLOR,
        allDay: false,
        editable: true,
      })
    })
  }
  if (props.holiday?.[0]?.[0]) {
    theHolidayISO.value = props.holiday[0]
  }
  $q.loading.hide()
}

const onConfirmClick = () => {
  const hours = []
  if (currentEvents.value?.length) {
    currentEvents.value.forEach(({start, end}) => {
      hours.push([new Date(start).toISOString(), new Date(end).toISOString()])
    })
  }
  emit('close', hours, theHolidayISO.value)
}

function handleDateSelect(selectInfo) {
  calendarApi = selectInfo.view.calendar
  calendarApi.unselect()
  let isOverlap = calendarApi.getEvents().some((event) => {
    return selectInfo.start < event.end && selectInfo.end > event.start
  })

  if (isOverlap) {
    $q.notify({
      type: 'negative',
      message: 'Selected time overlaps with an existing available time.',
      timeout: 2000,
    })
    return
  }

  calendarApi.addEvent({
    id: createEventId(),
    start: selectInfo.start,
    end: selectInfo.end,
    backgroundColor: SELECTED_COLOR,
    allDay: false,
    editable: true,
  })
}

async function handleEventClick(clickInfo) {
  clickInfo.event.remove()
}

function handleEvents(events) {
  currentEvents.value = events
}

const onSetHoliday = () => {
  doShowRangeDialog()
}

const doShowRangeDialog = () => {
  showRangeDialog.value = true
  $q.dialog({
    component: RangeDialog,
    componentProps: {
      holiday: true,
      title: 'Set holiday time',
      range: theHolidayISO.value,
      verified: props.verfied,
    },
  })
    .onOk(async (data) => {
      theHolidayISO.value = data
      if (Array.isArray(data) && data.length == 2) {
        $q.loading.show()
        const holiday = []
        holiday.push(data)
        await confStore.patch({holiday})
        $q.loading.hide()
      }
      showRangeDialog.value = false
    })
    .onCancel(() => {
      showRangeDialog.value = false
    })
}

onMounted(async () => {
  if (!calendarRef.value) return
  const view = calendarRef.value.calendar.view
  window.calendarView = view
  calendarApi = view.calendar
  await init()
})
</script>

<style scoped>
::v-deep(.fc-day-today) {
  background-color: transparent !important;
}

::v-deep(.fc-timegrid-event) {
  text-align: center;
}
</style>
