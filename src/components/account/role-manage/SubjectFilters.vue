<template>
  <div class="fit">
    <div class="row q-col-gutter-x-lg">
      <div class="col-12">
        <q-input
          class="bg-teal-1 overflow-hidden rounded-borders-xl"
          outlined
          rounded
          dense
          :label="'Search by name'"
          v-model="filters.search"
          @keyup.enter="onKeyupEnter"
          @update:model-value="onSearchUpdate">
          <template v-slot:prepend>
            <q-btn v-if="searchedAlready" round flat dense icon="close" @click="doClear"></q-btn>
            <q-btn v-else round flat dense icon="search" @click="doSearch"></q-btn>
          </template>
          <template v-slot:append>
            <q-btn round flat dense v-if="showMenu" class="rotate-180" icon="arrow_drop_down" @click="showMenu = false"></q-btn>
            <q-btn round flat dense v-else icon="arrow_drop_down" @click="getFliters"></q-btn>
          </template>
        </q-input>
      </div>
    </div>
    <div>
      <q-menu max-width="770px" @hide="onMenuHide" v-model="showMenu" fit :offset="[0, 4]" class="shadow-3 rounded-borders-md">
        <q-card>
          <q-card-section class="scroll">
            <div class="q-py-md text-h6">Curriculums</div>
            <div class="q-gutter-sm q-mb-md" v-for="item in props.curriculumList" :key="item._id">
              <q-checkbox v-model="filters.selectionCurriculum" :val="item._id" :label="item.name" @update:model-value="onCurriculumUpdate"> </q-checkbox>
              <div class="q-ml-lg">
                <div class="text-subtitle1 text-weight-medium">Subjects</div>
                <q-checkbox
                  v-model="filters.selectionSubject"
                  :val="subject._id"
                  :label="subject.name"
                  v-for="subject in item.subjectList"
                  :key="subject._id"
                  @update:model-value="onSubjectUpdate" />
              </div>
              <q-separator class="q-my-md" />
            </div>
          </q-card-section>
        </q-card>
      </q-menu>
    </div>
  </div>
</template>
<script setup>
/*
  imports
*/
import {ref, onMounted, watch, computed} from 'vue'

/*
  props && emit
*/
const props = defineProps({
  modelValue: Object,
  curriculumList: Array,
})

// const selectionCurriculum = ref([])
// const selectionSubject = ref([])
const emit = defineEmits(['update:modelValue'])

/*
  consts
*/

const showMenu = ref(false)
const searchedAlready = ref(false)

const filters = ref({search: '', selectionCurriculum: [], selectionSubject: []})

/*
  computed
*/

const enableClearFilter = computed(() => {
  return filters.value.follower || (filters.value.search && searchedAlready.value)
})

/*
  methods
*/
const onCurriculumUpdate = (v, e) => {
  let newSelectionSubject = new Set()
  props.curriculumList.forEach((element) => {
    if (v.includes(element._id)) {
      element.subjectList.forEach((subject) => {
        newSelectionSubject.add(subject._id)
      })
    }
  })
  filters.value.selectionSubject = Array.from(newSelectionSubject)
  updateModelValue()
}

const onSubjectUpdate = (v, e) => {
  updateModelValue()
}
const onSearchUpdate = () => {
  searchedAlready.value = false
  updateModelValue()
}

const getFliters = () => {
  showMenu.value = true
}

const onClearFiltersClick = () => {
  filters.value = {search: ''}
  updateModelValue()
}

const onMenuHide = (evt) => {
  updateModelValue()
}

const onKeyupEnter = () => {
  doSearch()
}

const onFilterUpdate = (val) => {
  if ($q.screen.gt.sm && filters.value.search) {
    searchedAlready.value = true
  }
}

const updateModelValue = () => {
  emit('update:modelValue', filters.value)
}

const doSearch = () => {
  searchedAlready.value = true
  updateModelValue()
}

const doClear = () => {
  searchedAlready.value = false
  filters.value.search = null
  filters.value.selectionCurriculum = []
  filters.value.selectionSubject = []
  updateModelValue()
}
</script>
