<template>
  <q-card class="class-card q-ma-md">
    <q-card-section class="row justify-between text-grey-8">
      <div class="text-body1 full-width">
        <div class="full-width">
          <q-chip
            v-if="currentCurriculumMap?.[_class.curriculum]?.label"
            :label="currentCurriculumMap?.[_class.curriculum]?.label"
            square
            color="teal"
            outline />
          <q-chip v-if="subjectMap?.[_class.subject]?.name" :label="subjectMap?.[_class.subject]?.name" square color="teal" outline />
        </div>
        <div class="flex justify-between items-center full-width">
          <div style="width: calc(100% - 5rem)">{{ _class.name }}</div>
          <DialogAddSubjectClass v-if="isUserEditing || _class?.host === userId" :item="term" :isEditing="true" :editingData="_class" />
          <q-btn
            v-if="isUserEditing || _class?.host === userId"
            rounded
            unelevated
            dense
            icon="o_delete"
            text-color="red-4"
            @click="() => onDeleteClick(_class)" />
        </div>
        <!-- <q-tooltip anchor="top middle" self="bottom middle">Click to edit</q-tooltip> -->
        <!-- <q-popup-edit v-model="_class.name" :cover="false" class="bg-teal text-white" v-slot="scope"> -->
        <!--   <q-input -->
        <!--     dark -->
        <!--     color="white" -->
        <!--     v-model="scope.value" -->
        <!--     dense -->
        <!--     autofocus -->
        <!--     counter -->
        <!--     @keyup.enter="onUpdateSubject({scope, id: _class._id, termItem: item})"> -->
        <!--     <template v-slot:append> -->
        <!--       <q-icon class="cursor-pointer" name="done" @click="onUpdateSubject({scope, id: _class._id, termItem: item})" /> -->
        <!--     </template> -->
        <!--   </q-input> -->
        <!-- </q-popup-edit> -->
      </div>
      <!-- <div> -->
      <!--   <q-btn -->
      <!--     v-if="isUserEditing" -->
      <!--     icon="class" -->
      <!--     flat -->
      <!--     dense -->
      <!--     class="text-blue-4" -->
      <!--     @click="onArchiveClass({id: _class._id, item})" -->
      <!--     :disable="isArchivedDisable"> -->
      <!--     <q-tooltip anchor="top middle" self="bottom middle">Archive</q-tooltip> -->
      <!--   </q-btn> -->
      <!-- </div> -->
    </q-card-section>

    <q-card-actions type="flex" align="around" class="q-px-md q-pb-md">
      <q-btn v-if="isSchool" unelevated no-caps @click.stop="() => emit('teacherClick', _class, true)" style="padding: 0" v-ripple>
        <div class="column justify-center items-center q-py-xs q-px-lg rounded-borders bg-grey-3 cursor-pointer relative-position">
          <div class="text-caption">Teachers</div>
          <div class="text-green-7">{{ _class?.count?.teacher || '0' }}</div>
        </div>
      </q-btn>
      <q-separator vertical inset />
      <q-btn unelevated no-caps style="padding: 0" v-ripple @click.stop="() => emit('studentClick', _class, true)">
        <div class="column justify-center items-center q-py-xs q-px-lg rounded-borders bg-grey-3 cursor-pointer relative-position">
          <div class="text-caption">Students</div>
          <div class="text-green-7">{{ _class?.count?.student || '0' }}</div>
        </div>
      </q-btn>
    </q-card-actions>

    <q-card-actions type="flex" align="between" class="q-px-md q-pb-md">
      <q-btn flat class="text-teal-4 text-caption bg-white" no-caps label="Applicatoin format setting" @click="() => goToApplicationSetting(_class)" />
      <q-btn flat class="text-teal-4 text-caption bg-white" no-caps label="View report" :disable="true" />
    </q-card-actions>
  </q-card>
</template>

<script setup>
import {computed} from 'vue'
import {useQuasar} from 'quasar'

import {useRoute, useRouter} from 'vue-router'
import useSchool from 'src/composables/common/useSchool'
import useSubject from 'src/composables/account/academic/useSubject'
import useAcademicSetting from 'src/composables/account/academic/useAcademicSetting'
import useClasses from 'src/composables/account/school/useClasses'
import ConfirmDialog from 'src/components/utils/dialogs/ConfirmDialog.vue'
import DialogAddSubjectClass from './DialogAddSubjectClass.vue'

const props = defineProps({
  _class: {
    type: Object,
    require: true,
  },
  term: {
    type: Object,
    require: true,
  },
})
const emit = defineEmits(['studentClick', 'teacherClick'])

const $q = useQuasar()
const $route = useRoute()
const $router = useRouter()
const {isSchool, isUserEditing, userId} = useSchool()
const {map: subjectMap} = useSubject()
const {currentCurriculumList} = useAcademicSetting()
const {deleteOneById} = useClasses()

const currentCurriculumMap = computed(() =>
  currentCurriculumList.value.reduce((acc, cur) => {
    acc[cur._id] = cur
    return acc
  }, {})
)

async function onDeleteClick(item) {
  $q.dialog({
    component: ConfirmDialog,
    componentProps: {
      title: '',
      message: 'Deleting this class will result in clearing all teachers and students data. Are you sure to delete?',
    },
  })
    .onOk(async () => {
      $q.loading.show()
      const id = item._id
      await deleteOneById(id)
      $q.loading.hide()
    })
    .onCancel(() => {})
    .onDismiss(() => {})
}

function goToApplicationSetting(_class) {
  const path = `/account/classes/subject/${_class._id}/self-enroll`
  const query = {back: $route.path}
  $router.push({path, query})
}
</script>
