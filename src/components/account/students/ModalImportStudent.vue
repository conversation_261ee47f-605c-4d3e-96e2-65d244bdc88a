<template>
  <q-dialog v-model="isModalVisible" ref="dialogRef" @hide="onHide" class="dialog">
    <q-card>
      <q-card-section>
        <q-select
          outlined
          v-model="activeGrade"
          :options="isAdmin ? gradeList : visibleGrades"
          label="Select grade"
          color="primary"
          @update:model-value="onSelectGrade"
          options-selected-class="text-primary">
        </q-select>
      </q-card-section>
      <q-card-actions class="justify-center items-start">
        <div class="full-width q-gutter-md" style="max-height: 20rem; overflow: auto">
          <SubjectClassImportBtn
            v-for="item in classList"
            :key="item._id"
            :name="item.name"
            @click="onSelectClass(item)"
            :type="activeClass === item._id ? 'active' : studentDict[item._id]?.some((e) => e.joined) ? 'checked' : 'normal'"></SubjectClassImportBtn>
        </div>
        <q-item class="full-width justify-between" clickable dense v-ripple tag="label" v-if="!isEmpty(studentList)">
          <q-item-section avatar> </q-item-section>
          <q-item-section side style="padding-right: 0px">
            <q-checkbox v-model="checkedAll" left-label label="All" @update:model-value="onCheckedAll" />
          </q-item-section>
        </q-item>
        <q-virtual-scroll class="q-mt-md q-mb-md fit" :items="studentList" v-slot="{item, index}">
          <q-item clickable dense v-ripple tag="label" :key="index">
            <q-item-section avatar>
              <q-avatar class="" size="28px">
                <img :src="item.avatar || '/v2/img/avatar.png'" />
              </q-avatar>
            </q-item-section>
            <q-item-section class="items-center">
              <div class="text-left full-width">{{ item.name.join(' ') }}</div>
            </q-item-section>
            <q-item-section side>
              <q-checkbox v-model="item.checked" :disable="item.joined" :color="item.joined ? 'grey' : ''" @update:model-value="onChecked" />
            </q-item-section>
          </q-item>
        </q-virtual-scroll>
        <template v-if="isEmpty(studentList)">
          <NoData v-if="!loading" />
          <div v-else class="text-center q-pa-xl text-grey">
            <q-spinner-ball color="primary" size="2em" class="full-width" />
          </div>
        </template>
        <div v-else class="flex justify-end full-width">
          <q-btn rounded outline class="self-end q-ma-sm" color="primary" label="Close" @click="onHide" />
          <q-btn
            rounded
            class="self-end q-ma-sm"
            color="primary"
            :disable="studentList.filter((item) => item.checked && !item.joined).length === 0"
            label="Confirm"
            icon="done"
            @click="onSubmit" />
        </div>
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import {ref, computed, onMounted} from 'vue'
import {useDialogPluginComponent} from 'quasar'
import useGrade from 'src/composables/account/academic/useGrade'
import SubjectClassImportBtn from './SubjectClassImportBtn.vue'
import {pubStore} from 'stores/pub'
import useSchool from 'src/composables/common/useSchool'
import useClassApply from 'src/composables/account/school/useClassApply.js'
import useClasses from '../../../composables/account/school/useClasses'

const {getList: getAppliedStudents, deleteOneById} = useClassApply()
const {list: getClassListWithTeachers} = useClasses()
const {schoolId, isAdmin} = useSchool()
const emit = defineEmits([...useDialogPluginComponent.emits, 'save'])
const {dialogRef} = useDialogPluginComponent()
const {list: gradeList} = useGrade()
const classList = ref([])
const studentList = ref([])
const studentDict = ref({})
const activeGrade = ref()
const activeClass = ref()
const checkedAll = ref(false)
const pub = pubStore()

const props = defineProps({
  isVisible: {
    type: Boolean,
  },
  setIsVisible: {
    type: Function,
  },
  subjectClass: {
    type: String,
  },
})

const onHide = () => {
  isModalVisible.value = false
  activeGrade.value = null
  activeClass.value = null
  checkedAll.value = false
  classList.value = []
  studentList.value = []
}

const isModalVisible = computed({
  get() {
    return props.isVisible
  },
  set(newValue) {
    props.setIsVisible(newValue)
  },
})

const onSelectGrade = async () => {
  activeClass.value = null
  checkedAll.value = false
  studentList.value = []
  studentDict.value = {}
  await getClasses()
}

const loading = ref(false)

const getClasses = async () => {
  loading.value = true
  $q.loading.show()
  let query = {
    grade: activeGrade.value.value,
    $limit: 1000,
    school: schoolId.value,
    del: false,
  }
  if (!isAdmin.value) {
    let schoolUserData = await App.service('school-user').get(pub?.user?.schoolUser?._id)
    let classFilter = []
    if (schoolUserData.head?.length) {
      classFilter.push(...schoolUserData.head)
    }
    if (schoolUserData.class?.length) {
      classFilter.push(...schoolUserData.class.filter((e) => e !== 'cloudRoom'))
    }
    query._id = {$in: classFilter}
  }
  const res = await App.service('classes').find({query})
  classList.value = res.data
  $q.loading.hide()
  loading.value = false
}

const getStudentsByClass = async (classId) => {
  if (studentDict.value[classId]) {
    studentList.value = studentDict.value[classId]
    return
  }
  $q.loading.show()
  const [standardStudents, joinedStudents] = await Promise.all([
    App.service('students').find({query: {class: classId, $limit: 1000, del: false}}),
    App.service('students').find({query: {subjectClass: props.subjectClass, $limit: 1000}}),
  ])
  const list = standardStudents.data.map((s) => ({
    ...s,
    joined: joinedStudents.data.some((j) => j._id === s._id),
    checked: joinedStudents.data.some((j) => j._id === s._id),
  }))
  studentDict.value[classId] = list
  studentList.value = list
  $q.loading.hide()
}

const onSelectClass = (item) => {
  activeClass.value = item._id
  getStudentsByClass(item._id)
}

const visibleGrades = computed(() => {
  if (!gradeList.value?.length || !getClassListWithTeachers.value?.length) return []
  const gradesWithClasses = new Set(getClassListWithTeachers.value.filter((c) => c.del === false).map((c) => c.grade))
  return gradeList.value.filter((g) => gradesWithClasses.has(g.value))
})
const onCheckedAll = (e) => {
  studentList.value.forEach((item) => {
    if (!item.joined) {
      item.checked = e
    }
  })
}

const onChecked = () => {
  let everyChecked = studentList.value.every((item) => item.checked && !item.joined)
  let everyUnChecked = studentList.value.every((item) => !item.checked && !item.joined)
  if (everyChecked) {
    checkedAll.value = true
  } else if (everyUnChecked) {
    checkedAll.value = false
  } else {
    checkedAll.value = null
  }
}

const onSubmit = async () => {
  try {
    $q.loading.show()
    const list = studentList.value.filter((item) => item.checked && !item.joined)
    if (!list.length) {
      $q.notify({type: 'warning', message: 'No students selected'})
      $q.loading.hide()
      return
    }
    const classData = await App.service('classes').get(props.subjectClass)
    const classApplyRes = await getAppliedStudents({class: props.subjectClass})
    if (classData.maxParticipants) {
      let count = classData.count?.student || 0
      if (list.length > classData.maxParticipants - count) {
        $q.dialog({
          title: 'You can not proceed this action because it will result in exceeding the max participant no of this class',
          ok: {
            label: 'I got it',
            noCaps: true,
            rounded: true,
          },
        }).onOk(() => {
          onHide()
        })
        $q.loading.hide()
        return
      }
    }
    const appliedMap = new Map(classApplyRes?.data?.map((s) => [s.student, s._id]))
    const operations = list.map(async (student) => {
      const classApplyId = appliedMap.get(student._id)
      if (classApplyId) {
        await deleteOneById(classApplyId)
      }
      return App.service('students').patch(student._id, {
        $addToSet: {subjectClass: props.subjectClass},
        $import: true,
      })
    })
    await Promise.all(operations)
    emit('save')
    $q.notify({type: 'positive', message: 'Students added successfully'})
    await deletePendingAndRejectedEnrolments()
  } catch (err) {
    console.error(err)
    $q.notify({type: 'negative', message: 'Error adding students'})
  } finally {
    $q.loading.hide()
    onHide()
  }
}
async function deletePendingAndRejectedEnrolments() {
  const classData = await App.service('classes').get(props.subjectClass)
  if (classData.maxParticipants) {
    const count = classData.count?.student || 0
    if (count === classData.maxParticipants) {
      const classApplyRes = await getAppliedStudents({class: props.subjectClass})
      const filtered = classApplyRes?.data?.filter((e) => [0, -1].includes(e.status)) || []
      const appliedMap = new Map(filtered.map((s) => [s.student, s._id]))
      const operations = studentList.value.map(async (student) => {
        const classApplyId = appliedMap.get(student._id)
        if (classApplyId) {
          await deleteOneById(classApplyId)
        }
      })
      await Promise.all(operations)
    }
  }
}
</script>
<style lang="scss">
body.screen--sm,
body.screen--md {
  .dialog {
    .q-card {
      min-width: 600px;
    }
  }
}
</style>
