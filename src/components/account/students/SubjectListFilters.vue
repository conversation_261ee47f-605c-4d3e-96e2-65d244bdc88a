<template>
  <div class="fit">
    <div class="row q-col-gutter-x-lg">
      <div class="col-12 flex">
        <q-input
          class="bg-teal-1 overflow-hidden rounded-borders-xl"
          outlined
          rounded
          dense
          :label="'Search by name'"
          v-model="filters.search"
          @keyup.enter="onKeyupEnter"
          @update:model-value="onSearchUpdate">
          <template v-slot:prepend>
            <q-btn v-if="searchedAlready" round flat dense icon="close" @click="doClear"></q-btn>
            <q-btn v-else round flat dense icon="search" @click="doSearch"></q-btn>
          </template>
          <!-- <template v-slot:append>
            <q-btn round flat dense v-if="showMenu" class="rotate-180" icon="arrow_drop_down" @click="showMenu = false"></q-btn>
            <q-btn round flat dense v-else icon="arrow_drop_down" @click="getFliters"></q-btn>
          </template> -->
        </q-input>
        <q-checkbox v-model="filters.enrolCheck" label="Self-enrolled" @update:model-value="onEnrolCheckUpdate" />
        <q-checkbox v-model="filters.importCheck" label="Added manually" @update:model-value="onImportCheckUpdate" />
      </div>
    </div>
  </div>
</template>
<script setup>
/*
  imports
*/
import {ref, onMounted, watch, computed} from 'vue'

/*
  props && emit
*/
const props = defineProps({
  modelValue: Object,
  curriculumList: Array,
})

// const selectionCurriculum = ref([])
// const selectionSubject = ref([])
const emit = defineEmits(['update:modelValue'])

/*
  consts
*/

const showMenu = ref(false)
const searchedAlready = ref(false)

const filters = ref({search: '', enrolCheck: true, importCheck: true})

/*
  computed
*/

const enableClearFilter = computed(() => {
  return filters.value.follower || (filters.value.search && searchedAlready.value)
})

/*
  methods
*/

const onEnrolCheckUpdate = (v, e) => {
  updateModelValue()
}
const onImportCheckUpdate = (v, e) => {
  updateModelValue()
}
const onSearchUpdate = () => {
  searchedAlready.value = false
  updateModelValue()
}

const getFliters = () => {
  showMenu.value = true
}

const onClearFiltersClick = () => {
  filters.value = {search: ''}
  updateModelValue()
}

const onMenuHide = (evt) => {
  updateModelValue()
}

const onKeyupEnter = () => {
  doSearch()
}

const onFilterUpdate = (val) => {
  if ($q.screen.gt.sm && filters.value.search) {
    searchedAlready.value = true
  }
}

const updateModelValue = () => {
  emit('update:modelValue', filters.value)
}

const doSearch = () => {
  searchedAlready.value = true
  updateModelValue()
}

const doClear = () => {
  searchedAlready.value = false
  filters.value.search = null
  updateModelValue()
}
</script>
