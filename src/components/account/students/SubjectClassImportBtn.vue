<template>
  <q-btn color="primary" :label="name" v-if="type === 'active'" @click="onClick" />
  <q-btn outline color="grey" :label="name" v-if="type === 'normal'" @click="onClick" />
  <q-btn outline color="primary" :label="name" v-if="type === 'checked'" @click="onClick">
    <q-icon name="done" size="14px" />
  </q-btn>
</template>

<script setup>
import {ref, computed, watchEffect, onMounted} from 'vue'

const props = defineProps({
  type: {
    type: String,
    default: 'normal',
    enum: ['normal', 'active', 'checked'],
  },
  name: {
    type: String,
  },
})

const emit = defineEmits(['click'])

const onClick = () => {
  emit('click')
}
</script>
