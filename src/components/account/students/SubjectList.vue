<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <section class="q-pa-md" style="max-height: calc(100vh - 10rem)">
    <div class="flex justify-between">
      <div class="row justify-between items-center q-mb-xs q-mt-lg q-ma-md">
        <div class="col-xs-12">
          <Filters v-model="filters" />
        </div>
      </div>

      <div class="flex items-center">
        <q-btn
          unelevated
          color="negative"
          size="md"
          class="q-mx-xs"
          no-caps
          label="Bulk delete"
          @click="onDelete({isBulk: true})"
          v-if="selectedStudents.length > 0 && (isHost || isAdmin || isHead)" />
        <q-btn
          unelevated
          color="blue-7"
          size="md"
          class="q-mx-xs"
          no-caps
          label="Self-enrol application"
          @click="onApplicationClick"
          v-if="isHost || isAdmin || isHead" />
        <q-btn unelevated color="primary" size="md" class="q-mx-xs" no-caps label="Import" @click="onImportClick" v-if="isHost || isAdmin || isHead" />
      </div>
    </div>
    <q-table
      class="q-my-md"
      :columns="currentColumns"
      :rows="filteredList"
      row-key="_id"
      :selection="isHost || isAdmin || isHead ? 'multiple' : 'none'"
      v-model:selected="selectedStudents"
      v-model:pagination="pagination"
      :rows-per-page-options="[5, 10, 20]">
      <template v-slot:body-cell-name="props">
        <q-td :props="props">
          <div :class="[props.row?.del ? archivedClass : '']">
            <div>
              <span v-if="props.row?.name?.[0]" class="q-mr-xs">{{ props.row.name[0] }}</span>
              <span v-if="props.row?.name?.[1]">{{ props.row.name[1] }}</span>
            </div>

            <pre>{{ props.id }}</pre>
            <pre>{{ props.password }}</pre>
          </div>
        </q-td>
      </template>

      <template v-slot:body-cell-email="props">
        <q-td :props="props">
          <div :class="[props.row?.del ? archivedClass : '']">
            <div v-if="isHost || isAdmin">
              <div v-if="isSchool">
                <div v-if="props.row?.email && !props.row?.email?.includes('@classcipe.com')">{{ props?.row?.email || '-' }}</div>
                <div v-else-if="props?.row?.id">{{ props?.row?.id }}</div>
              </div>
              <div v-else>
                <div v-if="props?.row?.mobile">{{ props?.row?.mobile }}</div>
                <div v-else-if="!props.value?.includes('@classcipe.com')">{{ props?.row?.email || '-' }}</div>
              </div>
            </div>
            <div v-else>/</div>
          </div>
        </q-td>
      </template>

      <template v-slot:body-cell-enrolBy="props">
        <q-td :props="props">
          <div :class="[props.row?.del ? archivedClass : '']" class="flex q-gutter-sm">
            <q-badge transparent align="middle">{{ props?.row?.selfEnrol ? 'Self-enrolled' : 'Add manually' }}</q-badge>
          </div>
        </q-td>
      </template>
      <template v-slot:body-cell-class="props">
        <q-td :props="props">
          <div :class="[props.row?.del ? archivedClass : '']" class="flex q-gutter-sm">
            <div v-if="props?.row?.classInfo?.length">
              {{ props.row.classInfo.map((e) => e?.name).join(', ') }}
            </div>
          </div>
        </q-td>
      </template>

      <template v-slot:body-cell-actions="props">
        <q-td :props="props">
          <div :class="[props.row?.del ? archivedClass : '']">
            <q-btn icon="delete" flat dense class="text-red-4" @click="onDelete({user: props.row, isBulk: false})">
              <q-tooltip anchor="top middle" self="bottom middle">Delete</q-tooltip>
            </q-btn>
          </div>
        </q-td>
      </template>

      <template v-slot:no-data>
        <div class="flex flex-center full-width">
          <NoData message="No student" messageColor="grey" />
        </div>
      </template>
    </q-table>

    <ModalImportStudent :isVisible="isImportModalVisible" :setIsVisible="setImportModalVisible" :subjectClass="classId" @save="updateFilteredList">
    </ModalImportStudent>
  </section>
</template>

<script setup>
import {useQuasar} from 'quasar'
import {computed, ref, watch, onMounted, watchEffect} from 'vue'
import {useRoute, useRouter} from 'vue-router'

import useSchool from 'src/composables/common/useSchool'
import {pubStore} from 'stores/pub'
import Filters from './SubjectListFilters.vue'
import ModalImportStudent from './ModalImportStudent.vue'
import useClassApply from 'src/composables/account/school/useClassApply.js'

const isImportModalVisible = ref(false)
const setImportModalVisible = (bool) => (isImportModalVisible.value = bool)
const filters = ref({search: '', enrolCheck: true, importCheck: true})
const pub = pubStore()
const $q = useQuasar()
const $route = useRoute()
const $router = useRouter()
const {isSchool, userId, schoolId, isAdmin} = useSchool()
const classId = ref($route.query?.classId)
const isHost = ref(false)
const isHead = ref(false)
const userIds = ref([])
const selectedStudents = ref([])
const {getList: getAppliedStudents, deleteOneById} = useClassApply()

const archivedClass = 'text-strike text-grey'

const props = defineProps({})

const pagination = ref({
  page: 1,
  rowsPerPage: 10,
  // rowsNumber: 0,
})

onMounted(async () => {
  await initAllData()
})

const studentList = ref([])
async function updateFilteredList(arg) {
  let students = await App.service('students').find({query: {$limit: 1000, school: schoolId.value, subjectClass: classId.value, $sort: {name: 1}}})
  let applyData = await App.service('class-apply').find({query: {$limit: 1000, class: classId.value, status: 1}})
  let list = students.data.map((e) => {
    let isApply = applyData.data.find((a) => a.student === e._id)
    if (isApply) {
      e.selfEnrol = true
      e.importEnrol = false
    } else {
      e.selfEnrol = false
      e.importEnrol = true
    }
    return e
  })
  studentList.value = list
}

const onImportClick = () => {
  setImportModalVisible(true)
}

async function initAllData() {
  try {
    $q.loading.show()
    await updateFilteredList()
    // await getSchoolPlanCount(true)
    await getSchoolUser()

    // 判断老师身份
    let classData = await App.service('classes').get(classId.value)
    isHost.value = classData?.host === userId.value
  } catch (error) {
    console.error(error)
    $q.notify({type: 'negative', message: `server error: ${error.message || 'request failed'}`})
  } finally {
    $q.loading.hide()
  }
}

const getSchoolUser = async () => {
  let schoolUserData = await App.service('school-user').get(pub?.user?.schoolUser?._id)
  isHead.value = schoolUserData?.head?.includes(classId.value)
}
const columns = [
  {name: 'name', label: 'Name', required: true, align: 'left'},
  {name: 'email', label: 'Email/Classcipe ID', required: true, align: 'left', field: (row) => row?.email || row?.mobile},
  {name: 'class', label: 'Standard Class', required: true, align: 'left', field: (row) => row},
  {name: 'enrolBy', label: 'Enrol by', required: true, align: 'left', field: (row) => row},
  {name: 'actions', label: 'Actions', required: true, align: 'left'},
]
const currentColumns = computed(() => {
  let list = columns

  if (!isAdmin.value && !isHost.value && !isHead.value) {
    return list.slice(0, -1)
  }
  return list
})

const filteredList = computed(() => {
  let list = Acan.clone(studentList.value)
  if (filters.value.search) {
    list = list.filter((e) => {
      const {
        name: [firstName, lastName],
      } = e
      return [firstName, lastName].some((_) => _.toLowerCase().includes(filters.value.search.toLowerCase()))
    })
  }
  if (!filters.value.enrolCheck && filters.value.importCheck) {
    list = list.filter((e) => e.importEnrol)
  } else if (filters.value.enrolCheck && !filters.value.importCheck) {
    list = list.filter((e) => e.selfEnrol)
  } else if (!filters.value.enrolCheck && !filters.value.importCheck) {
    list = list.filter((e) => !e.selfEnrol && !e.importEnrol)
  }

  return list
})

async function onDelete({user, isBulk = false}) {
  $q.dialog({
    title: `Confirm Delete`,
    message: `Please confirm that you want to delete student(s)? `,
    cancel: true,
  }).onOk(async () => {
    let userIds = []
    if (isBulk) {
      userIds = selectedStudents.value.map((e) => e._id)
    } else {
      userIds = [user._id]
    }
    const findIfApplied = await getAppliedStudents({class: classId.value})
    try {
      await Promise.all(
        userIds.map(async (id) => {
          const applied = findIfApplied?.data?.find((s) => s.student === id)
          if (applied) {
            await deleteOneById(applied._id)
          } else {
            await App.service('students').patch(id, {
              $pull: {subjectClass: classId.value},
            })
          }
        })
      )

      $q.notify({type: 'positive', message: 'Student(s) deleted successfully'})
      await initAllData()
    } catch (error) {
      console.error(error)
      $q.notify({type: 'negative', message: 'Student(s) deleted unsuccessfully'})
    } finally {
      selectedStudents.value = []
    }
  })
}

async function onApplicationClick() {
  $router.push({path: `/account/classes/subject/${classId.value}/self-enroll`, query: {back: $route.path}})
}
</script>

<style lang="scss" scoped>
.rounded-lg {
  border-radius: 0.5rem;
}
</style>
