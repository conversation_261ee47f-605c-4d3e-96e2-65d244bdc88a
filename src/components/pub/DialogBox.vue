<template>
  <q-dialog :maximized="$q.screen.lt.sm" ref="dialogRef" @hide="onDialogHide">
    <q-card class="q-dialog-plugin criteria-dialog column overflow-hidden" :class="{'my-card': $q.screen.gt.xs}">
      <q-toolbar class="">
        <q-toolbar-title>
          {{ title }}
        </q-toolbar-title>
        <q-btn flat icon="close" @click="onDialogOK" />
      </q-toolbar>
      <div class="col overflow-auto q-pa-md">
        <component :is="component" :data="data" :one="one" />
      </div>
    </q-card>
  </q-dialog>
</template>
<style scoped>
.my-card {
  min-width: 750px;
}
</style>
<script setup>
import {useDialogPluginComponent} from 'quasar'
defineEmits([...useDialogPluginComponent.emits])

const {dialogRef, onDialogHide, onDialogOK} = useDialogPluginComponent()

defineProps(['component', 'title', 'data', 'one'])
</script>
