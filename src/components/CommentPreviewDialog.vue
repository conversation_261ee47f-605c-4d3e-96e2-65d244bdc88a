<template>
  <q-dialog :model-value="modelValue" @update:model-value="$emit('update:modelValue', $event)" persistent style="min-height: auto">
    <div
      class="column no-wrap bg-white rounded-lg full-width"
      :class="[$q.screen.lt.sm ? 'comment-dialog-width-mobile' : $q.screen.lt.md ? 'comment-dialog-width-tab' : 'comment-dialog-width']">
      <div class="self-end" style="padding: 0 6px">
        <q-btn flat rounded dense icon="close" @click="onClose" />
      </div>
      <div class="relative-position overflow-auto" style="padding: 2px 16px 8px">
        <div class="row full-width" style="height: 450px">
          <div class="col-12 col-md-8 flex items-center justify-center" style="flex-direction: column; justify-content: flex-start">
            <div style="width: 400px; position: relative">
              <q-img v-if="backgroundHash" class="comment-background-image" :src="hashToUrl(backgroundHash)" style="width: 100%; height: 100%" />
              <div
                v-if="response?.point"
                class="pointer"
                :style="{
                  left: `${response.point.x * 100}%`,
                  top: `${response.point.y * 100}%`,
                  width: `${response.point.width * 1.2}px`,
                  height: `${response.point.height * 1.2}px`,
                  backgroundColor: response.point.color,
                }"></div>
            </div>
            <div style="max-height: 200px; min-width: 30%">
              <CommentPreview :response="response" :isPreview="true" :key="response?._id" />
            </div>
          </div>

          <div class="col-12 col-md-4 q-pl-md" style="max-height: 450px; overflow-y: auto">
            <TeacherComment
              :isMediaDialog="true"
              :sessionTakeawaySnapshotId="sessionTakeawaySnapshotId"
              :comments="comments"
              :response="response"
              :isReadonly="!isEditing" />
          </div>
        </div>
      </div>
    </div>
  </q-dialog>
</template>

<script setup>
import {defineProps, defineEmits} from 'vue'
import CommentPreview from 'src/pages/account/takeaway/components/questions/CommentPreview.vue'
import TeacherComment from 'src/pages/account/takeaway/components/TeacherComment.vue'

defineProps({
  modelValue: {
    type: Boolean,
    required: true,
  },
  response: {
    type: Object,
    default: null,
  },
  backgroundHash: {
    type: String,
    default: '',
  },
  sessionTakeawaySnapshotId: {
    type: String,
    default: '',
  },
  comments: {
    type: Array,
    default: () => [],
  },
  isEditing: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:modelValue'])

const onClose = () => {
  emit('update:modelValue', false)
}
</script>

<style scoped>
.comment-dialog-width {
  max-width: 70%;
  max-height: 95%;
}

.comment-dialog-width-tab {
  max-width: 90%;
  max-height: 97.5%;
}

.comment-dialog-width-mobile {
  max-width: 97.5%;
  max-height: 97.5%;
}

.comment-background-image {
  top: 0;
  left: 0;
  z-index: 1;
  max-height: 250px;
  cursor: pointer;
}

.pointer {
  position: absolute;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  opacity: 0.5;
  z-index: 1;
}

.comments-data-wrapper {
  max-height: 80vh;
  overflow: auto;
  padding-bottom: 40px;
}

@media screen and (max-width: 900px) {
  .add-more-button {
    font-size: 12px;
  }

  .comments-data-wrapper {
    padding-bottom: 20px;
  }
}
</style>
