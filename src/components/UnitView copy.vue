<template>
  <div class="library-detail pc-max">
    <OutlineBox v-if="publish" publish :_id="outline?._id" :outline="outline.outline" />
    <template v-else>
      <div class="column overflow-hidden" v-if="Acan.isEmpty(content)">
        <div class="col text-center q-pa-xl text-grey" v-if="loading">
          <q-spinner-ball color="primary" size="2em" class="full-width" />
        </div>
        <NoData v-else></NoData>
      </div>
      <div v-else>
        <LibrarySlide @change="onSlideChanged" v-if="!publish" :content="slideContent(content)" :poster="parentIsCourse" />
        <!--
        <template v-if="slideCount || isImport">
        </template>
        -->
        <div class="q-pa-md" v-if="!publish">
          <div class="q-pb-md text-h6">{{ content.name }}</div>
          <q-separator></q-separator>
        </div>
        <OverviewPage
          @loaded="onOverviewPageLoaded"
          :isSysPremium="isSysPremium"
          :linkGroup="linkGroup"
          v-if="slide === 0 && parentIsCourse"
          :content="overview"
          :taskOutline="outline"
          :gradeOptions="gradeOptions"></OverviewPage>
        <TaskEditAddonCom v-else :slideId="content.sid" :page="slide" :pageData="content" />

        <!--
        <template v-else-if="slideCount">
          <div class="row flex items-center">
            <q-space></q-space>
            <q-icon
              v-if="currentQuestion?.type"
              size="2rem"
              :color="questionTypes[currentQuestion.type]?.['color']"
              :name="questionTypes[currentQuestion.type]?.['icon']"></q-icon>
            {{ questionTypes[currentQuestion?.type]?.label }}
            <div class="text-h6 q-pa-sm">
              <span v-if="parentIsCourse"> {{ slide }}/{{ slideCount - 1 }} </span>
              <span v-else> {{ slide + 1 }}/{{ slideCount }} </span>
            </div>
          </div>
          <div class="rounded-borders border-1 q-pa-md q-mb-md" v-if="currentQuestion?.type == 'choice'">
            <div v-for="(option, index) in currentQuestion.options" :key="index">
              <div class="row text-body1" :class="{'text-grey': !option.on}">
                {{ alphabat[index] }}: {{ option.val }}
                <q-space></q-space>
                <q-icon name="check" color="primary" v-if="option.on"></q-icon>
              </div>
            </div>
          </div>
          <div class="text-center rounded-borders border-1 q-pa-md q-mb-md" v-if="locked">
            <div class="text-h5 q-pb-md">Please purchase to view the details</div>
            <q-btn rounded class="full-width" color="primary" label="Buy now" no-caps @click="buyFn"></q-btn>
          </div>
          <NoData v-else-if="!currentQuestion?.type" message="No interactive question added."></NoData>
          <div class="rounded-borders border-1 q-my-md" style="max-width: calc(100vw - 32px)">
            <QuestionOutlines :preview="true" :slideID="content.sid" />
          </div>
          <div class="rounded-borders border-1 q-pa-md" v-if="currentMaterial">
            <div class="text-body1 text-weight-medium">Materials</div>
            <q-carousel
              v-model="material"
              transition-prev="slide-right"
              transition-next="slide-left"
              swipeable
              animated
              ref="carousel"
              control-color="primary"
              padding
              height="100px"
              class="full-width rounded-borders">
              <q-carousel-slide
                class="column full-height no-wrap"
                v-for="(item, i) in chunkArray(currentMaterial?.list, $q.screen.lt.sm ? 1 : $q.screen.lt.md ? 2 : 3)"
                :key="i"
                :name="i + 1">
                <div class="row justify-start items-center no-wrap" style="height: 80px; max-height: 80px">
                  <div class="q-px-sm col-12 col-sm-6 col-md-4 full-height" v-for="(child, j) in item" :key="j">
                    <q-img
                      :id="child._id"
                      class="rounded-borders full-height"
                      :src="
                        child.type == 'youtube'
                          ? youtubeIdThumb(child.url)
                          : hashToUrl(child.type == 'image' ? child.key : currentPage.pic) || '/v2/img/no-img.png'
                      ">
                      <div class="flex justify-center custom-caption full-height" :class="{'absolute-top': !$q.fullscreen.isActive}">
                        <div class="flex items-center">
                          <q-btn dense round flat icon="volume_down" v-if="child.type == 'audio'" @click="onVideoOrAudioClick(child)"></q-btn>
                          <q-btn dense round flat icon="picture_as_pdf" :href="hashToUrl(child.key)" target="_blank" v-else-if="child.type == 'pdf'"></q-btn>
                          <q-btn
                            @click="toggleFullscreen(child)"
                            dense
                            round
                            flat
                            :icon="$q.fullscreen.isActive ? 'fullscreen_exit' : 'fullscreen'"
                            v-else-if="child.type == 'image'"></q-btn>
                          <q-btn dense round flat icon="play_circle" v-else @click="onVideoOrAudioClick(child)"></q-btn>
                        </div>
                      </div>
                    </q-img>
                  </div>
                </div>
              </q-carousel-slide>
              <template v-slot:control>
                <q-carousel-control position="left" :offset="[5, 40]" v-if="material != 1">
                  <q-btn size="sm" round color="white" text-color="primary" icon="navigate_before" @click="$refs.carousel.previous()" />
                </q-carousel-control>
                <q-carousel-control
                  position="right"
                  :offset="[5, 40]"
                  v-if="material != Math.ceil(currentMaterial?.list?.length / ($q.screen.lt.sm ? 1 : $q.screen.lt.md ? 2 : 3))">
                  <q-btn size="sm" round color="white" text-color="primary" icon="navigate_next" @click="$refs.carousel.next()" />
                </q-carousel-control>
              </template>
            </q-carousel>
          </div>
        </template>
        -->
        <!--
        <OutlineBox v-else :outline="outline.outline" :assess="outline.assess" />
        -->
      </div>
    </template>
    <q-dialog v-model="videoOrAudioDialog">
      <q-card style="width: 700px; max-width: 80vw">
        <q-card-section>
          <div class="text-h6">{{ currentItem.desc }}</div>
        </q-card-section>

        <q-card-section class="q-pt-none">
          <div class="row full-width">
            <q-video
              v-if="currentItem.type == 'youtube'"
              :src="`https://www.youtube.com/embed/${currentItem.url}?autoplay=1&start=${currentItem.ext?.start}`"
              :ratio="16 / 10"
              class="video-item" />
            <audio v-else-if="currentItem.type == 'audio'" :src="hashToUrl(currentItem.key)" autoplay controls class="video-item" />
            <video v-else :src="hashToUrl(currentItem.key)" autoplay controls class="video-item" />
          </div>
        </q-card-section>

        <q-card-actions align="right" class="bg-white text-teal">
          <q-btn flat label="OK" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup>
/*
  imports
*/
import {ref, watch, onMounted, computed, inject} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {pubStore} from 'stores/pub'
import {curriculumStore} from 'stores/curriculum'
import LibrarySection from 'components/LibrarySection.vue'
import LibrarySlide from 'components/LibrarySlide.vue'
import OverviewPage from 'components/OverviewPage.vue'
import CardList from 'components/CardList.vue'
import LibraryCard from 'components/library/LibraryCard.vue'
import OutlineBox from 'components/OutlineBox.vue'
import ReviewBox from 'components/detail/ReviewBox.vue'
import TaskBasic from 'components/detail/TaskBasic.vue'
//import QuestionOutlines from 'src/pages/addon/QuestionOutlines.vue'
import TaskEditAddonCom from 'pages/com/TaskEditAddonCom.vue'

/*
  consts
*/
const curriculum = curriculumStore()
const gradeOptions = ref(null)
const route = useRoute()
const router = useRouter()
const pub = pubStore()
const content = ref(null)
const {youtubeIdThumb} = Fn
const props = defineProps({
  id: String,
  publish: Boolean,
  linkGroup: Boolean,
  isLib: Boolean,
  isImport: Boolean,
  isSession: Boolean,
  parentIsCourse: Boolean,
  isPremium: Boolean,
  isSysPremium: Boolean,
})
const emit = defineEmits(['change', 'loaded'])
const alphabat = ref(['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'])
const outline = ref({})
const overview = ref({})
const videoOrAudioDialog = ref(false)
const reviewList = ref([])
const loading = ref(false)
const recommendList = ref([])
const questions = ref([])
const materials = ref([])

const questionTypes = inject('QuestionTypes')
watch(
  () => props.id,
  (val) => {
    if (!props.id) return
    content.value = {}
    loadUnit()
  }
)

const slide = ref(0)
const slideCount = ref(0)
const material = ref(1)
const carousel = ref(null)
const locked = ref(false)
const onSlideChanged = (val, slides) => {
  slide.value = val
  slideCount.value = slides.length
  locked.value = slides[val]?.['locked']
  getQuestionsAndMaterialsBySlide(val)
}

const currentQuestion = ref({})
const currentMaterial = ref({})
const currentPage = ref({})
const getQuestionsAndMaterialsBySlide = (slide) => {
  currentPage.value = content.value?.pages?.[props.parentIsCourse ? slide - 1 : slide]
  currentQuestion.value = questions.value?.find((item) => item.page == currentPage.value?._id)
  currentMaterial.value = materials.value?.find((item) => item.page == currentPage.value?._id)
  material.value = 1
}

const currentItem = ref(null)
const onVideoOrAudioClick = (item) => {
  videoOrAudioDialog.value = true
  currentItem.value = item
}

const slideContent = (content) => {
  let pages = []
  const prePage = {
    _id: content?._id,
    pic: content?.cover,
  }

  pages.push(prePage)
  if (content?.pages?.length > 0) {
    pages.push(...content.pages)
  }
  return {
    ...content,
    pages,
  }
}

const buyFn = async () => {
  if (!pub.user._id) return router.push({path: '/login', query: {back: location.pathname + location.search}})
  const rs = await App.service('order').create({link: {id: content.value._id, mode: content.value.mode}})
  if (!rs) $q.notify({type: 'negative', message: 'Bought unsuccessfully'})
  else $q.notify({type: 'positive', message: 'Bought successfully'})
  router.push({path: `/com/${content.value.mode}/edit/${rs.link.newId}`, query: {back: route.fullPath}})
}

const toggleFullscreen = (item) => {
  const target = document.getElementById(item._id)

  $q.fullscreen
    .toggle(target)
    .then(() => {
      // success!
    })
    .catch((err) => {
      alert(err)
      // uh, oh, error!!
      // console.error(err)
    })
}
const chunkArray = (array, chunkSize) => {
  const chunkedArray = []
  let i = 0
  while (i < array?.length) {
    chunkedArray.push(array.slice(i, i + chunkSize))
    i += chunkSize
  }
  return chunkedArray
}

async function loadUnit() {
  const id = props.id || route.params.id
  if (!id) return
  loading.value = true
  const query = {}
  let links = null
  let rs = await App.service(props.isSession ? 'session' : props.isPremium ? 'service-auth' : 'unit').get(id, {query})
  if (!rs) {
    loading.value = false
    return
  }

  if (props.isPremium) {
    links = rs.linkSnapshot
    rs = rs.unitSnapshot
  }
  if (pub.user._id) {
    if (props.isSession) {
      outline.value = rs.task?.outline
      gradeOptions.value = await curriculum.gradeOptions(rs.school || rs.uid)
    } else {
      gradeOptions.value = await curriculum.gradeOptions(pub.user.school || pub.user._id)
      outline.value = (await App.service('task-outline').get('byRid', {query: {_id: rs._id}})) || {}
    }
  }
  loading.value = false
  content.value = rs
  overview.value = props.isSession ? rs.task : rs

  if (props.isSession || props.isLib) {
    questions.value = content.value?.questions
    materials.value = content.value?.materials
  } else if (!props.isSession && rs.sid) {
    questions.value = await App.service('questions').get('list', {query: {id: rs.sid}})
    materials.value = await App.service('materials').get('list', {query: {id: rs.sid}})
  }
  if (props.publish) {
    emit('loaded', childIdsWithNullBloom())
  }
  if (props.isPremium) {
    let liveCount = 0
    for (const key in links) {
      if (links[key].sessionType === 'live') {
        liveCount++
      }
    }
    emit('loaded', liveCount)
  }
}

const onOverviewPageLoaded = (val) => {
  emit('loaded', val)
}

const childIdsWithNullBloom = () => {
  if (!outline.value?.outline?.data) return []
  const findIdsWithNullBloom = (obj) => {
    const results = []
    if (obj._id && (obj.bloom === null || obj.bloom === undefined) && (!obj.child || obj.child.length === 0)) {
      results.push(obj._id)
    }
    if (obj.custom && obj.custom.length > 0) {
      obj.custom.forEach((customObj) => {
        if (customObj._id && (obj.bloom === null || obj.bloom === undefined)) {
          results.push(customObj._id)
        }
      })
    }
    if (obj.child && obj.child.length > 0) {
      obj.child.forEach((child) => {
        results.push(...findIdsWithNullBloom(child))
      })
    }
    return results
  }

  const results = []
  Object.values(outline.value.outline.data).forEach((value) => {
    results.push(...findIdsWithNullBloom(value))
  })

  return results
}

watch(
  () => route.path,
  () => {
    if (route.path.indexOf('/task/') !== 0) return
    // loadUnit()
  }
)
onMounted(loadUnit)
</script>
<style lang="sass">
.border-1
  border: 1px solid $grey-3
.video-item
  width:100%
</style>
