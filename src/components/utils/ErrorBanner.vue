<template>
  <Transition>
    <q-card v-show="modelValue" class="wrapper q-pa-md bg-red flex justify-between items-center full-width rounded-none" flat>
      <div class="flex items-center q-gutter-sm">
        <q-icon name="error" color="red-1" size="sm" />
        <div class="text-grey-1">{{ message }}</div>
      </div>
      <q-btn dense rounded flat size="sm" icon="close" @click.stop="() => emit('update:modelValue', false)" />
    </q-card>
  </Transition>
</template>

<script setup>
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  message: {
    type: String,
    defaut: 'Error occured!',
  },
})
const emit = defineEmits(['update:modelValue'])
</script>

<style lang="scss" scoped>
.v-enter-active,
.v-leave-active {
  transition: opacity 0.5s ease;
}

.v-enter-from,
.v-leave-to {
  opacity: 0;
}
</style>
