<template>
  <q-item clickable dense class="q-my-md q-py-sm rounded-borders" @click="() => onYoutubeClick(item?.youtube)">
    <q-item-section>
      <q-img :src="getYoutuberImage(item.youtube)" />
      <div class="flex items-center q-mt-sm">
        <div class="q-mr-sm" style="max-width: calc(100% - 2rem)">{{ item?.label }}</div>
        <q-icon name="o_open_in_new" class="text-grey-6" size="sm" />
      </div>
      <div v-if="item?.prompt" class="rounded-sm bg-teal-1 q-px-xs">{{ item.prompt }}</div>
    </q-item-section>
  </q-item>
</template>

<script setup>
import {getYoutuberImage, onYoutubeClick} from 'src/boot/const.js'

defineProps({
  item: {
    type: Object,
    require: true,
  },
})
</script>
