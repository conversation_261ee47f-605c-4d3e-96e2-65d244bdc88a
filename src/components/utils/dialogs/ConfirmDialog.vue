<template>
  <q-dialog ref="dialogRef" @hide="onDialogHide" class="rounded-xl z-max absolute-position">
    <div class="bg-teal-1 column no-wrap q-gutter-sm q-pa-lg justify-between" style="min-width: 320px; max-width: 420px; width: 50%; border-radius: 0.75rem">
      <div class="text-subtitle1 text-bold text-preline">{{ title }}</div>
      <div class="text-grey-9 text-preline">{{ message }}</div>

      <q-separator class="q-my-md" />
      <div class="row justify-between full-width">
        <q-btn
          v-if="hasCancelButton"
          class="col-5 q-ml-sm q-px-md text-teal"
          style="border: 1px solid #999"
          rounded
          dense
          flat
          no-caps
          :label="cancelButtonLabel"
          @click="onDialogCancel" />
        <q-btn class="col-5 q-ml-sm q-px-md bg-teal-4" rounded dense flat color="grey-2" no-caps :label="okButtonLabel" @click="onOKClick" />
      </div>
    </div>
  </q-dialog>
</template>

<script setup>
import {useDialogPluginComponent} from 'quasar'

defineProps({
  // ...your custom props
  title: {
    type: String,
    default: 'Confirm',
  },
  message: {
    type: String,
    default: 'Are you sure you want to delete this?',
  },
  okButtonLabel: {
    type: String,
    default: 'Yes',
  },
  cancelButtonLabel: {
    type: String,
    default: 'Cancel',
  },
  hasCancelButton: {
    type: Boolean,
    default: true,
  },
})

defineEmits([...useDialogPluginComponent.emits])

const {dialogRef, onDialogHide, onDialogOK, onDialogCancel} = useDialogPluginComponent()

function onOKClick() {
  onDialogOK()
}
</script>

<style scoped>
.text-preline {
  white-space: pre-line;
}
</style>
