<template>
  <q-dialog ref="dialogRef" @hide="onDialogHide" class="rounded-xl z-max absolute-position">
    <div class="bg-teal-1 column no-wrap q-gutter-sm q-pa-lg justify-between" style="min-width: 320px; max-width: 420px; width: 50%; border-radius: 0.75rem">
      <div v-if="title" class="text-subtitle1 text-bold">{{ title }}</div>
      <div v-if="message" class="text-grey-9">{{ message }}</div>

      <q-separator class="q-my-md" />
      <q-btn class="q-ml-sm q-px-md bg-teal-4 full-width" rounded dense flat color="grey-2" no-caps :label="okButtonLabel" @click="onOKClick" />
    </div>
  </q-dialog>
</template>

<script setup>
import {useDialogPluginComponent} from 'quasar'

defineProps({
  // ...your custom props
  title: {
    type: String,
    default: '',
  },
  message: {
    type: String,
    default: 'Are you sure you want to delete this?',
  },
  okButtonLabel: {
    type: String,
    default: 'I got it',
  },
})

defineEmits([...useDialogPluginComponent.emits])

const {dialogRef, onDialogHide, onDialogOK} = useDialogPluginComponent()

function onOKClick() {
  onDialogOK()
}
</script>
