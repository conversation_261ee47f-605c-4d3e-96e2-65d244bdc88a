<template>
  <q-card class="rounded-md q-mb-md overflow-hidden">
    <q-expansion-item v-model="isExpanded" expand-separator>
      <template v-slot:header>
        <q-item-section>
          <div class="flex items-center">
            <q-icon name="img:/v2/img/homepage/icon-youtube-logo.png" size="lg" />
            <div class="text-h6 q-ml-sm">Youtube</div>
          </div>
        </q-item-section>
      </template>

      <div v-if="slideGroups?.length">
        <q-carousel
          v-model="slide"
          transition-prev="scale"
          transition-next="scale"
          swipeable
          animated
          navigation
          padding
          arrows
          height="300px"
          class="shadow-1 rounded-borders">
          <q-carousel-slide v-for="group in slideGroups" :key="group.key" :name="group.key" class="column no-wrap flex-center">
            <div class="row items-center justify-center">
              <YoutubeItem v-for="item in group.child" :item="item" :key="item.youtube" class="q-mx-sm" :class="[getColClass()]" />
            </div>
          </q-carousel-slide>
        </q-carousel>
      </div>
    </q-expansion-item>
  </q-card>
</template>

<script setup>
import {ref, onMounted, onUnmounted} from 'vue'
import {useDebounceFn} from '@vueuse/core'
import YoutubeItem from 'src/components/utils/YoutubeItem.vue'

// Note: Please follow the structure below.
const arr = [
  {label: 'Join Classcipe Services & Expand your teaching potential', youtube: 'TJiem-W91UU'},
  {label: 'Welcome to Classcipe Teacher & Service Verification', youtube: '9WiWgSt3Pp4'},
  {label: 'The application process for premium workshop', youtube: 'X8ZjiT4cJrw'},
  {label: 'How to verify under 1v1 mentoring - Academic', youtube: 'ySGx5SZY0Bc'},
  {label: "How to verify content under 'Premium Course Material'", youtube: '6nN92Ywpp28'},
  {label: 'Maximize Your Impact: 1v1 Mentoring & Education Consulting', youtube: 'TLOIr_h1_d0'},
]

const props = defineProps({
  list: {
    type: Array,
    default: () => [],
  },
})

const isExpanded = ref(false)
const slides = ref([])
const slideGroups = ref([])
const slide = ref('')
const currentGroupNumber = ref(1)

onMounted(() => {
  if (props.list?.length) {
    slides.value = props.list
  } else {
    slides.value = arr
  }
  calculateGroup()
  window.addEventListener('resize', calculateGroupDebounce)
})
onUnmounted(() => {
  window.removeEventListener('resize', calculateGroupDebounce)
})

const calculateGroupDebounce = useDebounceFn((...args) => calculateGroup(...args), 200)
function calculateGroup() {
  const width = window.innerWidth
  if (width > 1000) groupBy3()
  else if (width > 600) groupBy2()
  else groupBy1()
}

function groupBy3() {
  slideGroups.value = []
  for (let i = 0; i < slides.value.length; i += 3) {
    const first = slides.value?.[i] || null
    const second = slides.value?.[i + 1] || null
    const third = slides.value?.[i + 2] || null
    const child = []
    if (first) child.push(first)
    if (second) child.push(second)
    if (third) child.push(third)
    const key = `group-${i}`
    slideGroups.value.push({key, child})
  }
  currentGroupNumber.value = 3
  slide.value = slideGroups.value?.[0]?.key || ''
}
function groupBy2() {
  slideGroups.value = []
  for (let i = 0; i < slides.value.length; i += 2) {
    const first = slides.value?.[i] || null
    const second = slides.value?.[i + 1] || null
    const child = []
    if (first) child.push(first)
    if (second) child.push(second)
    const key = `group-${i}`
    slideGroups.value.push({key, child})
  }
  currentGroupNumber.value = 2
  slide.value = slideGroups.value?.[0]?.key || ''
}
function groupBy1() {
  slideGroups.value = []
  for (let i = 0; i < slides.value.length; i += 1) {
    const first = slides.value?.[i] || null
    const child = []
    if (first) child.push(first)
    const key = `group-${i}`
    slideGroups.value.push({key, child})
  }
  currentGroupNumber.value = 1
  slide.value = slideGroups.value?.[0]?.key || ''
}

function getColClass() {
  if (currentGroupNumber.value === 3) return 'col-3'
  if (currentGroupNumber.value === 2) return 'col-5'
  if (currentGroupNumber.value === 1) return 'col-11'
  return 'col-12'
}
</script>
