<template>
  <div class="full" v-if="item">
    <div class="full">
      <img :src="hashToUrl(item)" class="" />
    </div>
  </div>
</template>

<script setup>
defineProps({
  item: {
    type: Object,
    default: () => ({}),
  },
})
</script>

<style scoped lang="scss">
// line-height: 0;
// overflow: hidden;
.full {
  width: 100%;
  height: 100%;
  position: relative;
  text-align: center;
  overflow: hidden;
}
.full img {
  height: 100%;
  object-fit: contain;
  max-width: 100%;
}
.icon-button {
  padding: 1rem 2rem;
  border-radius: 0.5rem;
  .icon-play {
    color: #eee;
  }
  &:hover {
    background: rgba(0, 0, 0, 0.5);
    .icon-play {
      color: #fff;
      transform: scale(1.2);
    }
  }
}
</style>
