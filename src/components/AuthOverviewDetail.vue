<template>
  <q-page :class="{'q-pt-md': !isImport}">
    <OnePage v-if="isTool" :isPreview="true" :assessmentToolOneId="content._id"></OnePage>
    <template v-else>
      <template v-if="!loading && (!Acan.isEmpty(defaultTpl) || outline)">
        <div class="col-12" v-if="isImport">
          <q-img
            spinner-color="white"
            :fit="content.cover ? 'cover' : 'contain'"
            class="fit rounded-borders"
            :class="{'rounded-borders': $q.screen.gt.md}"
            :ratio="16 / 9"
            :src="content.cover || '/v2/img/avatar.png'">
          </q-img>
        </div>
        <div class="row q-mt-md" v-if="!Acan.isEmpty(defaultTpl)">
          <div class="col-12 q-mb-md" :class="{'q-pa-md shadow-3 rounded-borders-md': isStudent}" v-for="(item, lidx) in defaultTpl" :key="lidx">
            <div class="text-h6" :class="{'text-teal-6': !isStudent}">{{ item.name }}</div>
            <div class="inline-block" v-if="item.type == 'choice-mark' || item.type == 'choice'">
              <div v-for="(choice, cidx) in rearrangeObject(item.value)" :key="cidx">
                <div class="text-weight-medium q-mt-sm">
                  <span v-html="convertToLink(choice.value)"></span>
                </div>
                <div class="q-pl-md">
                  <span v-html="convertToLink(choice.mark)"></span>
                </div>
                <div v-for="(item, iidx) in choice.child" :key="iidx">
                  <div class="q-pl-md">
                    <span v-html="convertToLink(item.value)"></span>
                  </div>
                  <div class="q-pl-lg">
                    <span v-html="convertToLink(item.mark)"></span>
                  </div>
                </div>
              </div>
            </div>
            <div class="inline-block" v-else-if="['string', 'number'].includes(typeof item.value)">
              <span v-html="convertToLink(item.value)"></span>
            </div>
            <div class="inline-block" v-else v-for="(b, bi) in item.value" :key="bi">
              <template v-if="item.code == 'words'">
                <q-chip class="text-weight-medium" :ripple="false" size="12px" color="teal-1" text-color="primary">{{ b }}</q-chip>
              </template>
              <span v-else v-html="convertToLink(b)"></span>
            </div>
          </div>
          <div class="col-12" v-for="(item, lidx) in defaultTpl" :key="lidx">
            <div v-if="((defaultTpl.length > 3 && lidx == 3) || (defaultTpl.length < 4 && lidx + 1 == defaultTpl.length)) && outline">
              <OutlinePreview :isStudent="isStudent" :outline="outline" :gradeOptions="gradeOptions" />
            </div>
          </div>
        </div>
        <OutlinePreview :isStudent="isStudent" v-else-if="outline" :outline="outline" :gradeOptions="gradeOptions" />
      </template>
      <template v-else>
        <div v-if="loading" class="text-center q-pa-xl text-grey">
          <q-spinner-ball color="primary" size="2em" class="full-width" />
        </div>
        <NoData v-else />
      </template>
    </template>
  </q-page>
</template>

<script setup>
/*
  imports
*/
import {ref, onMounted} from 'vue'
import {curriculumStore} from 'stores/curriculum'
import {pubStore} from 'stores/pub'
import OutlinePreview from 'components/OutlinePreview.vue'
// import AccountMenu from 'components/AccountMenu.vue'
// import PromptPage from 'components/PromptPage.vue'
import OnePage from 'src/pages/account/assessment-tool/OnePage.vue'
// import useUnitPlanTemplate from 'src/composables/account/unit-plan-template/useUnitPlanTemplate'

const props = defineProps(['authId'])

/*
  consts
*/
const curriculum = curriculumStore()
// const {mergeTemplateDataByMode} = useUnitPlanTemplate()
const content = ref({})
const outline = ref(null)
const loading = ref(false)
const gradeOptions = ref(null)
const pub = pubStore()
const defaultTpl = ref({})
const isTool = ref(false)

const convertToLink = (text) => {
  const urlRegex = /(https?:\/\/[^\s]+)/g
  const newText = text.toString()?.replace(urlRegex, '<a href="$1" target="_blank">$1</a>')
  return newText.split('\n').join('<br/>')
}

const rearrangeObject = (obj) => {
  for (let key in obj) {
    if (!Array.isArray(obj[key])) {
      delete obj[key]
    }
  }
  //moves items ahead if their keys do not contain a semicolon
  const sortedObj = Object.fromEntries(
    Object.entries(obj).sort(([keyA], [keyB]) => {
      if (keyA.includes(':') && !keyB.includes(':')) {
        return 1
      } else if (!keyA.includes(':') && keyB.includes(':')) {
        return -1
      } else {
        return 0
      }
    })
  )
  const item = []
  for (const [index, [, value]] of Object.entries(Object.entries(sortedObj))) {
    if (!Array.isArray(value)) continue
    if (index == 0) {
      item.push(...value)
    } else if (item[index - 1]) {
      item[index - 1]['child'] = value
    }
  }
  return item
}

const getServiceAuth = async () => {
  await App.service('service-auth')
    .get(props.authId)
    .then((res) => {
      if (!res?.unitSnapshot) return
      content.value = res.unitSnapshot
      outline.value = content.value.outline
    })
}

const main = async () => {
  loading.value = true
  await getServiceAuth()
  console.log(content.value)

  if (content.value.mode == 'tool') {
    isTool.value = true
    loading.value = false
    return
  }

  if (pub.user._id) {
    gradeOptions.value = await curriculum.gradeOptions(pub.user?.school || pub.user._id)
  }
  loading.value = false
}
onMounted(main)
</script>
