<template>
  <div>
    <div class="row">
      <!-- Left part -->
      <div class="col q-ma-md" style="min-width: 0">
        <q-tab-panels v-model="tab" animated swipeable vertical transition-prev="jump-up" transition-next="jump-up" style="z-index: 9; width: 100%">
          <q-tab-panel v-for="(section, index) in currentTaskSections" :key="section._id" :name="section._id" style="padding: 0; margin: 0">
            <q-card class="q-px-md q-py-sm rounded-xl" style="border: 1px solid #6b6b6b; min-height: 350px">
              <div class="row items-center q-mt-sm q-pl-na q-gutter-x-na">
                <q-space />
                <div
                  v-if="section.status !== 'completed' && section.complain && section.complainStatus == 'pending'"
                  style="color: #f55c44; font-weight: bold; font-size: 12px">
                  Complain Pending
                </div>
                <div class="text-right q-ml-sm text-teal">
                  {{ `${index + 1}/${currentTaskSections.length}` }}
                </div>
              </div>

              <div class="flex text-h6 q-my-lg items-center">
                {{ section.name }}
              </div>

              <q-card
                ><q-expansion-item
                  expand-separator
                  default-opened
                  header-class="flex items-center q-ml-none q-pl-none justify-between text-subtitle1 text-weight-medium "
                  content-class="q-pa-none"
                  class="q-ma-none q-pa-none">
                  <template #header>
                    <div>
                      <span>Prompt</span>
                    </div>
                  </template>

                  <pre style="white-space: pre-wrap; word-wrap: break-word; max-height: 200px; overflow-y: auto; min-height: 100px">{{ section.prompt }}</pre>
                </q-expansion-item></q-card
              >
            </q-card>
          </q-tab-panel>
        </q-tab-panels>
      </div>

      <!-- Right part -->
      <div style="width: 180px">
        <div style="position: relative; z-index: 999; width: 100%; height: 100%">
          <div class="flex justify-end text-bold text-teal q-mr-xl text-subtitle1 q-mb-sm">Total {{ currentTaskSections.length }}</div>
          <div style="max-height: 500px; overflow-y: auto">
            <div style="position: absolute; left: 7px; border-left: 2px solid grey; height: 100%; max-height: 500px"></div>
            <q-tabs v-model="tab" vertical indicator-color="transparent" stretch no-caps>
              <q-tab v-for="(section, index) in currentTaskSections" :key="index" :name="section._id" class="q-py-sm q-mb-lg">
                <div class="row">
                  <span style="position: absolute; left: -18px; top: 12px; z-index: 999">
                    <q-avatar color="teal" text-color="white" size="16px">
                      {{ index + 1 }}
                    </q-avatar>
                  </span>
                  <div style="width: 3px" :style="section._id === tab ? 'background-color: #26A69A' : ''"></div>
                  <div class="row justify-between q-py-sm" style="background-color: #26a69a1a; width: 140px">
                    <div class="text-caption">
                      &nbsp;<q-tooltip v-if="section.name.length > 8">{{ section.name }}</q-tooltip>
                      {{ truncate(section.name) }}
                    </div>
                    <div class="text-right text-caption text-bold text-teal">USD {{ section.salesPrice }}&nbsp;</div>
                  </div>
                </div>
              </q-tab>
            </q-tabs>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import {ref, computed, watch, onMounted} from 'vue'
import {useRoute, useRouter} from 'vue-router'

const route = useRoute()
const router = useRouter()

const props = defineProps({
  currentTaskSections: Array,
})

const tab = ref(props.currentTaskSections[0]?._id || '')
const defaultSection = computed(() => props.currentTaskSections?.[0])
const isCollapsed = ref(false)

function toggle() {
  isCollapsed.value = !isCollapsed.value
}

watch(
  defaultSection,
  (newVal) => {
    if (newVal) tab.value = newVal._id
  },
  {immediate: true}
)

function truncate(text, length = 8) {
  return text.length > length ? text.slice(0, length) + '…' : text
}
</script>

<style scoped>
.q-tabs .q-tab__content {
  width: calc(100% - 20px) !important;
  align-items: stretch;
}
.q-tabs--left .q-tab__indicator {
  left: 0 !important;
  right: auto !important;
  width: 3px !important;
}
::v-deep(.q-splitter__separator) {
  width: 3px !important;
  z-index: 1 !important;
  pointer-events: none;
  position: relative;
  left: 10px;
}
pre {
  all: unset;
  white-space: pre-wrap;
}
</style>
