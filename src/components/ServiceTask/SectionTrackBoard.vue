<template>
  <q-card class="q-pa-md full-width" :elevation="2" style="box-shadow: 0px 4px 4px 0px #00000040">
    <div class="q-pa-md row no-wrap items-center">
      <q-img :src="hashToUrl(section?.taskDetails?.cover)" style="width: 222px; height: 120px; border-radius: 8px" class="q-mr-md" />
      <div class="col" style="">
        <div class="q-mt-xs">
          <span class="text-amber-10" style="font-size: 14px">{{ section.taskDetails?.name }}</span>
          <span class="text-teal-5 q-ml-sm" style="font-size: 16px; font-weight: 500">{{
            servicePackage.mentoringTypeList.find((e) => e.value === 'steam')?.label
          }}</span>
        </div>
        <div class="row items-center justify-between">
          <div class="text-h5 text-bold">{{ section.name }}</div>
        </div>
        <div class="row justify-end q-mt-xl q-mr-xl">
          <div class="text-teal-4 text-bold" style="font-size: 24px">
            <div class="row items-center q-gutter-xs">
              <span class="text-bold">USD {{ sectionTracking.creditedPoints }}</span>
              <q-icon v-if="sectionTracking.cancelledByServicer" name="info" color="grey-6" size="24px">
                <q-tooltip anchor="top middle" self="bottom middle" fit style="font-size: medium">
                  Task cancelled by you<br />
                  Income of this {{ sectionTracking.status === 'completed' ? 'completed' : 'incompleted' }} section: USD {{ sectionTracking.creditedPoints }}/{{
                    section.costPrice
                  }}
                </q-tooltip>
              </q-icon>
              <q-icon v-else-if="sectionTracking.status === 'cancelled'" name="info" color="grey-6" size="24px">
                <q-tooltip anchor="top middle" self="bottom middle" fit style="font-size: medium">
                  Task cancelled by participant<br />
                  Compensation of this incompleted section: USD {{ sectionTracking.creditedPoints }}/{{ section.costPrice }}
                </q-tooltip>
              </q-icon>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="sectionTracking.status === 'completed'" class="q-pl-md text-body2 text-grey-9 q-ma-none">
      Completed on {{ formatDate(sectionTracking.completedTime) }}
    </div>
    <div v-else class="q-pl-md text-body2 text-grey-9 q-ma-none">Cancelled on {{ formatDate(sectionTracking.cancelledTime) }}</div>
  </q-card>
</template>

<script setup>
import {servicePackageStore} from 'stores/service-package'
import {computed, onMounted} from 'vue'

const servicePackage = servicePackageStore()

const props = defineProps({
  sectionTracking: Array,
})

const section = computed(() => props.sectionTracking?.sectionSnapshot)

function formatDate(isoString) {
  const date = new Date(isoString)

  const day = String(date.getDate()).padStart(2, '0')
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const year = date.getFullYear()

  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')

  return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`
}

onMounted(() => {
  servicePackage.init()
})
</script>
