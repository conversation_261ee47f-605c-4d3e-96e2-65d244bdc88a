<template>
  <q-dialog v-model="showDialog" persistent>
    {{ console.log('qwerty', serviceTask.currentSection) }}
    <q-card class="q-pa-md" style="width: 450px; max-width: 90vw; border-radius: 12px; background-color: #e0f2f1">
      <!-- Title Section -->
      <q-card-section class="q-pa-na">
        <div class="text-subtitle1 q-mb-md"><strong>Reason for Complaint</strong></div>
        <div class="text-body2 q-mb-na">
          {{ complain?.evidencesStudent?.[0]?.content }}
        </div>
      </q-card-section>

      <!-- Evidence preview Section -->
      {{ console.log('uploaded evidence', files) }}

      <q-card-section v-if="files?.length">
        <div class="text-subtitle2 q-my-md">Evidences</div>
        <div class="row q-gutter-sm">
          <div v-for="(file, index) in files" :key="index" style="max-width: fit-content; border-radius: 10px">
            <div class="q-pa-xs" style="display: inline-block; border-radius: 10px">
              <div style="position: relative; display: inline-block">
                <q-img
                  v-if="file.mime.includes('image')"
                  :src="hashToUrl(file.hash)"
                  style="height: 45px; width: 60px; object-fit: cover; border-radius: 8px" />
                <q-video
                  v-else
                  :src="hashToUrl(file.hash)"
                  style="width: 60px; height: 45px; border-radius: 10px; object-fit: cover"
                  :controls="true"
                  autoplay="false " />
              </div>
            </div>
          </div>
        </div>
      </q-card-section>
      <div class="q-mb-md" style="height: 1px; background-color: #ccc"></div>
      <!-- Actions button -->
      <q-card-actions class="q-pa-none q-mt-lg">
        <div class="row q-col-gutter-sm full-width">
          <q-btn flat label="Cancel" color="primary" class="full-width" style="border-radius: 100px; border: 1px solid black" @click="showDialog = false" />
        </div>
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import {ref, watch, computed, onMounted} from 'vue'
import {serviceTaskStore} from 'stores/serviceTask'
const emit = defineEmits(['close'])

const props = defineProps({
  complain: {
    type: Object,
    required: true,
    default: () => ({}),
  },
})

const showDialog = ref(true)
const serviceTask = serviceTaskStore()
const files = computed(() => props.complain?.evidencesStudent?.[0]?.attachments)

watch(showDialog, (val) => {
  if (!val) emit('close')
})
</script>

<style scoped>
.absolute-top-right {
  position: absolute;
  top: -8px;
  right: -8px;
  z-index: 1;
}
</style>
