<template>
  <q-dialog v-model="showDialog" persistent>
    {{ console.log('qwerty', serviceTask.currentSection) }}
    <q-card class="q-pa-md" style="width: 450px; max-width: 90vw; border-radius: 12px; background-color: #e0f2f1">
      <!-- angry emoji -->

      <div class="q-pa-none text-center coloumn">
        <q-icon name="sentiment_very_dissatisfied" color="negative" size="64px" />
        <div class="text-h6 text-negative q-mt-sm">You have received a complaint</div>
      </div>

      <!-- Title Section -->
      <q-card-section class="q-pa-na">
        <div class="text-subtitle1 q-mb-md"><strong>Reason for Complaint</strong></div>
        <div class="text-body2 q-mb-na">
          {{ props.complain?.evidencesStudent?.[0].content }}
        </div>
      </q-card-section>

      <!-- Evidence preview Section -->
      <q-card-section v-if="studentfiles?.length">
        <div class="text-subtitle2 q-my-md">Evidences</div>
        <div class="row q-gutter-sm">
          <div v-for="(file, index) in studentfiles" :key="index" style="max-width: fit-content; border-radius: 10px">
            <div class="q-pa-xs" style="display: inline-block; border-radius: 10px">
              <div style="position: relative; display: inline-block">
                <q-img
                  v-if="file.mime.includes('image')"
                  :src="hashToUrl(file.hash)"
                  style="width: 60px; height: 45px; object-fit: cover; border-radius: 10px; overflow: hidden" />
                <q-video v-else :src="hashToUrl(file.hash)" style="width: 60px; height: 45px; border-radius: 10px" :controls="true" autoplay="false" />
              </div>
            </div>
          </div>
        </div>
      </q-card-section>
      <div class="q-mb-md" style="height: 1px; background-color: #ccc"></div>
      <q-card-section>
        <div class="text-subtitle1 q-mb-md"><strong>Your response*</strong></div>
        <RecordInput v-model="AppealReason" :counter="true" :maxlength="1000" outlined placeholder="Please enter the reason" />
      </q-card-section>
      <q-card-section v-if="Appealfiles?.length" class="q-pt-none">
        <div class="text-subtitle2 q-my-sm">Appealing evidences</div>
        <div class="row q-gutter-sm">
          {{ console.log('fhruf', Appealfiles) }}
          <div v-for="(file, index) in Appealfiles" :key="index" style="border-radius: 10px">
            <div class="q-pa-xs" style="display: inline-block; border-radius: 10px">
              <div style="position: relative; display: inline-block">
                <q-img
                  v-if="file.mime.includes('image')"
                  :src="hashToUrl(file.cover)"
                  style="width: 60px; height: 45px; border-radius: 10px; overflow: hidden" />
                <q-video v-else :src="hashToUrl(file.cover)" style="width: 60px; height: 45px; border-radius: 10px" :controls="true" autoplay="false" />
                <q-btn round dense color="negative" icon="close" size="10px" class="absolute" style="top: -6px; right: -6px" @click="removeFile(index)" />
              </div>
            </div>
          </div>
        </div>
      </q-card-section>

      <div class="row items-center q-gutter-sm">
        <!-- Upload Button -->
        <q-btn
          flat
          label="Upload"
          icon="file_upload"
          text-color="primary"
          @click="handleFileChange"
          style="border: 1px solid #d1d5db; border-radius: 8px; padding: 8px 16px; width: 45%; text-transform: none" />

        <!-- Record Audio Button -->
        <q-btn
          flat
          label="Record audio"
          icon="spatial_audio_off"
          icon-pack="material-icons-sharp"
          text-color="primary"
          style="border: 1px solid #d1d5db; border-radius: 8px; padding: 8px 16px; width: 45%; text-transform: none" />
      </div>
      <div class="q-my-lg" style="height: 1px; background-color: #ccc"></div>
      <!-- Actions button -->
      <q-card-actions class="q-pa-none">
        <div class="row q-col-gutter-sm full-width">
          <div class="col-6">
            <q-btn label="Confirm" color="primary" class="full-width" style="border-radius: 100px" @click="submitAppeal" />
          </div>
          <div class="col-6">
            <q-btn flat label="Cancel" color="primary" class="full-width" style="border-radius: 100px; border: 1px solid black" @click="showDialog = false" />
          </div>
        </div>
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import {ref, watch, computed} from 'vue'
import {serviceTaskStore} from 'stores/serviceTask'
import RecordInput from 'src/components/utils/inputs/RecordInput.vue'
const emit = defineEmits(['close'])

const props = defineProps({
  complain: {
    type: Object,
    required: true,
  },
  setComplain: {
    type: Function,
    required: true,
  },
})

const Appealfiles = ref([])
const AppealReason = ref('')
const showDialog = ref(true)
const serviceTask = serviceTaskStore()
const studentfiles = computed(() => props.complain?.evidencesStudent?.[0].attachments)

async function handleFileChange() {
  const rs = await Fn.fileUpLoadUiX('image/*,video/*,audio/*,.pdf')
  if (!rs) {
    return
  }
  console.log('value of rs:', rs)
  if (rs.message) {
    return $q.notify({type: 'negative', message: rs.message})
  }

  // files.value.push({
  //   cover: rs._id, // your “hash”
  //   mime: rs.mime,
  //   filename: rs.originalName || rs.filename || 'unknown',
  // })

  Appealfiles.value.push({cover: rs._id, mime: rs.mime, filename: rs.originalName || rs.filename || 'unknown'})
}

function removeFile(index) {
  Appealfiles.value.splice(index, 1)
}

async function submitAppeal() {
  if (!AppealReason.value.trim()) {
    $q.notify({type: 'negative', message: 'Please provide Appeal reason'})
    return
  }
  const data = {
    tags: 'Associated task complains',
    evidencesTeacher: [
      {
        content: AppealReason.value,
        attachments: Appealfiles.value?.map((f) => ({
          filename: f.filename,
          mime: f.mime,
          hash: f.cover,
        })),
      },
    ],
  }
  await App.service('teaching-accident').patch(props.complain._id, data)
  $q.notify({type: 'positive', message: 'Appeal submitted successfully'})
  props.setComplain(data)
  emit('close')
}

watch(showDialog, (val) => {
  if (!val) emit('close')
})
</script>

<style scoped>
.absolute-top-right {
  position: absolute;
  top: -8px;
  right: -8px;
  z-index: 1;
}
</style>
