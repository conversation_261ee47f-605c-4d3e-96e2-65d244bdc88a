<template>
  <q-dialog v-model="showDialog" persistent>
    {{ console.log('fgbfihg', serviceTask.currentSection) }}
    {{ console.log('vnibnt', complain) }}
    <q-card class="q-pa-md" style="width: 450px; max-width: 90vw; border-radius: 12px; background-color: #e0f2f1">
      <!-- classcipe reason -->
      <div v-if="complain?.status === 'approved'">
        <q-card-section class="q-pa-na">
          <div class="text-subtitle1 q-mb-md"><strong>Reason of approval by Classcipe</strong></div>
          <div class="text-body2 q-mb-na">{{ complain?.checkReason }}</div>
          <div class="q-my-md" style="height: 1px; background-color: #ccc"></div>
        </q-card-section>
      </div>
      <div v-else>
        <div class="q-pa-none text-center coloumn">
          <q-icon name="sentiment_very_dissatisfied" color="negative" size="64px" />
          <div class="text-h6 text-negative q-mt-sm">You have received a complaint</div>
        </div>
      </div>

      <!-- Title Section -->
      <q-card-section class="q-pa-na">
        <div class="text-subtitle1 q-mb-md"><strong>Reason provided by student</strong></div>
        <div class="text-body2 q-mb-na">
          {{ studentReason }}
        </div>
      </q-card-section>

      <!-- Evidence  Section -->
      <q-card-section v-if="files?.length">
        <div class="text-subtitle2 q-my-md">Evidences provided by the student</div>
        <div class="row q-gutter-sm">
          <div v-for="(file, index) in files" :key="index" style="max-width: fit-content; border-radius: 10px">
            <div class="q-pa-xs" style="display: inline-block; border-radius: 10px">
              <div style="position: relative; display: inline-block">
                <q-img
                  v-if="file.mime.includes('image')"
                  :src="hashToUrl(file.hash)"
                  style="width: 60px; height: 45px; object-fit: cover; border-radius: 10px; overflow: hidden" />
                <q-video v-else :src="hashToUrl(file.hash)" style="width: 60px; height: 45px; border-radius: 10px" :controls="true" autoplay="false" />
              </div>
            </div>
          </div>
        </div>
      </q-card-section>
      <div class="q-mb-md" style="height: 1px; background-color: #ccc"></div>
      <!-- teacher responce apeal-->
      <q-card-section class="q-pa-na">
        <div class="text-subtitle1 q-mb-md"><strong>Your responce</strong></div>
        <div class="text-body2 q-mb-na">
          {{ complain?.evidencesTeacher?.[0]?.content }}
        </div>
      </q-card-section>

      <!-- applealing evidence -->
      <q-card-section v-if="Appealfiles?.length">
        {{ console.log('Appealfiles', Appealfiles) }}

        <div class="text-subtitle2 q-my-md">Appealing evidence</div>
        <div class="row q-gutter-sm">
          <div v-for="(file, index) in Appealfiles" :key="index" style="max-width: fit-content; border-radius: 10px">
            <div class="q-pa-xs" style="display: inline-block; border-radius: 10px">
              <div style="position: relative; display: inline-block">
                <q-img
                  v-if="file.mime.includes('image')"
                  :src="hashToUrl(file.hash)"
                  style="width: 60px; height: 45px; object-fit: cover; border-radius: 10px; overflow: hidden" />
                <q-video v-else :src="hashToUrl(file.hash)" style="width: 60px; height: 45px; border-radius: 10px" :controls="true" autoplay="false" />
              </div>
            </div>
          </div>
        </div>
      </q-card-section>
      <div class="q-mb-md" style="height: 1px; background-color: #ccc"></div>
      <!-- Actions button -->
      <q-card-actions class="q-pa-none q-mt-lg">
        <div class="row q-col-gutter-sm full-width">
          <q-btn flat label="Cancel" color="primary" class="full-width" style="border-radius: 100px; border: 1px solid black" @click="showDialog = false" />
        </div>
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import {ref, watch, computed} from 'vue'
import {serviceTaskStore} from 'stores/serviceTask'
const emit = defineEmits(['close'])

const props = defineProps({
  complain: {
    type: Object,
    required: true,
  },
})

const showDialog = ref(true)
const serviceTask = serviceTaskStore()
const studentReason = computed(() => props.complain?.evidencesStudent?.[0]?.content || 'No reason provided')
const files = computed(() => props.complain?.evidencesStudent?.[0]?.attachments)

const Appealfiles = computed(() => props.complain?.evidencesTeacher?.[0]?.attachments)

watch(showDialog, (val) => {
  if (!val) emit('close')
})

watch(
  () => props.complain,
  (newVal) => {
    if (newVal?.reason) {
      console.log('Student reason:', newVal.reason)
    }
  },
  {immediate: true, deep: true}
)
</script>

<style scoped>
.absolute-top-right {
  position: absolute;
  top: -8px;
  right: -8px;
  z-index: 1;
}
</style>
