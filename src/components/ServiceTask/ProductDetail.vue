<template>
  {{ console.log('taskData', taskData) }}
  <q-card
    ><q-expansion-item
      expand-separator
      default-opened
      header-class="flex items-center justify-between text-h6 text-weight-bold"
      content-class="q-pa-md"
      :class="{
        'bg-white rounded-borders-md q-my-md': true,
        'bg-grey-3 text-grey-7': isRefunded,
      }"
      :style="{
        filter: isRefunded ? 'grayscale(100%)' : 'none',
      }">
      <template #header>
        <div>
          <span v-if="!isStudentViewDetails"> <span v-if="!isBuying">*</span> Product detail </span>
          <span v-else>Product details</span>
          <div v-if="isBuying && isSys" class="text-right text-teal-5 text-bold text-h6">
            Total price: USD
            {{ (task.price / 100).toFixed(2) }}
          </div>
        </div>
        <div class="row items-center q-gutter-x-sm">
          <q-chip
            v-if="isSys"
            square
            :color="task.status ? 'green-2' : 'red-2'"
            :text-color="task.status ? 'green-7' : 'red-7'"
            style="font-weight: 500"
            :ripple="false"
            :label="task.status ? 'Published' : 'Unpublished'" />
          <div v-if="isBuying && isSys" class="text-teal-5 text-h6 text-bold q-ml-md">
            Total price: USD
            {{ (task.price / 100).toFixed(2) }}
          </div>
        </div>
      </template>

      <div class="q-pa-md">
        <q-item v-if="isEditAllow && !task.status" class="q-pa-sm">
          <q-item-section side>
            <q-btn flat dense text-color="grey-8" icon="edit" @click="openEditProductDetail()" />
          </q-item-section>
        </q-item>
        <q-img
          v-if="task.cover"
          class="q-my-md"
          contain
          :src="hashToUrl(task.cover)"
          style="width: 90%; max-width: 580px; aspect-ratio: 580 / 264; border-radius: 4px" />
        <div class="text-weight-medium q-pa-sm">
          <div class="text-subtitle1 text-weight-medium q-mb-sm">
            Service type:
            <q-chip square color="teal-1" text-color="teal-7" style="font-weight: 500" :ripple="false">
              {{ servicePackage.mentoringTypeList.find((e) => e.value === task.mentoringType)?.label }}
            </q-chip>
          </div>

          <div class="text-subtitle1 text-weight-medium q-mb-sm">
            Service provider qualification:
            <q-chip square color="teal-1" text-color="teal-7" style="font-weight: 500" :ripple="false">
              {{ servicePackage.qualificationList.find((e) => e.value === task.qualification)?.label }}
            </q-chip>
          </div>

          <div v-if="task.name" class="text-subtitle1 text-weight-medium q-mt-md">
            {{ task.name }}
          </div>
          <div v-if="task.description" class="text-subtitle2 text-weight-regular">
            {{ task.description }}
          </div>

          <div v-if="task.points?.length && task.points[0].trim()" class="text-subtitle1 text-weight-medium q-mt-md">
            Selling Points
            <div v-for="(point, i) in task.points" :key="i" class="text-subtitle2 text-weight-regular">
              {{ `${i + 1}. ${point}` }}
            </div>
          </div>
        </div>
      </div>
    </q-expansion-item></q-card
  >
</template>

<script setup>
import {ref, watch, computed} from 'vue'
import {useRoute} from 'vue-router'
import {servicePackageStore} from 'stores/service-package'

const servicePackage = servicePackageStore()
const route = useRoute()

const props = defineProps({
  openEditProductDetail: Function,
  isEditAllow: Boolean,
  isBuying: Boolean,
  taskData: Object,
})

const isStudentViewDetails = computed(() => route.query.viewDetails === 'true')
const isSys = computed(() => route.path.includes('sys'))
const task = computed(() => (isSys.value ? servicePackage : props.taskData))
const isRefunded = computed(() => task.value.associatedTaskStatus === 'refunded')
</script>
