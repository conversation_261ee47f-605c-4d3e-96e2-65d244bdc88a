<template>
  <q-card v-if="isSys" class="q-my-md rounded-borders-md">
    <q-card-section>
      <div class="row q-mb-sm justify-between items-center">
        <div class="row items-center">
          <div class="text-h6">*Sections</div>
          <div class="q-ml-sm text-h5 text-primary text-weight-medium" style="font-size: large">
            Total sales price: USD
            {{ currentTaskSections?.reduce((sum, section) => sum + (section.salesPrice || 0), 0).toFixed(2) }}, Total sections cost : USD
            {{ currentTaskSections?.reduce((sum, section) => sum + (section.costPrice || 0), 0).toFixed(2) }}
          </div>
        </div>
        <div v-if="!servicePackage.status">
          <q-btn
            class="q-mx-sm q-py-sm"
            icon="add"
            size="sm"
            color="white"
            text-color="primary"
            rounded
            no-caps
            style="
              width: 120px;
              background: #fffbfe;
              font-weight: 500;
              box-shadow:
                0px 1px 3px 1px #00000026,
                0px 1px 2px 0px #00000026;
            "
            @click="addSectionClick()">
            <span class="text-body2" style="font-size: 12px">Add Section</span>
          </q-btn>
        </div>
      </div>
      <div v-if="isSys && currentTaskSections.length" style="width: 100%">
        <SectionListSys :currentTaskSections="currentTaskSections" />
      </div>
      <div v-else-if="isSys" class="flex justify-center items-center" style="height: 420px; width: 100%; max-width: 1088px">
        <NoData message="No Section Added" messageColor="grey-8" messageClass="text-weight-bold text-subtitle1" />
      </div>
    </q-card-section>
  </q-card>
  <div v-else-if="isBookingOrBooked">
    <div v-if="!currentTaskSections.length" class="flex justify-center items-center" style="height: 420px; width: 100%; max-width: 1088px">
      <NoData />
    </div>
    <div v-else style="width: 100%">
      <SectionBookList :currentTaskSections="currentTaskSections" />
    </div>
  </div>
  <div v-else>
    <div v-if="!currentTaskSections.length" class="flex justify-center items-center" style="height: 420px; width: 100%; max-width: 1088px">
      <NoData />
    </div>
    <div v-else style="width: 100%">
      <SectionList :currentTaskSections="currentTaskSections" :unAssigned="unAssigned" />
    </div>
  </div>
  {{ console.log('currentTaskSections', serviceTask.sections, currentTaskSections, isUnassigned) }}
</template>

<script setup>
import {useRoute, useRouter} from 'vue-router'
import {serviceTaskStore} from 'stores/serviceTask'
import {servicePackageStore} from 'stores/service-package'
import SectionList from './SectionList.vue'
import {computed, onMounted} from 'vue'
import SectionListSys from 'src/pages/sys/package/service-task/SectionListSys.vue'
import NoData from 'src/components/pub/NoData.vue'
import SectionBookList from './SectionBookList.vue'

const serviceTask = serviceTaskStore()
const servicePackage = servicePackageStore()
const route = useRoute()
const router = useRouter()

const props = defineProps({
  sections: {
    type: Array,
    default: () => [],
  },
  unAssigned: {
    type: Boolean,
    default: false,
  },
})

const isSys = computed(() => route.path.includes('sys'))
const isBookingOrBooked = computed(() => route.path.includes('/booking/') || route.path.includes('/booked/'))
const isUnassigned = computed(() => route.query.subtab === 'unassigned')

const currentTaskSections = computed(() => {
  if (props.sections.length) {
    return props.sections
  }
  return [...serviceTask.sections]
})

function addSectionClick() {
  const currentPath = route.path
  const fullQueryString = Object.entries(route.query)
    .map(([key, value]) => `${key}=${value}`)
    .join('&')
  router.push({
    path: `${currentPath}/create-section`,
    query: {
      ...route.query,
      back: `/sys/package/edit?${fullQueryString}`,
    },
  })
}

onMounted(async () => {
  // if (!isSys.value) {
  //   const rs = await serviceTask.getSections(route.query.id)
  //   console.log('Sections', rs, route.query.id)
  // }
})
</script>
