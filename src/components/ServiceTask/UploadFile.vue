<template>
  {{ console.log('currentSection: ', currentSection) }}

  <!-- Common Upload Section -->
  <div v-if="showUploadSection">
    <div class="text-h6">Uploaded files</div>
    <DragUploader @uploaded="handleFileChange" :class="uploaderClasses" :style="uploaderStyle" />
  </div>

  <!-- Comments Section -->

  <div v-if="currentSection">
    <Comments
      :currentSection="currentSection"
      :bookerInfo="bookerInfo"
      :servicerInfo="servicerInfo"
      :isReadOnly="isReadOnly"
      :isReadonlyforProvider="isReadonlyforProvider"
      :localUpload="localUpload" />
  </div>
</template>
<script setup>
import {onMounted, ref, computed} from 'vue'
import {useRoute} from 'vue-router'
import {serviceTaskStore} from 'stores/serviceTask'
import Comments from './Comments.vue'
import DragUploader from 'components/utils/inputs/DragUploader.vue'

const serviceTask = serviceTaskStore()

const route = useRoute()

const props = defineProps({
  sectionId: {type: String, required: true},
  isReadOnly: {type: Boolean},
  isReadonlyforProvider: {type: Boolean},
  bookerInfo: {type: Object, default: () => ({})},
  servicerInfo: {type: Object, default: () => ({})},
})

const currentSection = computed(() => serviceTask.currentSection)
const isStudent = computed(() => route.query.tab === 'myAssociatedTask')
const isServiceProvider = computed(() => route.query.tab === 'taskManagement')
const isOngoing = computed(() => route.query.subtab === 'ongoing')

const localUpload = ref(null)

const showUploadSection = computed(() => {
  if (isStudent.value) return !props.isReadOnly && isOngoing.value
  if (isServiceProvider.value) return !props.isReadonlyforProvider
  return false
})

const uploaderStyle = computed(() => ({
  width: '700px',
  minWidth: '300px',
  height: '150px',
  maxHeight: '200px',
}))

const uploaderClasses = computed(() => (isServiceProvider.value ? 'q-my-md' : 'q-mt-md'))

const findType = (mime) => {
  if (!mime) return ''
  if (mime.includes('image')) return 'image'
  if (mime.includes('video')) return 'video'
  if (mime.includes('audio')) return 'audio'
  if (mime.includes('pdf')) return 'pdf'
  return mime
}

async function handleFileChange(rs) {
  if (!rs) return
  console.log('value of rs:', rs)
  if (rs.message) return $q.notify({type: 'negative', message: rs.message})

  if (currentSection.value) {
    // currentSection.value.files.push(rs)
    // await serviceTask.uploadFile(currentSection.value._id, rs._id)
    const newUpload = await App.service('section-comments').create({
      sectionId: currentSection.value?._id,
      cover: rs._id,
      mime: findType(rs.mime),
    })
    localUpload.value = newUpload
  }
}

onMounted(async () => {
  console.log('Mounted with sectionId:', props.sectionId)
  await serviceTask.getSection(props.sectionId)
})
</script>
