<template>
  <q-dialog ref="dialogRef" @hide="onDialogHide" full-width full-height maximized class="content-dialog">
    <q-card class="column">
      <q-card-actions align="between">
        <div class="text-h6"></div>
        <q-btn flat round dense icon="close" v-close-popup />
      </q-card-actions>

      <q-separator />

      <q-card-section class="col pc-max row position-relative q-pt-none">
        <div class="col-12 col-sm-5 col-md-5 full-height">
          <div v-if="!isEmpty(linkList)" class="overflow-auto q-px-md q-pt-sm content-list-height" id="scroll-area-with-virtual-scroll-1">
            <q-virtual-scroll
              separator
              scroll-target="#scroll-area-with-virtual-scroll-1"
              component="q-list"
              class="col full-width overflow-auto q-pb-xl q-mb-xl"
              :items="linkList"
              @virtual-scroll="onVirtualScroll">
              <template v-slot:after>
                <div v-if="list.data.length === list.limit" class="row justify-center q-my-md">
                  <q-spinner-ball color="primary" size="2em" class="full-width" />
                </div>
                <div v-else class="q-pa-md text-grey text-center hidden">It's over</div>
              </template>
              <template v-slot="{item: o, index: i}">
                <div class="q-pa-sm">
                  <SessionBoard
                    isMyContent
                    isVerify
                    small
                    :session="o"
                    :exceptions="exceptions"
                    :key="i"
                    :activedLink="activedLink"
                    @preview="onPreview"
                    :onLinkItemClick="onLinkItemClick"></SessionBoard>
                </div>
              </template>
            </q-virtual-scroll>
          </div>
          <template v-else>
            <div v-if="loading" class="text-center q-pa-xl text-grey">
              <q-spinner-ball color="primary" size="2em" class="full-width" />
            </div>
            <NoData v-else />
          </template>
        </div>
        <div class="col-12 col-sm-7 col-md-7 gt-xs full-height">
          <q-scroll-area ref="scrollAreaRef" class="full-height" style="">
            <div class="bg-white border-1-grey rounded-borders q-ma-md q-px-md q-mb-xl q-pb-xl">
              <div v-if="activedLink && $q.screen.gt.xs">
                <div v-if="activedLink.mode === 'video'">
                  <PreviewPage v-if="activedLink.video" isPreview :key="activedLink._id" :height="'400px'" :videoId="activedLink.video" />
                  <NoData v-else messageColor="grey" size="9rem" message="Please add video to this content by editing"></NoData>
                </div>
                <UnitView v-else linkGroup :id="activedLink._id" parentIsCourse />
              </div>
              <NoData v-else messageColor="grey" size="9rem" message="Please choose one content from the left list to view the details"></NoData>
            </div>
          </q-scroll-area>
        </div>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script setup>
import {ref, computed, watch, inject, onMounted} from 'vue'
import {useDialogPluginComponent} from 'quasar'
import SessionBoard from 'components/SessionBoard.vue'
import UnitView from 'components/UnitView.vue'
import {useRoute, useRouter} from 'vue-router'
import {pubStore} from 'stores/pub'
import ContentDialogDetail from 'components/ContentDialogDetail.vue'
import useUnit from 'src/composables/account/unit/useUnit.js'
import PreviewPage from 'src/pages/InteractiveVideo/PreviewPage.vue'

/*
  props & emits
*/
const props = defineProps({id: String})
defineEmits([...useDialogPluginComponent.emits])

/*
  consts
*/
const {getList, relateLinkList, allRelateLinkList} = useUnit()
const {dialogRef, onDialogHide, onDialogOK, onDialogCancel} = useDialogPluginComponent()
const title = ref('Detail')
const unconfirmed = ref(true)
const loading = ref(false)
const query = ref({})
const router = useRouter()
const scrollAreaRef = ref(null)
const search = ref('')
const activedLink = ref(null)
const linkDialog = ref(false)
const linkList = ref([])
const list = ref([])
const pub = pubStore()
const contentsType = inject('ContentsType')

const tabs = [
  {
    value: 'unit',
    label: 'Unit Plan / Service Module',
    mode: ['unit', 'pdUnit'],
  },
  {
    value: 'task_session',
    label: 'Task / Service Session',
    mode: ['task', 'pdTask'],
  },
  {
    value: 'video_session',
    label: 'Video Session',
    mode: ['video'],
  },
]
const tabMap = tabs.reduce((acc, cur) => {
  acc[cur.value] = cur
  return acc
}, {})
const currentTab = ref(tabs[0].value)
watch(currentTab, requery)

/*
  watch
*/

/*
  computeds
*/

/*
  methods
*/

const onUnitLoad = (val) => {
  if (['unit', 'pdUnit'].includes(activedLink.value.mode)) {
    activedLink.value.liveCount = val
  }
}

const onPreview = (link) => {
  linkDialog.value = false
  if ($q.screen.lt.sm) {
    linkDialog.value = true
  }
}

const onVirtualScroll = async ({index, to}) => {
  if (index !== to) return // console.warn(index, to, direction)
  const {limit = 10, skip = 0, data} = list.value
  if (Acan.isEmpty(data)) return // has last page
  console.warn('need load more')
  query.value.$skip = skip + limit
  await find()
}

const onLinkItemClick = (link) => {
  activedLink.value = link
  scrollAreaRef.value.setScrollPosition('vertical', 0, 300)
}

async function find() {
  loading.value = true
  const rs = await relateLinkList({rid: props.id})
  console.log('rs', rs)

  linkList.value = rs
  list.value = {limit: 10, skip: 0, total: linkList.value.length, data: []}
  loading.value = false
}

async function requery() {
  activedLink.value = null
  query.value.$skip = 0
  linkList.value.length = 0
  list.value = {}
  await find()
}

onMounted(async () => {
  console.log('onMounted', props.id)
  find()
})
</script>

<style lang="sass" scope>
.content-dialog
 .content-list-height
   height: calc(100vh - 280px)
   body.screen--xs &
      height: calc(100vh - 140px)
 .scroll
   body.screen--xs &
      max-height: 100vh
      width: auto
      height: auto
</style>
