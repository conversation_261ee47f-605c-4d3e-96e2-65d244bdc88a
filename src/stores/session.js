import {defineStore} from 'pinia'
import {ref} from 'vue'
// import {QuestionTypes} from '../boot/const'
const Service = App.service('session')
import {pubStore} from './pub'
const pub = pubStore()
import {unitStore} from './unit'
const unit = unitStore()
let token = await App.authentication.getAccessToken()

export const sessionStore = defineStore('session', {
  state: () => ({
    one: ref(null),
    childs: ref([]),
    childMaps: ref({}),
    roomStat: ref(null),
    takeaway: ref(null),
    myTakeaway: ref(null),
    packUserDoc: ref(null),
    isPremiumLecture: ref(false),
    isInterviewConsultant: ref(false),
    pageId: ref(null),
    tagColorList: ref([
      'red-3',
      'pink-3',
      'purple-3',
      'deep-purple-3',
      'indigo-3',
      'blue-3',
      'light-blue-3',
      'cyan-3',
      'teal-3',
      'green-3',
      'light-green-3',
      'lime-3',
      'yellow-3',
      'amber-3',
    ]),
  }),
  getters: {
    isOwner() {
      // 是否创建人
      return this.one.uid === pub.user._id
    },
    isUnit() {
      return this.one.type.toLowerCase().includes('courses')
    },
    isTask() {
      return !this.isUnit && !this.isTool
    },
    isTool() {
      return this.one.type.toLowerCase().includes('tool')
    },
    isMember() {
      // 是否学员
      return this._isMember(this.one)
    },
    status() {
      return this.statusFn(this.one)
    },
    membersCount() {
      return this.one.regNum || this.one.students.length
    },
    task() {
      return this.one.task
    },
    // temp
    page() {
      return this.one.pages.find((v) => v._id === this.pageId)
    },
    question() {
      return unit._getQuestion(this.one.questions, this.pageId)
    },
    tagTypes() {
      return ['assess', 'outline', 'skills', 'goal']
    },
    numbersOfOutline() {
      return unit._getNumbersOfOutline(this.tagTypes, this.question)
    },
    material() {
      return unit._getMaterial(this.one.materials, this.pageId)
    },

    linkGroup() {
      return this.one.task.linkGroup?.map((group) => {
        const link = this.one.task.link
          ?.filter((v) => v.group === group._id)
          .map((v) => {
            return this.childMaps[v.id]
          })
          .filter((v) => v)
        return {
          ...group,
          link,
        }
      })
    },
  },
  actions: {
    async get(_id, dense = false) {
      token = await App.authentication.getAccessToken()
      // 页面主体为session的时候使用
      this.one = await Service.get(_id)
      if (dense) return this.one // 简化查询
      // 查询子课堂
      if (this.one.childSize) {
        const $in = this.one.childs.map((v) => v.sid)
        const {data} = (await Service.find({query: {_id: {$in}, $limit: 100}})) ?? {}
        // const {data} = (await Service.find({query: {pid: _id, $limit: 100}})) ?? {}
        for (const o of data) this.childMaps[o.task._id] = o
        // this.one.childs = this.one.childs.map((v) => Object.assign(v, maps[v._id]))
      }
      this.roomStat = await App.service('rooms').get(this.one.sid)
      // 查询takeaway
      if (this.one.ended) this.takeaway = await App.service('session-snapshot').get(_id)
      if (this.one.ended && pub.isStudent) {
        const rs = await App.service('session-takeaway-snapshot').find({query: {session: _id, uid: pub.user._id}})
        if (rs.data?.[0]?.hash) this.myTakeaway = rs.data[0]
      }
      // 获取预定内容
      if (this.one.booking) {
        const {servicePackUser} = await App.service('service-booking').get(this.one.booking)
        this.packUserDoc = servicePackUser
        this.isPremiumLecture = servicePackUser.pid
        if (['interview', 'interviewTeacher'].includes(servicePackUser.snapshot.consultant?.type)) {
          this.isInterviewConsultant = true
        }
      }
      return this.one
    },
    viewRoute(doc, type) {
      const path = `/session/${type || doc.type}/${doc._id}`
      return {path, query: {back: location.pathname + location.search}}
    },
    toRoom(doc, m, route, openRoster) {
      const {sid, type, task} = doc
      const isMember = this._isMember(doc)
      if (!m) m = isMember ? 's' : 'd'
      // 评估表
      if (type.toLowerCase().includes('tool')) return `/v2/account/assessment-tool/${task._id}/${isMember ? 'student' : 'teacher'}/${sid}`
      if (m === 's') {
        // 学员端
        return `/v2/s/${sid}?back=${encodeURIComponent(route.fullPath)}`
      } else {
        // 教师端
        let host = roomHost
        return `${host}/${m}/${sid}?token=${token}${openRoster ? '&roster=1' : ''}&back=${encodeURIComponent(route.fullPath)}`
      }
    },
    statusFn(doc) {
      let text = ''
      const now = new Date()
      if (new Date(doc.start) > now) {
        text = 'Scheduled'
      } else if (new Date(doc.start) < now && doc.status !== 'close') {
        text = 'Ongoing'
      } else if (doc.del) {
        text = 'Archived'
      } else if (doc.status == 'close') {
        text = 'Ended'
      }
      return text
    },
    _getTitle(doc) {
      return doc.classId ? 'Class session' : 'Workshop'
    },
    _isMember(doc) {
      return doc.students.includes(pub.user._id) || doc.regData?._id
    },
  },
})
