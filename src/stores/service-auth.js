import {ref} from 'vue'
import {defineStore} from 'pinia'
const Service = App.service('service-auth')
export const serviceAuthStore = defineStore('service-auth', () => {
  // 获取 workshop 已经认证过的数据
  const workshopMap = ref({})
  async function getWorkshopMap() {
    const $select = ['curriculum', 'subject']
    const {data} = await Service.find({query: {status: 2, type: 'workshop', $select, $sort: {_id: -1}, $limit: 100}})
    for (const o of data) {
      const {curriculum, subject} = o
      Acan.objSet(workshopMap.value, Acan.objClean([curriculum, subject || undefined]).join('.'), true)
    }
  }
  if (Acan.isEmpty(workshopMap.value)) getWorkshopMap() // 首次获取
  function isWorkshopAuth({curriculum, service}) {
    const subject = service?.type?.[0]
    if (subject) return workshopMap.value?.[curriculum]?.[subject] || false
    return workshopMap.value?.[curriculum] || false
  }
  return {workshopMap, isWorkshopAuth}
})
