import {defineStore} from 'pinia'

export const serviceTaskStore = defineStore('serviceTask', {
  state: () => ({
    sections: [],
    currentSection: {},
    isSectionsAvaialable: false,
  }),
  actions: {
    async saveSection(data) {
      await App.service('section').create(data)
      this.sections.push(data)
    },
    async getSections(serviceTaskId) {
      const query = {
        serviceTaskId: serviceTaskId,
      }

      const sections = await App.service('section').find({
        query: {
          serviceTaskId: serviceTaskId,
          $sort: {
            sectionNo: 1,
          },
        },
      })

      console.log('query', query, sections)
      this.sections = sections.data
      this.isSectionsAvaialable = true
      return sections.data
    },
    async getSection(id) {
      const section = await App.service('section').get(id)
      console.log('getSection', section)
      this.currentSection = section
      return section
    },

    async deleteSection(id) {
      await App.service('section').remove(id)
      this.sections = this.sections.filter((section) => section.id !== id)
    },

    async handleConfirmAsCompleted(id, isMarked) {
      if (isMarked) {
        const {success, nextSection, isLocked} = await App.service('section').patch('confirmAsCompleted', {_id: this.currentSection._id})
        if (nextSection) {
          this.currentSection.status = 'completed'
          const currentSectionIndex = this.sections.findIndex((sec) => sec._id === this.currentSection._id)
          if (currentSectionIndex !== -1) {
            this.sections[currentSectionIndex].status = this.currentSection.status
          }
          const nextSectionIndex = this.sections.findIndex((sec) => sec._id === nextSection._id)
          if (nextSectionIndex !== -1) {
            this.sections[nextSectionIndex].status = nextSection.status
          }
        }
        return {nextSection: nextSection ? true : false, isLocked}
      }
    },

    async updateNextSectionStatus(nextSection, status) {
      console.log('nextSection is calling', nextSection, status, this.currentSection, this.currentSection.serviceTaskId)
      if (nextSection) {
        await App.service('section').patch(nextSection._id, {status})
      } else {
        await App.service('section').patch(this.currentSection.serviceTaskId, {
          lastOne: true,
          curServicer: this.currentSection.servicer,
          bookingId: this.currentSection.bookingId,
        })
      }
    },

    async handleMarkAsCompleted(id, isMarked) {
      console.log('zxc', id, isMarked)

      if (isMarked) {
        this.currentSection = await App.service('section').patch(id, {
          markAsCompleted: true,
          markAsCompletedTime: new Date(),
        })
      }
    },
  },
})
