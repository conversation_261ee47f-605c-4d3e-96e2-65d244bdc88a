export default [
  {
    path: '/unit/',
    component: () => import('layouts/IdLayout.vue'),
    children: [
      {path: ':id', component: () => import('pages/unit/UnitLibDetail.vue')}, // libray detail
      {path: 'overview/:id', component: () => import('pages/unit/OverviewLibPage.vue')},
      {path: 'slides/:id', component: () => import('pages/unit/SlidesLibPage.vue')},
      {path: ':id/:linkId', component: () => import('pages/unit/UnitLibDetail.vue')}, // libray link content detail
      {path: 'overview/:id/:linkId', component: () => import('pages/unit/OverviewLibPage.vue')},
      {path: 'slides/:id/:linkId', component: () => import('pages/unit/SlidesLibPage.vue')},
      // my content
      {path: 'my/:id', component: () => import('pages/unit/UnitDetail.vue')}, // my content detail
      {path: 'my/overview/:id', component: () => import('pages/unit/OverviewPage.vue')},
      {path: 'my/slides/:id', component: () => import('pages/unit/SlidesPage.vue')},
      // my service-auth content
      {path: 'auth/:authId', component: () => import('pages/unit/AuthDetail.vue')}, // service-auth detail
      {path: 'auth/:authId/:id', component: () => import('pages/unit/AuthDetail.vue')}, // service-auth link-content detail
      {path: 'auth/overview/:authId', component: () => import('pages/unit/AuthOverview.vue')},
      {path: 'auth/slides/:authId', component: () => import('pages/unit/AuthSlides.vue')},
      {path: 'auth/overview/:authId/:id', component: () => import('pages/unit/AuthOverview.vue')},
      {path: 'auth/slides/:authId/:id', component: () => import('pages/unit/AuthSlides.vue')},
      // service-pack-user content 购买后的服务包
      {path: 'packUser/:authId', component: () => import('pages/unit/AuthDetail.vue')}, // service-auth detail
      {path: 'packUser/:authId/:id', component: () => import('pages/unit/AuthDetail.vue')},
    ],
  },
  {
    path: '/unit/',
    component: () => import('layouts/MainLayout.vue'),
    children: [
      {path: 'my', component: () => import('pages/unit/ListPage.vue')}, // my content list
      {path: 'published', component: () => import('pages/unit/PublishedPage.vue')}, // my published
      {path: 'saved', component: () => import('pages/unit/SavedPage.vue')}, // my saved
      {path: 'library/:type', component: () => import('pages/unit/libraryList.vue')}, // library task
    ],
  },
]
