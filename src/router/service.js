export default {
  path: '/service/',
  component: () => import('layouts/DetailLayout.vue'),
  children: [
    {path: 'pack/:id', component: () => import('pages/service/detail/index.vue')}, // 服务包详情
    {path: 'pack-user/:id', component: () => import('pages/service/purchased/index.vue')}, // 已购买的服务包详情
    // {path: 'pack-user/:id/:unitId', component: () => import('pages/.vue')}, // 已购买的服务包详情->lecture详情 只做取数，调用课件详情组件
    // {path: 'auth/:id', component: () => import('pages/.vue')}, // 认证项详情
    // {path: 'auth/:id/:unitId', component: () => import('pages/.vue')}, // 认证项详情 -> task详情 只做取数，调用课件详情组件
  ],
}
