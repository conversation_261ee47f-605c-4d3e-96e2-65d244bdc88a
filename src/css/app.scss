// app global css in SCSS form

// *{font-size:1rem;}

::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}

::-webkit-scrollbar-track {
  border-radius: 3px;
  background: rgba(0, 0, 0, 0);
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.08);
}
/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.12);
  -webkit-box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.05);
}

html,
body,
#q-app,
#q-app > main {
  height: 100%;
}

.ellipsis-1-line {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}
.ellipsis-3-lines {
  display: -webkit-box;
  overflow: hidden;
  white-space: normal !important;
  text-overflow: ellipsis;
  word-wrap: break-word;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}
.ellipsis-4-lines {
  display: -webkit-box;
  overflow: hidden;
  white-space: normal !important;
  text-overflow: ellipsis;
  word-wrap: break-word;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
}
.ellipsis-5-lines {
  display: -webkit-box;
  overflow: hidden;
  white-space: normal !important;
  text-overflow: ellipsis;
  word-wrap: break-word;
  -webkit-line-clamp: 5;
  -webkit-box-orient: vertical;
}
.rotate-338 {
  transform: rotate(338deg) /* rtl:ignore */;
}
.q-border-1-grey {
  border: 1px solid $grey-7;
}
.q-border-1 {
  border: 1px solid rgba(0, 0, 0, 0.12);
}
.q-border-2 {
  border: 2px solid rgba(0, 0, 0, 0.12);
}
.q-border-2-transparent {
  border: 2px solid transparent;
}
.q-border-3-transparent {
  border: 3px solid transparent;
}
.q-border-3 {
  border: 3px solid rgba(0, 0, 0, 0.12);
}
.q-border-2-primary {
  border: 2px solid $primary;
}
.q-border-3-primary {
  border: 3px solid $primary;
}
.rounded-borders-top-left-right {
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}
.rounded-borders-bottom-left-right {
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
}
.rounded-borders-bottom-left-right-md {
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px;
}
.rounded-borders-bottom-left-right-lg {
  border-bottom-left-radius: 16px;
  border-bottom-right-radius: 16px;
}
.rounded-borders-sm {
  border-radius: 8px;
}
.rounded-borders-md {
  border-radius: 12px;
}
.rounded-borders-lg {
  border-radius: 16px;
}
.rounded-borders-xl {
  border-radius: 20px;
}
.pc-body {
  max-width: 1040px;
  width: 100%;
  margin: 0 auto;
}
.pc-max {
  max-width: 1200px;
  width: 100%;
  margin: 0 auto;
}
.pc-md {
  max-width: 1000px;
  width: 100%;
  margin: 0 auto;
}
.pc-sm {
  max-width: 840px;
  width: 100%;
  margin: 0 auto;
}
.q-carousel__arrow .q-btn {
  background-color: rgba(0, 0, 0, 0.3);
}
.q-carousel__slide {
  background-size: contain;
  background-repeat: no-repeat;
}

.q-carousel .q-carousel__thumbnail {
  box-shadow:
    0 1px 5px rgb(0 0 0 / 20%),
    0 2px 2px rgb(0 0 0 / 14%),
    0 3px 1px -2px rgb(0 0 0 / 12%);
}
.q-carousel .q-carousel__navigation-icon--inactive {
  opacity: 0.2;
}

.divider {
  border: none;
  height: 1px;
  width: 100%;
  background: $grey-4;
}

.avatar {
  height: 5rem;
  width: 5rem;
  border-radius: 100%;
}

.loader {
  width: 3.42857143rem;
  height: 3.42857143rem;
  font-size: 1.14285714rem;
  display: block;
  position: fixed;
  top: 50%;
  left: 50%;
  margin: 0;
  text-align: center;
  z-index: 1000;
  -webkit-transform: translateX(-50%) translateY(-50%);
  -ms-transform: translateX(-50%) translateY(-50%);
  transform: translateX(-50%) translateY(-50%);
}
.loader.active,
.loader.visible {
  display: block;
}
.loader:after,
.loader:before {
  width: 3.42857143rem;
  height: 3.42857143rem;
  margin: 0 0 0 -1.71428571rem;
}
.loader:before {
  position: absolute;
  content: '';
  top: 0;
  left: 50%;
  border-radius: 500rem;
  border: 0.2em solid rgba(0, 0, 0, 0.1);
}
.loader:after {
  position: absolute;
  content: '';
  top: 0;
  left: 50%;
  -webkit-animation: loader 0.6s linear;
  animation: loader 0.6s linear;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  border-radius: 500rem;
  border-color: #767676 transparent transparent;
  border-style: solid;
  border-width: 0.2rem;
  box-shadow: 0 0 0 1px transparent;
}
@-webkit-keyframes loader {
  from {
    -webkit-transform: rotate(0);
    transform: rotate(0);
  }
  to {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@keyframes loader {
  from {
    -webkit-transform: rotate(0);
    transform: rotate(0);
  }
  to {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

.doc-card-title {
  font-size: 1.2rem;
  margin-left: -24px;
  padding: 2px 10px 2px 24px;
  background: #e0e0e0;
  color: #616161;
  position: relative;
  border-radius: 3px 5px 5px 0;
}
.doc-card-title:after {
  content: '';
  position: absolute;
  top: 100%;
  left: 0;
  width: 0;
  height: 0;
  border: 0 solid transparent;
  border-top-color: #bebebe;
  border-width: 9px 0 0 11px;
}

.q-carousel .q-carousel__thumbnail--active {
  transform: translate(0px, -6px);
}

.q-carousel__navigation--bottom {
  padding-top: 10px;
}
.gray-filter {
  filter: grayscale(100%);
  -webkit-filter: grayscale(100%);
  -moz-filter: grayscale(100%);
  -ms-filter: grayscale(100%);
  -o-filter: grayscale(100%);
  filter: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg'><filter id='grayscale'><feColorMatrix type='matrix' values='0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0 0 0 1 0'/></filter></svg>#grayscale");
  filter: progid:DXImageTransform.Microsoft.BasicImage(grayscale=1);
  -webkit-filter: grayscale(1);
  color: grey;
}

// Fix: `q-chip` in `q-select` if q-chip is too long overflow issue
.q-field__control-container .q-chip {
  height: auto;
  .ellipsis {
    white-space: initial;
  }
}

.no-ellipsis.q-chip {
  height: auto;
  .ellipsis {
    white-space: initial;
  }
}

// some utils tailwind-like, use  !important to cover some quasar styles
.rounded-none {
  border-radius: 0px !important;
}
.rounded-sm {
  border-radius: 0.125rem !important; /* 2px */
}
.rounded {
  border-radius: 0.25rem !important; /* 4px */
}
.rounded-md {
  border-radius: 0.375rem !important; /* 6px */
}
.rounded-lg {
  border-radius: 0.5rem !important; /* 8px */
}
.rounded-xl {
  border-radius: 0.75rem !important; /* 12px */
}
.rounded-2xl {
  border-radius: 1rem !important; /* 16px */
}
.rounded-3xl {
  border-radius: 1.5rem !important; /* 24px */
}
.rounded-full {
  border-radius: 9999px !important;
}

.flex-1 {
  flex: 1;
}

.student-theme {
  .student-bg {
    background: #89e3d6;
  }
  .q-btn--push:before {
    border-bottom: 8px solid $teal-7;
  }
}

.text-wrap {
  white-space: normal;
  word-break: break-word;
}

.text-pre-line {
  white-space: pre-line;
  word-break: break-word;
}

@keyframes pulseGlow {
  0%,
  100% {
    box-shadow:
      0 0 0 0 rgba(255, 160, 100, 0.3),
      inset 0 0 20px rgba(255, 160, 100, 0.08);
  }
  50% {
    box-shadow:
      0 0 8px 1px rgba(255, 160, 100, 0.25),
      inset 0 0 30px rgba(255, 160, 100, 0.12);
  }
}

@keyframes slideIn {
  0% {
    transform: translateX(-4px);
    opacity: 0.8;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

.highlighted-table-row {
  position: relative;
  background: linear-gradient(90deg, rgba(255, 160, 100, 0.12) 0%, rgba(255, 160, 100, 0.06) 100%) !important;
  animation:
    pulseGlow 2s ease-in-out infinite,
    slideIn 0.4s ease-out;
  transition: all 0.3s ease !important;
}
