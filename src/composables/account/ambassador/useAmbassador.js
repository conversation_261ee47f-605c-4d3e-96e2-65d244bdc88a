import {ref, onMounted} from 'vue'

import {TabMap, RoleMap} from 'src/pages/ambassador/utils.js'
import useSchool from 'src/composables/common/useSchool'

// Ref: https://dev.classcipe.com/doc/#/fio/service?id=service-auth-model
const service = App.service('ambassador-auth')
const isHistoricalEvidenceExpend = ref(true)

const isInitMap = ref({
  getList: true,
  getCount: true,
})
const list = ref([])
let _option = null

const search = ref('')
const duration = ref({form: '', to: ''})

const tab = ref(TabMap.pending.value)
const role = ref(RoleMap.teacher.value)
const currentVerification = ref('ambassador')
function setCurrentVerification(item) {
  currentVerification.value = item.value
}

const pagination = ref({
  page: 1,
  rowsPerPage: 10,
  rowsNumber: 0,
})
const query = ref({})

export default () => {
  const {userId} = useSchool()

  onMounted(async () => {
    // await Promise.all([getCount(), getList()])
    // await Promise.all([getCount()])
  })

  async function getList(isRefetch = false, option) {
    if (!userId.value) return []
    if (!isRefetch) {
      if (!isInitMap.value.getList) return
      isInitMap.value.getList = false
    }

    const {page, rowsPerPage} = pagination.value
    const ext = {$skip: (page - 1) * rowsPerPage, $limit: rowsPerPage}
    const dto = {
      query: {$limit: 2000, $sort: {updatedAt: -1}, ...ext},
    }

    _option = option
    if (_option) {
      dto.query = {...dto.query, ...query.value, ..._option}
    } else {
      dto.query = {...dto.query, ...query.value}
    }

    if (dto.query?.enable === null) delete dto.query.enable
    if (dto.query?.enable === false) dto.query.enable = {$ne: true}

    const res = await service.find(dto)
    list.value = res?.data ?? []
    list.value = list.value.sort((a, b) => +new Date(b.updatedAt) - +new Date(a.updatedAt))
    return res
  }

  async function init() {
    if (_option) {
      await getList(true, _option)
    } else {
      await getList(true)
    }
  }

  async function getOneById(id) {
    const res = await service.get(id)
    return res
  }

  async function createOne(dto, isRefetch = true) {
    const res = await service.create(dto)
    if (isRefetch) await init()
    return res
  }

  async function patchOneById(id, dto, isRefetch = true) {
    const res = await service.patch(id, dto)
    if (isRefetch) await init()
    return res
  }

  async function deleteOneById(id, isRefetch = true) {
    const res = await service.remove(id)
    if (isRefetch) await init()
    return res
  }

  return {
    isHistoricalEvidenceExpend,
    list,
    getList,
    getOneById,
    createOne,
    patchOneById,
    deleteOneById,

    tab,
    role,
    search,
    duration,
    currentVerification,
    setCurrentVerification,

    pagination,
    query,
  }
}
