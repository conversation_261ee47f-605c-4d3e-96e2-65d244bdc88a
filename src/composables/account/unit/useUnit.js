import {useDebounceFn} from '@vueuse/core'

const service = App.service('unit')

async function getOneByIdRaw(id = '', options = {}) {
  const res = await service.get(id, options)
  return res
}

const getOneById = useDebounceFn((...args) => getOneByIdRaw(...args), 250)

export default () => {
  async function createOne(dto) {
    const res = await service.create(dto)
    return res
  }

  // async function getOneById(id = '') {
  //   const res = await service.get(id)
  //   return res
  // }

  async function patchOneById(id, dto, query = {}) {
    const res = await service.patch(id, dto, query)
    return res
  }

  async function copyOne(dto) {
    const res = await service.patch('copy', dto)
    return res
  }

  async function copyTool(query) {
    const res = await service.get('copyTool', {query})
    return res
  }

  async function removeOneById(id) {
    const res = await service.remove(id)
    return res
  }

  async function getList(query) {
    const res = await service.find({query})
    return res
  }

  async function relateLinkList(query) {
    const res = await service.get('relateLinkList', {query})
    return res
  }

  async function allRelateLinkList(query) {
    const res = await service.get('allRelateLinkList', {query})
    return res
  }

  async function publish(query) {
    const res = await service.patch('publish', query)
    return res
  }

  async function unPublish(query) {
    const res = await service.patch('unPublish', query)
    return res
  }

  async function getRecommendIdea(query = {key: ''}) {
    const res = await service.get('recommendIdea', {query})
    return res
  }

  async function getRecommendWords(query = {key: ''}) {
    const res = await service.get('recommendWords', {query})
    return res
  }

  return {
    getList,
    getOneById,
    getOneByIdRaw,
    createOne,
    patchOneById,
    removeOneById,

    copyOne,
    copyTool,
    relateLinkList,
    allRelateLinkList,

    publish,
    unPublish,

    getRecommendIdea,
    getRecommendWords,
  }
}
