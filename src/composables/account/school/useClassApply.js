const service = App.service('class-apply')

// ref: https://github.com/zran-nz/doc/blob/master/fio/school.md#class-apply-model
export default () => {
  async function createOne(dto) {
    const res = await service.create(dto)
    return res
  }

  async function getOneById(id = '') {
    const res = await service.get(id)
    return res
  }

  async function patchOneById(id, dto) {
    const res = await service.patch(id, dto)
    return res
  }

  async function deleteOneById(id) {
    const res = await service.remove(id)
    return res
  }

  async function getList(query) {
    const res = await service.find({query})
    return res
  }

  async function getCount(classId) {
    const res = await service.get('count', {query: {class: classId}})
    return res
  }

  async function getQuestionByUid(uid) {
    try {
      const res = await App.service('class-question-logs').get(uid)
      return res
    } catch (error) {
      if (error?.code === 404) return []
      else console.error(error)
    }
  }

  return {
    createOne,
    patchOneById,
    getOneById,
    getList,
    getCount,
    getQuestionByUid,
    deleteOneById,
  }
}
