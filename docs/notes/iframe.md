# Iframe

## Common

- Check is iframe

```javascript
if (window.self !== window.top) {
  console.log('in an iframe')
} else {
  console.log('not in an iframe')
}
```

## Pass variables to iframe

You can't pass variables to iframe directly, due to security issue.

We use window object.

> in the parent document

```javascript
const myVariable = 'hello world'
const iframe = document.createElement('iframe')
iframe.src = 'path/to/your/iframe'
iframe.onload = () => {
  iframe.contentWindow.myVariable = myVariable
}
document.body.appendChild(iframe)
```

> in the iframe

```javascript
const queryString = window.location.search
const urlParams = new URLSearchParams(queryString)
const myVariable = urlParams.get('myVariable')
console.log(window.myVariable) // "hello world"
```

## Sending out variables from iframe

You can't send out variables directly, due to security issue.

We use (`Window.postMessage()`)[https://developer.mozilla.org/en-US/docs/Web/API/Window/postMessage], in the same domain.

> in the iframe

```javascript
const message = 'hello world'
// "*" indicates that message cant be sent to any domain
parent.postMessage(message, '*') // generally use "*" is pretty much
```

> in the parent document

```javascript
window.addEventListener('message', receiveMessage, false)

function receiveMessage(event) {
  // check domain
  if (event.origin !== 'https://your-domain.com') return
  const message = event.data
  console.log(message)
}
```
