# notify

we mostly use `$q.notify` to notify user some simple message

REF: <https://quasar.dev/quasar-plugins/notify#introduction>

example:

```javascript
import { useQuasar } from 'quasar'

const $q = useQuasar()

// success
$q.notify({ type: 'positive', position: 'top', message: 'Curriculum updated successfully'})

// error/inValid
$q.notify({ type: 'negative', position: 'top', message: 'Curriculum updated unsuccessfully})

// default (in `quasar.config.js`)
notify {
   type: 'positive',
   position: 'top'
}

// simple usage
$q.notify('Teacher updated successfully') // should be work like success
```

1. using type `positive` or `negative`, just these two color, not 'info', 'warning' or elses
2. message pattern
   - success: `Item updated successfully` (remember add `ed`, successful`ly`)
   - error: `Item updated unsuccessfully` (remember add `ed`, `un`successfully)
