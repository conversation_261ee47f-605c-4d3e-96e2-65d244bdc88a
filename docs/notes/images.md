# images

## In `src/assets/`

Description: For inside project using, like: only use in component (Use this method first, or check the **In `public/`** part)

Example:

```vue
<template>
  <!-- Use `~assets` with path for `src/assets` image -->
  <img src="~assets/img/logo2.png" />
</template>
```

## In `public/`

Description: For third part using, like: image in email, logo, ...etc.

Example:

```vue
<template>
  <!-- Combine `PATH_PREFIX` with path for `public` image -->
  <img :src="`${PATH_PREFIX}/img/assessment-tool/waiting-bg.png`" alt="waiting background" />
</template>

<script setup>
// Like: `/v2`, `/v666`, ...
// Currently is `/v2`
import {PATH_PREFIX} from 'src/boot/const'
</script>
```
