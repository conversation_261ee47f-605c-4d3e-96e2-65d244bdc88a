# icon name

> icon name cheat sheet

Demo page: <https://dev.classcipe.com/v2/account/info>

Quasar ion: <https://quasar.dev/vue-components/icon#webfont-usage>

## Main

- School info

  - icon name: `widget`
  - color: `#585808d`

- Payments

  - icon name: `wallet`
  - color: `#e5c185`

- Personal info

  - icon name: `person`
  - color: `#513742`

- Login & security

  - icon name: `lock`
  - color: `#74a892`

- Payments & payouts

  - icon name: `account_balance_wallet`
  - color: `#008585`

- Orders
  - icon name: `list_alt`
  - color: `#003f5c`

## Manage

- Classes

  - icon name: `widgets`
  - color: `#58508d`

- Teachers

  - icon name: `psychology`
  - color: `#bc5090`

- Students

  - icon name: `school`
  - color: `#bc5090`

- Space Manage

  - icon name: `space_dashboard`
  - color: `#ff6361`

- Space Manage

  - icon name: `workspaces`
  - color: `#ffa600`

- Role Manage
  - icon name: `vpn_key`
  - color: `#c7522a`

## Setting

- Academic Setting

  - icon name: `history_edu`
  - color: `#9575cd`

- Academic Term

  - icon name: `history_edu`
  - color: `#e5c185`

- Curriculum

  - icon name: `menu_book`
  - color: `#ff8531`

- Planning Format

  - icon name: `text_snippet`
  - color: `#74a892`

- Tags setting

  - icon name: `sell`
  - color: `#008585`

- Attendance

  - icon name: `fact_check`
  - color: `#00202e`

- Teacher & Service verification
  - icon name: `workspace_premium`
  - color: `#003f5c`
