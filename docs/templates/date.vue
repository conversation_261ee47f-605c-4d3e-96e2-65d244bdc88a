<template>
  <div>{{ formatNow }}</div>
</template>

<script setup>
import {date} from 'quasar'
import {TIME_FORMAT} from 'src/boot/const'

// export const MMDD_FORMAT = 'MM/DD HH:mm'
// export const TIME_FORMAT = 'MM/DD/YYYY HH:mm'
// export const DATE_FORMAT = 'YYYY-MM-DD'
// export const TIME_FORMAT_NZ = 'DD/MM/YYYY HH:mm'
// export const DATE_FORMAT_NZ = 'DD/MM/YYYY'
// export const DATE_PICKER_FORMAT = 'DD/MM/YYYY'

const now = new Date()
// const formatNow = date.formatDate(now, 'YYYY-MM-DD HH:mm')
const formatNow = date.formatDate(now, TIME_FORMAT)
</script>
