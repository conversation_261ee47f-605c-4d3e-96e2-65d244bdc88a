<template>
  <div>
    <q-select
      ref="selectDom"
      dense
      color="orange-9"
      multiple
      use-chips
      use-input
      @input-value="(v) => (search = v.toFirstUpperCase())"
      new-value-mode="add-unique"
      @filter="filterFn"
      v-model="selected"
      :options="options"
      @update:model-value="onUpdated"
      @add="addOption($event)">
      <template v-slot:prepend>
        <span class="text-body2" :class="{'text-negative': selected.length}"> * </span>
      </template>
      <template v-slot:append>
        <q-btn v-if="showCreateBtn()" flat rounded dense color="teal" icon="add" no-caps label="Create" @click="selectDom.add(search, true)" />
        <q-btn
          v-if="!isEmpty(selected)"
          class="gt-xs"
          flat
          rounded
          dense
          color="red-3"
          size="sm"
          icon="delete_outline"
          no-caps
          @click=";(selected = []), onUpdated" />
      </template>
    </q-select>
  </div>
</template>

<script setup>
import {ref} from 'vue'

const search = ref('')
const _options = ['aaa', 'bbb', 'ccc']
const options = ref(_options)
const selected = ref([])

const selectDom = ref(null)

function filterFn(val = '', update) {
  search.value = val
  update(() => {
    options.value = _options.filter((e) => e.includes(search.value))
  })
}

async function addOption(val) {
  // const tag = search.value
  selectDom.value?.updateInputValue('')
}

async function onUpdated(o) {
  console.log(o)
}

function showCreateBtn() {
  let show = true
  if (search.value) {
    let needle = search.value.toLowerCase()
    let list = []
    _options.forEach((item) => {
      list.push(item.toLowerCase())
    })
    if (list.includes(needle)) {
      show = false
    }
  } else {
    show = false
  }
  return show
}
</script>
