<template>
  <div>
    <draggable v-model="list" group="people" @start="drag = true" @end=";(drag = false), onUpdateSort(listId, list)" item-key="element" handle="._move">
      <template #item="{element}">
        <div :id="`at-section-${element}`">
          <div class="relative-position q-my-md">
            <q-btn flat round dense color="grey-7" icon="drag_indicator" class="_move move-btn">
              <q-tooltip anchor="top middle" self="bottom middle" :offset="[10, 10]">Sort</q-tooltip>
            </q-btn>
            <div>{{ element.message }}</div>
          </div>
        </div>
      </template>
    </draggable>
  </div>
</template>

<script setup>
import {ref} from 'vue'
import draggable from 'vuedraggable'

const listId = 'list-001'
const list = ref([
  {
    _id: '01',
    message: 'hello',
  },
  {
    _id: '02',
    message: 'world',
  },
])

function onUpdateSort(listId, list) {
  // update sort
}
</script>

<style lang="scss">
._move {
  opacity: 0.8;
}
</style>
