<template>
  <q-table title="Subjects" :rows="rows" :columns="columns" :rows-per-page-options="[10, 25, 50, 100]" row-key="_id">
    <template v-slot:body-cell-subject="props">
      <q-td :props="props">
        <div>{{ props.row.name }}</div>
      </q-td>
    </template>
    <template v-slot:body-cell-grade="props">
      <q-td :props="props">
        <div v-if="props.row.grade?.length <= 1">
          <span>{{ props.row.grade[0] }}</span>
        </div>
        <div v-else>
          <span v-for="grade in props.row.grade.slice(0, 1)" :key="grade">{{ grade }}</span>
          <span class="text-grey-6">+ {{ props.row.grade.length }} more</span>
        </div>
      </q-td>
    </template>
    <template v-slot:body-cell-action="props">
      <q-td :props="props">
        <div>
          <q-btn icon="delete" color="red-4" />
        </div>
      </q-td>
    </template>
  </q-table>
</template>

<script setup>
const columns = [
  {
    name: 'subject',
    required: true,
    label: 'Subject',
    align: 'left',
    field: (row) => row.name,
    format: (val) => `${val}`,
    sortable: false,
  },
  {name: 'grade', align: 'center', label: 'Grade', field: 'grade'},
  {name: 'associated-units', align: 'center', label: 'Associated units', field: 'associatedUnits'},
  {name: 'associated-tasks', align: 'center', label: 'Associated tasks', field: 'associatedTasks'},
  {name: 'action', align: 'center', label: 'Action', field: 'action'},
]

const rows = [
  {
    _id: '001',
    name: 'Social Social',
    grade: ['Grade 1'],
    count: {
      unit: 15,
      task: 2,
      classes: 3,
      coordinators: 3,
    },
  },
  {
    _id: '002',
    name: 'Empty data',
    grade: ['Nursery 1', 'Nursery 2', 'Nursery 3'],
    count: {
      unit: 0,
      task: 0,
      classes: 0,
      coordinators: 0,
    },
  },
  {
    _id: '003',
    name: 'Music',
    grade: ['Nursery 1', 'Nursery 2', 'Nursery 3', 'Nursery 4', 'Nursery 5', 'Nursery 6', 'Nursery 8'],
    count: {
      unit: 0,
      task: 15,
      classes: 2,
      coordinators: 3,
    },
  },
]
</script>
