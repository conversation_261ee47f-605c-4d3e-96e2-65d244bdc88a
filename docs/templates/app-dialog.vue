<template>
  <div>
    <q-btn @click="isDialogShow = true" />

    <q-dialog v-model="isDialogShow" persistent>
      <div class="bg-teal-1 q-pa-md rounded-lg">
        <div class="flex justify-end full-width">
          <q-btn flat rounded dense icon="close" @click="isDialogShow = false" />
        </div>
        <div class="q-px-lg q-pb-lg">
          <div>Some stuff</div>
        </div>
        <div class="flex justify-end q-gutter-md">
          <q-btn rounded flat no-caps label="Go back" icon="arrow_back" class="text-grey-7" style="border: 1px solid" @click="() => {}" />
          <q-btn rounded flat no-caps label="Confirm" icon="done" class="bg-teal-4 text-white" @click="() => {}" />
        </div>
      </div>
    </q-dialog>

    <!-- confirm dialog -->
    <q-btn rounded flat no-caps label="Go back" icon="arrow_back" class="text-grey-7" style="border: 1px solid" @click="confirmClick" />
    <!-- ok dialog -->
    <q-btn rounded flat no-caps label="Go back" icon="arrow_back" class="text-grey-7" style="border: 1px solid" @click="okClick" />
  </div>
</template>

<script setup>
import {ref} from 'vue'
import {useQuasar} from 'quasar'

import ConfirmDialog from 'src/components/utils/dialogs/ConfirmDialog.vue'
import OkDialog from 'src/components/utils/dialogs/OkDialog.vue'

const $q = useQuasar()
const isDialogShow = ref(false)

async function confirmClick(item) {
  const title = ''
  const message = ''
  const okButtonLabel = ''
  $q.dialog({
    component: ConfirmDialog,
    componentProps: {title, message, okButtonLabel},
  })
    .onOk(async () => {
      $q.loading.show()
      // do something
      $q.loading.hide()
    })
    .onCancel(() => {})
    .onDismiss(() => {})
}

async function okClick(item) {
  const title = ''
  const message = ''
  const okButtonLabel = ''
  $q.dialog({
    component: OkDialog,
    componentProps: {title, message, okButtonLabel},
  })
    .onOk(async () => {
      $q.loading.show()
      // do something
      $q.loading.hide()
    })
    .onCancel(() => {})
    .onDismiss(() => {})
}

async function tryCatch() {
  try {
    $q.loading.show()
    // await do somethingg
    $q.notify({type: 'positive', message: 'Operator successfully'})
  } catch (error) {
    console.error(error)
    $q.notify({type: 'negative', message: error.message})
  } finally {
    $q.loading.hide()
  }
}
</script>
