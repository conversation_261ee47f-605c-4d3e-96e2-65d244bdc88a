<template>
  <q-card v-show="isTipShow" class="q-pa-md bg-amber-2 flex justify-between items-center" flat style="width: 45rem">
    <div class="flex items-center q-gutter-sm">
      <q-icon name="error" color="amber-7" size="sm" />
      <div class="text-grey-8">Warn: ...</div>
    </div>
    <q-btn dense rounded flat size="sm" icon="close" @click.stop="isTipShow = false" />
  </q-card>
</template>

<script setup>
import {ref} from 'vue'
const isTipShow = ref(false)
</script>
