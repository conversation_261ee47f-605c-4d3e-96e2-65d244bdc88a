<template>
  <main>
    <!-- icon button -->
    <q-btn rounded unelevated label="delete" text-color="grey-8" @click="onClick()" />

    <!-- icon button -->
    <q-btn rounded unelevated dense icon="delete" size="lg" text-color="grey-8" @click="onClick()" />

    <!-- often used buttons -->
    <div class="flex justify-end q-gutter-md">
      <q-btn rounded flat no-caps label="Go back" icon="arrow_back" class="text-grey-7" style="border: 1px solid" @click="() => {}" />
      <q-btn rounded flat no-caps label="Confirm" icon="done" class="bg-teal-4 text-white" @click="() => {}" />
    </div>

    <!-- item button -->
    <q-btn
      flat
      no-caps
      dense
      label="Subject"
      class="q-px-sm"
      :class="[true ? 'bg-teal-4 text-white' : 'bg-white text-teal-4']"
      style="border: 1px solid"
      @click="() => {}" />
  </main>
</template>

<script setup>
import {useQuasar} from 'quasar'
const $q = useQuasar()

const onClick = () => {
  $q.notify({type: 'positive', message: 'Click'})
}
</script>
