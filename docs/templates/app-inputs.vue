<template>
  <main>
    <q-input text-color="dark" v-model="search" outlined dense autofocus placeholder="Search">
      <template v-slot:prepend>
        <q-icon name="search" />
      </template>
    </q-input>

    <q-select outlined v-model="selected" :options="options" map-options emit-value label="Country" multiple use-chips />

    <RecordInput v-model="input" :counter="true" :maxlength="1000" outlined placeholder="input" />
  </main>
</template>

<script setup>
import {ref} from 'vue'

const input = ref('')
const search = ref('')
const selected = ref([])
const options = [
  {label: 'Google', value: 'google'},
  {label: 'Facebook', value: 'facebook'},
  {label: 'Twitter', value: 'twitter'},
]
</script>
