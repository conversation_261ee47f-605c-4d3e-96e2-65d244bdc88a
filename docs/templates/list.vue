<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <q-page class="max-pc q-pa-md">
    <h3>template page</h3>
    <pre>{{ list }}</pre>
  </q-page>
</template>

<script setup>
import {ref, watch, watchEffect} from 'vue'
import {useQuasar} from 'quasar'
import {pubStore} from 'stores/pub'
import {useRoute, useRouter} from 'vue-router'

const $q = useQuasar()
const $route = useRoute()
const $router = useRouter()

const pub = pubStore()
const schoolId = ref('')

const isLoading = ref(false)

const list = ref([])

watchEffect(async () => {
  schoolId.value = pub.user?.schoolInfo?.school || ''
  $q.loading.show()
  isLoading.value = true
  await getList()
  $q.loading.hide()
  isLoading.value = false
})

const getList = async () => {
  const res = await App.service('curriculum').get('pubList')
  list.value = res
}
</script>
