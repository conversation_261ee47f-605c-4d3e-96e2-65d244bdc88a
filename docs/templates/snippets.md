===== Vue =====

```javascript
import {ref, computed, onMounted} from 'vue'
```

===== Quasar =====

```javascript
import {useQuasar} from 'quasar'
const $q = useQuasar()
```

===== Vue Router =====

```javascript
import {useRouter, useRoute} from 'vue-router'

const $router = useRouter()
const $route = useRoute()

// usage
// const path = `/${isSys.value ? 'sys' : 'account'}/academic-setting/subject/${$route.params.tab}/${res._id}`
// const query = {...$route.query, back: $route.fullPath}
// await $router.push({path, query})
```

===== utils =====

```javascript
import nameFormatter from 'src/utils/formatters/nameFormatter'
```
