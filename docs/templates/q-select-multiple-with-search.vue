<template>
  <q-select
    ref="qSelectDom"
    v-model="keywordTexts"
    :options="keywordOptions.filter((e) => !keywordTexts.includes(e))"
    filled
    :placeholder="keywordTexts.length ? '' : 'Identify the key words by using the delimiting function in the sentence above or entering directly.'"
    multiple
    use-input
    use-chips
    color="orange-8"
    input-debounce="0"
    stack-label
    new-value-mode="add-unique"
    :disable="isCooperating"
    @update:modelValue="(texts) => onAddText(texts)"
    @inputValue="(v) => (currentInputText = v)"
    @filter="filterFn">
    <template v-slot:append>
      <q-btn
        v-if="currentInputText && !keywordTexts.find((e) => e === currentInputText) && !keywordOptions.find((e) => e === currentInputText)"
        flat
        dense
        color="teal"
        style="border: 1px solid #aaa"
        no-caps
        label="Create"
        icon="add"
        :disable="isCooperating"
        @click="qSelectDom.add(currentInputText, true), qSelectDom.updateInputValue('')" />
    </template>
  </q-select>
</template>

<script setup>
import {ref} from 'vue'

const isCooperating = ref(false)
const qSelectDom = ref(null)
const keywordTexts = ref('')
const currentInputText = ref('')
const keywordOptions = ref([])
async function getKeywordOptions(search = '') {
  const res = await App.service('unit').get('recommendWords', {query: {key: search}})
  keywordOptions.value = res
}
async function filterFn(value, update) {
  update(async () => {
    await getKeywordOptions(value)
  })
}

function onAddText(texts) {
  console.log(texts)
}
</script>
