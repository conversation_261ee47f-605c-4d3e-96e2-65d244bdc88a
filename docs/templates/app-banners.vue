<template>
  <main>
    <q-card v-if="isBannerShow" class="q-my-md q-pa-md bg-amber-2 flex justify-between items-center" flat style="width: 45rem">
      <div class="flex items-center q-gutter-sm">
        <q-icon name="error" color="amber-7" size="sm" />
        <div class="text-grey-8">A revised version has been saved in draft mode. To view the draft, click the "Edit" button above.</div>
      </div>
      <q-btn dense rounded flat size="sm" icon="close" @click.stop="isBannerShow = false" />
    </q-card>

    <!-- full width -->
    <q-card v-if="isBannerShow" class="q-pa-md bg-amber-2 flex justify-between items-center full-width" flat>
      <div class="flex items-center q-gutter-sm">
        <q-icon name="error" color="amber-7" size="sm" />
        <div class="text-grey-8">Only school admin and class head teacher can edit all the class settings.</div>
      </div>
      <q-btn dense rounded flat size="sm" icon="close" @click.stop="isBannerShow = false" />
    </q-card>
  </main>
</template>

<script setup>
import {ref} from 'vue'

const isBannerShow = ref(false)
</script>
